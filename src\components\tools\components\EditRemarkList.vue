<template>
    <div class="m-remark-list">
        <el-scrollbar style="height: 100%" class="overflow-x-hide" ref="scrollDiv">
            <div style="height: 100%">
                <EditRemark 
                v-for="(item, index) in viewportsFilter" :key="index" 
                ref="remarkBox"
                @_click="onClickRemark(item)" 
                :viewportData="item" 
                :sSOPInstanceUID="sSOPInstanceUID"></EditRemark>
            </div>
        </el-scrollbar>
    </div>

</template>
<script>
import EditRemark from './EditRemark.vue';

export default {
    components: {
        EditRemark
    },
    props: {
        viewports: {
            type: Array,
            default: () => []
        },
        sSOPInstanceUID: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            scrollStop: false,
        }
    },
    computed: {
        viewportsFilter() {
            const items = this.viewports.filter(item => item.isCaptrue) || []
            return items
        }
    },
    watch: {
        sSOPInstanceUID(later) {
            setTimeout(() => {
                if (!later) {
                    return
                }
                // 点击备注项的时候，列表不滚动
                if (this.scrollStop) {
                    this.scrollStop = false
                    return
                }
                const idx = this.viewportsFilter.findIndex(item => item.sSOPInstanceUID == later)
                if (idx != -1) {
                    const domWrap = this.$refs.scrollDiv.$refs['wrap']
                    const domCurrent = this.$refs['remarkBox'][idx]
                    this.$nextTick(() => {
                        let offset = domCurrent?.$el?.offsetTop || 0
    
                        domWrap.scrollTo({ top: offset - 10, behavior: 'smooth' });
                    })
                }
            }, 10);
        }
    },
    methods: {
        onClickRemark(item) {
            this.scrollStop = true
            this.$emit('selectThumb', item.sSOPInstanceUID)
        }
    },
    mounted() {
        setTimeout(() => {
            if (this.$refs.scrollDiv) {
                this.$refs.scrollDiv.update()
            }
        }, 2000);
    }
}

</script>
<style lang="scss" scoped>
.m-remark-list {
    height: calc(100% - 440px);
    border-top: 1px solid rgb(210, 210, 221);
    ::v-deep .el-scrollbar__bar {
        z-index: 2;
    }
}
</style>