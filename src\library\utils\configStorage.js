
const configs = {
    windowCenter: {             // 窗宽窗位-设备项
        modalityList: [
            {id: 1, name: 'CT', tags: ['CT']},
            {id: 2, name: 'PT', tags: ['PT']},
            {id: 3, name: 'NM', tags: ['NM']},
            {id: 4, name: 'MR', tags: ['MR']},
            {id: 5, name: '截屏图', tags: ['IMG']},
            {id: 6, name: 'PT-MIP图', tags: ['PT-MIP']}
        ],
        wLList: {               // 设备对于渲染 voi 值
            1: [
                {ww: '80', wl: '35', name: '脑'},
                {ww: '1200', wl: '-600', name: '肺'},
                {ww: '200', wl: '40', name: '肝脏'},
                {ww: '400', wl: '40', name: '纵膈'},
                {ww: '300', wl: '40', name: '腹'},
                {ww: '150', wl: '40', name: '脊'},
                {ww: '1500', wl: '450', name: '骨'}
            ],
            2: [
                {ww: '6', wl: '0', name: 'Pet1'},
                {ww: '10', wl: '0', name: 'Pet2'},
                {ww: '15', wl: '0', name: 'Pet3'},
                {ww: '20', wl: '0', name: 'Pet4'},
                {ww: '30', wl: '0', name: 'Pet5'},
            ],
            3: [
                {ww: '20000', wl: '10000', name: 'NM1'},
                {ww: '75', wl: '35', name: 'NM2'},
            ],
            4: [
                {ww: '500', wl: '100', name: 'MR1'},
                {ww: '906', wl: '453', name: 'MR2'},
                {ww: '1280', wl: '640', name: 'MR3'},
                {ww: '4088', wl: '2044', name: 'MR4'},
                {ww: '4286', wl: '2143', name: 'MR5'},
                {ww: '5038', wl: '2519', name: 'MR6'},
            ],
            5: [
                {ww: '256', wl: '128', name: 'IMG1'},
            ],
            6: [
                {ww: '6', wl: '0', name: 'Pet1'},
                {ww: '10', wl: '0', name: 'Pet2'},
                {ww: '15', wl: '0', name: 'Pet3'},
                {ww: '20', wl: '0', name: 'Pet4'},
                {ww: '30', wl: '0', name: 'Pet5'},
            ],
        }
    },
    tools: {                  // 工具配置
		fontSize: 16,
        padding: 2,
		toolWidth: 1,
		toolColor: '#00FF00',
		// activeColor: 'rgb(0, 255, 0)',
        fontWeight: 'bold',
        fontFamily: 'Microsoft YaHei',
	},
    toolsColor: {
        Length: '',
        ArrowAnnotate: '',
        TextMarker: '',
        CircleRoi: '',
        EllipticalRoi: '',
        RectangleRoi: '',
    },
    mouseEvents: {           // 鼠标滚轮按下,鼠标右键
        mid: 'Pan',
        right: 'Zoom'
    },
    toolRepeatUse: false,
    toolNewImageUse: false,  // 翻页后标注还能保持
    toolSync: true,
    pet: {                   // pet 测量方法配置
        WindowLevel: {
            u: 6,
            l: 0
        },
        methodSUV: 1, // 1 默认
    },
    rebuildMate: [            // 重建匹配规则
        // {
        //     scheme: 0,
        //     checkPart: '全身',
        //     customName: '',
        //     sort: '',
        //     differ: { number: '', series: '' },
        //     series1: {
        //         checkModality: 'CT',
        //         mathSymbol: 0,
        //         mathValue: 150,
        //         seriesNumber:'',
        //         seriesDesc: '',
        //         imageDesc: ''
        //     },
        //     series2: {
        //         checkModality: 'PT',
        //         mathSymbol: 0,
        //         mathValue: 150,
        //         seriesNumber:'',
        //         seriesDesc: '',
        //         imageDesc: ''
        //     }
        // }
    ],
    rebuildAbout: {            // 序列匹配-相关设置
        number: 2,
        multipleNotAuto: true,
    },
    dotProbe: 6,                // 点测量半径值
    circleRoiDiameter: true,    // 圆测量直径
    circleMeasure: {
        thresholdType: 1,
        percentThreshold: 25,
        staticThreshold: 3.5,
    },
    modalityTagItems: {
        CT: { 
            leftTop: [
                { id: 13, value: 'PatientsName', tagName: "Patient's Name" },
                { id: 18, value: 'PatientsBirthDate', tagName: "Patient's Birth Date" },
                { id: 17, value: 'PatientID', tagName: "Patient ID" },
                { id: 16, value: 'PatientsAge', tagName: "Patient's Age" },
            ], 
            rightTop: [
                { id: 25, value: 'SeriesDate', tagName: "Series Date" },
                { id: 24, value: 'Modality', tagName: "Modality" },
                { id: 11, value: 'InstitutionName', tagName: "Institution Name" },
            ], 
            rightBottom: [
                { id: 22, value: 'PatientPosition', tagName: "Patient Position" },
                { id: 6, value: 'wwwc', tagName: "WL" },
                { id: 7, value: 'ImagePositionPatient', tagName: "Image Position Patient" },
                { id: 5, value: 'instanceNumber', tagName: "Instance Number" },
            ], 
            leftBottom: [] 
        },
        PT: { 
            leftTop: [
                { id: 13, value: 'PatientsName', tagName: "Patient's Name" },
                { id: 18, value: 'PatientsBirthDate', tagName: "Patient's Birth Date" },
                { id: 17, value: 'PatientID', tagName: "Patient ID" },
                { id: 16, value: 'PatientsAge', tagName: "Patient's Age" },
            ], 
            rightTop: [
                { id: 25, value: 'SeriesDate', tagName: "Series Date" },
                { id: 24, value: 'Modality', tagName: "Modality" },
            ], 
            rightBottom: [
                { id: 22, value: 'PatientPosition', tagName: "Patient Position" },
                { id: 6, value: 'wwwc', tagName: "WL" },
                { id: 7, value: 'ImagePositionPatient', tagName: "Image Position Patient" },
                { id: 5, value: 'instanceNumber', tagName: "Instance Number" },
            ], 
            leftBottom: []
        },
        MR: { 
            leftTop: [
                { id: 13, value: 'PatientsName', tagName: "Patient's Name" },
                { id: 17, value: 'PatientID', tagName: "Patient ID" },
                { id: 15, value: 'PatientsSex', tagName: "Patient's Sex" },
                { id: 29, value: 'PercentPhaseFieldOfView', tagName: "Percent Phase Field Of View" },
            ], 
            rightTop: [
                { id: 23, value: 'SeriesDescription', tagName: "Series Description" },
                { id: 19, value: 'StudyDate', tagName: "Study Date" },
                { id: 24, value: 'Modality', tagName: "Modality" },
            ], 
            rightBottom: [
                { id: 32, value: 'EchoTime', tagName: "Echo Time" },
                { id: 31, value: 'RepetitionTime', tagName: "Repetition Time" },   
                { id: 30, value: 'SequenceName', tagName: "Sequence Name" }, 
                { id: 22, value: 'PatientPosition', tagName: "Patient Position" },
                
            ], 
            leftBottom: [
                { id: 7, value: 'ImagePositionPatient', tagName: "Image Position Patient" },
                { id: 6, value: 'wwwc', tagName: "WL" },
                { id: 5, value: 'instanceNumber', tagName: "Instance Number" },
            ] 
        },
        NM: { 
            leftTop: [
                { id: 13, value: 'PatientsName', tagName: "Patient's Name" },
                { id: 17, value: 'PatientID', tagName: "Patient ID" },
                { id: 15, value: 'PatientsSex', tagName: "Patient's Sex" },
            ], 
            rightTop: [
                { id: 23, value: 'SeriesDescription', tagName: "Series Description" },
                { id: 19, value: 'StudyDate', tagName: "Study Date" },
                { id: 24, value: 'Modality', tagName: "Modality" },
            ], 
            rightBottom: [
                { id: 22, value: 'PatientPosition', tagName: "Patient Position" },
            ], 
            leftBottom: [
                { id: 7, value: 'ImagePositionPatient', tagName: "Image Position Patient" },
                { id: 6, value: 'wwwc', tagName: "WL" },
                { id: 5, value: 'instanceNumber', tagName: "Instance Number" },
            ] 
        },
        default: { 
            leftTop: [
                { id: 13, value: 'PatientsName', tagName: "Patient's Name" },
                { id: 17, value: 'PatientID', tagName: "Patient ID" },
                { id: 15, value: 'PatientsSex', tagName: "Patient's Sex" },
            ], 
            rightTop: [
                { id: 23, value: 'SeriesDescription', tagName: "Series Description" },
                { id: 19, value: 'StudyDate', tagName: "Study Date" },
                { id: 24, value: 'Modality', tagName: "Modality" },
            ], 
            rightBottom: [
                { id: 22, value: 'PatientPosition', tagName: "Patient Position" },
            ], 
            leftBottom: [
                { id: 7, value: 'ImagePositionPatient', tagName: "Image Position Patient" },
                { id: 6, value: 'wwwc', tagName: "WL" },
                { id: 5, value: 'instanceNumber', tagName: "Instance Number" },
            ] 
        },
    },
    synchronization: {
        start: true,
        position: false,
        zoomPan: true,
        window: false
    },
    crosshairsShow: true,  // 定位线默认显示
    mipClickLocation: true,  // MIP图点击定位病灶功能：
    crosshairsType: {      // 定位线类型
        CT: 'large',
        PT: 'large',
        FUSE: 'large',
        MIP: 'large',
        toolColor: '#FF0000',
    },
    mipCrosshairDisplay: '0',  
    readDefaultLayout: '', // 阅图模式自动布局
    defaultLayout: [
        { name: 'PetCt', key: 'PTCT', value: '', module: '' },
        { name: 'PetMr', key: 'PTMR', value: '', module: '' },
        { name: 'SpetCt', key: 'NMCT', value: '', module: '' },
        { name: 'CT', key: 'CT', value: '', module: '' },
        { name: 'PET', key: 'PT', value: '', module: '' },
        { name: 'MR', key: 'MR', value: '', module: '' }
    ],
    rebuildLayout: [  // 重建默认存在的布局
        // CT/MR、PT/NM     TODO 注意不要改这些存储位置
        // 0、1冠
        // 2、3矢
        // 4、5横
        // 6、7mip
        // 8 冠融合  1,0
        // 9 矢融合  3,2
        // 10 横融合 5,4
        // 11 mip融合 6 7
        // 由于 MIP 是通过 vtk.js 实现的。 cornerstone 没有 MIP 图
        {
            img: 'images/Layout3D_1.png',
            text: 'PET/CT、融合横断位',
            id: '2x2axial',
            options: {
                layout: {
                    column: 2,
                    row: 2
                },
                selectValue: [4,5, 10,7], // 4,5,10, 7
            }
        },

        {
            img: '/images/Layout3D_1.png',
            text: 'PET/CT、融合冠状位',
            id: '2x2coronal',
            hidden: true,
            options: {
                layout: {
                    column: 2,
                    row: 2
                },
                selectValue: [0,1,8, 7], // originSeries 对应 下标位置
            }
        },
        {
            img: '/images/Layout3D_1.png',
            text: 'PET/CT、融合矢状位',
            id: '2x2sagittal',
            hidden: true,
            options: {
                layout: {
                    column: 2,
                    row: 2
                },
                selectValue: [2,3,9, 7],
            }
        },
        {
            img: 'images/Layout3D_2.png',
            text: 'PET/CT 三维图',
            id: '3x3column',
            options: {
                layout: {
                    column: 3,
                    row: 3
                },
                selectValue: [1,3,5, 0,2,4, 8,9,10]
            }
        },
        {
            img: 'images/Layout3D_9.png',
            text: 'PET/CT 3x3 融合横断位',
            id: '3x3axial',
            options: {
                layout: {
                    column: 3,
                    row: 3
                },
                selectValue: [5,5,5, 4,4,4, 10, 10, 10],
            }
        },
        {
            img: 'images/Layout3D_5.png',
            text: 'PET/CT 4x4 非融合横断位',
            id: '4x4',
            options: {
                layout: {
                    column: 4,
                    row: 4
                },
                selectValue: [
                    5, 5, 5, 5,
                    4, 4, 4, 4, 
                    5, 5, 5, 5,
                    4, 4, 4, 4
                ], // originSeries 对应 下标位置
            }
        },
        {
            img: 'images/Layout3D_8.png',
            text: 'CT/MR 4x4 横断位',
            id: '4x4CT/MR',
            options: {
                layout: {
                    column: 4,
                    row: 4
                },
                selectValue: [4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], // originSeries 对应 下标位置
            }
        },
        {
            img: 'images/Layout3D_7.png',
            text: 'PT/NM 4x4 横断位',
            id: '4x4PT/NM',
            options: {
                layout: {
                    column: 4,
                    row: 4
                },
                selectValue: [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
            }
        },
        {
            img: 'images/Layout3D_11.png',
            text: 'CT/MR 融合',
            id: '1x1x1fuse',
            options: {
                layout: {
                    column: 2,
                    num: 3,
                    row: 2
                },
                selectValue: [10,8,9],
                containerStyle: {
                    flexDirection: 'column',
                },
                itemStyle: [
                    {height: '100%', width: '50%'},
                    {height: '50%', width: '50%'},
                    {height: '50%', width: '50%'},
                ]
            }
        },
        {
            img: 'images/Layout3D_10.png',
            text: 'PET/CT 三维图+3D',
            id: '3x4PT/NM',
            options: {
                layout: {
                    column: 4,
                    row: 3
                },
                selectValue: [1, 3, 5, 7, 0, 2, 4, 6, 8, 9, 10, 11],
            }
        },
    ],
    isRemark: false,         // 是否截图时候填备注
    isReadShowRemark: false, // 阅图是否在图像上显示备注
    showRebuild: {
        colormap: 'x_hot',
        petU: 10,
        mipMagnify: true
    },
    layoutFill: true,
    imageFilter2Mode: 1,     // 平滑
    interpolationMode: 1,   // 插值
    imageSampleDistance: 1, // MIP 采样
    imageFilterSharpen: 0, // 图像锐化
    imageFilteWithoutCnS: 0, // 冠矢位图像不处理
    imageClipClarity: 900,
    showReadModality: [],   // 阅图显示设备（在截图的基础上显示）
    rebuildSyncZoom: false, // 同步冠、矢缩放
    showShortcutKey: true,  // 工具栏显示快捷键提示
    defaultResetMode: ['rotation', 'size', 'wwwc'], // 重置按钮选择重置哪些东西
    lengthUnit: 'cm',
    syncMipWwwc: false,// 同步 MIP PT 的窗宽窗位
    excludeDescriptions: [], // 排除的序列描述
    readDcmSeting: {
        isSrollAll: 0, // 整页滚动 1 单页
        selectSync: false, // 选择同步
        mergeMri: false, // 大坪医院合并MR图像
        mergeNo: ['screen', 'pages'],
    },
    lockSync: {
        start: true,            // 启动锁
        windowColor: false,     // 窗宽窗位同步
        convert: true,          // 旋转，翻转，反片
        moveZoom: true,         // 移动、缩放
        scroll:  true,          // 翻页
    },
    dragDefaultLayoutId: '',    // 拖拽默认布局
    scrollMouseWheelLoop: true, // 翻页循环
    zoomToCenter: false, // 通过图像中心放大
    textShadow: 1, 
    showDirection: true,
    measurementAccuracy: '2',
}



// 获取默认配置
function _getDefaultTemplate(key) {
    let defaultTemplate = {};
    switch (key) {
        case 'configs-WL':
            defaultTemplate = configs.windowCenter
            break;
        case 'configs-tools':
            defaultTemplate = configs.tools
            break;
        case 'configs-toolsColor':
            defaultTemplate = configs.toolsColor
            break;
        case 'configs-lengthUnit':
            defaultTemplate = configs.lengthUnit
            break;
        case 'configs-measurementAccuracy':
            defaultTemplate = configs.measurementAccuracy
            break;
        case 'configs-mouse':
            defaultTemplate = configs.mouseEvents
            break;
        case 'configs-toolNewImageUse':
            defaultTemplate = configs.toolNewImageUse
            break;
        case 'configs-toolRepeatUse':
            defaultTemplate = configs.toolRepeatUse
            break;
        case 'configs-toolSync':
            defaultTemplate = configs.toolSync
            break;
        case 'configs-PET':
            defaultTemplate = configs.pet
            break;
        case 'configs-rebuild-about':
            defaultTemplate = configs.rebuildAbout
            break;
        case 'configs-rebuild-mate':
            defaultTemplate = configs.rebuildMate
            break;
        case 'configs-dot':
            defaultTemplate = configs.dotProbe
            break;
        case 'configs-circleRoi':
            defaultTemplate = configs.circleRoiDiameter
            break;
        case 'configs-circleMeasure':
            defaultTemplate = configs.circleMeasure
            break;
        case 'configs-modalityTagItems':
            defaultTemplate = configs.modalityTagItems
            break;
        case 'synchronization':
            defaultTemplate = configs.synchronization
            break;
        case 'configs-crosshairs-show':
            defaultTemplate = configs.crosshairsShow
            break;
        case 'configs-crosshairs-type':
            defaultTemplate = configs.crosshairsType
            break;
        case 'configs-mipCrosshairDisplay':
            defaultTemplate = configs.mipCrosshairDisplay
            break
        case 'configs-read-default-layout':
            defaultTemplate = configs.readDefaultLayout
            break;
        case 'configs-default-layout':
            defaultTemplate = configs.defaultLayout
            break;
        case 'configs-rebuild-layout':
            defaultTemplate = configs.rebuildLayout
            break;
        case 'setting-remark':
            defaultTemplate = configs.isRemark
            break;
        case 'setting-read-show-remark':
            defaultTemplate = configs.isReadShowRemark
            break;
        case 'configs-show-rebuild':
            defaultTemplate = configs.showRebuild
            break;
        case 'configs-layoutFill':
            defaultTemplate = configs.layoutFill
            break;
        case 'configs-interpolationMode':
            defaultTemplate = configs.interpolationMode
            break;
        case 'configs-imageFilter2Mode':
            defaultTemplate = configs.imageFilter2Mode
            break;
        case 'configs-imageSampleDistance':
            defaultTemplate = configs.imageSampleDistance
            break;
        case 'configs-imageFilterSharpen':
            defaultTemplate = configs.imageFilterSharpen
            break;
        case 'configs-imageFilteWithoutCnS':
            defaultTemplate = configs.imageFilteWithoutCnS
        case 'configs-imageClipClarity':
            defaultTemplate = configs.imageClipClarity
            break;
        case 'configs-show-read-modality':
            defaultTemplate = configs.showReadModality
            break; 
        case 'configs-rebuild-sync-zoom':
            defaultTemplate = configs.rebuildSyncZoom
            break;
        case 'configs-show-shortcut-key':
            defaultTemplate = configs.showShortcutKey
            break;
        case 'configs-default-reset-mode':
            defaultTemplate = configs.defaultResetMode
            break;
        case 'configs-syncMipWwwc':
            defaultTemplate = configs.syncMipWwwc
            break;
        case 'configs-exclude-descriptions':
            defaultTemplate = configs.excludeDescriptions
            break;
        case 'configs-readDcmSeting':
            defaultTemplate = configs.readDcmSeting
            break;
        case 'configs-dragLockSync':
            defaultTemplate = configs.lockSync
            break;
        case 'configs-dragDefaultLayoutId':
            defaultTemplate = configs.dragDefaultLayoutId
            break;
        case 'configs-scrollMouseWheelLoop':
            defaultTemplate = configs.scrollMouseWheelLoop
            break; 
        case 'configs-zoomToCenter':
            defaultTemplate = configs.zoomToCenter
            break; 
        case 'configs-textShadow':
            defaultTemplate = configs.textShadow
            break; 
        case 'configs-show-direction':
            defaultTemplate = configs.showDirection
            break; 
        case 'configs-mipClickLocation':
            defaultTemplate = configs.mipClickLocation
            break; 
        default:
            break;
    }
    return defaultTemplate;
}

// 获取配置列表,有缓存要缓存,没有要默认配置,并把配置存到缓存中
export default function getConfigByStorageKey(key){
    let obj = localStorage.getItem(key)
    if (!obj){
        let defaultTemplate = _getDefaultTemplate(key);
        localStorage.setItem(key, JSON.stringify(defaultTemplate))
        obj = defaultTemplate;
    }else {
        try {
            obj = JSON.parse(obj)
        } catch (error) {
            console.log(error)
        }
    }
    return obj;
}

// export 默认配置方法
export function getConfigByKey(key) {
    return _getDefaultTemplate(key);
}

// 通过设备获取WL配置列表
export function getListByModality(modality) {
    const { modalityList, wLList } = getConfigByStorageKey('configs-WL')
    const findObj = modalityList.find(item => {
        if (item.tags.includes(modality) || item.tags.includes('ALL')){
            return true;
        }
    })
    if (findObj){
        const res = wLList[findObj.id] || [];
        return res;
    }
    return [];
}


