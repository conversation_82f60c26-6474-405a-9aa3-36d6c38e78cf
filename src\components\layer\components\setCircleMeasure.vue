<template>
    <div class="c-content">
        <ul>
            <li>
                <span>阈值类型</span>
                <el-radio-group class="radio-group" v-model="thresholdType">
                    <el-radio v-for="item in thresholdTypeList" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
                </el-radio-group>
            </li>
            <li>
                <span>百分比阈值 ( % ) </span> 
                <el-input-number v-model="percentThreshold" :min="1" :max="100" :step="1" :step-strictly="true" size="small"></el-input-number>
                <span></span> 
            </li>
            <li>
                <span>固定阈值 ( SUV ) </span>
                <el-input-number v-model="staticThreshold" :min="1" :max="999" size="small"></el-input-number>
            </li> 
            <li>
                <span>在融合图上显示HU值</span>
                <el-switch
                    v-model="isShowHU"
                    active-color="#13ce66"
                    inactive-color="#eeeeee">
                </el-switch>
            </li> 
        </ul> 
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setCircleMeasure',
    data() {
        return {
            storageKey: 'configs-circleMeasure',
            
            
            thresholdType: 1,
            percentThreshold: 25,
            staticThreshold: 3.5,
            isShowHU: true,
            thresholdTypeList: [
                {
                    value: 1,
                    name: '百分比阈值'
                },
                {
                    value: 2,
                    name: '固定阈值'
                }
            ]
        }
    },
    mounted() {
        const dataObj = getConfigByStorageKey(this.storageKey);
        this.thresholdType = dataObj.thresholdType
        this.percentThreshold = dataObj.percentThreshold
        this.staticThreshold = dataObj.staticThreshold
        this.isShowHU = !!dataObj.isShowHU
    },
    methods: {
        onClickSave() {
            // 设置新的缓存
            const dataObj = {
                thresholdType: this.thresholdType,
                percentThreshold: this.percentThreshold,
                staticThreshold: this.staticThreshold,
                isShowHU: this.isShowHU,
            }
            localStorage.setItem(this.storageKey, JSON.stringify(dataObj));

            this.$message({
                message: '保存成功！',
                type: 'success'
            });
            // this.$emit('close')
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    > ul{
        flex: 1;
        li{
            height: 70px;
            display: flex;
            align-items: center;
            padding-bottom: 30px;
            span{
                padding-right: 10px;
            }
        }
    }
    > footer{
        height: 40px;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}
</style>