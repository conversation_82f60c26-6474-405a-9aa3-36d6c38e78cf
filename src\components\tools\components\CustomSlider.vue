<template>
    <div class="custom-slider">
        <el-input class="input" size="mini" v-model="valueWW" @keyup.enter.native="onEnterWindowLevel('WW')" v-floorNumber></el-input>
        <div class="parent-box" ref="scroll">
            <div class="pointer pointer-max" @touchstart="onTouchMoveMax" @mousedown="onMoveMax" :style="{top: curArrowWWValue + 'px'}">
            </div>
            <div v-if="WLUnitStype !== 'PT' && WLUnitStype !== ''" class="pointer pointer-mid" @touchstart="onTouchMoveMid" @mousedown="onMoveMid" :style="{top: curArrowMidalue + 'px'}">
            </div>
            <div class="pointer pointer-min" @touchstart="onTouchMoveMin" @mousedown="onMoveMin" :style="{top: curArrowWLValue + 'px'}">
            </div>
            <canvas class="canvas" ref="canvas"></canvas>
        </div>
        <el-input class="input" size="mini" v-model="valueWL" @keyup.enter.native="onEnterWindowLevel('WL')" v-floorNumber></el-input>
    </div>
</template>
<script>
export default {
    props: {
        WLUnitStype: { // 计算单位（CT、PT）
            type: String,
            default: 'CT'
        },
        renderWindow: {
            type: [String, Number],
            default: 0
        },
        renderLevel: {
            type: [String, Number],
            default: 0
        },
        showWindow: {
            type: [Number, String],
            default: 0
        },
        showLevel: {
            type: [Number, String],
            default: 0
        },
        colormapId: {
            type: String,
            default: ''
        },
        invert: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            defaultWLMaxMin: [
                {id: 'CT', max: 3072, min: -1024},
                {id: 'PT', max: 10, min: 0},
                {id: 'MR', max: 3072, min: -1024},
                {id: 'NM', max: 3072, min: -1024},
                {id: 'IMG', max: 256, min: 0},
            ],
            valueWW: 0,             // 窗宽
            valueWL: 0,             // 窗位
            arrowPositionTopMin: 0, // 箭头位置top最小值 值小在上 （用的 css top，像素值）
            arrowPositionTopMax: 0, // 箭头位置top最大值 值大在下 （用的 css top，像素值）
            curArrowWWValue: 0,    // 当前ww值
            curArrowWLValue: 18,   // 当前wl值
            curArrowMidalue: 9,    // CT中间值
            arrowHeight: 18,       // 箭头高度
        }
    },
    computed: {
        windowLevel(){
            return this.renderWindow + this.renderLevel
        }
    },
    watch: {
        WLUnitStype: {
            handler(){
                this.onLastSelectChangeed();
            },
            immediate: true
        },
        windowLevel(){
            this.setWLPosition(this.showWindow, this.showLevel)

            this.updateColorbar();
        },
        colormapId: {
            handler(){
                this.updateColorbar();
            },
            immediate: true
        },
        invert: {
            handler(){
                this.updateColorbar();
            },
            immediate: true
        },
    },
    methods: {
        // 最后一个选择的画布被改变
        onLastSelectChangeed(){
            this.setWLValueZone();
            this.setWLPosition(this.showWindow, this.showLevel)
        },
        // 设置窗宽窗位的范围
        setWLValueZone(){
            // 获取默认范围
            const defaultWindowLevel = this.defaultWLMaxMin.find(item => {
                return item.id === this.WLUnitStype
            })
            if (!defaultWindowLevel) return;

            this.valueWW = defaultWindowLevel.max;
            this.valueWL = defaultWindowLevel.min;
        },
        // 设置窗宽窗位的位置
        /**
         * wvalue, lvalue 输入框显示的值   PT 显示的值滑块区间是一样的。
         */
        setWLPosition(showWvalue, showLvalue){
            if (!this.WLUnitStype) return;
            let wvalue = Number(showWvalue);
            let lvalue = Number(showLvalue);
            let w = wvalue;
            let l = lvalue;
            if (this.WLUnitStype !== 'PT'){
                // CT 要转换成为 voi 值
                w = lvalue + wvalue / 2;
                l = lvalue - wvalue / 2;
            }
            // 设置上下箭头位置
            this.setWLTopBottom(w, l)

            // 设置中间值 -- 放在这里，存在箭头渲染慢，以为在外层渲染方法中使用 settimeout
            this.setMiddleArrow(w, l)
        },
        /**
         * 设置中间箭头的位置
         * w l 区间值
         */
        setMiddleArrow(w, l){
            let positionValue = (l + w) / 2;

            const cellvalue = (this.valueWW - this.valueWL) / (this.arrowPositionTopMax - this.arrowHeight);

            const midpos = (this.valueWW - positionValue) / cellvalue;
            const mid = (this.arrowHeight / 2) + midpos;

            this.curArrowMidalue = mid;

        },
        // ww 箭头
        onMoveMax(e){
            const el = e.target;
            let disY = e.clientY - el.offsetTop;
            document.onmousemove = (e) => {
                if (this.WLUnitStype === '') return;
                let moreValue = e.clientY - disY;
                // 不能大于 wl 箭头位置（用的是 css top 像素）
                if (moreValue > this.curArrowWLValue  - this.arrowHeight){
                    moreValue = this.curArrowWLValue - this.arrowHeight
                }else if (moreValue > this.arrowPositionTopMax){
                    // 不能超过最大箭头位置
                    moreValue = this.arrowPositionTopMax;
                }else if (moreValue < this.arrowPositionTopMin){
                    // 不能小于最小箭头位置
                    moreValue = this.arrowPositionTopMin
                }
                this.curArrowWWValue = moreValue;

                this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)
            }
            document.onmouseup = (e) => {
                document.onmousemove = null;
                document.onmouseup   = null;
            }

        },
        // wl 箭头
        onMoveMin(e){
            const el = e.target;
            let disY = e.clientY - el.offsetTop;
            document.onmousemove = (e) => {
                if (this.WLUnitStype === '') return;
                let moreValue = e.clientY - disY;
                // 不能小于 ww 箭头位置（用的是 css top 像素）
                if (moreValue < this.curArrowWWValue + this.arrowHeight){
                    moreValue = this.curArrowWWValue + this.arrowHeight
                }else if (moreValue > this.arrowPositionTopMax){
                    // 不能超过最大箭头位置
                    moreValue = this.arrowPositionTopMax;
                }else if (moreValue < this.arrowPositionTopMin){
                    // 不能小于最小箭头位置
                    moreValue = this.arrowPositionTopMin
                }
                this.curArrowWLValue = moreValue;
                this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)
            }
            document.onmouseup = (e) => {
                document.onmousemove = null;
                document.onmouseup   = null;
            }
        },
        // 中间箭头
        onMoveMid(e){
            const el = e.target;
            let disY = e.clientY - el.offsetTop;
            document.onmousemove = (e) => {
                let moreValue = e.clientY - disY;

                if (moreValue > ( this.arrowPositionTopMax - (this.arrowHeight / 2) )){
                    // 不能超过最大箭头位置
                    moreValue = this.arrowPositionTopMax - (this.arrowHeight / 2)
                }else if (moreValue < ( this.arrowPositionTopMin + (this.arrowHeight / 2) )){
                    // 不能小于最小箭头位置
                    moreValue = this.arrowPositionTopMin + (this.arrowHeight / 2)
                }
                const value = (this.curArrowWLValue - this.curArrowWWValue) / 2;

                // 移动中间箭头后，相应需要变换的 ww、wl 箭头
                let arrowWW = (moreValue - value);  
                let arrowWL = (moreValue + value);
                if (arrowWW < this.arrowPositionTopMin){
                    // TODO 应该赋 ww 箭头最大值，并且通过输入框中的值（center）装换窗位相应的箭头位置
                    // this.curArrowWWValue = this.arrowPositionTopMin;
                    // this.curArrowWLValue = TODO
                    return;
                }else if (arrowWL > this.arrowPositionTopMax){
                    // TODO 应该赋 wl 箭头最大值，并且通过输入框中的值（width）装换窗位相应的箭头位置
                    // this.curArrowWWValue = TODO;
                    // this.curArrowWLValue = this.arrowPositionTopMax;
                    // this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)
                   return;
                }
                else if (arrowWW == this.curArrowWWValue && arrowWL == this.curArrowWLValue){
                    return;
                }
                this.curArrowMidalue = moreValue;
                this.curArrowWWValue = arrowWW;
                this.curArrowWLValue = arrowWL;

                this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)

            }
            document.onmouseup = (e) => {
                document.onmousemove = null;
                document.onmouseup = null;
            }
        },
        onTouchMoveMax(e) {
            const el = e.changedTouches[0].target;
            let disY = e.changedTouches[0].clientY - el.offsetTop;
            document.ontouchmove = (e) => {
                if (this.WLUnitStype === '') return;
                let moreValue = e.changedTouches[0].clientY - disY;
                // 不能大于 wl 箭头位置（用的是 css top 像素）
                if (moreValue > this.curArrowWLValue  - this.arrowHeight){
                    moreValue = this.curArrowWLValue - this.arrowHeight
                }else if (moreValue > this.arrowPositionTopMax){
                    // 不能超过最大箭头位置
                    moreValue = this.arrowPositionTopMax;
                }else if (moreValue < this.arrowPositionTopMin){
                    // 不能小于最小箭头位置
                    moreValue = this.arrowPositionTopMin
                }
                this.curArrowWWValue = moreValue;

                this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)
            }
            document.ontouchend = () => {
                document.ontouchmove = null;
                document.ontouchend   = null;
            }
        },
        // wl 箭头
        onTouchMoveMin(e){
            const el = e.changedTouches[0].target;
            let disY = e.changedTouches[0].clientY - el.offsetTop;
            document.ontouchmove = (e) => {
                if (this.WLUnitStype === '') return;
                let moreValue = e.changedTouches[0].clientY - disY;
                // 不能小于 ww 箭头位置（用的是 css top 像素）
                if (moreValue < this.curArrowWWValue + this.arrowHeight){
                    moreValue = this.curArrowWWValue + this.arrowHeight
                }else if (moreValue > this.arrowPositionTopMax){
                    // 不能超过最大箭头位置
                    moreValue = this.arrowPositionTopMax;
                }else if (moreValue < this.arrowPositionTopMin){
                    // 不能小于最小箭头位置
                    moreValue = this.arrowPositionTopMin
                }
                this.curArrowWLValue = moreValue;
                this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)
            }
            document.ontouchend = () => {
                document.ontouchmove = null;
                document.ontouchend   = null;
            }
        },
        onTouchMoveMid(e) {
            const el = e.changedTouches[0].target;
            let disY = e.changedTouches[0].clientY - el.offsetTop;
            document.ontouchmove = (e) => {
                let moreValue = e.changedTouches[0].clientY - disY;

                if (moreValue > ( this.arrowPositionTopMax - (this.arrowHeight / 2) )){
                    // 不能超过最大箭头位置
                    moreValue = this.arrowPositionTopMax - (this.arrowHeight / 2)
                }else if (moreValue < ( this.arrowPositionTopMin + (this.arrowHeight / 2) )){
                    // 不能小于最小箭头位置
                    moreValue = this.arrowPositionTopMin + (this.arrowHeight / 2)
                }
                const value = (this.curArrowWLValue - this.curArrowWWValue) / 2;

                // 移动中间箭头后，相应需要变换的 ww、wl 箭头
                let arrowWW = (moreValue - value);  
                let arrowWL = (moreValue + value);
                if (arrowWW < this.arrowPositionTopMin){
                    // TODO 应该赋 ww 箭头最大值，并且通过输入框中的值（center）装换窗位相应的箭头位置
                    // this.curArrowWWValue = this.arrowPositionTopMin;
                    // this.curArrowWLValue = TODO
                    return;
                }else if (arrowWL > this.arrowPositionTopMax){
                    // TODO 应该赋 wl 箭头最大值，并且通过输入框中的值（width）装换窗位相应的箭头位置
                    // this.curArrowWWValue = TODO;
                    // this.curArrowWLValue = this.arrowPositionTopMax;
                    // this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)
                   return;
                }
                else if (arrowWW == this.curArrowWWValue && arrowWL == this.curArrowWLValue){
                    return;
                }
                this.curArrowMidalue = moreValue;
                this.curArrowWWValue = arrowWW;
                this.curArrowWLValue = arrowWL;

                this.changeWLOnTopBottom(this.curArrowWWValue, this.curArrowWLValue)

            }
            document.ontouchend = (e) => {
                document.ontouchmove = null;
                document.ontouchend = null;
            }
        },
        // 通过滑条改变WL
        /**
         * wlTop     像素位置 px
         * wlBottom  像素位置 px
         */
        changeWLOnTopBottom(wlTop, wlBottom){

            if (isNaN(wlTop) || isNaN(wlBottom)) return;

            const cellvalue = (this.valueWW - this.valueWL) / (this.arrowPositionTopMax - this.arrowHeight)

            let top =  this.valueWW - wlTop * cellvalue;
            let bottom =  this.valueWW - (wlBottom - this.arrowHeight) * cellvalue;

            top = Number(top.toFixed(2));
            bottom = Number(bottom.toFixed(2));
            // console.log(wlTop, wlBottom)
            // console.log('top', top)
            // console.log('bottom', bottom)

            let w = 0;
            let l = 0;
            if (this.WLUnitStype === 'PT'){
                w = top;
                l = bottom;
            }else {
                // CT MR ?
                w = top - bottom;
                l = (bottom + top) / 2;
            }
            // 向上触发，改变窗宽窗位
            this.$emit('changeSlider', w, l)
        },
        // 设置滑条上的最高和最低点
        /**
         * topvalue     voi 数值
         * bottomvalue  voi 数值
         */
        setWLTopBottom(ww, wl){
            let topvalue = Number(ww.toFixed(2))
            let bottomvalue = Number(wl.toFixed(2))
            // voi 超过最大、最小范围
            if (topvalue > this.valueWW) this.valueWW = topvalue;
            if (bottomvalue < this.valueWL) this.valueWL = bottomvalue;
            
            const cellvalue = (this.valueWW - this.valueWL) / (this.arrowPositionTopMax - this.arrowHeight)

            const toppos = (this.valueWW - topvalue) / cellvalue;
            const bottompos = (this.valueWW - bottomvalue) / cellvalue;

            const top = this.arrowHeight + toppos;
            const bottom = this.arrowHeight + bottompos;
            
            // 箭头显示的位置（px）
            this.curArrowWWValue = - this.arrowHeight + top
            this.curArrowWLValue = bottom
        },
        // 触发改变 窗宽窗位
        onEnterWindowLevel(){
            this.setWLPosition(this.showWindow, this.showLevel)

            this.updateColorbar()
        },
        // canvas 颜色条
        updateColorbar() {
            const canvas = this.$refs.canvas;
            if (!canvas || !this.colormapId) return
            let colormap = cornerstone.colors.getColormap(this.colormapId);
            const lookupTable = colormap.createLookupTable();
            const ctx = canvas.getContext('2d');
            const colorbar = ctx.createImageData(canvas.width, canvas.height); // 宽高
            const bandTop = this.curArrowWWValue // curArrowWLValue是top偏移值 而坐标循环是从上到下  18是光标元素的高
            const bandBottom = this.curArrowWLValue - this.arrowHeight
            const bandHeight = Math.round(bandBottom - bandTop) || 1
            const bandWidth = canvas.width;

            lookupTable.setTableRange(0, bandHeight);

            let color, value, pixel
            for(let col = 0; col < canvas.height; col++) {
                value = col - bandTop
                if (value < 0) value = 0
                if (value > bandHeight) value = bandHeight

                color = lookupTable.mapValue(this.invert ? value : bandHeight - value);
    
                for(let row = 0; row < bandWidth; row++) {
                    pixel = (col * bandWidth + row) * 4 ;
                    colorbar.data[pixel] = color[0];
                    colorbar.data[pixel+1] = color[1];
                    colorbar.data[pixel+2] = color[2];
                    colorbar.data[pixel+3] = color[3];
                }
            }
    
            ctx.putImageData(colorbar, 0, 0);
        },
        resetScroll() {
            // TODO 在没有图的拖拽，它应该不能拖拽
            // TODO  scroll高度动态获取
            // const scrollHeight = this.$refs.scroll.clientHeight;
            this.arrowPositionTopMax = 156 + 2;
            this.arrowPositionTopMin = 0;
        }
    },
    mounted() {
        this.resetScroll()
    },
}
</script>
<style lang="scss" scoped>
.custom-slider{
    // display: flex;
    // flex-direction: column;
    height: 100%;
    position: relative;
}
.input{
    width: 56px;
    ::v-deep .el-input__inner{
        text-align: center;
    }
    &:first-child{
        position: absolute;
        left: 0px;
        top: 0px;
        z-index: 1;
    }
    &:last-child{
        position: absolute;
        bottom: 0px;
        left: 0px;
    }
}
.parent-box{
    position: absolute;
    top: 10px;
    flex: 1;
    width: 56px;
    height: 178px;
    border: 1px solid #DCDFE6;
    border-top: none;
    border-bottom: none;
    background: #eee;
    .pointer{
        position: absolute;
        right: -41px;
        width: 40px;
        height: 18px;
        background: #6294b7;
        cursor: pointer;
        z-index: 2;
        // border: 1px solid #303133;
        &.pointer-max {
            top: 0px;
            clip-path: polygon(30% 0, 100% 0, 100% 100%, 0 100%);
        }
        &.pointer-mid {
            top: 9px;
            clip-path: polygon(0 50%, 30% 0, 100% 0, 100% 100%, 30% 100%);
            z-index: 1;
            width: 56px;
            right: -57px;   
        }
        &.pointer-min {
            top: 18px;
            clip-path: polygon(0 0, 100% 0, 100% 100%, 30% 100%);
        }
    }
    .canvas {
        position: absolute;
        width: 54px;
        height: 152px;
        left: 0px;
        top: 16px;
    }
}
</style>