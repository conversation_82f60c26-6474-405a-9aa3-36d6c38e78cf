// import toolState from './store/toolState.js'
import csTools from '$library/cornerstone/cornerstoneTools'
import store from '$src/store/index.js'

import Api from '$api'

function getHostIpPort() {
  if (store.state.seriesInfo) {
    return store.state.seriesInfo.hostIpPort; 
  }
  return '';
}

function newImageIdStateManager() {

  let ip = null;
  const prefix = 'dicomweb://';
  const url = "/image/web/view/getFile?sSOPInstanceUID=";
  const toolList = [
    'ArrowAnnotate', 'Length', 'Bidirectional', 'Angle', 'EllipticalRoi',
    'CircleRoi', 'RectangleRoi', 'FreehandRoi', 'TextMarker',
    'LesionArea', 'FreehandLength'
  ];
  const toolIcon = {
    ArrowAnnotate: "iconarrows-1",
    Length: "iconline",
    Bidirectional: "iconzhongxin",
    Angle: "iconangle",
    Elliptical<PERSON><PERSON>: "iconellipse",
    CircleRoi: "iconyuanquan",
    RectangleR<PERSON>: "iconrectangle",
    <PERSON>handRoi: "iconbrush",
    TextMarker: "iconwenben",
    LesionArea: "iconcelianggongju1",
    FreehandLength: "iconquxian",
  };
  const storageKey = 'measurements-tools';
  let toolState = {};
  // 请求后端接口
  function _axiosData(key) {
    // return _getLocalData()
    // return {
    //   // 序列
    //   "mpr:1.2.840.113619.2.405.3.1729364999.858.1625440823.71.2.840.113619.2.405.3.1729364999.858.1625440822.9341.2.840.113619.2.405.3.1729364999.858.1625440822.9342.7900000000000205z": {
    //     // 图像实例
    //     "mpr:1.2.840.113619.2.405.3.1729364999.858.1625440822.934:1,0,0,0,1,0:0,0,-533.7000000000028": {
    //       // 工具
    //       Length: [
    //         {
    //           title: "",
    //           toolName: "Length",
    //           value: "87.94mm",
    //           icon: "iconline",
    //           // 绘制信息
    //           toolData: {
    //             visible: true,
    //             active: false,
    //             invalidated: false,
    //             handles: {
    //               start: {
    //                 x: 212.09831753500703,
    //                 y: 183.98938052433508,
    //                 highlight: true,
    //                 active: false,
    //               },
    //               end: {
    //                 x: 302.08196697328043,
    //                 y: 187.45029011811482,
    //                 highlight: true,
    //                 active: false,
    //                 moving: false,
    //               },
    //               textBox: {
    //                 active: false,
    //                 hasMoved: false,
    //                 movesIndependently: false,
    //                 drawnIndependently: true,
    //                 allowedOutsideImage: true,
    //                 hasBoundingBox: true,
    //                 x: 302.08196697328043,
    //                 y: 187.45029011811482,
    //                 boundingBox: {
    //                   width: 90.2265625,
    //                   height: 25,
    //                   left: 376.79999995231634,
    //                   top: 216.50000000000003,
    //                 },
    //               },
    //             },
    //             uuid: "5dfd81cb-6ccf-4806-adb0-f92d206408ec",
    //             length: 87.93958465879676,
    //             unit: "mm",
    //           },
    //         },
    //       ],
    //     },
    //   },
    // };
  }

  // 获取本地数据
  function _getLocalData() {
    let obj = localStorage.getItem(storageKey)
    if (!obj) {
      localStorage.setItem(storageKey, JSON.stringify({}))
    }else {
      obj = JSON.parse(obj)
    }
    if (obj === null) {
      obj = {}
    }
    return obj
  }

  /**
   * 保存本地数据
   * 有序列id保存序列id数据，没有保存全部
   * @param {*} seriesId  序列id
   */
  function saveToolData(studyId, seriesId, callBack) {
    if (Object.keys(toolState).length) {
      if (!seriesId) {
        // localStorage.setItem(storageKey, JSON.stringify(toolState))
      }else {
        // 单个，单个保存状态
        const params = {
          sInfo: JSON.stringify(toolState[seriesId]),
          sKey: studyId,
          sSeriesId: seriesId
        }
        Api.saveMeasure(params).then(res => {
          callBack && callBack(res)
        })
      }
      return true
    }
    return false
  }

  // 获取数据
  function getData() {
    return toolState;
  }
  // 获取数据并渲染
  function getDataAndRender(key, forceRequest = false) {
    // 获取数据
    if (!Object.keys(toolState).length || forceRequest) {


      Api.findMeasure({ sKey: key }).then(res => {
        if (res.success) {
          let obj = {}
          res.data.forEach(item => {
            const info = JSON.parse(item.sInfo)
            obj[item.sSeriesId] = info
          });
          toolState = Object.assign({}, toolState, obj)
          _addImageIdToolState(obj)
          triggerRender()
        }
      })

      // toolState = _axiosData(key);

    }
    // console.log(toolState)
    // _addImageIdToolState();
    // triggerRender();
  }

  // 移除工具状态
  function removeImageIdToolState(imageId, toolName, toolData) {
    cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(
      imageId,
      toolName,
      toolData
    );
    triggerRender();
  }

  /**
   * 添加工具状态
   * @param {*} series        序列id
   * @param {*} allImageId    图像全称id 如：dicomweb/xxxxxx
   * @param {*} toolName      工具名称   如：Length
   * @param {*} data          工具数据
   * @returns
   */
  function addToolState(patientId, series, allImageId, toolName, data, title = '') {
    let imageId = _getImageId(allImageId)

    if (imageId === "") {
      return;
    }
    // 不定的有这个对象属性
    if (!toolState[series]) {
      toolState[series] = {};
    }
    if (!toolState[series][imageId]) {
      toolState[series][imageId] = {};
    }
    if (!toolState[series][imageId][toolName]) {
      toolState[series][imageId][toolName] = [];
    }
    // 拼接数据
    const value = _getToolInfo(toolName, data);
    const tool = Object.assign(
      {},
      { toolData: data, value, toolName, title, icon: toolIcon[toolName], patientId}
    );
    toolState[series][imageId][toolName].push(tool);
  }

  /**
   * 删除工具状态
   * @param {*} series
   * @param {*} allImageId
   * @param {*} toolName
   * @param {*} uuid        工具 id 值
   * @returns
   */
  function delToolState(series, allImageId, toolName, uuid) {
    let imageId = _getImageId(allImageId)

    if (imageId === "") {
      return;
    }
    if (!toolState[series]){
      return;
    }
    const tools = toolState[series][imageId][toolName];
    if (tools) {
      for (let index = 0; index < tools.length; index++) {
        const tool = tools[index];
        if (uuid === tool.toolData.uuid) {
          tools.splice(index, 1);
          break;
        }
      }
    }
  }
  /**
   * 重建tab 中 删除整个tab的标注
   * @param {*} ptUid 
   * @param {*} ctUid 
   * @param {*} thickness 
   */
  function removeRebuildAllTool(ptUid, ctUid, thickness, isAnomalyFuse) {
    const rebuildSeriesId = [
      `mpr${ptUid}${ptUid}&&${ctUid}${thickness}x`,
      `mpr${ptUid}${ptUid}&&${ctUid}${thickness}y`,
      `mpr${ptUid}${ptUid}&&${ctUid}${thickness}z`,
    ];
    if (isAnomalyFuse) {
      // 如果是mr不重建的融合，特殊处理
      rebuildSeriesId.push(`${ctUid}`)
    }else {
      rebuildSeriesId.push(`mpr${ctUid}${ptUid}&&${ctUid}${thickness}x`)
      rebuildSeriesId.push(`mpr${ctUid}${ptUid}&&${ctUid}${thickness}y`)
      rebuildSeriesId.push(`mpr${ctUid}${ptUid}&&${ctUid}${thickness}z`)
    }

    rebuildSeriesId.forEach(series => {
      for (const imageId in toolState[series]) {
        if (Object.hasOwnProperty.call(toolState[series], imageId)) {
          const tools = toolState[series][imageId];
          // 所有工具
          for (const toolName in tools) {
            if (Object.hasOwnProperty.call(tools, toolName)) {
              const tool = tools[toolName];

              if (tool && tool.length) {
                // 工具数据
                for (let index = 0; index < tool.length; index++) {
                  const toolData = tool[index];
                  let clearImageId = imageId
                  // 如果不是 mpr 的就拼接 iamgeId 值
                  if (!imageId.includes('mpr')) {
                    if (!ip) {
                      ip = getHostIpPort();
                    }
                    clearImageId = prefix + ip + url + imageId
                  }
                  cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(clearImageId, toolName, toolData.toolData);
                }
                delete tools[toolName];
              }
            }
          }
        }
      }

    });
    
  }

  function isExistTool(series) {
    let exist = false
    for (const key in toolState[series]) {
      if (Object.hasOwnProperty.call(toolState[series], key)) {
        const imageId = toolState[series][key];

        // 图像
        for (const key in imageId) {
          if (Object.hasOwnProperty.call(imageId, key)) {
            const tools = imageId[key];
            // 应该要停止
            if (tools && tools.length) {
              exist = true
            }
          }
        }
      }
    }
    return exist
  }
  /**
   * 更新工具状态
   * @param {*} series
   * @param {*} allImageId
   * @param {*} toolName
   * @param {*} uuid
   */
  function updateToolState(series, allImageId, toolName, data) {
    let imageId = _getImageId(allImageId)

    if (imageId === "") {
      return;
    }
    if (toolState[series] && toolState[series][imageId] && toolState[series][imageId][toolName]) {
        const tools = toolState[series][imageId][toolName];
        for (let index = 0; index < tools.length; index++) {
            const tool = tools[index];
            if (data.uuid === tool.toolData.uuid) {
                const value = _getToolInfo(toolName, data);
                tools[index].toolData = data
                tools[index].value = value
                break;
            }
        }
    }

  }
  
  /**
   * 更新工具状态名称
   * @param {*} series
   * @param {*} allImageId
   * @param {*} toolName
   * @param {*} uuid
   */
  function updateToolStateName(series, allImageId, toolName, uuid, title) {
    let imageId = _getImageId(allImageId)

    if (imageId === "") {
      return;
    }
    const tools = toolState[series][imageId][toolName];
    if (tools) {
      for (let index = 0; index < tools.length; index++) {
        const tool = tools[index];
        if (uuid === tool.toolData.uuid) {
            tools[index].title = title
            break;
        }
      }
    }
  }

  // 清除当前序列选中
  function clearSelect(series, allImageId) {
    let imageId = _getImageId(allImageId)
    if (imageId === "") {
      return;
    }
    toolList.map(toolName => {
      if (!toolState[series] || !toolState[series][imageId]) {
        return;
      }
      const tool = toolState[series][imageId][toolName];
      if (tool) {
        for (let index = 0; index < tool.length; index++) {
          tool[index].toolData.select = false;
        }
      }
    })
  }

  // 清除所有
  function clearAll() {
    toolState = {}
  }


  // 获取序列所有图像工具
  function _getOriginalSeriesTool(seriesUid) {
    if (!seriesUid || !toolState[seriesUid]) {
      return {};
    }
    return toolState[seriesUid];
  }

  // 获取序列中所有工具列表
  function getTransitionSeriesTool(seriesUid) {
    const seriesTool = _getOriginalSeriesTool(seriesUid);
    let result = [];
    // 遍历序列中的每一张图像
    for (const key in seriesTool) {
      if (Object.hasOwnProperty.call(seriesTool, key)) {
        const imageObj = seriesTool[key];
        // 是否是重建
        const isMpr = key.includes('mpr')

        let imageId = '';
        if (isMpr || key.includes(window.configs.protocol)) {
          imageId = key;
        }else {
          if (!ip) {
            ip = getHostIpPort();
          }
          imageId = prefix + ip + url + key;
        }

        // 每一张图像中的每个类型工具
        for (const key in imageObj) {
          if (Object.hasOwnProperty.call(imageObj, key)) {
            const tools = imageObj[key];
            // 相同类型工具集合
            for (let i = 0; i < tools.length; i++) {
              const tool = tools[i];
              const newTool = Object.assign({}, tool, { imageId });
              result.push(newTool);
            }
          }
        }
      }
    }
    return result;
  }

  // 获取当前
  function getElementAllTools(element, seriesUid) {
    const enabledElement = cornerstone.getEnabledElement(element);
    if (!enabledElement.image) {
      return [];
    }
    let imageId = _getImageId(enabledElement.image.imageId);

    const seriesTool = _getOriginalSeriesTool(seriesUid);
    let result = [];
    const imageObj = seriesTool[imageId];
    if (imageObj) {
      // 每一张图像中的每个类型工具
      for (const key in imageObj) {
        if (Object.hasOwnProperty.call(imageObj, key)) {
          const tools = imageObj[key];
          // 相同类型工具集合
          for (let i = 0; i < tools.length; i++) {
            const tool = tools[i];
            const newTool = Object.assign({}, tool, { imageId: enabledElement.image.imageId });
            result.push(newTool);
          }
        }
      }
    }

    return result;
  }

  function _getRoiData(tool) {

    let max = tool.cachedStats.max
    let min = tool.cachedStats.min
    let mean = tool.cachedStats.mean
    const meanStdDevSUV = tool.cachedStats.meanStdDevSUV
    if (meanStdDevSUV) {
      max = meanStdDevSUV.max
      min = meanStdDevSUV.min
      mean = meanStdDevSUV.mean
    }
    return `Max:${max.toFixed(2)}${
      tool.unit
    } Min:${min.toFixed(2)}${
      tool.unit
    } Avg:${mean.toFixed(2)}${
      tool.unit
    }`;
  }

  function _getToolInfo(name, tool) {
    let value = "";
    switch (name) {
      case "Length":
        value = `${tool.length.toFixed(2)}${tool.unit}`;
        break;
      case "Bidirectional":
          value = ` L:${tool.longestDiameter} W:${tool.shortestDiameter} `;
          break;
      case "Angle":
        value = `${tool.rAngle.toFixed(2)}${String.fromCharCode(parseInt('00B0', 16))}`;
        break;
      case "EllipticalRoi": case "CircleRoi": case "RectangleRoi":
        value = _getRoiData(tool);
        break;
      case "FreehandRoi":
        // 勾画
        if (!tool.meanStdDevSUV) {
          if (tool.meanStdDev) {
            value = `Area:${tool.area.toFixed(2)}cm${String.fromCharCode(178)}
            Max:${tool.meanStdDev.max.toFixed(2)}${
              tool.unit
            } Avg:${tool.meanStdDev.mean.toFixed(2)}${
              tool.unit
            }`;
          }
        } else {
          value = `Area:${tool.area.toFixed(2)}cm${String.fromCharCode(178)}
          Max:${tool.meanStdDevSUV.max.toFixed(2)}${
            tool.unit
          } Avg:${tool.meanStdDevSUV.mean.toFixed(2)}${
            tool.unit
          }`;
        }
        if (tool.totalVolume) value += ` Volume:${Number(tool.totalVolume).toFixed(2)}cm³`
        if (tool.TLG) value += ` TLG:${Number(tool.TLG).toFixed(2)}`
        break;
      case "FreehandLength":
        value = `${(tool.totalLength / 10).toFixed(1)}${tool.unit}`
        break;
      case "TextMarker":
      case "LesionArea":
        value = `${tool.text}`;
        break;
      default:
        break;
    }
    return value;
  }
  function _getImageId(allImageId) {
    let imageId = "";
    if (allImageId.includes("dicomweb")) {
      let startIdx = allImageId.indexOf("=");
      startIdx += 1;
      if (startIdx) {
        imageId = allImageId.slice(startIdx);
      }
    }else {
      imageId = allImageId
    }
    return imageId
  }
  // 添加工具到图像状态中
  function _addImageIdToolState(curToolState) {
    // 序列
    for (const seriesName in curToolState) {
      if (Object.hasOwnProperty.call(curToolState, seriesName)) {
        // 是否是重建
        const isMpr = seriesName.includes('mpr')

        // 图像
        let seriesObj = curToolState[seriesName];
        for (const key in seriesObj) {
          if (Object.hasOwnProperty.call(seriesObj, key)) {
            const image = seriesObj[key];

            let imageId = '';
            if (isMpr) {
              imageId = key;
            }else {
              if (!ip) {
                ip = getHostIpPort();
              }
              imageId = prefix + ip + url + key;
            }
            // 工具
            for (const toolName in image) {
              if (Object.hasOwnProperty.call(image, toolName)) {
                const tools = image[toolName];

                for (let i = 0; i < tools.length; i++) {
                  const tool = tools[i];
                  csTools.globalImageIdSpecificToolStateManager.addImageIdToolState(
                    imageId,
                    toolName,
                    tool.toolData
                  );
                }
              }
            }
          }
        }
      }
    }
  }

  /**
   * TODO 这个方法图像会跑中间？
   */
  function triggerRender() {
    // const myEvent = new Event("resize");
    // window.dispatchEvent(myEvent);
  }

  return {
    getData,
    saveToolData,
    getDataAndRender,
    removeImageIdToolState,
    updateToolState,
    addToolState,
    delToolState,
    updateToolStateName,
    getTransitionSeriesTool,
    getElementAllTools,
    isExistTool,
    clearSelect,
    clearAll,
    triggerRender,
    removeRebuildAllTool
  };
}

const imageIdStateManager = newImageIdStateManager();

export { imageIdStateManager };
