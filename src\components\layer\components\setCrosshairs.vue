<template>
    <div class="c-content">
        <div class="plan">
            <span>默认显示定位线</span>
            <el-switch
                v-model="defaultShowLines"
                active-color="#13ce66"
                inactive-color="#eeeeee"
                @change="onChangeShow">
            </el-switch>
        </div>
        <div class="plan">
            <span>MIP上定位线：</span>
            <el-select v-model="mipCrosshairDisplay" @change="setMipCrosshairDisplay" size="small">
                <el-option
                v-for="item in mipCrosshairDisplayOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="plan">
            <span>MIP图点击定位病灶功能：</span>
            <el-switch
                v-model="mipClickLocation"
                active-color="#13ce66"
                inactive-color="#eeeeee"
                @change="onChangeMipClickLocation">
            </el-switch>
        </div>

        <div class="plan">
            <span>定位线颜色：</span>
            <el-color-picker v-model="crosshairsType.toolColor" :predefine="predefineColors" 
            @change="onClickChange" size="small" color-format="rgb"></el-color-picker>
        </div>




        <span>定位线类型：</span>
        <section>
            <div class="item">
                <span>CT \ MR:</span>
                <el-radio @change="onClickChange" v-model="crosshairsType.CT" label="small">小定位线</el-radio>
                <el-radio @change="onClickChange" v-model="crosshairsType.CT" label="large">大定位线</el-radio>
            </div>
            <div class="item">
                <span>PT \ NM:</span>
                <el-radio @change="onClickChange" v-model="crosshairsType.PT" label="small">小定位线</el-radio>
                <el-radio @change="onClickChange" v-model="crosshairsType.PT" label="large">大定位线</el-radio>
            </div>
            <div class="item">
                <span>融合:</span>
                <el-radio @change="onClickChange" v-model="crosshairsType.FUSE" label="small">小定位线</el-radio>
                <el-radio @change="onClickChange" v-model="crosshairsType.FUSE" label="large">大定位线</el-radio>
            </div>
            <div class="item">
                <span>MIP:</span>
                <el-radio @change="onClickChange" v-model="crosshairsType.MIP" label="small">小定位线</el-radio>
                <el-radio @change="onClickChange" v-model="crosshairsType.MIP" label="large">大定位线</el-radio>
                <el-radio @change="onClickChange" v-model="crosshairsType.MIP" label="arrowline">箭头+横线</el-radio>
            </div>
        </section>
        
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setCrosshairs',
    data() {
        return {
            defaultShowLines: true,
            mipClickLocation: true,
            mipCrosshairDisplay: '0',
            mipCrosshairDisplayOptions: [
                {
                    value: '0',
                    label: '自动'
                },
                {
                    value: '1',
                    label: '固定显示'
                },
                {
                    value: '2',
                    label: '固定隐藏'
                },
            ],
            predefineColors: [
                '#FF0000',
                '#00FF00',
                '#FFFF00',
                '#00A2A2',
                '#FF0080',
            ],
            storageKey: {
                mipCrosshairDisplay: 'configs-mipCrosshairDisplay',
                color: 'configs-crosshairs-color',
                mipClickLocation: 'configs-mipClickLocation',
            },
            crosshairsType: {
                CT: 'large',
                PT: 'large',
                FUSE: 'large',
                MIP: 'large',
                toolColor: '#FF0000',
            }
        }
    },
    methods: {
        onClickChange(){
            localStorage.setItem('configs-crosshairs-type', JSON.stringify(this.crosshairsType))
            this.$message({
                message: '设置成功！',
                type: 'success'
            });
            cornerstoneTools.store.state.crosshairsType = this.crosshairsType;
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent)
            })
        },
        onChangeShow(val) {
            localStorage.setItem('configs-crosshairs-show', val)
            this.$message({
                message: '设置成功！',
                type: 'success'
            });
        },
        onChangeMipClickLocation(val) {
            console.log(val)
            localStorage.setItem(this.storageKey.mipClickLocation, val)
            this.$message({
                message: '设置成功！',
                type: 'success'
            });
        },
        
        setMipCrosshairDisplay(){
            localStorage.setItem(this.storageKey.mipCrosshairDisplay, JSON.stringify(this.mipCrosshairDisplay))
            this.$store.commit('GET_MIPCROSSHAIRDISPLAY')
            this.$message({
                message: '设置成功！',
                type: 'success'
            });
        }
    },
    mounted() {
        // 获取默认显示定位线
        this.crosshairsType   = getConfigByStorageKey('configs-crosshairs-type');
        this.defaultShowLines = getConfigByStorageKey('configs-crosshairs-show');
        this.mipCrosshairDisplay = getConfigByStorageKey(this.storageKey.mipCrosshairDisplay);
        this.mipClickLocation = getConfigByStorageKey(this.storageKey.mipClickLocation);

    },
}
</script>
<style lang="scss" scoped>
.c-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    > section{
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-top: 20px;
        .item{
            height: 40px;
            line-height: 40px;
            > span{
                display: inline-block;
                width: 130px;
                padding-right: 30px;
                text-align: right;
            }
        }
        
    }
    > footer{
        height: 40px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
    }
}
.plan{
    height: 40px;
    display: flex;
    align-items: center;
    margin: 0 0 20px;
    span{
        padding-right: 10px;
    }
}
</style>