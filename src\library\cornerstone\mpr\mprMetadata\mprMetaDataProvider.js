import mprMetaDataStore from './mprMetaDataStore.js';
import customLocalMeta from '../store/customLocalMeta.js';

const metaKey = ['patientStudyModule', 'generalSeriesModule', 'petIsotopeModule', 'geModule']

// 图像元数据---一些图像信息
function provider(moduleName, imageId) {
    const meta = mprMetaDataStore.get(imageId);
    if(!meta){
        return;
    }
    if (meta[moduleName]){
        if (metaKey.includes(moduleName)) {
            const [ , seriesNumber ] = imageId.split(':')
            if (customLocalMeta.data[seriesNumber]) {
               return customLocalMeta.data[seriesNumber][moduleName]
            }
        }
        return meta[moduleName];
    }
    // if(moduleName === "imagePlaneModule"){
    //     const imagePlaneModule = meta.imagePlaneModule;
    //     return imagePlaneModule;
    // }

    // // 
    // if(moduleName === "patientStudyModule"){
    //     return meta.patientStudyModule;
    // }

    // if(moduleName === "generalSeriesModule"){
    //     return meta.generalSeriesModule;
    // }

    return;
}

export default provider;
