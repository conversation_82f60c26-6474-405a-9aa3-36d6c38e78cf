# 1. 阅图

## 1.1 截图阅图

### 1.1.1 截图展示
    
1. 从服务端获取截图数据，在视图窗口展示
2. 视图窗口划分为多个网格展示，支持N行M列任意调整布局（N,M <= 10）

### 1.1.2 截图操作

1. 鼠标滚轮触发切换展示中的截图
2. 鼠标操作进行截图的平移、缩放、旋转、调窗、反片，支持操作后重置
3. 鼠标操作进行截图的标注，支持箭头、直线、角度、圆、椭圆、矩形、勾画、文字标注、橡皮擦工具，支持标注数据保存至服务器
4. 支持截图的备注、删除和拖拽调整顺序，支持隐藏备注和图像附带的信息
5. 支持带标注的截图打印、图像另存

## 1.2 图像阅图

### 1.2.1 图像展示
    
1. 从服务端获取图像数据，根据日期、序列号分组，可选择多组图像进行同时展示
2. 视图窗口划分为多个网格展示，支持N行M列任意调整布局（N,M <= 10）

### 1.2.2 图像操作

1. 鼠标滚轮触发改变图像序号（翻页），支持切换至任意序号
2. 鼠标操作进行图像的平移、缩放、旋转、调窗、伪彩，反片，支持设置任意窗宽窗位、一键切换预设窗位，支持操作后重置
3. 鼠标操作进行图像的标注，支持箭头、直线、角度、圆、椭圆、矩形、勾画、文字标注、橡皮擦工具，支持标注数据保存至服务器
4. 图像SUV值测量功能： 对PT图像的特定区域进行SUV值测量，测量值有SUV最大值、最小值、平均值、std值、面积，可选择圆、椭圆、矩形、不规则形的测量区域
5. 体测量功能： 对单序列PT图像内的圆形区域进行病灶区域测量（指定SUV阈值测量，阈值可调节），测量值有SUV最大值、最小值、平均值，MTV、TLG值（多层图像累加计算）
6. 支持自动播放任意一个图像序列，支持从慢到快的多种速度进行播放
7. 支持将当前视窗截图保存，能够隐藏图像附带的信息
8. 支持带标注的图像打印、图像另存



# 2. 重建

## 2.1 重建图像展示

1. 从服务端获取图像数据，根据日期、序列号分组，选择单组或两组特定类型的图像数据后可以重建三维体数据
2. 能够对体数据进行不同方向、厚度的重新切片，获取冠状位、矢状位、横截面三个方向的切面图像
3. 能够将PT图像与CT、MR图像进行融合，得到融合后的切片图像和MIP图像
4. 能够由体数据渲染出MIP图像并展示
5. 视图窗口可同时展示多个不同方向的切片图像或MIP图像，支持任意调整布局

## 2.2 重建图像操作

1. 定位线功能： 同时展示多个方向的切片图像时，点击其中一个图像中的某一点，其他方向的视图窗口会同步定位到这个位置，用十字线标记该位置
2. MIP图病灶定位功能： 同时展示MIP图像和切片图像时，点击MIP图像中的病灶区域，能够使切片图像同步定位到这个位置，用十字线标记该位置
3. MIP图旋转展示功能： 能够通过鼠标操作进行MIP图像的全方位旋转和任意缩放
4. 鼠标滚轮触发改变图像序号（翻页），支持切换至任意序号
5. 鼠标操作进行图像的平移、缩放、旋转、调窗、伪彩，反片，支持设置任意窗宽窗位、一键切换预设窗位，支持操作后重置
6. 鼠标操作进行图像的标注，支持箭头、直线、角度、圆、椭圆、矩形、勾画、文字标注、橡皮擦工具，支持标注数据保存至服务器
7. 图像SUV值测量功能： 对PT图像的特定区域进行SUV值测量，测量值有SUV最大值、最小值、平均值、std值、面积，可选择圆、椭圆、矩形、不规则形的测量区域
8. 体测量功能： 对单序列PT图像内的圆形区域进行病灶区域测量（指定SUV阈值测量，阈值可调节），测量值有SUV最大值、最小值、平均值，MTV、TLG值（多层图像累加计算）
9. 操作同步功能： 对相同方向不同类型的切片图像，支持同步操作（包括滚动、缩放、平移、调窗、基本的标注操作）
10. 图像进行融合时，支持分别调整两组图像的窗宽窗位
11. 坐标/旋转角度微调功能： 图像进行融合时，支持微调其中一组图像的水平/垂直位置和旋转角度，从而微调融合图像的位置
12. 支持自动播放任意一个图像序列，支持从慢到快的多种速度进行播放
13. 支持将当前视窗截图保存，能够隐藏图像附带的信息
14. 支持带标注的图像打印、图像另存

# 3. 重建对比

1. 选择任意2组或3组已有的重建数据，在视图窗口内同时展示这些数据的MIP图像和切面图像
2. 支持所有重建视图中包含的功能
3. 可以分别操作每个视图，也可开启同步操作（包括滚动、缩放、平移、伪彩）
4. 病灶评估功能： 能够保存已有的病灶区域测量数据并进行对比，对比结果用图表形式展示


# 4. 拖拽重建

1. 可以任意地将图像序列拖拽到视图窗口中进行查看，拖拽后自动进行图像重建
2. 同时展示多个窗口，可在单个窗口内分别选择展示不同方向的图像、融合图像、MIP图像
3. 多个窗口会同步操作（包括滚动、缩放、平移和定位线） 
4. 支持所有重建视图中包含的功能

# 5. 重建VR

1. 选择任意一个图像序列，进行体数据重建，将体数据渲染成3D图像并展示
2. 能够通过鼠标拖拽和滚轮进行图像的全方位旋转和任意缩放
3. 能够调整图像的伪彩、投影密度、分层透明度

# 6. 重建MPR

1. 选择任意一个图像序列，进行体数据重建，将体数据进行任意三个角度的切片，并展示切片图像
2. 鼠标操作进行图像的平移、缩放、调窗，支持操作后重置
3. 定位线工具： 在图像上用十字线显示当前的切片方向、位置，拖拽十字线可以改变切片方向、位置
4. 点测量工具： 在图像上显示当前鼠标指针位置的SUV测量值，点击后在图上标注这一点的测量值，暂不支持保存数据
5. 能够调整图像的投影密度

# 7. 配置工具及其他功能

## 7.1 系统设置

弹出设置界面，能够设置图像系统内的一些功能选项，仅保存在本地（浏览器本地存储）

### 7.1.1 显示设置

1. 定位线显示设置： 是否默认显示定位线，定位线尺寸（大、小）
2. 工具样式设置： 标注字体大小，字体加粗， 工具大小，工具颜色
3. 窗宽窗位： 针对不同的图像类型，保存多个窗宽窗位的预设值
4. 显示方案（重建）： 设置重建时默认使用的伪彩

### 7.1.2 其他设置

1. 工具操作设置： 标注重复使用（开/关），标注同步（开/关）， 鼠标滚轮、右键的操作（平移、缩放、窗宽窗位、透镜、橡皮擦）
2. PET-SUV设置： 选择SUV校正因子：Body Weight \ Lean Body Mass \ Body Surface Area
3. 图像质量： 图像插值（开/关）， MIP图质量（高、中、低）

### 7.1.3 选择项设置

1. 点测量： 调整点测量半径值
2. 圆测量： 圆拖拽测量方式（半径、直径）
3. 体测量： 阈值类型（百分比阈值、固定阈值），分别调整百分比阈值和固定阈值

### 7.1.4 图像信息

针对不同的图像类型，调整在图像上显示的附带信息，可以分别调整左上角、左下角、右上角、右下角的信息条目

## 7.2 布局设置

弹出设置界面，能够设置在使用重建和重建对比功能时视图窗口的布局和模式，仅保存在本地（浏览器本地存储）

### 7.2.1  默认布局

1. 能够保存多个预设的布局，布局可任意调整为M行N列（N,M <= 7），每个窗口可以配置特定的图像类型、方向
2. 已保存的布局支持编辑和删除

### 7.2.2  默认模式

能够配置进入图像系统时默认使用的读图模式（重建、拖拽重建），通过读取病例所带的图像的类型来区分


# 系统的硬件配置要求

本系统中的部分功能，例如MIP图像的显示等，对电脑硬件配置有一定的要求，在性能低于最低要求的电脑上使用时可能会有操作不流畅等问题。    

具体影响：    
1. MIP图像受限于电脑GPU的性能，性能不足时会引起操作MIP图像时卡顿，或MIP图无法显示。
2. 切片处理速度受限于CPU的性能，性能不足时会引起读取切片速度较慢，切片图像显示卡顿。
3. 可同时显示的图像数量受限于内存大小，系统剩余内存不足时会引起浏览器无响应。
4. VR图像的显示需要一定的GPU性能支持，建议只在达到最低配置要求的电脑上使用。

## 最低配置


处理器： Intel(R) Core i5-7200 @2.50GHz     
内存： 8GB RAM （可支持同时阅读 700 MB 大小的DICOM图像）    
显卡： Intel(R) HD Graphics 620 或 NVIDIA GeForce GT 630 或 AMD Radeon HD 6570    


## 推荐配置


操作系统： Windows 10 64位操作系统    
处理器： Intel(R) Core i7-11700 @2.50GHz    
内存： 16GB RAM  （可支持同时阅读 1.6 GB 大小的DICOM图像）     
显卡： Intel(R) UHD Graphics 750 或 NVIDIA GeForce GT 650 或 AMD Radeon HD 7750    


## 图像质量设置项

可以在“系统设置-其他设置-图像质量”中设置图像质量，其中：     
1. MIP图像质量的高、中、低档位应分别对应达到推荐配置、达到最低配置、低于最低配置的机器使用。    
2. 图像插值建议在机器达到最低配置时开启。    

# 浏览器要求

推荐使用Chrome浏览器的最新版本，或者Firefox、Edge浏览器。 优先选择64位版本。    

## 开启硬件加速

务必开启浏览器的硬件加速功能，否则系统可使用的硬件性能将大大受限。    