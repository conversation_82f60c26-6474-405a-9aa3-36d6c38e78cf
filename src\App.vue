<template>
	<div id="app">
		<router-view/>
	</div>
</template>
<script>

import axios from 'axios'

import checkUpdateString from '../public/checkUpdate.json'

export default {
  name: 'app',
  data() {
    return {
      checkTimer: null
    }
  },
  mounted() {
    this.checkIfAppUpdated()

    this.$Api.queryClientIp({}).then(res => {
      if (res.success) {
        let [ protocol, , port ] = window.configs.apricotAssist.split(':')
        window.configs.apricotAssist = `${protocol}://${res.data.clientIp}:${port}`
      }
    })
  },
  methods: {
    checkIfAppUpdated() {
      if (process.env.NODE_ENV === 'development') return
      if (!checkUpdateString) return
      if (!this.checkTimer) {
        this.checkTimer = setInterval(() => {
          axios.get(location.origin + `/app/webdicom/checkUpdate.json?t=` + +new Date() , { 
            timeout: 500,
            headers: {
              'Cache-Control': 'no-cache,no-store',
              'If-Modified-Since': '0',
            }
         }).then(res => {
            if (res) {
              const data = (res.data);
              // console.log(data)
              // console.log(checkUpdateString)
              if (data != checkUpdateString) {
                const refresh = () => {
				  // const hasParam = window.location.hash.indexOf('?') > -1 ? '&' : '?'
				  // window.location.href = window.location.origin
				  //  + window.location.pathname + window.location.hash + hasParam + 't=' + +new Date();
                  window.location.reload()
                }
                const tempRefresh = '_temp' + (Math.random()).toString(16).slice(2)
                window[tempRefresh] = refresh

                this.$notify({
                  showClose: true,
                  type: 'warning',
                  title: '系统通知',
                  dangerouslyUseHTMLString: true,
                  message: `<div>
                        页面已更新，刷新或重新打开即更新。如在看图写报告中，点击 × 关闭通知!<br/>
                        <div><button class="el-button el-button--primary el-button--mini" type="button" onClick="${tempRefresh}()">立即刷新</button></div>
                    </div>`,
                  duration: 0
                });
                clearTimeout(this.checkTimer)
              }
              return data;
            } else {
              return [];
            }
          }, () => {
            return []
          })
        }, 60000);
      } else {
        clearTimeout(this.checkTimer)
      }
    }
  }
}

</script>
<style lang="scss">
#app {
	font-family: Avenir, Helvetica, Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-align: center;
	color: #2c3e50;
  contain: content;
}

#nav {
	padding: 30px;

	a {
		font-weight: bold;
		color: #2c3e50;

		&.router-link-exact-active {
			color: #42b983;
		}
	}
}
</style>
