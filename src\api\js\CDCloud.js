import request from '../request'
import qs from 'qs'
const baseUrl = window.configs.protocol + '//' + window.configs.imageServe
const reportUrl = window.configs.protocol + '//' + window.configs.reportServe

export default {
    getStudy(params){
        return request({
            url: baseUrl + '/cdcloud/image/web/view/study/getlistByCaseId',
            method: 'POST',
            params
        })
    },
    findAllShotInstance(params) {
        return request({
            url: baseUrl + '/web/view/findAllShotInstance',
            method: 'POST',
            params
        })
    },
    printPDF(data){
        return request({
            url: reportUrl + '/img/print/printpdf',
            method: 'POST',
            data
        })
    },
    printReportPdf(data){
        return request({
            url: reportUrl + '/img/print/imgprintpdf',
            method: 'POST',
            data
        })
    },
    getTemplate(data){
        return request({
            url: reportUrl + '/template/find/page',
            method: 'POST',
            data
        })
    },
    saveScreen(data){
        return request({
            url: reportUrl + '/cdcloud/image/web/view/saveScreenshot',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        }) 
    },
    authToken(params){
        return request({
            url: baseUrl + '/cdcloud/auth/authCodeLogin',
            method: 'POST',
            params
        })
    }    
}

