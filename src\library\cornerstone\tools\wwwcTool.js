// import external from '../externalModules.js';
// import BaseTool from './base/BaseTool.js';
// import { wwwcCursor } from './cursors/index.js';

import fun from '$library/utils/function.js';

const BaseTool = csTools.importInternal("base/BaseTool");
const external = csTools.external;

import csTools from '$library/cornerstone/cornerstoneTools'

const { wwwcCursor } = csTools.importInternal("tools/cursors");
/**
 * @public
 * @class WwwcTool
 * @memberof Tools
 *
 * @classdesc Tool for setting wwwc by dragging with mouse/touch.
 * @extends Tools.Base.BaseTool
 */
export default class WwwcTool extends BaseTool {
    constructor(props = {}) {
        const defaultProps = {
            name: "Wwwc",
            strategies: { basicLevelingStrategy },
            supportedInteractionTypes: ["Mouse", "Touch"],
            configuration: {
                orientation: 0,
            },
            svgCursor: wwwcCursor,
        };

        super(props, defaultProps);
    }

    mouseDragCallback(evt) {
        this.applyActiveStrategy(evt);
        external.cornerstone.setViewport(evt.detail.element, evt.detail.viewport);
    }

    touchDragCallback(evt) {
        // Prevent CornerstoneToolsTouchStartActive from killing any press events
        evt.stopImmediatePropagation();
        this.applyActiveStrategy(evt);
        external.cornerstone.setViewport(evt.detail.element, evt.detail.viewport);
    }
}

/**
 * Here we normalize the ww/wc adjustments so the same number of on screen pixels
 * adjusts the same percentage of the dynamic range of the image.  This is needed to
 * provide consistency for the ww/wc tool regardless of the dynamic range (e.g. an 8 bit
 * image will feel the same as a 16 bit image would)
 *
 * @param {Object} evt
 * @param {Object} { orienttion }
 * @returns {void}
 */
function basicLevelingStrategy(evt) {
    const { orientation } = this.configuration;
    const eventData = evt.detail;


    const seriesModule = cornerstone.metaData.get(
		'generalSeriesModule',
		eventData.image.imageId
	);
    const modality = seriesModule && seriesModule.modality;


    const maxVOI =
        eventData.image.maxPixelValue * eventData.image.slope +
        eventData.image.intercept;
    const minVOI =
        eventData.image.minPixelValue * eventData.image.slope +
        eventData.image.intercept;
    const imageDynamicRange = maxVOI - minVOI;
    const multiplier = imageDynamicRange / (1024 + 1024); // 降低值变化

    if (modality === 'PT') {
        // PT 图像只要设置 U 
        let { u } = fun.transformVoiWLToUL(eventData.viewport.voi.windowWidth, eventData.viewport.voi.windowCenter, eventData.image.imageId);
        
        let speed = 100

        // U越小，变化的速度变慢
        if ( u < 0.5) {
            speed = 800
        }else if ( u < 1) {
            speed = 600
        }else if (u < 2) {
            speed = 500
        }else if (u < 5) {
            speed = 400
        }else if (u < 10) {
            speed = 300
        }else if (u < 20) {
            speed = 200
        }
        
        const deltaY = (-eventData.deltaPoints.page.y) * multiplier / speed;

        u += deltaY;

        if (u < 0.01) {
            u = 0.01
        }

        const { w: ww, l: wl } = fun.transformULtoVoiWL(u, 0, eventData.image.imageId);

        eventData.viewport.voi.windowWidth = ww;
        eventData.viewport.voi.windowCenter = wl;

    }else {
        const deltaX = eventData.deltaPoints.page.x * multiplier;
        const deltaY = (-eventData.deltaPoints.page.y) * multiplier;
        
        let ww = 0
        let wl = 0
        if (orientation === 0) {
            ww = eventData.viewport.voi.windowWidth + deltaX;
            wl = eventData.viewport.voi.windowCenter + deltaY;
        } else {
            ww = eventData.viewport.voi.windowWidth + deltaY;
            wl = eventData.viewport.voi.windowCenter + deltaX;
        }
        // 最低值
        // if (ww <= 0) {
        //     ww = 1
        // }
        eventData.viewport.voi.windowWidth = ww
        eventData.viewport.voi.windowCenter = wl
        
    }

    // Unset any existing VOI LUT
    eventData.viewport.voiLUT = undefined;
}
