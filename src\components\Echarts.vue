<template>
  <div class="echarts"></div>
</template>

<script type="text/javascript">

import * as echarts from 'echarts/core';

// 引入柱状图图表，图表后缀都为 Chart
import { BarChart, LineChart } from 'echarts/charts';
// 引入提示框，标题，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
import {
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  GridComponent,
  DatasetComponent,
  LegendComponent,
  TransformComponent
} from 'echarts/components';
// 标签自动布局，全局过渡动画等特性
import { LabelLayout, UniversalTransition } from 'echarts/features';
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  ToolboxComponent,
  GridComponent,
  DatasetComponent,
  LegendComponent,
  TransformComponent,
  BarChart,
  LineChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);

// 例子：
// <Echarts
//   :options="chartOptions"
//   :opts="{
//     width: '1200px',
//     height: '550px'
//   }"
//   @ready="chartReady"
// />

export default {
  props: {
    options: { // 初始化后setOption传入选项
      type: Object
    },
    theme: {
      type: Object,
      default() {
        return null
      }
    },
    opts: { // init 第三参数
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.chart = echarts.init(this.$el, this.theme, this.opts)
    this.$emit('ready', this.chart)
    if (this.options) this.chart.setOption(this.options)
  },
  beforeDestroy() {
    if (this.chart && this.chart.dispose) this.chart.dispose()
  }
}
</script>

<style></style>
