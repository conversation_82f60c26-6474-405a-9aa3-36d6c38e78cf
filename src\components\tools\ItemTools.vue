<template>
    <div class="item-tools" :style="style" @dblclick="stopPropagate">
        <div class="item-tools-flex" :style="flexStyle" ref="ulBox">
            <div class="item-tools-item">
                <div class="item-tools__tool" title="选择" @click="onClickTool('Airtools')" :class="[activeTool == 'Airtools' ? 'i-active' : '']"><i class="iconfont iconjiantou"></i></div>
                <div class="item-tools__tool" title="平移" @click="onClickTool('Pan')" :class="[activeTool == 'Pan' ? 'i-active' : '']"><i class="iconfont iconyidong"></i></div>
                <div class="item-tools__tool" title="调窗" @click="onClickTool('Wwwc')" :class="[activeTool == 'Wwwc' ? 'i-active' : '']"><i class="iconfont icontiaochuang"></i></div>
                <div class="item-tools__tool" title="定位线 快捷键: 字母 P" v-if="isMPR" @click="onClickTool('NuclearCrosshairs')"><i class="iconfont iconmidpoint"></i></div>
                <div class="item-tools__tool">
                    <i v-if="player.status === 1" class="iconfont iconplay-1" @click="onClickPlayClip(2)"></i>
                    <i v-else-if="player.status === 2" class="iconfont iconstop-circle" @click="onClickPlayClip(1)"></i>
                    <i v-else class="iconfont iconplay-1" style="color: #c7c7c7;"></i>
                </div>
            </div>
            <div class="item-tools--type1"><el-input v-model.number="curIndex" @keyup.enter.native="onEnterIndex" class="item-tools-input" size="mini"></el-input></div>
            <div class="item-tools-item">
                <div class="item-tools__tool" title="直线 快捷键: 字母 I" @click="onClickTool('Length')" :class="[activeTool == 'Length' ? 'i-active' : '']"><i class="iconfont iconline"></i></div>
                <div class="item-tools__tool" title="圆 快捷键: 字母 O" @click="onClickTool('CircleRoi')" :class="[activeTool == 'CircleRoi' ? 'i-active' : '']"><i class="iconfont iconyuanquan"></i></div>

                <el-popover
                    v-model="popover.visibleInfo" popper-class="xx-popover" :visible-arrow="false" style="margin-bottom: 0px;"
                    placement="top" width="140" trigger="click">
                    <ul class="popover-list">
                        <li class="popover-lis--item" v-if="remark.isCaptrue" :class="{'is-dot': remark.isShowRemark}" @click="onClickIsShowRemark">
                            <span>截图备注</span>
                            <!-- <i class="el-icon-edit"></i>
                            <div class="popover-listInner" style="width: 220px;" @click.stop="() => {}">
                                <EditRemark @setRemark="setRemark" :sSOPInstanceUID="remark.sSOPInstanceUID" :sRemarkCn="remark.sRemarkCn" :sRemarkEn="remark.sRemarkEn"></EditRemark>
                            </div> -->
                        </li>
                        <li  class="popover-lis--item" @click="onClickOverlayVisible" :class="{'is-dot': isOverlayVisible}">图像信息</li>
                        <li  class="popover-lis--item el-icon-collection-tag custom-icon" @click="onClickDicomTags">图像标签 (Tag)</li>
                        <!-- <li  class="popover-lis--item">显示标注</li> -->
                        <!-- <li  class="popover-lis--item" @click="resetTools">删除标注</li> -->
                    </ul>
                    <div slot="reference" v-popover:popover class="item-tools__tool"><i class="iconfont iconyonghuxinxi"></i></div>
                </el-popover>

                <el-popover
                    v-model="popover.visibleTransform" popper-class="xx-popover" :visible-arrow="false" style="margin-bottom: 0px;"
                    placement="top" width="140" trigger="click">
                    <ul class="popover-list">
                        <li  class="popover-lis--item " ref="ref1" @mouseout="onMouseOut('ref1')" @mouseover="onMouseOver('ref1')">
                            <span>旋转</span>
                            <i class="el-icon-caret-right"></i>
                            <ul class="popover-listInner">
                                <li  class="popover-lis--item" @click="onCommandRotate(90)">顺时针 90</li>
                                <li  class="popover-lis--item" @click="onCommandRotate(-90)">逆时针 90</li>
                                <li  class="popover-lis--item" @click="onCommandRotate(180)">旋转 180</li>
                                <li  class="popover-lis--item" @click="onCommandRotate('reset')">还原</li>
                            </ul>
                        </li>
                        <li  class="popover-lis--item" @click="onCommandRotate('h')" :class="{'is-dot': viewportInfo.h}">水平翻转</li>
                        <li  class="popover-lis--item" @click="onCommandRotate('v')" :class="{'is-dot': viewportInfo.v}">垂直翻转</li>
                        <li  class="popover-lis--item" @click="onClickAutoWindow(false)">适应窗口</li>
                        <li  class="popover-lis--item" @click="onClickAutoWindow(true)">适应图像</li>
                    </ul>
                    <div slot="reference" v-popover:popover class="item-tools__tool"><i class="iconfont iconloop"></i></div>
                </el-popover>

                <el-popover
                    v-model="popover.visibleLut" popper-class="xx-popover" :visible-arrow="false" style="margin-bottom: 0px;"
                    placement="top" width="140" trigger="click">
                    <ul class="popover-list">
                        <li  class="popover-lis--item " ref="ref2" @mouseout="onMouseOut('ref2')" @mouseover="onMouseOver('ref2')" v-if="modality !== 'IMG'">
                            <span>伪彩</span>
                            <i class="el-icon-caret-right"></i>
                            <ul class="popover-listInner popover-listInner--canvas">
                                <li class="popover-lis--item" v-for="item in colormapsList" :key="item.id" @click="onClickColormaps(item.id)"><canvas :ref="item.id" width="66px" height="20px"></canvas>  {{ item.name }}</li>
                            </ul>
                        </li>
                        <li  class="popover-lis--item " ref="ref3" @mouseout="onMouseOut('ref3')" @mouseover="onMouseOver('ref3')">
                            <span>窗宽窗位</span>
                            <i class="el-icon-caret-right"></i>
                            <ul class="popover-listInner">
                                <li  class="popover-lis--item" v-for="(item, index) in windowWwwc" :key="item.id" @click="onClickSetWindowWwwc({ww: item.ww, wl: item.wl})">{{ item.ww+'/'+item.wl }}</li>
                            </ul>
                        </li>
                        <li  class="popover-lis--item" @click="onClickInvert">反片</li>

                        <li v-if="layers.show" class="popover-lis--item" ref="ref4" @mouseout="onMouseOut('ref4')" @mouseover="onMouseOver('ref4')">
                            <span>融合度调整</span>
                            <i class="el-icon-caret-right"></i>
                            <ul class="popover-listInner popover-listInner--canvas custom-layers">
                                <li class="popover-lis--item">
                                    <div class="popover-container">
                                        <div>
                                            <span class="top">{{ layers.opacity1  }}</span>
                                            <el-slider
                                                v-model="layers.opacity1"
                                                vertical
                                                @input="onSliderOpacity($event, 0)"
                                                :show-tooltip="false"
                                                class="c-slider"
                                                height="60px">
                                            </el-slider>
                                            <span class="bottom">PT</span>
                                        </div>

                                        <div>
                                            <span class="top">{{ layers.opacity2  }}</span>
                                            <el-slider
                                                v-model="layers.opacity2"
                                                vertical
                                                @input="onSliderOpacity($event, 1)"
                                                :show-tooltip="false"
                                                class="c-slider"
                                                height="60px">
                                            </el-slider>
                                            <span class="bottom">CT</span>
                                        </div>
                                    </div>
                                    <span class="lock" :class="[ layers.lock ? 'el-icon-lock' : 'el-icon-unlock' ]" @click="layers.lock = !layers.lock"></span>
                                </li>
                            </ul>
                        </li>
                    </ul>
                    <div slot="reference" v-popover:popover class="item-tools__tool"><i class="iconfont iconyanseziduan"></i></div>
                </el-popover>
            </div>
        </div>


    </div>
</template>
<script>
import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js'
import { getListByModality } from '$library/utils/configStorage.js'
// import EditRemark from './components/EditRemark.vue'
export default {
    // components: {
    //     EditRemark
    // },
    props: {
        element: {
            type: [Array, HTMLCollection, HTMLDivElement ],
            default: () => { [] }
        },
        activeTool: {
            type: String,
            default: 'Wwwc'
        },
        crosshairsTool: {
            type: Boolean,
            default: false
        },
        isOverlayVisible: {
            type: Boolean,
            default: false
        },
        imageIdIndex: {
            default: 0
        },
        imageId: {
            default: null
        },
        remark: {
            type: Object,
            default: () => { return {} }
        },
        tabId: {
            default: 'none',
        }
    },
    data() {
        return {
            style: {
                bottom: '-29px'
            },
            flexStyle: {},
            curIndex: 0,
            popover: {
                visibleInfo: false,
                visibleTransform: false,
                visibleLut: false,
            },
            viewportInfo: {
                v: false,
                h: false
            },
            modality: '',
            windowWwwc: [],
            colormapsList: [
                { id: 'gray', name: 'Gray'},
                { id: 'ge_color', name: 'ge_color' },
                { id: 'hot', name: 'Hot' },
                { id: 'hot2', name: 'Hot2' },
                { id: 'darkHot', name: 'darkHot' },
                
                { id: 'perfusion', name: 'Perfusion' },
                { id: 'rainbow', name: 'Rainbow' },
                { id: 'x_rain', name: 'x_rain' },
                { id: 'rainbow_brain', name: 'rainbow_brain' },
                { id: 'x_hot', name: 'X_hot' },

                { id: 'x_brain', name: 'x_brain' },
                { id: 'copper', name: 'Copper' },
                { id: 'hotMetalBlue', name: 'Hot Metal Blue' },
                { id: 'pet20Step', name: 'PET 20 Step' },
                { id: 'spectral', name: 'Spectral' },

                { id: 'blues', name: 'Blues' },
                { id: 'cool', name: 'Cool' },
                { id: 'jet', name: 'Jet' },
            ],
            player: {                   // 播放
                status: 1,              // 1 未播放， 2 播放，3 不能播放
                speed: 10,
                type: 'active'
            },
            layers: {
                lock: false,
                opacity1: 100,
                opacity2: 50,
            },
            isMPR: false,
        }
    },
    computed: {
        renderScroll() {
            return this.$store.state.renderScroll;
        }
    },
    watch: {
        renderScroll: {
            handler() {
                setTimeout(() => {
                    this.initStyle()
                    this.initGetViewportStatus()
                }, 0);
            },
            immediate: true
        },
        imageIdIndex: {
            handler() {
                const val = this.imageIdIndex + 1
                this.curIndex = val
            },
            immediate: true
        }
    },
    methods: {
        onSliderOpacity(value, idx) {
            value = Math.round(value)
            const layersDom = cornerstone.getLayers(this.element)
            if (layersDom.length && layersDom[idx]) {
                const layer = layersDom[idx]
                if (layer.options.opacity !== undefined) {
                    const opacity = value / 100
                    layer.options.opacity = opacity
                    
                    // 同步锁
                    if (this.layers.lock) {
                        const targetIdx = idx ? 0 : 1
                        const targetLayer = layersDom[targetIdx]
                        if (targetLayer.options.opacity !== undefined) {
                            const targetOpacity = (100 - value)
                            if (idx) {
                                this.layers.opacity1 = targetOpacity
                            }else {
                                this.layers.opacity2 = targetOpacity
                            }
                        }
                    }
                }
                cornerstone.updateImage(this.element)
            }
        },
        // 初始化工具显示位置
        initStyle() {
            try {
                let parent = this.$parent
                while (parent && parent.NAME !== 'AreaFit') {
                    parent = parent.$parent
                }
                const parentDom = parent.$el.querySelector('.box-area')
                const toolWidth    = this.$refs.ulBox.clientWidth
                let curDom = this.$parent.$el
                let isToolItem = false
                
                // 拖拽布局的区域不一样
                if (curDom.offsetParent && curDom.offsetParent.getAttribute('class').includes('image-area')) {
                    curDom = curDom.offsetParent
                    isToolItem = true
                }

                // 当前元素信息
                const curHeight = curDom.clientHeight + 3
                const curWidth  = curDom.clientWidth + 3
                const offsetTop = curDom.offsetTop
                const offsetLeft = curDom.offsetLeft

                // 父元素信息
                const parentHeight = parentDom.clientHeight
                const parentWidth  = parentDom.clientWidth
                
                // 超过目标高度-放在下面
                if (offsetTop + curHeight < parentHeight) {
                    this.style = {
                        bottom: '-29px'
                    }
                }else {
                    // 放在上边
                    // 已经是最上面一个了，就放在当前元素下
                    if (offsetTop === 0) {
                        let width = 'calc(100% - 18px)'
                        const allStack = cornerstoneTools.getToolState(this.element, 'stack')
                        // 当前序列只有一张的时候不需要 -18px
                        if (allStack && allStack.data.length && allStack.data[0].imageIds.length === 1) {
                            width = ''
                        }
                        this.style = {
                            bottom: '0px',
                            width: offsetLeft === 0 && curWidth > parentWidth ? width : ''
                        }
                    }else {
                        this.style = {
                            top: isToolItem ? '-45px' : '-29px'
                        }
                    }
                }
                // 当前工具宽度
                if (offsetLeft + toolWidth > parentWidth) {
                    this.style.right = toolWidth - curWidth + 3 + 'px'
                }
                // 如果当前宽度大于工具宽度，做 flex 样式
                // if (curWidth > toolWidth) {
                //     this.flexStyle = { 'justify-content': 'space-between', width: '100%'}
                // }else {
                //     this.flexStyle = { }
                // }
                this.$nextTick(() => {
                    const offset = (this.$parent.$el.offsetLeft + this.$refs.ulBox.clientWidth) - parentWidth
                    if (offset > 0) {
                        this.style.left = '-' + offset + 'px'
                    }
                })
            } catch (error) {
                // error
            }
        },
        // 获取图像信息
        async initGetViewportStatus() {
            if(!this.imageId) {
                return
            }
            this.isMPR = this.imageId.includes('mpr:')

            const image = await cornerstone.loadAndCacheImage(this.imageId);
            const viewport = cornerstone.getViewport(this.element)
            
            // 是否是融合显示
            const layers = cornerstone.getLayers(this.element)
            if (layers.length == 2) {
                this.layers.show = true
                this.layers.opacity1 = layers[0].options.opacity * 100
                this.layers.opacity2 = layers[1].options.opacity * 100
            }else {
                this.layers.show = false
            }

            this.viewportInfo = {
                h: viewport.hflip,
                v: viewport.vflip
            }

            // 通过这二个 tag 判断是否是截屏图
            const desc = image.data && image.data.string('x0008103e') || null;
            const classUid = image.data && image.data.string('x00080016') || null;
            const imageType = image.data && image.data.string('x00080008') || null;
            
            if (image.color || this.$fun.isCaptrue(desc, classUid, false, imageType)){
                this.modality = 'IMG'
            }else {
                const seriesInfo = cornerstone.metaData.get('generalSeriesModule', image.imageId);
                this.modality = seriesInfo.modality;
            }
            this.windowWwwc = getListByModality(this.modality)

            this.playerGetStatus()

            setTimeout(() => {
                if (this.modality != 'IMG') {
                    this.colormapsList.forEach(_ => {
                        this.updateColorbar(_.id)
                    })
                }
            }, 200);

        },
        // 播放状态
        playerGetStatus(){
           try {
                let toolData = cornerstoneTools.getToolState(this.element, 'stack')  // 获取堆的信息
                if (toolData.data[0].imageIds.length === 1){
                    this.player.status = 3
                    return
                }

                let playInfo = cornerstoneTools.getToolState(this.element, 'playClip')
                if (playInfo !== undefined && playInfo.data !== undefined && playInfo.data.length > 0 && playInfo.data[0].intervalId !== undefined){
                    this.player.status = 2
                    return
                }
                this.player.status = 1
           } catch (error) {
                // error
           }

        },
        // 点击设置窗宽窗位
        onClickSetWindowWwwc({ ww, wl }){
            let width = Number(ww);
            let height = Number(wl);

            if (this.modality === 'PT'){
                const { patientWeight, correctedDose } = getPatientWeightAndCorrectedDose(this.imageId);
                if (patientWeight && correctedDose) {
                    const _wl = this.$fun.transformULtoVoiWL(ww, wl, this.imageId )
                    
                    width = _wl.w;
                    height = _wl.l;
                }
            }

            let viewport = cornerstone.getViewport(this.element);
            viewport.voi.windowWidth = width;
            viewport.voi.windowCenter = height;
            cornerstone.setViewport(this.element, viewport);

            this.popover.visibleLut = false
        },
        // 点击设置伪彩
        onClickColormaps(id) {
            const viewport = cornerstone.getViewport(this.element)
            viewport.colormap = cornerstone.colors.getColormap(id)
            cornerstone.setViewport(this.element, viewport)
            cornerstone.updateImage(this.element, true)

            this.popover.visibleLut = false
        },
        // 点击播放暂停
        onClickPlayClip(status) {
            this.player.status = status
            if (status === 2){
                cornerstoneTools.stopClip(this.element)
                cornerstoneTools.playClip(this.element, this.player.speed)
            }else {
                cornerstoneTools.stopClip(this.element)
            }
        },
        // 设置工具
        onClickTool(name){
            if (name === 'NuclearCrosshairs') {
                this.$emit('setCrosshairsTool')
            }else {
                this.$emit('setActiveTool', name)
            }
            
            // 当前启用工具点击工具是一样的
            if (this.activeTool === name) {
                // (关闭工具)-选择工具
                this.$store.state.disabledTool = !this.$store.state.disabledTool

            }
        },
        // 输入框 回车
        async onEnterIndex(){
            let n = Math.abs(parseInt(this.curIndex))
            if (isNaN(n)) {
                return
            }
            let toolData = cornerstoneTools.getToolState(this.element, 'stack')  // 获取堆的信息
            if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0){
                return;
            }
            let total = toolData.data[0].imageIds.length     // 总数
            if (n > total) {
                n = total
            }else if (n < 0) {
                n = 1
            }
            await cornerstoneTools.scrollToIndex(this.element, n - 1)
            this.$emit('update:imageIdIndex', n - 1)
            this.curIndex = n
        },
        // 阻止事件冒泡
        stopPropagate(e){
            e.stopPropagation()
        },
        // 显示隐藏遮罩
        onClickOverlayVisible() {
            this.$emit('setIsOverlayVisible', !this.isOverlayVisible)
            
            this.popover.visibleInfo = false
        },
        // 显示图像 Tags
        onClickDicomTags() {
            this.$store.state.dicomTagsElement = this.element
            this.$store.state.dicomTagsVisible = !this.$store.state.dicomTagsVisible
        },
        onClickIsShowRemark() {
            this.$emit('onClickIsShowRemark', !this.remark.isShowRemark)

            this.popover.visibleInfo = false
        },
        // 重置工具 TODO 需要更新才正确
        resetTools(){
            let tools = ['ArrowAnnotate', 'Angle', 'Length', 'CircleRoi',
                'TextMarker', 'EllipticalRoi', 'RectangleRoi', 'FreehandRoi',
                ]
            tools.forEach(_ => {
                cornerstoneTools.clearToolState(this.element, _)
            });
            cornerstone.updateImage(this.element, true)
            this.popover.visibleInfo = false
        },
        // 旋转
        onCommandRotate(command) {
            const viewport = cornerstone.getViewport(this.element)
            if (!viewport) { return }

            if (command === 'h'){
                viewport.hflip = !viewport.hflip
                this.viewportInfo.h = viewport.hflip
            }else if (command == 'v'){
                viewport.vflip = !viewport.vflip
                this.viewportInfo.v = viewport.vflip
            }else if (command == 'reset') {
                viewport.rotation = 0
            }else if (!isNaN(command)) {
                viewport.rotation += command
            }
            cornerstone.setViewport(this.element, viewport);

            this.popover.visibleTransform = false
        },
        // 图像适应
        onClickAutoWindow(status) {
            cornerstone.resize(this.element, true)
            if (status) {
                let viewport = cornerstone.getViewport(this.element);
                viewport.scale = 1
                cornerstone.setViewport(this.element, viewport);
            }

            this.popover.visibleTransform = false
        },
        // 点击反片
        onClickInvert() {
			const viewport = cornerstone.getViewport(this.element);
            viewport.invert = !viewport.invert;                               // 修改视图信息
            cornerstone.setViewport(this.element, viewport);                  // 设置视图
        },
        // 移入显示，移入隐藏计算显示 top 值
        onMouseOver(ref) {
            const curDom = this.$refs[ref]
            const layerDom = curDom.querySelector('.popover-listInner')
            const clientHeight = document.documentElement.clientHeight
            const layerHeight = layerDom.clientHeight

            let curTop = curDom.offsetTop
            let parentTop = curDom.offsetParent.offsetTop
            curTop = parentTop + curTop

            const residueHeight = clientHeight - curTop
            const isBig = residueHeight - layerHeight
            layerDom.style.top = isBig < 0 ? isBig + 'px' : '-6px'
        },
        // 移出隐藏
        onMouseOut(ref) {
            const curDom = this.$refs[ref]
            const layerDom = curDom.querySelector('.popover-listInner')
            layerDom.style.top = '-20000px'
        },
        // canvas 颜色条
        updateColorbar(colormapId) {
            let colormap = cornerstone.colors.getColormap(colormapId);
            const lookupTable = colormap.createLookupTable();
            const canvas = this.$refs[colormapId][0];
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            const height = canvas.height;
            const width = canvas.width;
            const colorbar = ctx.createImageData(66, 20); // 宽高

            lookupTable.setTableRange(0, width);
            
            for(let col = 0; col < width; col++) {
                const color = lookupTable.mapValue(col);
    
                for(let row = 0; row < height; row++) {
                    const pixel = (col + row * width) * 4;
                    colorbar.data[pixel] = color[0];
                    colorbar.data[pixel+1] = color[1];
                    colorbar.data[pixel+2] = color[2];
                    colorbar.data[pixel+3] = color[3];
                }
            }
    
            ctx.putImageData(colorbar, 0, 0);
        },
        // setRemark(remark) {
        //     this.$emit('setRemark', remark)
        //     this.popover.visibleInfo = false
        // }
    }
}
</script>
<style lang="scss" scoped>
.item-tools{
    width: 100%;
    height: 28px;
    padding: 0px;
    position: absolute;
    background: #efefef;
    z-index: 4;
    transition: none;
    .item-tools-flex{
        position: absolute;
        display: flex;
        justify-content: space-between;
        min-width: 100%;    
        background: #efefef;
    }
    .item-tools-item {
        display: flex;
        .item-tools__tool {
            width: 28px;
            height: 28px;
            line-height: 26px;
            background: #F5F9FC;
            border: 1px solid #D0D2D1;
            color: #142B4B;
            border-radius: 4px;
            cursor: pointer;
            &:hover {
                background-color: #D1DBE7;
            }
            &.item-tools--type1{
                width: 40px;
            }
            &.i-active{
                background-color: #6294B7;
                color: white;
            }
            > i {
                display: inline-block;
                width: 100%;
                height: 100%;
                font-size: 18px;
            }
        }
    }
    .item-tools-input{
        height: 28px;
        width: 42px;
        ::v-deep input{
            position: relative;
            top: 1px;
            width: 38px;
            height: 26px;
            border-color: #D0D2D1;
            font-size: 14px;
            text-align: center;
            border-radius: 4px !important;
        }
    }
}
.popover-listInner{
    position: absolute;
    left: 138px;
    top: -20000px;
    display: block;
    width: 140px;
    padding: 6px;
    background: white;
    &.popover-listInner--canvas{
        width: 190px;
        .popover-lis--item{
            padding-left: 0px;
            display: flex;
            align-items: center;
            canvas {
                margin-right: 4px;
            }
        }
        &.custom-layers{
            height: 150px;
            position: absolute;
            margin-top: -112px;
            width: 92px;
            .popover-lis--item{
                height: 100%;
                flex-direction: column;
                align-items: center;
                cursor: initial;
                background: #f5f9fc;
                border: 1px solid #eee;
                &:hover {
                    background: #f5f7fa;
                }
                .lock {
                    font-size: 16px;
                    color: #142b4b;
                    cursor: pointer;
                }
                .popover-container {
                    display: flex;
                    > div {
                        text-align: center;
                        > span {
                            display: inline-block;
                        }
                        .top {
                            padding-bottom: 4px;
                        }
                        .bottom {
                            padding-top: 2px;
                        }
                    }
                }
            }
        }
    }
}
</style>