import macro from 'vtk.js/Sources/macro';

import csTools from '$library/cornerstone/cornerstoneTools'
import getConfigByStorageKey from '$library/utils/configStorage.js'

let instanceId = 1;

function getWidgetNode(svgContainer, widgetId) {
  let node = svgContainer.querySelector(`#${widgetId}`);
  if (!node) {
    node = document.createElement('div');
    node.setAttribute('id', widgetId);
    node.setAttribute('style', `position: absolute; left: 0; top: 0; width: 0; height: 0; z-index: 2;`);
    svgContainer.appendChild(node);
  }
  return node;
}

// ----------------------------------------------------------------------------

function vtkSVGarrowWidget(publicAPI, model) {
  model.classHierarchy.push('vtkSVGarrowWidget');
  model.widgetId = `vtkSVGarrowWidget-${instanceId++}`;
  const storageList = {}

  publicAPI.render = (svgContainer, scale) => {  // scale = 1
    requestAnimationFrame(() => {

      if (!model.api) return
      const node = getWidgetNode(svgContainer, model.widgetId);
      node.innerHTML = ''

      const storageId = 'getStorageId()'

      const storageDataList = storageList[storageId] || []

      const width = parseInt(svgContainer.clientWidth, 10);
      const height = parseInt(svgContainer.clientHeight, 10);
      const toolsColor = getConfigByStorageKey('configs-toolsColor');
      const customColor =  toolsColor['ArrowAnnotate'] || csTools.toolColors.getFillColor() || '#00FF00'


      const getArrowSvgHtml = ({ firstPoint, secondPoint }) => {
        const svgArrow = document.createElement('div')
        const svgArrowhead = document.createElement('div')
        const dx = (secondPoint[0] - firstPoint[0]) * 0.01 * width;
        const dy = (secondPoint[1] - firstPoint[1]) * 0.01 * height;
        const distan = Math.sqrt(dx * dx + dy * dy)
        const theta = Math.atan2(dy, dx);
        const degrees = theta * (180 / Math.PI);

        svgArrow.setAttribute('style', `position: absolute; width: ${distan / 2}px; height: ${4}px;
        position: absolute;
          margin: 0;
          top: ${(0.5 + firstPoint[1] / 200) * height}px;
          left: ${(0.5 + firstPoint[0] / 200) * width}px;
          background: ${customColor};
          transform: rotate(${degrees}deg);
          transform-origin: center left;`)
        svgArrowhead.setAttribute('class', `markarrowhead`)
        svgArrowhead.setAttribute('style', `border-right-color: ${customColor};`)
        
        svgArrow.appendChild(svgArrowhead)
        // svgArrow.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}">
        //   <defs>
        //     <marker id="arrowhead" markerWidth="6" markerHeight="6" refX="6" refY="3" orient="auto">
        //       <polygon points="0 0, 6 3, 0 6" fill="${customColor}" />
        //     </marker>
        //   </defs>
        //   <line 
        //   x2="${(0.5 + firstPoint[0] / 200) * width}" 
        //   y2="${(0.5 + firstPoint[1] / 200) * height}" 
        //   x1="${(0.5 + secondPoint[0] / 200) * width}" 
        //   y1="${(0.5 + secondPoint[1] / 200) * height}" stroke="${customColor}" stroke-width="3" marker-end="url(#arrowhead)" />
        // </svg>`
        return svgArrow
      }
      const addLabelNode = (item, labelindex) => {
        const labelNode = getArrowSvgHtml(item)
        // const labelNodeHandler = document.createElement('i')


        // labelNodeHandler.setAttribute('class', 'markspan el-dialog__close el-icon el-icon-close')
        // labelNodeHandler.setAttribute('title', '删除标注')
        // labelNodeHandler.setAttribute('labelindex', labelindex)
        // labelNodeHandler.setAttribute('style', `position: absolute;
        // top: -4px;
        // right: -12px;
        // width: 12px;
        // height: 12px;
        // font-size: 12px;
        // background: ${customColor};
        // border-radius: 50%;
        // cursor: pointer;`)

        // labelNodeHandler.addEventListener('mousedown', (e) => {
        //   e.stopPropagation()
        //   e.preventDefault();
        //   storageDataList.forEach((item, index) => {
        //     if (e.target.getAttribute('labelindex') == index) {
        //       storageDataList.splice(index, 1)
        //     }
        //   })
        //   labelNode.remove()
        //   return
        // })

        // labelNode.appendChild(labelNodeHandler)

        node.appendChild(labelNode)

      }

      storageDataList.forEach((item, index) => {
        addLabelNode(item, index)
      })

      const {
        firstPoint,
        secondPoint,
        haveFirstPoint,
        api
      } = model;

      // const labelNode = document.createElement('div')
      // labelNode.setAttribute('style', `
      //     position: absolute;
      //     margin: auto;
      //     width: 1px;
      //     height: 1px;
      //     top: ${firstPoint[1]}%;
      //     bottom: 0;
      //     left: ${firstPoint[0]}%;
      //     right: 0;
      //     background: ${customColor};`)
      // node.appendChild(labelNode)

      if (haveFirstPoint) {
        node.appendChild(getArrowSvgHtml({ firstPoint, secondPoint, }))
      }

    })
  };


  publicAPI.mousedown = (p, api) => {
    if (!model.api) model.api = api
    if (!model.haveFirstPoint) {
      model.haveFirstPoint = true

      model.firstPoint = [...p]


    } else {
      model.haveFirstPoint = false


      const {
        firstPoint, secondPoint
      } = model

      const pointData = {
        firstPoint,
        secondPoint
      }
      const storageId = 'getStorageId()'


      if (!storageList[storageId]) storageList[storageId] = []
      const storageDataList = storageList[storageId]
      // console.log(pointData)
      storageDataList.push(pointData)

      model.firstPoint = [null, null]
      model.secondPoint = [null, null]


    }

    model.api.svgWidgetManager.render();

  };


  publicAPI.moveMark = (p, api) => {
    if (!model.api) model.api = api
    if (model.haveFirstPoint) {
      model.secondPoint = [...p]

    } else {
      model.firstPoint = [...p]
      model.secondPoint = [...p]


    }

    model.api.svgWidgetManager.render();

  };

  publicAPI.cancelTempPoint = (api) => {
    if (!model.api) model.api = api
    model.firstPoint = [null, null]
    model.secondPoint = [null, null]
    model.haveFirstPoint = false

    model.api.svgWidgetManager.render();
  };


  publicAPI.deletePointStorage = (p, api) => {
    const storageId = 'getStorageId()'
    const list = storageList[storageId] || []
    list.forEach((item, index) => {
      if (positionIsEqual(item.firstPoint, p)) {
        list.splice(index, 1)
      }
    })
    model.api.svgWidgetManager.render();
  };

  publicAPI.clearPointStorage = () => {
    const storageId = 'getStorageId()'
    storageList[storageId] = []
    if (!model.api) return  
    const { svgWidgetManager } = model.api;
    svgWidgetManager.render();
  };

  // function getStorageId() {
  //   const renderer = model.api.genericRenderWindow.getRenderer();
  //   const camera = renderer.getActiveCamera()
  //   const directionId = camera.getDirectionOfProjection().reduce((prev, curr) => String(prev) + (+curr).toFixed(5), '')
  //   const positionId = camera.getPosition().reduce((prev, curr) => String(prev) + String(Math.round(+curr)), '') // bugfix: 取整，否则初次取值和初次滚动后的值有偏差(每次滚动坐标变动1)
  //   return directionId + positionId
  // }

  function positionIsEqual(a, b, diff = 20) {
    if (!a || !b || !a.every || !b.every) {
      return false
    }
    return a.every((v, i) => {
      return Math.abs(v - b[i]) < diff
    })
  }

}

// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
  firstPoint: [null, null],
  secondPoint: [null, null],
  haveFirstPoint: '',
  api: null
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {
  Object.assign(model, DEFAULT_VALUES, initialValues);

  macro.obj(publicAPI, model);
  macro.get(publicAPI, model, ['widgetId']);
  macro.setGet(publicAPI, model, [
    'haveFirstPoint',
    'api'
  ]);

  macro.setGetArray(publicAPI, model, ['firstPoint'], 2);
  macro.setGetArray(publicAPI, model, ['secondPoint'], 2);

  vtkSVGarrowWidget(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(
  extend,
  'vtkSVGarrowWidget'
);

// ----------------------------------------------------------------------------

export default { newInstance, extend };
