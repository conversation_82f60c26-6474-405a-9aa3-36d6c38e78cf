import csTools from '$library/cornerstone/cornerstoneTools'

const { 
    addToolState, 
    clearToolState, 
    removeToolState, 
    getToolState,
    getModule, 
    toolStyle, 
    toolColors,
    external, 
    EVENTS, 
    store 
} = csTools

const { state } = store

// const { hideToolCursor, setToolCursor } = csTools.importInternal('store/setToolCursor')

const BaseAnnotationTool = csTools.importInternal('base/BaseAnnotationTool')
const pointInsideBoundingBox = csTools.importInternal('util/pointInsideBoundingBox')
const clipToBox = csTools.importInternal('util/clipToBox')
const triggerEvent = csTools.importInternal('util/triggerEvent')

const freehandUtils = csTools.importInternal('util/freehandUtils')
const getPixelSpacing = csTools.importInternal('util/getPixelSpacing')
const moveHandleNearImagePoint = csTools.importInternal('manipulators/moveHandleNearImagePoint')
const handleActivator = csTools.importInternal('manipulators/handleActivator')

const { freehandRoiCursor } = csTools.importInternal('tools/cursors')

const getNewContext = csTools.importInternal('drawing/getNewContext');
const draw = csTools.importInternal('drawing/draw');
const drawJoinedLines = csTools.importInternal('drawing/drawJoinedLines');
const drawHandles = csTools.importInternal('drawing/drawHandles');
const drawLinkedTextBox = csTools.importInternal('drawing/drawLinkedTextBox');

const throttle = csTools.importInternal('util/throttle');

const {
    FreehandHandleData,
  } = freehandUtils;

export default class FreehandLength extends BaseAnnotationTool {
    constructor(props = {}) {
        const defaultProps = {
            name: 'FreehandLength',
            supportedInteractionTypes: ['Mouse'],
            configuration: defaultFreehandConfiguration(),
            svgCursor: freehandRoiCursor,
            options: {
                mouseButtonMask: []
            }
        }
      
        super(props, defaultProps)
        
        this._drawing = false
        this._dragging = false

        this.throttledUpdateCachedStats = throttle(this.updateCachedStats, 110);
        
        // 添加绘制时事件处理
        this._drawingMouseDragCallback = this._drawingMouseDragCallback.bind(this)
        this._drawingMouseUpCallback = this._drawingMouseUpCallback.bind(this)
        // 修改时候的事件处理
        this._editMouseUpCallback = this._editMouseUpCallback.bind(this);
        this._editMouseDragCallback = this._editMouseDragCallback.bind(this);

    }
    // 创建绘制工具
    createNewMeasurement(eventData) {
        const goodEventData =
        eventData && eventData.currentPoints && eventData.currentPoints.image;

        if (!goodEventData) {
        logger.error(
            `required eventData not supplied to tool ${this.name}'s createNewMeasurement`
        );

        return;
        }

        const measurementData = {
            visible: true,
            active: true,
            select: false,
            invalidated: true,
            color: undefined,
            handles: {
                points: [],
            },
        };

        measurementData.handles.textBox = {
            active: false,
            hasMoved: false,
            movesIndependently: false,
            drawnIndependently: true,
            allowedOutsideImage: true,
            hasBoundingBox: true,
        };

        return measurementData;
    }

    addNewMeasurement(evt) {
        const eventData = evt.detail;
        this._startDrawing(evt);
        this._addPoint(eventData);
    }

    // 渲染工具
    renderToolData(evt) {
        const eventData = evt.detail;

        // If we have no toolState for this element, return immediately as there is nothing to do
        const toolState = getToolState(evt.currentTarget, this.name);

        if (!toolState) {
        return;
        }

        const { image, element } = eventData;
        const { rowPixelSpacing, colPixelSpacing } = getPixelSpacing(image);
        const config = this.configuration;

        // We have tool data for this element - iterate over each one and draw it
        const context = getNewContext(eventData.canvasContext.canvas);
        const lineWidth = toolStyle.getToolWidth();
        const { renderDashed } = config;
        const lineDash = getModule('globalConfiguration').configuration.lineDash;

        for (let i = 0; i < toolState.data.length; i++) {
            const data = toolState.data[i];

            if (data.visible === false) {
                continue;
            }

            draw(context, context => {
                let color = toolColors.getColorIfActive(data);
                let fillColor;

                if (data.active) {
                    color = toolColors.getColorIfActive(data);
                    fillColor = toolColors.getFillColor();
                } else {
                    fillColor = toolColors.getToolColor();
                }

                let options = { color };

                if (renderDashed) {
                    options.lineDash = lineDash;
                }

                if (data.handles.points.length) {
                    const points = data.handles.points;
                    drawJoinedLines(context, element, points[0], points, options);
                    
                    if (data.totalLength === undefined) {
                        // 绘制一个跟到鼠标的线
                        drawJoinedLines(
                            context,
                            element,
                            points[points.length - 1],
                            [config.mouseLocation.handles.start],
                            options
                        );
                    }
                }
                // Draw handles

                options = {
                    color,
                    fill: fillColor,
                    select: data.select,
                };
                if (config.alwaysShowHandles || (data.active && !this._dragging)) {
                    // 渲染所有句柄
                    options.handleRadius = config.activeHandleRadius;

                    if (this.configuration.drawHandles) {
                        drawHandles(context, eventData, data.handles.points, options);
                    }
                }
                // if (data.active && !this._dragging) {
                //     // 如果主动绘图，则在原点和鼠标处绘制手柄
                //     options.handleRadius = config.activeHandleRadius;

                //     if (this.configuration.drawHandles) {
                //         drawHandles(
                //         context,
                //         eventData,
                //         config.mouseLocation.handles,
                //         options
                //         );
                //     }

                //     const firstHandle = data.handles.points[0];

                //     if (this.configuration.drawHandles) {
                //         drawHandles(context, eventData, [firstHandle], options);
                //     }
                // }

                // 选择的时候，走这里渲染操作点
                if (!data.active && data.select) {
                    drawHandles(context, eventData, data.handles.points, options);
                }

                if (!data.handles.textBox.hasMoved) {
                    const startPoint = data.handles.points[0]
                    const endPoint   = data.handles.points[data.handles.points.length - 1 || 0]
                    const coords = {
                      x: Math.max(startPoint.x, endPoint.x),
                    };
          
                    // Depending on which handle has the largest x-value,
                    // Set the y-value for the text box
                    if (coords.x === startPoint.x) {
                      coords.y = startPoint.y;
                    } else {
                      coords.y = endPoint.y;
                    }
          
                    data.handles.textBox.x = coords.x;
                    data.handles.textBox.y = coords.y;
                }

                  
                // Update textbox stats
                if (data.invalidated === true && !data.active) {
                    if (data.totalLength) {
                        this.throttledUpdateCachedStats(image, element, data);
                    } else {
                        this.updateCachedStats(image, element, data);
                    }
                }
                
                const text = textBoxText(data, rowPixelSpacing, colPixelSpacing);

                drawLinkedTextBox(
                    context,
                    element,
                    data.handles.textBox,
                    text,
                    data.handles.points,
                    textBoxAnchorPoints,
                    color,
                    lineWidth,
                    0,
                    true,
                    data.select
                );
            });
        }

        function textBoxText(annotation, rowPixelSpacing, colPixelSpacing) {
            const measuredValue = _sanitizeMeasuredValue(annotation.totalLength);
      
            // measured value is not defined, return empty string
            if (!measuredValue) {
              return '';
            }
      
            // Set the length text suffix depending on whether or not pixelSpacing is available
            let suffix = 'mm';

            suffix = csTools.store.state.lengthUnit;
      
            if (!rowPixelSpacing || !colPixelSpacing) {
              suffix = 'pixels';
            }

            annotation.unit = suffix;

            if (annotation.unit === 'cm') {
                return "".concat((measuredValue / 10).toFixed(1), " ").concat(suffix);
            }
            return `${measuredValue.toFixed(2)} ${suffix}`;
        }

        function textBoxAnchorPoints(handles) {
            return handles;
        }
    }
    // 更新数据 text显示数据
    updateCachedStats(image, element, data) {
        const { rowPixelSpacing, colPixelSpacing } = getPixelSpacing(image);

        let totalLength = 0

        for (let index = 0; index < data.handles.points.length; index++) {
            const point = data.handles.points[index];

            if (point.lines.length) {
                // 长度计算
                const { x, y } = point.lines[0]
                const dx = (x - point.x) * (colPixelSpacing || 1);
                const dy = (y - point.y) * (rowPixelSpacing || 1);
                const length = Math.sqrt(dx * dx + dy * dy);

                totalLength += length
            }
        }

        data.totalLength = totalLength;

        // false 数据不在计算
        data.invalidated = false;
    }
    pointNearTool(element, data, coords) {
        const validParameters = data && data.handles && data.handles.points;

        if (!validParameters) {
            throw new Error(
                `invalid parameters supplied to tool ${this.name}'s pointNearTool`
            );
        }

        if (!validParameters || data.visible === false) {
            return false;
        }

        const isPointNearTool = this._pointNearHandle(element, data, coords);

        if (isPointNearTool !== undefined) {
            return true;
        }

        return false;
    }
    handleSelectedCallback(evt, toolData, handle, interactionType = 'mouse') {
        const { element } = evt.detail;
        const toolState = getToolState(element, this.name);
    
        // 设置选中
        toolData.select = true;
        // 触发选中事件
        triggerEvent(element, EVENTS.SELECT_CLICK, { element, toolData });
    
        if (handle.hasBoundingBox) {
          // Use default move handler.
          moveHandleNearImagePoint(evt, this, toolData, handle, interactionType);
    
          return;
        }
    
        const config = this.configuration;
    
        config.dragOrigin = {
          x: handle.x,
          y: handle.y,
        };
    
        // Iterating over handles of all toolData instances to find the indices of the selected handle
        for (let toolIndex = 0; toolIndex < toolState.data.length; toolIndex++) {
          const points = toolState.data[toolIndex].handles.points;
    
          for (let p = 0; p < points.length; p++) {
            if (points[p] === handle) {
              config.currentHandle = p;
              config.currentTool = toolIndex;
            }
          }
        }
    
        this._modifying = true;
    
        this._activateModify(element);
    
        // Interupt eventDispatchers
        preventPropagation(evt);
    }
    // 绘图结束
    cancelDrawing(element) {
        const toolState = getToolState(element, this.name);
    
        const config = this.configuration;
    
        const data = toolState.data[config.currentTool];
    
        // data.active = false;
        // data.highlight = false;
        data.visible = false
    
        // Reset the current handle
        config.currentHandle = 0;
        config.currentTool = -1;
        // 为什么删除不了？
        removeToolState(element, this.name, data);
    
        this._deactivateDraw(element);
    
        external.cornerstone.updateImage(element);
      }
    /**
     * Returns a handle of a particular tool if it is close to the mouse cursor
     *
     * @private
     * @param {Object} element - The element on which the roi is being drawn.
     * @param {Object} data      Data object associated with the tool.
     * @param {*} coords
     * @returns {Number|Object|Boolean}
     */
    _pointNearHandle(element, data, coords) {
        if (!data || data.handles === undefined || data.handles.points === undefined) {
            return;
        }

        if (data.visible === false) {
            return;
        }

        for (let i = 0; i < data.handles.points.length; i++) {
            const handleCanvas = external.cornerstone.pixelToCanvas(
                element,
                data.handles.points[i]
            );

            if (external.cornerstoneMath.point.distance(handleCanvas, coords) < 4) {
                return i;
            }
        }

        // Check to see if mouse in bounding box of textbox
        if (data.handles.textBox) {
            if (pointInsideBoundingBox(data.handles.textBox, coords)) {
                return data.handles.textBox;
            }
        }
    }
    // 鼠标按下
    _drawingMouseDragCallback(evt) {
        if (!this.options.mouseButtonMask.includes(evt.detail.buttons)) {
          return;
        }
        this._drawingDrag(evt);
    }
    // 鼠标按下
    _drawingMouseDownCallback(evt) {
        const eventData = evt.detail;
        const { buttons, currentPoints, element } = eventData;

        if (!this.options.mouseButtonMask.includes(buttons)) {
            return;
        }

        const coords = currentPoints.canvas;

        const config = this.configuration;
        const currentTool = config.currentTool;
        const toolState = getToolState(element, this.name);
        if (!toolState) {
            return
        }
        const data = toolState.data[currentTool];

        const handleNearby = this._pointNearHandle(element, data, coords);
        if (handleNearby === undefined) {
            this._addPoint(eventData);
        }

        preventPropagation(evt);

        return;
    }
    // 鼠标抬起
    _drawingMouseUpCallback(evt) {
        const { currentPoints, element } = evt.detail;
        const config = this.configuration;

        const toolState = getToolState(element, this.name);
    
        const currentTool = config.currentTool;
    
        const data = toolState.data[currentTool];
        if (!data) {
            return
        }
        // 只有一个点，进入删除
        if (data.handles.points.length <= 1) {
            this.cancelDrawing(element)
            return
        }
    
        this._dragging = false;

        const lastHandlePlaced = config.currentHandle;
        // 获取鼠标抬起最后一个点
        const newHandleData = new FreehandHandleData(currentPoints.image);

        this._endDrawing(element, newHandleData);

        preventPropagation(evt);
        return;
    }
    _editMouseDragCallback(evt) {
        const eventData = evt.detail;
        const { element, buttons } = eventData;
    
        if (!this.options.mouseButtonMask.includes(buttons)) {
          return;
        }

    
        const toolState = getToolState(element, this.name);
    
        const config = this.configuration;
        const data = toolState.data[config.currentTool];
        const currentHandle = config.currentHandle;
        const points = data.handles.points;
        let handleIndex = -1;

        // Set the mouseLocation handle
        this._getMouseLocation(eventData);
    
        data.active = true;
        data.highlight = true;
        points[currentHandle].x = config.mouseLocation.handles.start.x;
        points[currentHandle].y = config.mouseLocation.handles.start.y;
    
        handleIndex = this._getPrevHandleIndex(currentHandle, points);
    
        if (currentHandle >= 0) {
          const lastLineIndex = points[handleIndex].lines.length - 1;
          const lastLine = points[handleIndex].lines[lastLineIndex];

          if (!lastLine) {
            return
          }
          
          lastLine.x = config.mouseLocation.handles.start.x;
          lastLine.y = config.mouseLocation.handles.start.y;
        }

        external.cornerstone.updateImage(element);
    }
    _editMouseUpCallback(evt) {
        const eventData = evt.detail;
        const { element } = eventData;
        const toolState = getToolState(element, this.name);
    
        this._deactivateModify(element);
    
        this._endDrawing(element);
    
        external.cornerstone.updateImage(element);
    }
    _getPrevHandleIndex(currentHandle, points) {
        if (currentHandle === 0) {
          return points.length - 1;
        }
    
        return currentHandle - 1;
    }
    _drawingDrag(evt) {
        const eventData = evt.detail;
        const { element } = eventData;
    
        const toolState = getToolState(element, this.name);
    
        const config = this.configuration;
        const currentTool = config.currentTool;
    
        const data = toolState.data[currentTool];
    
        // Set the mouseLocation handle
        this._getMouseLocation(eventData);

        this._addPointPencilMode(eventData, data.handles.points);
        this._dragging = true;
    
        // Force onImageRendered
        external.cornerstone.updateImage(element);
    }
    // 添加最小点
    _addPointPencilMode(eventData, points) {
        const config = this.configuration;
        const { element } = eventData;
        const mousePoint = config.mouseLocation.handles.start;
    
        const handleFurtherThanMinimumSpacing = handle =>
          this._isDistanceLargerThanSpacing(element, handle, mousePoint);
    
        if (points.every(handleFurtherThanMinimumSpacing)) {
            this._addPoint(eventData);
        }
    }
    // 如果两个点比this.configuration.spacing更远，则返回true。
    _isDistanceLargerThanSpacing(element, p1, p2) {
        return this._compareDistanceToSpacing(element, p1, p2, '>');
    }
    // 将两点之间的距离与this.configuration.spacing进行比较。
    _compareDistanceToSpacing(
        element,
        p1,
        p2,
        comparison = '>',
        spacing = this.configuration.spacing
        ) {
        if (comparison === '>') {
            return external.cornerstoneMath.point.distance(p1, p2) > spacing;
        }

        return external.cornerstoneMath.point.distance(p1, p2) < spacing;
    }
    // 结束绘画
    _endDrawing(element, newHandleData) {
        const toolState = getToolState(element, this.name);
        const config = this.configuration;
        const data = toolState.data[config.currentTool];
    
        data.active = false;
        data.highlight = false;
    
        if (this._modifying) {
          this._modifying = false;
          data.invalidated = true;
        }
    
        // Reset the current handle
        config.currentHandle = 0;
        config.currentTool = -1;
        
        // 有最后一点，绘制最后一个点
        if (newHandleData !== undefined) {
            const points = data.handles.points;
            const lastX = points[points.length - 1].x
            const lastY = points[points.length - 1].y
            if (lastX.toFixed(1) != newHandleData.x.toFixed(1) || lastY.toFixed(1) != newHandleData.y.toFixed(1)) {
                points[points.length - 1].lines.push({ x: newHandleData.x, y: newHandleData.y });
                points.push(newHandleData);
            }

        }
    
        if (this._drawing) {
          this._deactivateDraw(element);
        }
    
        external.cornerstone.updateImage(element);
    
        this.fireModifiedEvent(element, data);
        this.fireCompletedEvent(element, data);
    }
    // 添加点
    _addPoint(eventData) {
        const { currentPoints, element } = eventData;
        const toolState = getToolState(element, this.name);
    
        // Get the toolState from the last-drawn polygon
        const config = this.configuration;
        const data = toolState.data[config.currentTool];
        
        const newHandleData = new FreehandHandleData(currentPoints.image);
    
        // If this is not the first handle
        if (data.handles.points.length) {
          // Add the line from the current handle to the new handle
          data.handles.points[config.currentHandle - 1].lines.push(
            currentPoints.image
          );
        }
    
        // Add the new handle
        data.handles.points.push(newHandleData);
    
        // Increment the current handle value
        config.currentHandle += 1;
    
        // Force onImageRendered to fire
        external.cornerstone.updateImage(element);
        this.fireModifiedEvent(element, data);
    }
    /**
   * Begining of drawing loop when tool is active and a click event happens far
   * from existing handles.
   *
   * @private
   * @param {Object} evt - The event.
   * @returns {undefined}
   */
    _startDrawing(evt) {
        const eventData = evt.detail;
        const measurementData = this.createNewMeasurement(eventData);
        const { element } = eventData;
        const config = this.configuration;

        this._activateDraw(element);
        this._getMouseLocation(eventData);

        addToolState(element, this.name, measurementData);

        const toolState = getToolState(element, this.name);

        config.currentTool = toolState.data.length - 1;

        this._activeDrawingToolReference = toolState.data[config.currentTool];
    }
    // 绘制时添加的事件
    _activateDraw(element) {
        this._drawing = true;
    
        state.isMultiPartToolActive = true;
        // hideToolCursor(this.element);

        // Drag/Pencil Mode
        element.addEventListener(EVENTS.MOUSE_DRAG, this._drawingMouseDragCallback);
        element.addEventListener(EVENTS.MOUSE_UP, this._drawingMouseUpCallback);
        element.addEventListener(EVENTS.MOUSE_CLICK, this._drawingMouseUpCallback);
        
    
        external.cornerstone.updateImage(element);
    }
    // 结束绘图移除事件
    _deactivateDraw(element) {
        this._drawing = false;
        state.isMultiPartToolActive = false;
        this._activeDrawingToolReference = null;
        // setToolCursor(this.element, this.svgCursor);

        element.removeEventListener(
            EVENTS.MOUSE_DRAG,
            this._drawingMouseDragCallback
        );
        element.removeEventListener(
            EVENTS.MOUSE_UP,
            this._drawingMouseUpCallback);

        element.removeEventListener(
            EVENTS.MOUSE_CLICK,
            this._drawingMouseUpCallback);

        external.cornerstone.updateImage(element);
    }
    /**
     * 添加修改事件
     */
    _activateModify(element) {
        state.isToolLocked = true;

        element.addEventListener(EVENTS.MOUSE_UP, this._editMouseUpCallback);
        element.addEventListener(EVENTS.MOUSE_DRAG, this._editMouseDragCallback);
        element.addEventListener(EVENTS.MOUSE_CLICK, this._editMouseUpCallback);

        external.cornerstone.updateImage(element);
    }

    /**
     * 修改事件
     */
    _deactivateModify(element) {
        state.isToolLocked = false;

        element.removeEventListener(EVENTS.MOUSE_UP, this._editMouseUpCallback);
        element.removeEventListener(EVENTS.MOUSE_DRAG, this._editMouseDragCallback);
        element.removeEventListener(EVENTS.MOUSE_CLICK, this._editMouseUpCallback);

        external.cornerstone.updateImage(element);
    }
    _getMouseLocation(eventData) {
        const { currentPoints, image } = eventData;
        // Set the mouseLocation handle
        const config = this.configuration;
    
        config.mouseLocation.handles.start.x = currentPoints.image.x;
        config.mouseLocation.handles.start.y = currentPoints.image.y;
        clipToBox(config.mouseLocation.handles.start, image);
    }
    // 触发 MEASUREMENT_MODIFIED 事件发送
    fireModifiedEvent(element, measurementData) {
        const eventType = EVENTS.MEASUREMENT_MODIFIED;
        const eventData = {
          toolName: this.name,
          toolType: this.name, // Deprecation notice: toolType will be replaced by toolName
          element,
          measurementData,
        };
    
        triggerEvent(element, eventType, eventData);
    }
    fireCompletedEvent(element, measurementData) {
        const eventType = EVENTS.MEASUREMENT_COMPLETED;
        const eventData = {
            toolName: this.name,
            toolType: this.name,
            element,
            measurementData,
        };
    
        triggerEvent(element, eventType, eventData);
    }
}
function defaultFreehandConfiguration() {
    return {
        hideHandlesIfMoving: false,
        mouseLocation: {
            handles: {
                start: {
                highlight: true,
                active: true,
                },
            },
        },
        spacing: 1,
        activeHandleRadius: 3,
        completeHandleRadius: 6,
        completeHandleRadiusTouch: 28,
        alwaysShowHandles: false,
        invalidColor: 'crimson',
        currentHandle: 0,
        currentTool: -1,
        drawHandles: true,
        renderDashed: false,
    };
}

function preventPropagation(evt) {
    evt.stopImmediatePropagation();
    evt.stopPropagation();
    evt.preventDefault();
}
function _sanitizeMeasuredValue(value) {
    const parsedValue = Number(value);
    const isNumber = !isNaN(parsedValue);
  
    return isNumber ? parsedValue : undefined;
}
  