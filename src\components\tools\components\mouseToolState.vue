<template>
    <ul class="row">
        <li class="item-01">
            <span>缩略图打印</span>
            <span class="btn" @click="onClickPrintThumbState">{{ showPrintThumb ? '关闭' : '打开' }}</span>
        </li>
        <li>操作提示：</li>
        <li>左键：{{ leftToolName }} ,</li>
        <li>滚轮按下：{{ midToolName }} ,</li>
        <li>右键：{{ rightToolName }} ,</li>
    </ul>
</template>
<script>
export default {
    props: {
        activeTool: '',
    },
    data() {
        return {
            midToolName: '',
            rightToolName: '',
            tools: [
                { key: 'Airtools', name: '选择' },
                { key: 'Wwwc', name: '调窗' },
                { key: 'Pan', name: '平移' },
                { key: 'Zoom', name: '缩放' },
                {key: 'Magnify', name: '透镜' },
                {key: 'Invert', name: '反片' },
                {key: 'StackScroll', name: '翻页' },
                {key: 'NuclearCrosshairs', name: '定位线' },
                {key: 'vtkInteractorStyleMPRSlice', name: 'MIP旋转' },

                {key: 'ArrowAnnotate', name: '箭头' },
                {key: 'Length', name: '直线' },
                {key: 'Bidirectional', name: '双向线' },
                {key: 'Angle', name: '角度' },
                {key: 'DragProbe', name: '点测量' },
                {key: 'EllipticalRoi', name: '椭圆' },
                {key: 'CircleRoi', name: '圆' },
                {key: 'RectangleRoi', name: '矩形' },
                {key: 'FreehandRoi', name: '勾画' },
                {key: 'EllipticalMeasure', name: '体测量' },
                
                {key: 'TextMarker', name: '标注' },
                {key: 'Eraser', name: '橡皮擦' },
            ],
            key: 'layout-print-thumb',
        }
    },
    computed: {
        mouseEvents() {
            // vuex 中的 鼠标工具
            return this.$store.state.mouseEvents;
        },
        showPrintThumb() {
            return this.$store.state.showPrintThumb
        },
        leftToolName() {
            const toolItem = this.tools.find(tool => tool.key === this.activeTool);
            if (toolItem) {
                return toolItem.name;
            }
            return '';
        }
    },
    watch: {
        mouseEvents: {
            handler() {
                const toolItemMid = this.tools.find(tool => tool.key === this.mouseEvents.mid);
                if (toolItemMid) {
                   this.midToolName = toolItemMid.name;
                }else {
                    this.midToolName = '';
                }

                const toolItemRight = this.tools.find(tool => tool.key === this.mouseEvents.right);
                if (toolItemRight) {
                   this.rightToolName = toolItemRight.name;
                }else {
                    this.rightToolName = '';
                }
            },
            deep: true,
            immediate: true
        },
    },
    methods: {
        onClickPrintThumbState() {
            this.$store.commit('setPrintThumb')
        }
    }
}
</script>
<style lang="scss">
.row {
    display: flex;
    text-indent: 10px;
    .item-01 {
        width: 140px;
        text-align: center;
        .btn {
            color: #ffffff;
            line-height: 23px;
            margin-left: 10px;
            border: 1px solid #eee;
            text-align: center;
            padding: 2px 6px;
            border-radius: 2px;
            cursor: pointer;
        }
    }
}
</style>