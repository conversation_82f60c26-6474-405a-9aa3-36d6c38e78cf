const logger = console.log;

// const csTools = cornerstoneTools;
import csTools from '$library/cornerstone/cornerstoneTools'

const { addToolState, clearToolState, getToolState, toolStyle, toolColors, getModule, store } = csTools;
const external = csTools.external;

const BaseTool = csTools.importInternal('base/BaseTool');
// State

// Drawing
const getNewContext = csTools.importInternal('drawing/getNewContext');
const draw = csTools.importInternal('drawing/draw');
const setShadow = csTools.importInternal('drawing/setShadow');
const drawRect = csTools.importInternal('drawing/drawRect');
// const drawJoinedLines = csTools.importInternal('drawing/drawJoinedLines');
// const drawLine = csTools.importInternal('drawing/drawLine');

// Util
// const { calculateEllipseStatistics } = csTools.importInternal('util/ellipseUtils');

const triggerEvent = csTools.importInternal('util/triggerEvent');
const { rectangleRoiCursor } = csTools.importInternal('tools/cursors');




export default class RectangleRoiTool extends BaseTool {
    constructor(props = {}) {
        const defaultProps = {
            name: 'RectangleCrop',
            supportedInteractionTypes: ['Mouse', 'Touch'],
            configuration: {
                color: '#FF0000',
                currentData: null
            },
            svgCursor: rectangleRoiCursor,
        };

        super(props, defaultProps);

        this.postMouseDownCallback = this.mouseDownCallback.bind(this);
        this.postTouchStartCallback = this.mouseDownCallback.bind(this);

        this.mouseDragCallback = this.mouseDragCallback.bind(this);
        this.touchDragCallback = this.mouseDragCallback.bind(this);

        this.touchEndCallback = this.doThings.bind(this);
		this.mouseUpCallback = this.doThings.bind(this);
		this.mouseClickCallback = this.doThings.bind(this);

    }

    addNewMeasurement(evt) {

        evt.stopImmediatePropagation();
        evt.stopPropagation();
        evt.preventDefault();

        // const eventData = evt.detail;
        // const measurementData = this.createNewMeasurement(eventData);
        // const element = evt.detail.element;

        // const enabled = external.cornerstone.getEnabledElement(element)
        // measurementData.modality = enabled.modality

        // addToolState(element, this.name, measurementData);
        this.drawing = true;
    }

    createNewMeasurement(eventData) {
        // const goodEventData =
        //     eventData && eventData.currentPoints && eventData.currentPoints.image;

        // if (!goodEventData) {
        //     return;
        // }
        // return {
        //     element: eventData.element,
        //     startPoints: eventData.currentPoints,
        //     currentPoints: eventData.currentPoints,
        //     initialRotation: eventData.viewport.rotation 
        // };
    }

    mouseDownCallback(evt) {
        const eventData = evt.detail;
        const { element, currentPoints } = eventData;
        const newData = {
            element,
            startPoints: currentPoints,
            currentPoints: currentPoints
        };

        this.currentData = newData

    }

    mouseDragCallback(evt) {
        const eventData = evt.detail;
        const { element } = eventData;

        const newData = {
            element,
            startPoints: this.currentData.startPoints,
            currentPoints: eventData.currentPoints
        };

        this.currentData = newData
        external.cornerstone.updateImage(element);
    }

    renderToolData(evt) {
        this._drawCircle(this.currentData);
    }

    _drawCircle(data) {
        if (!this.drawing) {
            return;
        }
        const { element, currentPoints, startPoints } = data;
        const {
            color
        } = this.configuration;
        const canvasEle = element.querySelector('canvas');
        const newContext = getNewContext(canvasEle);


        draw(newContext, (context) => {
            setShadow(context, this.configuration);

            // let startCanvas = external.cornerstone.pixelToCanvas(
            //   element,
            //   startPoints
            // );
            // const endCanvas = external.cornerstone.pixelToCanvas(
            //   element,
            //   currentPoints
            // );
            let startCanvas = startPoints.canvas;
            const endCanvas = currentPoints.canvas;

            // if (this.circleRoiDiameter) {
            //     startCanvas = {
            //         x: (startCanvas.x + endCanvas.x) / 2,
            //         y: (startCanvas.y + endCanvas.y) / 2
            //     };
            // }
            const rectOptions = { color };


            drawRect(
                context,
                element,
                startCanvas,
                endCanvas,
                rectOptions,
                'canvas',
                data.initialRotation
            );
        });
    }


    doThings(evt) {
        const eventData = evt.detail;
        const { buttons, element } = eventData;
        // if (![1].includes(buttons)) { // customEvent has no buttons
        //     return;
        // }

        this.drawing = false;


        const { currentPoints, startPoints } = this.currentData;

        const canvasEle = element.querySelector('canvas');
        const newContext = getNewContext(canvasEle);

        const startCanvas = startPoints.canvas;
        const endCanvas = currentPoints.canvas;

        const newCanvas = document.createElement('canvas')
        const newCanvasContext = newCanvas.getContext('2d')

        const rect = {
            left: Math.min(startCanvas.x, endCanvas.x) + 1,
            top: Math.min(startCanvas.y, endCanvas.y) + 1,
            width: Math.abs(startCanvas.x - endCanvas.x) - 2,
            height: Math.abs(startCanvas.y - endCanvas.y) - 2,
        };

        if (rect.width < 50) {
            return;
        }


        newCanvas.setAttribute('width', rect.width)
        newCanvas.setAttribute('height', rect.height)
        newCanvasContext.drawImage(canvasEle, rect.left, rect.top, rect.width, rect.height, 0, 0, rect.width, rect.height)


        const dataURLorigin = newCanvas.toDataURL("image/jpeg", 1);

        // const linkEl = document.createElement('a')
        //         linkEl.href = dataURLorigin;
        //         linkEl.download = 'd_' + new Date().toLocaleString() + '.jpg';
        //         linkEl.click();

        const modifiedEventData = {
            toolName: "RectangleCrop",
            element,
            dataURLorigin,
            dataCanvas: newCanvas
        }

        triggerEvent(
            element,
            csTools.EVENTS.MEASUREMENT_COMPLETED,
            modifiedEventData
        );

        csTools.clearToolState(element, this.name);
        external.cornerstone.updateImage(element);

    }



    pointNearTool(element, data, coords, interactionType) {
        return false;
    }

    updateCachedStats(image, element, data) {
        data.invalidated = false;
    }


}

