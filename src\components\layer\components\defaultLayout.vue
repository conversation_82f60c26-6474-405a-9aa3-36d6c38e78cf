<template>
    <el-dialog
        append-to-body
        title="重建默认布局"
        :visible.sync="innerVisible"
        :close-on-click-modal="false"
        @open="openDialog"
        width="740px"
        custom-class="my-dialog">
        <el-table 
            ref="mainTable"
            :data="tableData" 
            border
            highlight-current-row 
            tooltip-effect="dark" style="width: 100%" height="400px">
            <!-- <el-table-column label="位 置" width="60" align="center" prop="position"></el-table-column> -->
            <el-table-column label="名 称" prop="text"></el-table-column>
            <el-table-column v-for="item in selectData" width="70":label="item.name" prop="text" align="center">
                <template slot-scope="scope">
                    <el-radio v-model="radioList[item.key]" :label="scope.row.id">{{""}}</el-radio>
                </template>
            </el-table-column>
        </el-table>
        <div class="c-action">
            <el-button size="mini" @click="onClickSave">保 存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import ModeDialog from "$src/mixin/ModeDialog.js";
import getConfigByStorageKey from '$library/utils/configStorage.js';
import { dbLayoutSet } from '$library/db';
export default {
    mixins: [ ModeDialog ],
    data() {
        return {
            tableData: [],
            selectData: [],
            radioList: {}
        }
    },
    methods: {
        getDefaultLayout() {
            // 获取配置列表
            this.selectData = getConfigByStorageKey('configs-default-layout');

            // 弄成对象结构
            this.selectData.forEach(item => {
                let value = item.value
                if (!value && this.tableData[0]) {
                   value = this.tableData[0].id
                }
                this.$set(this.radioList, item.key, value)
            })

        },
        onClickSave() {

            // 遍历选中的值
            for (const key in this.radioList) {
                if (Object.hasOwnProperty.call(this.radioList, key)) {
                    
                    // 把值放入到数组对象中
                     for (let index = 0; index < this.selectData.length; index++) {
                        const item = this.selectData[index];
                        if (item.key == key) {
                            this.selectData[index].value = this.radioList[key]
                            break
                        }
                    }

                }
            }

            localStorage.setItem('configs-default-layout', JSON.stringify(this.selectData))
            
            // this.$message({
            //     message: '设置成功！',
            //     type: 'success'
            // });
            this.$store.dispatch('saveAllConfig')
            this.innerVisible = false
        },
        initData() {
            this.tableData = getConfigByStorageKey('configs-rebuild-layout').filter(_ => {
                return !_.hidden
            });

            dbLayoutSet.then(e => {
                e.getGroup(1).then(e => {
                    if (e.success) {
                        const layout = e.data.sort((a, b) => {
                            return a.position - b.position;
                        });
                        this.tableData = this.tableData.concat(layout)
                    }
                })
            })
            this.getDefaultLayout()
        },
        openDialog() {
            this.initData()
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .my-dialog{
    .el-radio__label{
        display: none;
    }
}
.c-action{
    display: flex;
    justify-content: end;
    padding-top: 10px;
}
</style>