// 函数队列
class Queue {
    constructor(_options = {}) {
        let options = _options
        if (typeof options !== 'object') options = {}
        this.list = [];
        this.timeInterval = isNaN(+_options.timeInterval) ? 100 : +_options.timeInterval;
        this.isRunning = false
        this.running = null
    }

    push() {
        const func = arguments[0]
        if (typeof func === 'function') {
            const queueUnit = () => new Promise(resolve => {
                try {
                    func([...arguments].slice(1))
                } catch (error) {
                    console.error(error)
                }
                return resolve()

            })
            this.list.push(queueUnit)
        }
    }

    start() {

        if (this.running) return

        const loop = () => {
            if (!this.list.length) {
                this.end()
                return
            }

            if (this.isRunning) return

            const popPromise = this.list.shift()

            this.isRunning = true

            popPromise().finally(() => {
                this.isRunning = false
            })
        }

        loop()

        this.running = setInterval(loop, this.timeInterval);
    }

    end(callback) {
        clearInterval(this.running)
        this.running = null
        callback && callback()
    }
}

const globalQueue = new Queue()

export {
    globalQueue,
    Queue
}