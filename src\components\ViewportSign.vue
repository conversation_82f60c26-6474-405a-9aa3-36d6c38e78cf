<template>
    <div>
        <div class="i-top-center i-overlay"><i class="iconfont iconai69" v-if="iIsPrinted == 1"></i><i v-if="iIsGraphic == 1" class="el-icon-s-claim"></i></div>
    </div>
</template>
<script>
export default {
    props: {
        iIsGraphic: 0,
        iIsPrinted: 0
    }
}
</script>
<style lang="scss" scoped>

.i-overlay{
    display: flex;
    transform: initial !important;
    top: 2px !important;
    i{
        color: #ff2e00;
        font-size: 18px;
        margin-right: 4px;
    }
}

</style>