<template>
    <div
        oncontextmenu="return false"
        @dblclick="onDblclickViewport"
        class="cornerstone-enabled-image viewportWrapper"
        :modality="applyInfo.modality"
    >
        <div v-if="!remark.isCaptrue" noprint="true" class="c-select-shade c-select-shade-top"></div>
        <div v-if="!remark.isCaptrue" noprint="true" class="c-select-shade c-select-shade-right"></div>
        <div v-if="!remark.isCaptrue" noprint="true" class="c-select-shade c-select-shade-bottom"></div>
        <div v-if="!remark.isCaptrue" noprint="true" class="c-select-shade c-select-shade-left"></div>
        <div
            @mousedown="onMouseRight"
            @click.prevent.ctrl="onSelectCurrentCtrl"
            @click.prevent.shift="onSelectCurrentShift"
            :orientation="state.orientation"
            :layoutId="state.layoutId"
            ref="element"
            :class="innnerClass"
            class="viewport viewport-element"
        >
            <canvas class="cornerstone-canvas" ref="canvas" />
            <ViewportOverlayCustom
                ref="overlay"
                v-if="isOverlayVisible"
                :initLoad="initLoad"
                :element="element"
                :imageId="imageId"
                :renderOverlay="renderOverlay"
                :imageIndex="imageIdIndex"
                :stackSize="imageIdsLength"
                :modality="applyInfo.modality"
                :invert="watchViewportAbsolute.invert"
                :thickness="state.thickness"
            />
            <ViewportOrientationMarkers
                :detail="renderMarkers"
                :scaleDetail="scaleOverlay"
                :imageId="imageId"
                :state="state"
                v-if="isOverlayVisible"
                :invert="watchViewportAbsolute.invert"
            />
            <ScaleOverlay
                :detail="scaleOverlay"
                v-if="isOverlayVisible"
                :invert="watchViewportAbsolute.invert"
            />
            <!-- <ViewportMeasurements :visible="isExistTool"></ViewportMeasurements> -->
            <ViewportGroup v-if="contrast" :groupName="groupName" :lock="state.lock"></ViewportGroup>
            <ViewportRemark
                v-if="remark.isShowRemark && remark.isCaptrue"
                :sRemarkCn="remark.sRemarkCn"
            ></ViewportRemark>
            <slot name="inner"></slot>
        </div>
        <div v-if="showAction" class="layer-tools">
            <ItemTools
                :isOverlayVisible="isOverlayVisible"
                :imageIdIndex.sync="imageIdIndex"
                :activeTool="activeTool"
                :imageId="firstImageId"
                :element="element"
                :remark="remark"
                :tabId="tabId"
                :crosshairsTool="crosshairsTool"
                @setCrosshairs="() => { $emit('setCrosshairs') }"
                @onClickIsShowRemark="onClickIsShowRemark"
                @setIsOverlayVisible="setIsOverlayVisible"
                @setActiveTool="setActiveTool"
                @setCrosshairsTool="setCrosshairsTool"
            ></ItemTools>
            <CornerstoneScrollbar1
                v-if="imageIds.length && imageIds.length >= 2"
                :max="scrollbarMax"
                :height="scrollbarHeight"
                :curVal="imageIdIndex"
                @onInputCallback="imageSliderOnInputCallback"
            />
        </div>

        <CoordinateTools
            v-if="!remark.isCaptrue"
            v-show="showCoordinateTools"
            @onOffsetChange="onOffsetChange"
            @resetOffset="resetCoordOffset"
            :offsetX="offsetX"
            :offsetY="offsetY"
            :orientation="state.orientation"
            :disabled="coordinateToolsDisabled"
            noprint="true"
        ></CoordinateTools>

        <RotationTools
            v-if="!remark.isCaptrue"
            ref="RotationTools"
            v-show="showRotationTools"
            :element="element"
            :imageId="imageId"
            :showState="showRotationTools"
            noprint="true"
        ></RotationTools>

        <LoadingIndicator v-if="isLoading" :imageProgress="imageProgress" />
        <slot></slot>
    </div>
</template>
<script>
import { vec3 } from 'gl-matrix';

import CornerstoneScrollbar1 from './CornerstoneScrollbar1'
import ViewportRemark from '$components/ViewportRemark'
import ViewportGroup from './ViewportGroup'
import ViewportOverlayCustom from './ViewportOverlayCustom'
import ViewportOrientationMarkers from './ViewportOrientationMarkers'
import ViewportMeasurements from './ViewportMeasurements'
import ScaleOverlay from './ScaleOverlay'
import LoadingIndicator from './LoadingIndicator'
import ItemTools from './tools/ItemTools.vue'
import CoordinateTools from './tools/CoordinateTools.vue'
import RotationTools from './tools/RotationTools.vue'
import VoiMapping from '$library/cornerstone/function/VoiMapping.js';

import eventBus from '$src/event.js'

import getConfigByStorageKey from '$library/utils/configStorage.js'

export default {
    components: {
        CornerstoneScrollbar1,
        ViewportOverlayCustom,
        ViewportGroup,
        ViewportMeasurements,
        ViewportOrientationMarkers,
        ScaleOverlay,
        LoadingIndicator,
        ItemTools,
        CoordinateTools,
        RotationTools,
        ViewportRemark
    },
    props: {
        show: {                    // 用于 v-show 状态改变监听            
            type: Boolean,
            default: () => { }
        },
        toolType: {
            type: String,
            default: 'read'
        },
        innnerClass: {
            type: String,
            default: ''
        },
        tools: {                    // 工具
            type: Array,
            default: () => {
                return [
                    // Mouse
                    {
                        name: 'Wwwc',
                        mode: 'active',
                        modeOptions: { mouseButtonMask: 1 },
                    },
                    // {
                    //     name: 'Zoom',
                    //     mode: 'active',
                    //     modeOptions: { mouseButtonMask: 2 },
                    // },
                    // {
                    //     name: 'Pan',
                    //     mode: 'active',
                    //     modeOptions: { mouseButtonMask: 4 },
                    // },
                    'Zoom', 'Pan',
                    // 箭头
                    {
                        name: 'ArrowAnnotate',
                        mode: 'passive',
                        props: { configuration: { isPrompt: false } }
                    },
                    // 直线
                    { name: 'Length', mode: 'passive' },
                    { name: 'Bidirectional', mode: 'passive' },
                    // 角度
                    { name: 'Angle', mode: 'passive' },
                    // 椭圆
                    { name: 'EllipticalRoi', mode: 'passive' },
                    // 圆形
                    { name: 'CircleRoi', mode: 'passive' },
                    // 矩形
                    { name: 'RectangleRoi', mode: 'passive' },
                    // 勾画
                    { name: 'FreehandRoi', mode: 'passive' },
                    // 勾画线条
                    { name: 'FreehandLength', mode: 'passive' },
                    // 标注
                    {
                        name: 'TextMarker', mode: 'passive',
                        props: {
                            configuration: {
                                markers: ['标注'],
                                current: '标注',
                                ascending: true,
                                loop: true,
                            }
                        }
                    },
                    {
                        name: 'Magnify',
                        props: {
                            configuration: {
                                magnifySize: 200,     // 放大镜大小
                                magnificationLevel: 3 // 放大镜倍数
                            }
                        }
                    },
                    'Rotate',
                    'Airtools', // 'MprCrosshairs', 
                    // 'ScaleOverlay',
                    // 'CobbAngle', 'Bidirectional', 
                    // 拖拽替换
                    { name: 'Probe', mode: 'passive' },
                    'DragProbe', 'Eraser', 'StackScroll',
                    // Scroll
                    { name: 'StackScrollMouseWheel', mode: 'active', props: { configuration: { loop: getConfigByStorageKey('configs-scrollMouseWheelLoop') } } },
                    // Touch
                    { name: 'PanMultiTouch', mode: 'active' },
                    { name: 'ZoomTouchPinch', mode: 'active' },
                    { name: 'StackScrollMultiTouch', mode: 'active' },
                    // { name: 'ReferenceLines', mode: 'active' },
                    { name: 'EllipticalMeasure', mode: 'passive' },
                    { name: 'LesionArea', mode: 'passive' },
                    { name: 'RectangleCrop', mode: 'passive' },
                ]
            }
        },
        imageIds: {                // 图像
            type: Array,
            default: () => {
                return [

                ]
            }
        },
        layerIds: {   // 第二层图像信息
            type: Array,
            default: () => {
                return [

                ]
            }
        },
        layerConfgs: {
            type: Object,
            default: () => {
                return {
                    opacity: 1,
                    visible: true,
                    viewport: {
                        colormap: 'hot',
                    }
                }
            }
        },
        cornerstoneOptions: {
            type: Object,
            default: () => {
                return {
                }
            }
        },     // cornerstone enable 配置参数
        isStackPrefetchEnabled: {   // 开启预加载
            type: Boolean,
            default: false
        },
        frameRate: 12,
        activeTool: {           // 当前工具
            type: String,
            default: 'Wwwc'
        },
        crosshairsTool: {
            type: Boolean,
            default: false
        },
        activeSelect: {         // 选中当前组件
            type: Boolean,
            default: false
        },
        showAction: {
            type: Boolean,
            default: false
        },
        showCoordinateTools: {
            type: Boolean,
            default: false
        },
        showRotationTools: {
            type: Boolean,
            default: false
        },
        activeScroll: {         // 选中当前才滚动
            type: Boolean,
            default: false
        },
        isOverlayVisible: {     // 是否显示覆盖层（患者信息）
            type: Boolean,
            default: true
        },
        state: {               // 附加参数，存起来，能通过 dom 图层方式获取这个序列中的属性参数
            type: Object,
            default: () => { }
        },
        isLook: {
            type: Boolean,
            default: false
        },
        contrast: {
            type: Boolean,
            default: false
        },
        remark: {
            type: Object,
            default: () => { return {} }
        },
        isCenterIndex: {
            type: Boolean,
            default: false
        },
        tabId: {
            default: 'none',
        }
    },
    data() {
        return {
            groupName: '',
            resizeThrottleMs: 200,
            element: null,
            imageIdIndex: 0,   // 图像下标
            imageId: '',       // 当前图像
            isPlaying: false,  // 是否播放

            // 滚动条参数
            scrollbarMax: 0,
            scrollbarHeight: '20px',

            // 覆盖层
            // overlay: {},            // 新图像的时候获取
            renderOverlay: {
                scale: null,
                windowWidth: null,
                windowCenter: null
            },                      // 改变缩放、窗宽窗位
            renderMarkers: {
                rotationDegrees: 0,
                isFlippedVertically: false,
                isFlippedHorizontally: false
            },       // 图像旋转，翻转

            // 多个图层
            layers: [],

            // 标尺
            scaleOverlay: {
                scale: null,
                columnPixelSpacing: null,
                rowPixelSpacing: null
            },

            //
            timer: null,

            // 用于监听，这些数据的变化...
            watchViewportRelative: {},
            watchViewportAbsolute: {
                invert: false,
                windowWidth: null,
                windowCenter: null,
                hflip: false,
                vflip: false,
                rotation: 0,
                colormap: undefined,
                offsetRotation: 0,
            },
            applyInfo: {},

            loadHandlerTimeout: null,
            isLoading: false,
            imageProgress: 0,  // 加载进度
            timerSelect: null, // 选择节流
            firstImageId: null,
            timerModified: null,
            seriesId: null,     // 当前组件视图序列id(组装过的)
            isExistTool: false, // 当前是否有工具
            isAssignWWc: true,  // 是否设置wwc
            selectToolState: false, // 是否选中工具
            offsetX: 0,
            offsetY: 0,
            coordinateToolsDisabled: false,
            initLoad: false,
            appElement: HTMLCollection,
            initRenderImage: true,  // 初始渲染图像时 true
        }
    },
    watch: {
        imageIds: {
            handler: function (later, before) {
                // 一张图像时候做判断，前后对比一样，不在初始化
                if (later && later.length === 1 && before && before.length === 1) {
                    if (later[0] === before[0]) {
                        return;
                    }
                }
                // 监听图像变化
                this.$nextTick(() => {
                    // 由于用v-if 需要在等待一轮 even loop
                    this.firstImageId = this.imageIds[0]
                    this.$nextTick(() => {
                        if (later.length) {
                            this.isLoading = true;
                            // if (this.isCenterIndex) {
                            //     this.imageIdIndex = Math.ceil(later.length / 2) - 1;
                            // }
                            this.init()
                            this.scrollbarMax = later.length - 1
                            this.scrollbarHeight = this.element
                                ? `${this.element.clientHeight}px`
                                : '100px';
                        } else {
                            // if (this.element){
                            //     cornerstone.reset(this.element)
                            // }
                        }
                    })
                })
            },
            deep: true,
            immediate: true
        },
        watchViewportAbsolute: {
            handler() {
                this.syncCallBack('absolute')
            },
            deep: true
        },
        activeTool: {
            handler(later, before) {
                // 监听工具变化
                // later !== before && 
                if (later !== 'Probe') {
                    this.trySetActiveTool();
                }
            },
            deep: true,
            immediate: true
        },
        show(later) {
            if (later) {
                // 监听 v-show 显示
                this.$nextTick(() => {
                    if (this.$refs.canvas.clientHeight == 0) {
                        // canvas 在隐藏后，调整浏览器窗口后，在显示（v-show="true"）会看不见，重新渲染 canvas
                        const myEvent = new Event('resize');
                        window.dispatchEvent(myEvent);
                    }
                })
            }
        },
        disabledTool: {
            handler() {
                // this.trySetDisabledTool();
                this.setAirtools();
            },
            deep: true
        },
        activeSelect: {
            handler() {
                // if (this.activeScroll){
                //     console.log('scroll')
                //     // 选中当前
                //     if (this.activeSelect){
                //         cornerstoneTools.setToolActiveForElement(this.element, 'StackScrollMouseWheel', {})
                //     }else {
                //         // 没有选中
                //         cornerstoneTools.setToolDisabledForElement(this.element, 'StackScrollMouseWheel', {})
                //     }

                // }
            },
            immediate: true
        },
        mouseEvents: {
            handler() {
                this.setMidRightTools();
            },
            deep: true,
            immediate: true
        },
        'listenTool.value': {
            handler() {
                // 工具发生变化，如果序列一样，就更新当前 ui 图像状态
                if (this.listenTool.seriesId === this.seriesId) {
                    this.isExistTool = cornerstoneTools.imageIdStateManager.isExistTool(this.seriesId)
                    cornerstone.updateImage(this.element)
                }
            }
        }
    },
    computed: {
        toolNewImageUse() {
            return this.$store.state.toolNewImageUse
        },
        listenTool() {
            return this.$store.state.listenTool
        },
        isFuse() {
            return this.layerIds.length
        },
        disabledTool() {
            return this.$store.state.disabledTool;
        },
        mouseEvents() {
            // vuex 中的 鼠标工具
            return this.$store.state.mouseEvents;
        },
        toolRepeatUse() {
            return this.$store.state.toolRepeatUse;
        },
        toolSync() {
            return this.$store.state.toolSync;
        },
        imageIdsLength() {
            return this.imageIds.length;
        },
        showRebuild() {
            return this.$store.state.showRebuild
        }
    },
    mounted() {
        this.appElement = document.getElementsByTagName('html')[0];
        // 当前图像 dom 元素
        this.$nextTick(() => {
            this.element = this.$refs.element;
            cornerstone.enable(this.element, this.cornerstoneOptions);
            // cornerstone.enable(this.element, {...this.cornerstoneOptions, renderer: 'webgl'});
            this.getElementModality()
            // 元素启用 enable 事件
            // this.handleOnElementEnabledEvent()
            if (this.isLook) {
                return;
            }
            // 添加工具事件
            this.addInitialToolsForElement()
            // 绑定元素事件
            this.bindInternalElementEventListeners();
            // 绑定 cornerstone 事件
            this.bindInternalCornerstoneEventListeners();

            // 通过布局 id 读取到组的名称
            if (this.state.layoutId) {
                let [group] = this.state.layoutId.split(',')
                let newGroup = parseInt(group)
                if (!isNaN(newGroup)) {
                    newGroup += 1
                    this.groupName = newGroup
                } else {
                    this.groupName = group
                }
            } else {
                this.groupName = ''
            }
        })

        eventBus.$on('changeLesionArea', this.changeLesionArea)
        eventBus.$on('windowfocus', (i) => {
            if (!this.imageIds.length) {
                return
            }
            // 切换到别的页面再切回来时视窗变全黑
            this.element && cornerstone.updateImage(this.element)
        })
    },
    beforeDestroy() {
        // 销毁工具事件
        // this.addInitialToolsForElement(true)
        // // 销毁元素事件
        this.bindInternalElementEventListeners(true);
        // // 销毁 cornerstone 事件
        this.bindInternalCornerstoneEventListeners(true);

        // if (this.isStackPrefetchEnabled) {
        //     this.enableStackPrefetching(true)
        // }
        cornerstoneTools.clearToolState(this.element, 'stackPrefetch');
        cornerstoneTools.stopClip(this.element);

        cornerstone.disable(this.element);

        this.element = null

        eventBus.$off('changeLesionArea', this.changeLesionArea)

    },
    methods: {
        async init() {
            this.initLoad = true
            this.setupLoadHandlers();
            try {
                // 拖拽后还原到第一个
                // if (cornerstone.getEnabledElement(this.element).image){
                //     this.imageIdIndex = 0
                // }
                this.imageIdIndex = this.state.imgIndex || 0
                const imageId = this.imageIds[this.imageIdIndex] || this.imageIds[0]
                let imageIds = [...this.imageIds];
                // 一张 dicom, 多帧 dicom 展开
                const protocol = window.configs.protocol + '//';
                if (imageIds.length === 1 && !imageIds[0].includes('?frame=') && !imageIds[0].includes(protocol)) {
                    let url = imageIds[0].slice(9)
                    cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.load(url, cornerstoneWADOImageLoader.internal.xhrRequest).then((dataSet) => {
                        let numFrames = dataSet.intString('x00280008');
                        // 截屏图，也是多帧图像！！！
                        if (numFrames && numFrames > 1) {
                            imageIds.splice(0)
                            for (let i = 0; i < numFrames; i++) {
                                let imageId = 'dicomweb:' + url + "?frame=" + i;
                                imageIds.push(imageId);
                            }
                            this.$emit('update:imageIds', imageIds);
                        }
                    }).catch(() => {
                        // console.log()
                    })
                }

                // 设置 图像堆、播放、参考线
                if (cornerstone.getLayers(this.element)) {
                    cornerstone.purgeLayers(this.element)
                }
                cornerstoneTools.clearToolState(this.element, 'stack', 'stackRenderer');
                cornerstoneTools.addStackStateManager(this.element, [
                    'stack',
                    'playClip',
                    'crosshairs',
                    'NuclearCrosshairs'
                    // 'referenceLines',
                ]);
                // 获取序列是否绘制工具状态
                this.seriesId = this.getSliceSeriesId(imageIds[0])
                this.isExistTool = cornerstoneTools.imageIdStateManager.isExistTool(this.seriesId)
                // 图像堆（第一层，PT | NM）
                let curStack = {
                    imageIds: imageIds,
                    currentImageIdIndex: this.imageIdIndex,
                    state: Object.assign(this.state, {
                        viewportType: this.getCodeViewportModality(),
                        seriesId: this.seriesId,        // 序列id 不同序列切面，id就不一样 CT/PT 序列不一样。 ctz 跟 ctx 不一样
                        rebuildId: this.state.seriesId, // 重建id，相同重建组 id 一样  CT/PT/FUSE id 一样 TODO 没有用了？
                    }),
                };
                if (this.isFuse) {
                    curStack.options = this.layerConfgs

                    curStack.options.viewport.colormap = this.showRebuild.colormap

                    curStack.isFuse = true

                    // 用state里加入的参数来初始化scale 放大倍率，解决偏移之后scale重置问题
                    const scaleParams = this.state.scaleParams || {}
                    Object.assign(curStack.options.viewport, scaleParams, scaleParams.translation || {})
                }




                let defaultViewport = this.state.defaultViewport?.current;


                if (defaultViewport && Object.keys(defaultViewport).length) {
                    // curStack = 融合pt
                    if (!curStack.options) {
                        curStack.options = {}
                    }
                    if (!curStack.options.viewport) {
                        curStack.options.viewport = {};
                    }
                    if (!curStack.options.viewport.translation) {
                        curStack.options.viewport.translation = { x: 0, y: 0 }
                    }

                    if (defaultViewport.windowWidth != undefined && defaultViewport.windowWidth != null) {
                        // 如果有 voi 又没有窗宽窗位，cornerstone会自动赋窗宽窗位的，不能直接设置 voi
                        if (!curStack.options.viewport.voi) {
                            curStack.options.viewport.voi = {}
                        }
                        curStack.options.viewport.voi.windowWidth = defaultViewport.windowWidth;
                    }
                    if (defaultViewport.windowCenter != undefined && defaultViewport.windowCenter != null) {
                        // 如果有 voi 又没有窗宽窗位，cornerstone会自动赋窗宽窗位的
                        if (!curStack.options.viewport.voi) {
                            curStack.options.viewport.voi = {}
                        }
                        curStack.options.viewport.voi.windowCenter = defaultViewport.windowCenter;
                    }

                    if (defaultViewport.hflip != undefined && defaultViewport.hflip != null) {
                        curStack.options.viewport.hflip = defaultViewport.hflip;
                    }
                    if (defaultViewport.vflip != undefined && defaultViewport.vflip != null) {
                        curStack.options.viewport.vflip = defaultViewport.vflip;
                    }
                    if (defaultViewport.rotation != undefined && defaultViewport.rotation != null) {
                        curStack.options.viewport.rotation = defaultViewport.rotation;
                    }

                    if (defaultViewport.scale != undefined && defaultViewport.scale != null) {

                        curStack.options.viewport.scale = this.getRatioRescale(defaultViewport)
                    }

                    if (defaultViewport.x != undefined && defaultViewport.x != null) {
                        curStack.options.viewport.translation.x = defaultViewport.x;
                    }
                    if (defaultViewport.y != undefined && defaultViewport.y != null) {
                        curStack.options.viewport.translation.y = defaultViewport.y;
                    }
                }
                // 获取融合默认 viewport
                const fuseDefaultViewport = this.state.defaultViewport?.fuse;
                if (fuseDefaultViewport) {
                    // 赋值默认融合伪彩
                    const fuseColor = fuseDefaultViewport.colormap
                    if ((fuseColor && typeof fuseColor === 'string') || (fuseColor && Object.keys(fuseColor).length)) {
                        if (!curStack.options) {
                            curStack.options = {}
                        }
                        if (!curStack.options.viewport) {
                            curStack.options.viewport = {};
                        }
                        curStack.options.viewport.colormap = fuseColor;
                    }
                }


                // 第一层
                cornerstoneTools.addToolState(this.element, 'stack', curStack);

                // 加载第一张图像
                
                const image = await cornerstone.loadAndCacheImage(imageId);
                if (defaultViewport) {
                    if (!defaultViewport.windowWidth && !defaultViewport.windowCenter) {
                        // console.log('!defaultViewport.windowWidth')
                        this.isAssignWWc = true // 没改过窗宽
                    } else if (defaultViewport.windowWidth && defaultViewport.windowWidth == image.windowWidth && defaultViewport.windowCenter && defaultViewport.windowCenter == image.windowCenter) {
                        // console.log('initWW')
                        this.isAssignWWc = true // 没改过窗宽
                    } else {
                        this.isAssignWWc = false // 改过窗宽
                    }
                }

                requestAnimationFrame(() => {

                    // 设置 viewport 默认值
                    let defViewport = cornerstone.getDefaultViewport(this.element, image);

                    // 显示
                    cornerstone.displayImage(this.element, image, defViewport);

                    // 往上放一个位置不知道会不会产生什么问题？？？
                    cornerstone.reset(this.element);

                    // 设置 PT 窗位 （调整suv参数）
                    this.setDefaultWwwc(image, imageId);


                    // 是否有融合图层序列，添加融合层（第二层，CT,MR）
                    if (this.isFuse) {

                        // 获取默认窗宽窗位
                        let defaultViewport = this.state.defaultViewport?.layer;
                        let viewport = {};

                        // if (windowWidth && windowWidth != 500) {
                        //     viewport.voi = {
                        //         windowCenter,
                        //         windowWidth
                        //     }
                        // }

                        if (defaultViewport && Object.keys(defaultViewport).length) {

                            if (defaultViewport.windowWidth != undefined && defaultViewport.windowWidth != null) {
                                // 有 voi 的时候，又没有窗宽、窗位值，cornerstone会默认设置值
                                if (!viewport.voi) {
                                    viewport.voi = {};
                                }
                                viewport.voi.windowWidth = defaultViewport.windowWidth;
                            }
                            if (defaultViewport.windowCenter != undefined && defaultViewport.windowCenter != null) {
                                // 有 voi 的时候，又没有窗宽、窗位值，cornerstone会默认设置值
                                if (!viewport.voi) {
                                    viewport.voi = {};
                                }
                                viewport.voi.windowCenter = defaultViewport.windowCenter;
                            }
                            if (defaultViewport.colormap != undefined && defaultViewport.colormap != null) {
                                viewport.colormap = defaultViewport.colormap;
                            }
                        }

                        let renderer = new cornerstoneTools.stackRenderers.FusionRenderer();
                        renderer.findImageFn = (imageIds, targetImageId) => {
                            let allStack = cornerstoneTools.getToolState(this.element, 'stack');
                            const source = allStack.data[0].imageIds
                            return this.getClosestImg(source, imageIds, targetImageId).imageId
                        };
                        const stack = Object.assign({},
                            { imageIds: this.layerIds },
                            {
                                currentImageIdIndex:
                                    this.getClosestImg(
                                        imageIds,
                                        this.layerIds,
                                        imageIds[this.imageIdIndex]
                                    ).imgIndex,
                                options: {
                                    opacity: 0.5,
                                    visible: true,
                                    viewport,
                                    isCtLayer: true
                                }
                            }
                        );
                        renderer.currentImageIdIndex = stack.currentImageIdIndex

                        cornerstoneTools.addToolState(this.element, 'stack', stack)
                        cornerstoneTools.addToolState(this.element, 'stackRenderer', renderer);
                        renderer.render(this.element);

                        // 设置当前活动层
                        // this.setActiveLayer()
                        // const enabledElement = cornerstone.getEnabledElement(this.element);
                        // enabledElement.syncViewports = false;
                    }


                    // 是否预取
                    if (this.isStackPrefetchEnabled) this.enableStackPrefetching();

                    // 是否播放
                    if (this.isPlaying) {
                        const validFrameRate = Math.max(this.frameRate, 1);
                        cornerstoneTools.playClip(this.element, validFrameRate);
                    }
                    if (!this.isFuse) {
                        let defaultViewport = this.state.defaultViewport?.current;

                        let viewport = cornerstone.getViewport(this.element);
                        if (defaultViewport && Object.keys(defaultViewport).length) {
                            if (defaultViewport.windowWidth != undefined && defaultViewport.windowWidth != null) {
                                viewport.voi.windowWidth = defaultViewport.windowWidth;
                            }
                            if (defaultViewport.windowCenter != undefined && defaultViewport.windowCenter != null) {
                                viewport.voi.windowCenter = defaultViewport.windowCenter;
                            }


                            if (defaultViewport.hflip != undefined && defaultViewport.hflip != null) {
                                viewport.hflip = defaultViewport.hflip;
                            }
                            if (defaultViewport.vflip != undefined && defaultViewport.vflip != null) {
                                viewport.vflip = defaultViewport.vflip;
                            }
                            if (defaultViewport.rotation != undefined && defaultViewport.rotation != null) {
                                viewport.rotation = defaultViewport.rotation;
                            }

                            if (defaultViewport.scale != undefined && defaultViewport.scale != null) {

                                viewport.scale = this.getRatioRescale(defaultViewport)
                            }
                            if (defaultViewport.x != undefined && defaultViewport.x != null) {
                                viewport.translation.x = defaultViewport.x;
                            }
                            if (defaultViewport.y != undefined && defaultViewport.y != null) {
                                viewport.translation.y = defaultViewport.y;
                            }
                            if (defaultViewport.colormap != undefined && defaultViewport.colormap != null) {
                                viewport.colormap = defaultViewport.colormap;
                            }
                            if (defaultViewport.invert !== undefined && defaultViewport.invert !== null) {
                                viewport.invert = !!defaultViewport.invert;
                            }


                            cornerstone.setViewport(this.element, viewport);
                        }
                    }

                    // 用state里加入的参数来初始化scale 放大倍率，解决偏移之后scale重置问题
                    let vsp = cornerstone.getViewport(this.element);

                    if (this.state.scaleParams) {
                        Object.assign(vsp, this.state.scaleParams)
                    }

                    cornerstone.setViewport(this.element, vsp);

                    // 有方案的时候，不在设置反色，按照方案走
                    if (!defaultViewport || defaultViewport.invert === undefined) {
                        // 设置PT 默认反片
                        this.setPTDefaultInvert(image);
                    }
                    // 切换布局，会重置所以注释，不知道会有什么影响
                    // this.onResize()
                    this.isLoading = false;

                    // 获取微调偏移值
                    this.changeCoordinateOffsetInput()
                    // 初始加载结束
                    this.initLoad = false

                })

            } catch (error) {
                console.log({
                    info: '图像加载失败',
                    data: error
                })
                console.error(error)

                this.initLoad = false
                this.isLoading = false
            }
        },
        getClosestImg(sourceImageIds, targetImageIds, targetImageId) {

            // 第一层
            let sourceImageIdsLen = sourceImageIds.length;
            let sourceImageIdsIdx = sourceImageIds.indexOf(targetImageId);

            // 第二层
            let targetImageIdsLen = targetImageIds.length;

            // 获取第二层，按照图像序列比例获取的下标
            // let scaleValue = targetStackLen / sourceStackLen;
            // let targetNewIdx = Math.round(sourceStackIdx * scaleValue) - 1 <= 0 ? 0 : Math.round(sourceStackIdx * scaleValue) - 1

            let targetNewIdx = this.$fun.getIndexBySeriesNumber(sourceImageIdsIdx, sourceImageIdsLen, targetImageIdsLen)
            // 返回响应的改变
            let closest = targetImageIds[targetNewIdx] || targetImageIds[0]
            return { imageId: closest, imgIndex: targetImageIds[targetNewIdx] ? targetNewIdx : 0 };
        },
        // 开启预取
        enableStackPrefetching(clear = false) {
            cornerstoneTools.stackPrefetch.setConfiguration({
                maxImagesToPrefetch: Infinity,
                preserveExistingPool: false,
                maxSimultaneousRequests: 6,
            });
            if (clear) {
                cornerstoneTools.stackPrefetch.disable(this.element);
            } else {
                cornerstoneTools.stackPrefetch.enable(this.element);
            }
        },
        /**
         * 设置鼠标右键、滚轮按下默认工具
         */
        setMidRightTools() {
            setTimeout(() => {
                // 设置右键
                cornerstoneTools.setToolActiveForElement(this.element, this.mouseEvents.right, { mouseButtonMask: 2 });
                // 设置滚轮按下
                cornerstoneTools.setToolActiveForElement(this.element, this.mouseEvents.mid, { mouseButtonMask: 4 });
            }, 0);
        },
        // 添加工具
        addInitialToolsForElement() {
            const AVAILABLE_TOOL_MODES = ['active', 'passive', 'enabled', 'disabled'];
            const TOOL_MODE_FUNCTIONS = {
                active: cornerstoneTools.setToolActiveForElement,
                passive: cornerstoneTools.setToolPassiveForElement,
                enabled: cornerstoneTools.setToolEnabledForElement,
                disabled: cornerstoneTools.setToolDisabledForElement,
            };

            for (let index = 0; index < this.tools.length; index++) {
                const tool = typeof this.tools[index] === 'string'
                    ? { name: this.tools[index] }
                    : Object.assign({}, this.tools[index]);

                // 如果是定位线，传入的参数不是切片模块，就跳过添加定位线
                // if (tool.name === 'MprCrosshairs' && this.toolType !== 'slice'){
                //     continue;
                // }

                const toolName = `${tool.name}Tool`;
                tool.toolClass = tool.toolClass || cornerstoneTools[toolName];

                if (!tool.toolClass) {
                    console.warn(`Unable to add tool with name '${tool.name}'.`);
                    continue;
                }

                cornerstoneTools.addToolForElement(
                    this.element,
                    tool.toolClass,
                    tool.props || {}
                );

                const hasInitialMode = tool.mode && AVAILABLE_TOOL_MODES.includes(tool.mode);

                if (hasInitialMode) {
                    // TODO: We may need to check `tool.props` and the tool class's prototype
                    // to determine the name it registered with cornerstone. `tool.name` is not
                    // reliable.
                    const setToolModeFn = TOOL_MODE_FUNCTIONS[tool.mode];
                    setToolModeFn(this.element, tool.name, tool.modeOptions || {});
                }
            }
            this.trySetActiveTool()
        },
        /**
         * 双击全屏
         */
        onDblclickViewport() {
            this.$emit('onDblclickViewport', this.element)
        },
        getElementModality() {
            const enabledElement = cornerstone.getEnabledElement(this.element);
            if (!enabledElement) return
            enabledElement.modality = this.isFuse ? 'FUSE' : this.state.sModality
        },
        setupLoadHandlers(clear = false) {
            if (clear) {
                cornerstoneTools.loadHandlerManager.removeHandlers(this.element);
                return;
            }

            // We use this to "flip" `isLoading` to true, if our startLoading request
            // takes longer than our "loadIndicatorDelay"
            const startLoadHandler = element => {
                clearTimeout(this.loadHandlerTimeout);

                // Call user defined loadHandler
                // if (this.startLoadHandler) {
                //     this.startLoadHandler(element);
                // }

                // We're taking too long. Indicate that we're "Loading".
                this.loadHandlerTimeout = setTimeout(() => {
                    this.isLoading = true
                }, 45);
            };

            const endLoadHandler = (element, image) => {
                clearTimeout(this.loadHandlerTimeout);

                // Call user defined loadHandler
                // if (this.endLoadHandler) {
                //     this.endLoadHandler(element, image);
                // }

                if (this.isLoading) {
                    this.isLoading = false
                }
            };

            const errLoadHandler = (element, image) => {
                clearTimeout(this.loadHandlerTimeout);
                this.isLoading = false
            };

            cornerstoneTools.loadHandlerManager.setStartLoadHandler(startLoadHandler, this.element);
            cornerstoneTools.loadHandlerManager.setEndLoadHandler(endLoadHandler, this.element);
            cornerstoneTools.loadHandlerManager.setErrorLoadingHandler(errLoadHandler, this.element);

        },
        // 关闭工具
        trySetDisabledTool() {
            if (!this.element || !this.activeTool) {
                return;
            }
            const toolsStatus = cornerstoneTools.getToolForElement(this.element, this.activeTool)
            if (!toolsStatus || toolsStatus.mode === 'disabled') {
                setTimeout(() => {
                    this.$emit('update:activeTool', 'Airtools')
                }, 0);
            } else {
                cornerstoneTools.setToolDisabledForElement(this.element, this.activeTool, {
                    mouseButtonMask: 1,
                })
                setTimeout(() => {
                    this.$emit('update:activeTool', 'Airtools')
                }, 0);
            }

        },
        // 设置工具
        trySetActiveTool() {
            // TODO (this.activeTool === 'MprCrosshairs' && this.toolType !== 'slice') 这个条件是否过时了
            if (
                !this.element
                || !this.activeTool
                // || (this.activeTool === 'MprCrosshairs' && this.toolType !== 'slice')
            ) {
                return;
            }

            // 拖拽用 Probe 工具做了替代启停效果
            // if (this.activeTool === 'drag') {
            //     cornerstoneTools.setToolActiveForElement(this.element, 'Probe', {
            //         mouseButtonMask: 1,
            //     });
            //     cornerstoneTools.setToolEnabledForElement(this.element, 'Probe', {
            //         mouseButtonMask: 1,
            //     });
            //     return;
            // }

            const validTools = cornerstoneTools.store.state.tools.filter(
                tool => tool.element === this.element
            );
            const validToolNames = validTools.map(tool => tool.name);

            if (!validToolNames.includes(this.activeTool)) {
                return
                // console.warn(
                // `Trying to set a tool active that is not "added". Available tools include: ${validToolNames.join(
                //     ', '
                // )}`
                // );
            }
            // 如果工具是定位线时，跳到选择，告诉父组件，需要启动定位线
            if (this.crosshairsTool && this.activeTool === 'Airtools') {
                this.$emit('setCrosshairs')
                return
            }

            // console.log(this.activeTool)
            cornerstoneTools.setToolActiveForElement(this.element, this.activeTool, {
                mouseButtonMask: 1,
            });
        },
        // 滚动回调，切换图像
        imageSliderOnInputCallback(value) {
            if (!this.element) return;
            // this.setViewportActive(); 滚动选择
            cornerstoneTools.scrollToIndex(this.element, value)
        },
        handleOnElementEnabledEvent(clear = false) {
            // const addOrRemoveEventListener = clear
            // ? 'removeEventListener'
            // : 'addEventListener';

            const handler = evt => {
                const elementThatWasEnabled = evt.detail.element;
                if (elementThatWasEnabled === this.element) {
                    this.$emit('onElementEnabled', evt)
                }
            };
            if (!clear) {
                cornerstone.events.addEventListener(
                    cornerstone.EVENTS.ELEMENT_ENABLED,
                    handler
                );
            }

            // Stop Listening
            if (clear) {
                cornerstone.events.removeEventListener(
                    cornerstone.EVENTS.ELEMENT_ENABLED,
                    handler
                );
            }
        },
        // 内部事件
        bindInternalCornerstoneEventListeners(clear = false) {
            const addOrRemoveEventListener = clear
                ? 'removeEventListener'
                : 'addEventListener';

            // Update image load progress
            cornerstone.events[addOrRemoveEventListener](
                'cornerstoneimageloadprogress',
                this.onImageProgress
            );

            // Update number of images loaded
            cornerstone.events[addOrRemoveEventListener](
                cornerstone.EVENTS.IMAGE_LOADED,
                this.onImageLoaded
            );
        },
        // 绑定事件
        bindInternalElementEventListeners(clear = false) {
            const addOrRemoveEventListener = clear
                ? 'removeEventListener'
                : 'addEventListener';

            // 图像改变事件
            this.element[addOrRemoveEventListener](
                cornerstone.EVENTS.NEW_IMAGE,
                this.onNewImage
            );
            // 图像渲染
            this.element[addOrRemoveEventListener](
                cornerstone.EVENTS.IMAGE_RENDERED,
                this.onImageRendered
            );
            // 设置选中的视图
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.MOUSE_CLICK,
                this.setViewportActive
            );
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.SELECT_CLICK,
                this.clickSelectTools
            );
            // 测量工具-绘制完成
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.MEASUREMENT_COMPLETED,
                this.toolRenderCompleted
            );
            // 测量工具-更新
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.MEASUREMENT_MODIFIED,
                this.toolRenderModified
            );
            // 测量工具-移除
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.MEASUREMENT_REMOVED,
                this.toolRenderRemoved
            );
            // // 测量工具-添加
            // this.element[addOrRemoveEventListener](
            //     cornerstoneTools.EVENTS.MEASUREMENT_ADDED,
            //     (e) => {
            //         console.log(e)
            //     }
            // );
            // 鼠标松开
            this.element[addOrRemoveEventListener](
                'mouseup',
                this.setViewportActiveCallBack
            );
            // this.element[addOrRemoveEventListener](
            //     'mousemove',
            //     (event) => {
            //         const pixelCoords = cornerstone.pageToPixel(this.element, event.pageX, event.pageY);
            //         console.log("pixelX=" + pixelCoords.x + ", pixelY=" + pixelCoords.y)
            //         console.log(cornerstone.getPixels(this.element, pixelCoords.x, pixelCoords.y, 1, 1))
            //     }
            // );
            // this.element[addOrRemoveEventListener](
            //     cornerstoneTools.EVENTS.MOUSE_DOWN,
            //     this.setViewportActiveCallBack
            // );
            // this.element[addOrRemoveEventListener](
            //     cornerstoneTools.EVENTS.TOUCH_PRESS,
            //     this.setViewportActive
            // );
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.TOUCH_END,
                this.setViewportActiveCallBack
            );
            this.element[addOrRemoveEventListener](
                cornerstoneTools.EVENTS.TOUCH_START,
                this.setViewportActive
            );
            // this.element[addOrRemoveEventListener](
            //     cornerstoneTools.EVENTS.STACK_SCROLL,
            //     this.setViewportActive
            // );
        },
        // 工具渲染完成
        toolRenderCompleted(event) {
            // 不等于体测量，添加是否翻页使用属性
            if (event.detail.toolName !== 'LesionArea' && event.detail.measurementData) {
                event.detail.measurementData.toolNewImageUse = this.toolNewImageUse;
            }
            // 图像裁剪保存工具 特殊处理
            if (event.detail.toolName === 'RectangleCrop') {
                this.rectangleCropComplete(event)
                return;
            }
            // 病灶测量 特殊处理
            if (event.detail.toolName === 'LesionArea') {
                this.lesionAreaDrawComplete(event)
                return;
            }
            // 勾画工具绘制完成后，修改还会进入渲染完成事件。单独判断跳过是否是修改
            if ((event.detail.toolName === 'FreehandRoi' || event.detail.toolName === 'FreehandLength') && event.detail.measurementData.connectId) {
                return;
            }

            // 改变 imageIdStateManager 工具信息
            this.changeToolState('add', { toolData: event.detail.measurementData, toolName: event.detail.toolName })

            // 添加同组工具
            if (!event.detail.notRenderGroup) {
                this.triggerToolGroupRender(event.detail.measurementData, event.detail.toolName, 'add')
            }

            // 向父组件提交事件
            this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'completed')

            // 调用设置空工具
            this.setAirtools(true);
            // 改变序列有工具就显示出来的 ui 图标
            this.changeToolUI();

            if (event.detail.toolName === 'FreehandRoi') {
                setTimeout(() => {
                    this.freehandToolCalculte()
                }, 600);
            }
        },
        // 工具更新
        toolRenderModified(event) {
            // 更新的时候隐藏鼠标
            // if (this.appElement.style.cursor != 'none') {
            //     this.appElement.style.setProperty('cursor', 'none', 'important');
            // }
            // if (!document.oncontextmenu) {
            //     this.appElement.style.cursor = ''; // 有时候不会触发抬起事件
            //     document.oncontextmenu = () => {
            //         this.appElement.style.cursor = '';
            //         document.oncontextmenu = null;
            //     }
            // }
            // if (!document.onmouseup) {
            //     this.appElement.style.cursor = ''; // 有时候不会触发抬起事件
            //     document.onmouseup = () => {
            //         this.appElement.style.cursor = '';
            //         document.onmouseup = null;
            //     }
            // }
            // 拖拽点测量不会保存
            if (!event.detail.measurementData) {
                return;
            }
            // console.log('工具更新')

            // 病灶测量 特殊处理
            if (event.detail.toolName === 'LesionArea') {
                this.lesionAreaUpdate(event)
                this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'modified');
                this.triggerLesionAreaGroupRender(event.detail.measurementData, 'modified' ) //  多序列同步

                return
            }

            // 节流
            clearTimeout(this.timerModified);
            this.timerModified = setTimeout(() => {
                // 改变工具列表的测量值更新
                this.changeToolState('modified', { toolData: event.detail.measurementData, toolName: event.detail.toolName })

                this.triggerToolGroupRender(event.detail.measurementData, event.detail.toolName, 'modified');

                this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'modifiedAndState')

            }, 0);
        },
        // 工具移除
        toolRenderRemoved(event) {
            // 病灶测量 特殊处理
            if (event.detail.toolName === 'LesionArea') {
                this.lesionAreaRemove(event)
                return;
            }

            if (event.detail && event.detail.element) {
                // 移除 imageIdStateManager 状态中的工具
                this.changeToolState('del', { toolData: event.detail.measurementData, toolName: event.detail.toolName })
                // 作用是刷新视图跟渲染工具list
                this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'removed')
            }
            // 触发其它组移除（非 cornerstoneTool 触发（自己代码调用触发移除））
            if (event.detail && (event.detail.element || event.detail.listRemoved)) {
                this.triggerToolGroupRender(event.detail.measurementData, event.detail.toolName, 'removed');
            }

            // 勾画工具 触发再次计算
            if (event.detail.toolName === 'FreehandRoi') {
                this.freehandToolCalculte()
                return;
            }
            this.changeToolUI()
        },
        /**
        / 改变 imageIdStateManager 存储工具状态
        **/
        changeToolState(status, data) {
            const seriesId = this.seriesId
            if (status !== 'del') {
                if (!this.toolSync) { // 关闭/打开测量工具同步时
                    data.toolData.modality = this.isFuse ? 'FUSE' : this.state.sModality
                } else {
                    data.toolData.modality = null
                }
            }
            switch (status) {
                case 'add':
                    cornerstoneTools.imageIdStateManager.addToolState(this.state.patientId, seriesId, this.imageId, data.toolName, data.toolData)
                    break;
                case 'del':
                    cornerstoneTools.imageIdStateManager.delToolState(seriesId, this.imageId, data.toolName, data.toolData.uuid)
                    break;
                case 'modified':
                    cornerstoneTools.imageIdStateManager.updateToolState(seriesId, this.imageId, data.toolName, data.toolData)
                    break;
                default:
                    break;
            }
        },
        /**
        / 截图裁剪保存
        **/
        rectangleCropComplete(event) {
            this.$emit('onSaveCropImage', event.detail)
            this.setAirtools(true);
        },
        // 渲染 ui 图标
        changeToolUI() {
            // this.listenTool.value = !this.listenTool.value
            // this.listenTool.seriesId = this.seriesId
            // this.isExistTool = cornerstoneTools.imageIdStateManager.isExistTool(this.seriesId)
        },
        // 设置返回
        triggerToolGroupRender(measurementData, toolName, status) {
            if (!this.toolSync) { // 关闭测量工具同步
                return
            }

            if (status === 'add') {
                measurementData.connectId = this.$fun.onlyValue();
            }

            const data = {
                sModality: this.state.sModality,
                seriesId: this.state.seriesId,
                orientation: this.state.orientation,
                toolName,
                measurementData,
                imageIdIndex: this.imageIdIndex,
                element: this.element,
                layoutId: this.state.layoutId,
                status
            }
            // 在 viewSlice.vue 中
            this.$emit('triggerToolGroupRender', data)

        },
        triggerLesionAreaGroupRender(measurementData,  status) {
            if (!this.toolSync) { // 关闭测量工具同步
                return
            }

            const data = {
                sModality: this.state.sModality,
                seriesId: this.state.seriesId,
                orientation: this.state.orientation,
                toolName: 'LesionArea',
                measurementData,
                imageIdIndex: measurementData.imageIdIndex,
                element: this.element,
                status
            }
            // 在 viewSlice.vue 中
            this.$emit('triggerLesionAreaGroupRender', data)
        },
        triggerLesionAreaSTAT( ) {  
            // 在 viewSlice.vue 中
            this.$emit('triggerLesionAreaSTAT')
        },
        /**
         * isCheckRun 是否检查退出不执行
         */
        setAirtools(isCheckRun = false) {
            // 工具渲染完成，工具是否连续使用
            if (isCheckRun && this.toolRepeatUse) return;

            setTimeout(() => {
                this.$emit('update:activeTool', 'Airtools')
            }, 0);
        },
        // 调整阈值时动态改变肿瘤区域，受eventbus触发，需要筛选出一个指定目标，不能重复触发
        changeLesionArea(toolData, suvThresValue) {
            // const imageId = this.imageIds[this.imageIdIndex]
            if (this.activeSelect) { // 只在选定中的窗口触发
                // 首先把目前已有的测量数据删除 同步删除对应的CT图像中的测量数据
                this.lesionAreaRemove({
                    detail: {
                        measurementData: toolData
                    }
                })
                setTimeout(() => {
                    
                    const newToolData =  { ...toolData }
                    // 触发EllipticalMeasure里面的重新测量方法
                    const toolObj = cornerstoneTools.getToolForElement(this.element, 'EllipticalMeasure')
                    if (toolObj && toolObj.outerMeasure) {
                        toolObj.outerMeasure({ 
                            detail: { 
                                element: this.element,
                                startImgIndex: newToolData.startImgIndex,
                                startPoints: {
                                    image: newToolData.handles.start
                                },
                                currentPoints: {
                                    image: newToolData.handles.end
                                },
                            }
                        }, newToolData, suvThresValue)
                    }
                    setTimeout(() => {
                        // 重新测量之后，选中那一条与之前选中的数据一致的数据，这样才能连续地重新测量
                        const seriesStateData = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(this.seriesId)
                        const newSelectedToolData = seriesStateData.find(item => // areaId已经变了，需要用别的数据辨别
                            {
                                const data = item.toolData || {}
                                return data.toolName === toolData.toolName && (
                                    toolData.currentPoints && Math.abs(toolData.currentPoints.image.x - data.currentPoints.image.x) < 0.1
                                    && Math.abs(toolData.currentPoints.image.y - data.currentPoints.image.y) < 0.1
                                    || toolData.startPoints && Math.abs(toolData.startPoints.image.x - data.startPoints.image.x) < 0.1
                                    && Math.abs(toolData.startPoints.image.y - data.startPoints.image.y) < 0.1
                                    || toolData.startPoints && toolData.currentPoints &&
                                     Math.abs(Math.abs(toolData.startPoints.image.x - toolData.currentPoints.image.x) 
                                     - Math.abs(data.startPoints.image.x - data.currentPoints.image.x)) < 0.1
                                    && Math.abs(Math.abs(toolData.startPoints.image.y - toolData.currentPoints.image.y) 
                                     - Math.abs(data.startPoints.image.y - data.currentPoints.image.y)) < 0.1
                                )
                            })
                        if (newSelectedToolData) {
                            this.$emit('selectTool', newSelectedToolData.toolData);
                        }
                    }, 0);
                }, 0);

            }
        },
        async lesionAreaDrawComplete(event) {
            const detail = event.detail
            const getMeasureData = detail.getMeasureData
            const startImgIndex = detail.startImgIndex || this.imageIdIndex
            const lesionAreaId = this.$fun.onlyValue()
            // 收到事件，对序列内所有图像进行处理，入参为点击采样坐标

            const imageIds = this.imageIds
            let loopMaxPixelValue = 0 


            // 从当前图片开始遍历，到某一张的测量值为0时停止
            const loopList = []
            const measurementDataList = []

            const startImgstatData = await getMeasureData(imageIds[startImgIndex])
            if (!startImgstatData || !startImgstatData.meanStdDevSUV || startImgstatData.meanStdDevSUV.stdDev < 0.3) {
                this.setAirtools(true);
                return false 
            } else {
                loopList.push({ imageId: imageIds[startImgIndex], imageIndex: startImgIndex})
                loopMaxPixelValue = startImgstatData.maxPixelValue
            }


            // 先检测肿瘤范围 从第几张到第几张
            // 界定范围时使用单层最大值，计算最大值时使用整体最大值
            const checkIfNeedMeasure = async (imageId) => {
                const statData = await getMeasureData(imageId, loopMaxPixelValue)

                if (!statData || !statData.meanStdDevSUV) {
                    return false 
                }
                // 测量值过小 不继续循环后面的图 
                if (statData.meanStdDevSUV.stdDev < 0.15) {
                    return false
                }
                // loopMaxPixelValue = Math.max(loopMaxPixelValue, statData.maxPixelValue)
                // console.log(loopMaxPixelValue)
                // if (Math.abs(statData.maxPixelValue - loopMaxPixelValue) < 0.062) loopMaxPixelValue = statData.maxPixelValue
                return imageId
            }

            for (let i = startImgIndex + 1; i < imageIds.length; i++) {
                const loopImage = await checkIfNeedMeasure(imageIds[i])
                if (!loopImage) {
                    break
                } else {
                    loopList.push({ imageId: loopImage, imageIndex: i})
                }
            }

            for (let i = startImgIndex - 1; i >= 0; i--) {
                const loopImage = await checkIfNeedMeasure(imageIds[i])
                if (!loopImage) {
                    break
                } else {
                    loopList.push({ imageId: loopImage, imageIndex: i})
                }
            }

            // 遍历已有图片 进行肿瘤区域的测量 
            for (let index = 0; index < loopList.length; index++) {
                const loopItem = loopList[index] || {};
                const id = loopItem.imageId;
                const measurementData = await getMeasureData(id, loopMaxPixelValue)
                if (!measurementData || !measurementData.lesionPoints.length) {
                    break
                }

                measurementData.seriesId = this.seriesId
                measurementData.imageIdIndex = loopItem.imageIndex
                measurementDataList.push(measurementData)
            } 

            //  计算总值后返回，统一加标注

            //             MTV 肿瘤代谢体积
            // 步骤1、实现方式在PT图像上以2.5作为阀值，勾画ROI区域。
            // 步骤2、在逐层勾画PT图像上的肿瘤边界
            // 步骤3、勾画区域的面积与层厚相乘得到体积
            // 步骤3、所有勾画层体积相加即可得到 MTV 值
            //             TLG 糖酵解总量
            // 步骤1、如MTV拿到每层的体积在乘SUV平均值得到每层 TLG
            // 步骤2、所有勾画层的TLG相加等于总的TLG

            let MTV = 0  // MTV 肿瘤代谢体积
            let totalSUVmax = 0
            let totalSUVmin = 0
            let totalSUVavg = 0
            let TLG = 0
            let minIndex = 99999
            let maxIndex = 0
            let maxArea = 0
            let sliceThickness 
            let maxLengthX = 0 
            let maxLengthY = 0
            let totalSUVpeak = 0
            measurementDataList.forEach(item => {
                // 取得序列中的最大最小值 总值 等
                sliceThickness = item.sliceThickness || this.state.thickness // 重建的mpr图片没有厚度数据
                item.sliceThickness = sliceThickness
                MTV += item.area * sliceThickness
                // maxArea = Math.max(item.area, maxArea)
                maxIndex = Math.max(item.imageIdIndex, maxIndex)
                minIndex = Math.min(item.imageIdIndex, minIndex)
                maxLengthX = Math.max(item.maxLengthX, maxLengthX)
                maxLengthY = Math.max(item.maxLengthY, maxLengthY)

                if (item._modality === 'PT') {
                    TLG += item.area * sliceThickness * item.meanStdDevSUV.mean
                    if (item.meanStdDevSUV.max > totalSUVmax) {
                        totalSUVpeak = item.meanStdDevSUV.peak
                        totalSUVmax = item.meanStdDevSUV.max
                    }
                    totalSUVmin = totalSUVmin === 0 ? item.meanStdDevSUV.min : Math.min(item.meanStdDevSUV.min, totalSUVmin)
                }
            })

            totalSUVavg = TLG / MTV
            MTV = (MTV / 10).toFixed(2) // cm2 * mm / 10 = cm3
            TLG = (TLG / 10).toFixed(2)
            // var d = 2 * Math.sqrt(v / Math.PI)

            let maxZdiff = Math.abs(1 + maxIndex - minIndex) * sliceThickness / 10 // cm
            let theDiameter =  0.75*maxZdiff + 0.25*
            Math.sqrt(maxZdiff*maxZdiff + Math.max(maxLengthX, maxLengthY)*Math.max(maxLengthX, maxLengthY)) // 

            // 添加标注
            measurementDataList.forEach(item => {
                item.startImgIndex = startImgIndex
                item.volume = MTV
                item.maxZdiff = maxZdiff
                item.theDiameter = theDiameter
                item.maxLengthX = maxLengthX
                item.maxLengthY = maxLengthY
                item.text += ` MTV=${MTV}` // cm³
                if (item._modality === 'PT') {
                    item.text += ` TLG=${TLG}`
                    item.TLG = TLG
                    item.totalSUVmax = totalSUVmax
                    item.totalSUVmin = totalSUVmin
                    item.totalSUVavg = totalSUVavg
                    item.totalSUVpeak = totalSUVpeak
                }

                item.lesionAreaId = lesionAreaId
                // if (!this.toolSync) { // 关闭/打开测量工具同步时
                    // item.modality = this.isFuse ? 'FUSE' : this.state.sModality
                // } else {
                    item.modality = null
                // }
                item.uuid = this.$fun.onlyValue()

                // 添加到imageIdStateManager 管理测量值列表
                cornerstoneTools.imageIdStateManager.addToolState(this.state.patientId, this.seriesId,
                    item.imageId, 'LesionArea', item, `体测量(Im=${item.imageIdIndex + 1})`)

                // 添加到globalImageIdSpecificToolStateManager  管理图片内的显示
                cornerstoneTools.globalImageIdSpecificToolStateManager.addImageIdToolState(
                    item.imageId,
                    'LesionArea',
                    item
                )

                this.triggerLesionAreaGroupRender(item, 'add') //  多序列同步

            })
            
            this.triggerLesionAreaSTAT()

            // 向父组件提交事件
            this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'completed')

            cornerstoneTools.imageIdStateManager.triggerRender()

            this.setAirtools(true);
        },
        lesionAreaRemove(event) {
            const detail = event.detail
            const measurementData = detail.measurementData
            const lesionAreaId = measurementData.lesionAreaId
            const seriesId = this.seriesId

            const seriesStateData = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)

            seriesStateData.forEach(item => {
                const toolData = item.toolData || {}
                if (toolData.lesionAreaId === lesionAreaId) {
                    cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(item.imageId, "LesionArea", toolData);
                    cornerstoneTools.imageIdStateManager.delToolState(seriesId, item.imageId, toolData.toolName, toolData.uuid);
                    
                    this.triggerLesionAreaGroupRender(toolData, 'removed')
                }
            })

            this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'removed')
            this.$emit('clearSelectTool');
            cornerstoneTools.imageIdStateManager.triggerRender()
        },
        lesionAreaUpdate(event) {
            const detail = event.detail
            const measurementData = detail.measurementData
            const lesionAreaId = measurementData.lesionAreaId
            const seriesId = this.seriesId

            if (!lesionAreaId) {
                return;
            }
            const seriesStateData = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
            // 移动标注时，同步同一个体测量的所有数据
            seriesStateData.forEach(item => {
                const toolData = item.toolData || {}
                if (toolData.lesionAreaId === lesionAreaId) {
                    toolData.handles.textBox.hasMoved = true
                    toolData.handles.textBox.x = measurementData.handles.textBox.x
                    toolData.handles.textBox.y = measurementData.handles.textBox.y
                }
            })
        },
        freehandToolCalculte() {
            // console.log('freehand工具更新统计值')
            const seriesId = this.seriesId

            const seriesStateData = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)

            let totalVolume = 0
            let TLG = 0
            seriesStateData.forEach(item => {
                const toolData = item.toolData || {}
                const { sliceThickness } = cornerstone.metaData.get('imagePlaneModule', item.imageId);
                const thickness = sliceThickness || this.state.thickness // 重建的mpr图片没有厚度数据
                const area = toolData.area || 0
                totalVolume += area * thickness
                if (toolData._modality === 'PT') {
                    TLG += area * thickness * toolData.meanStdDevSUV.mean
                }
            })
            totalVolume = (totalVolume / 10).toFixed(2)
            TLG = (TLG / 10).toFixed(2)


            seriesStateData.forEach(item => {
                const toolData = item.toolData || {}
                toolData.totalVolume = totalVolume
                toolData.TLG = TLG > 0 ? TLG : false
                // 更新imageIdStateManager
                cornerstoneTools.imageIdStateManager.updateToolState(this.seriesId, item.imageId, 'FreehandRoi', toolData)
            })

            // 更新测量值列表
            this.$emit('onToolRenderCompleted', this.element, this.state.orientation, 'modifiedAndState')
        },

        /**
         * status == absolute | relative
         * 异步回调，参数是相对、绝对
         */
        syncCallBack(status) {
            const clayData = Object.assign({},
                this.applyInfo, // 附加信息
                { el: this.element, orientation: this.state.orientation, layoutId: this.state.layoutId } // 渲染的元素、角度（x,y,z）
            )
            this.$emit('renderCallBack', Object.assign({}, this.watchViewportRelative, this.watchViewportAbsolute), clayData, status)
        },
        // 新图像事件处理
        onNewImage(event) {
            const { imageId, windowWidth, windowCenter } = event.detail.image;
            let viewport = event.detail.viewport;

            // 标注翻页保持效果
            if (this.toolNewImageUse) {
                const allStack = cornerstoneTools.getToolState(this.element, 'stack');
                if (allStack) {
                    let list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(allStack.data[0].state.seriesId)
                    list = list.filter(item => item.toolData.toolNewImageUse);
                    list.forEach(toolItem => {
                        // 移除原生工具状态
                        cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(toolItem.imageId, toolItem.toolName, toolItem.toolData)
                        // 移除在原生基础工具状态
                        cornerstoneTools.imageIdStateManager.delToolState(allStack.data[0].state.seriesId, toolItem.imageId, toolItem.toolName, toolItem.toolData.uuid)

                        // 添加
                        const measurement = this.$fun.deepClone(toolItem.toolData);
                        delete measurement.uuid;
                        measurement.invalidated = true;
                        cornerstoneTools.addToolState(this.element, toolItem.toolName, measurement);
                        cornerstoneTools.imageIdStateManager.addToolState(this.state.patientId, allStack.data[0].state.seriesId, imageId, toolItem.toolName, measurement)
                    })
                }
            }

            const { sopInstanceUid } =
                cornerstone.metaData.get('generalImageModule', imageId) || {};
            let currentImageIdIndex = this.imageIds.indexOf(imageId);

            // 多帧查询
            if (currentImageIdIndex === -1) {
                currentImageIdIndex = this.imageIds.indexOf(imageId + '?frame=0');
            }

            this.imageIdIndex = currentImageIdIndex

            const { modality } = cornerstone.metaData.get('generalSeriesModule', imageId) || {}

            // MR 并且 启动设置
            if ((modality === 'MR' || modality === 'NM') && this.isAssignWWc) {
                viewport.voi.windowWidth = windowWidth
                viewport.voi.windowCenter = windowCenter
                cornerstone.setViewport(event.detail.element, viewport)
            }

            // 序列中有不同大小的图像，会被裁剪切割..
            if ((event.detail.oldImage && event.detail.oldImage.columns != event.detail.image.columns) ||
                (event.detail.oldImage && event.detail.oldImage.rows != event.detail.image.rows)) {
                viewport.displayedArea.brhc.x = event.detail.image.columns
                viewport.displayedArea.brhc.y = event.detail.image.rows
                cornerstone.setViewport(event.detail.element, viewport)
            }
            
            const el = event.target 
            const elParent = el.parentElement || {}
            const elParentClass = elParent.getAttribute && elParent.getAttribute('class')
            if (elParentClass && elParentClass.indexOf('xx-full-screen') > -1) {
                // 全屏下不需要更新别的窗口，卡
                // console.log(elParentClass)
            } else {
                // 有这个属性，不在执行 newImage
                if (event.detail.notTriggerEvent) {
                    return
                }
                this.$emit('onNewImage', {
                    currentImageIdIndex,
                    seriesLength: this.imageIds.length,
                    el: this.element,
                    sopInstanceUid,
                    orientation: this.state.orientation,
                    layoutId: this.state.layoutId,
                    viewportType: this.getCodeViewportModality(),
                    initRenderImage: this.initRenderImage
                });
    
                this.initRenderImage = false; // 渲染过了就改为 false
            }
            
            eventBus.$emit('clearSelectLesionArea' )
        },
        // 图像渲染处理函数
        onImageRendered(event) {
            let viewport = event.detail.viewport;
            const image = event.detail.image
            const enabledElement = event.detail.enabledElement
            const layers = enabledElement.layers

            this.imageId = this.imageIds[this.imageIdIndex];

            // 如果当前 viewport 跟 图像默认窗宽窗位不一样。代表人为改过窗
            if (viewport.voi.windowWidth !== image.windowWidth || viewport.voi.windowCenter !== image.windowCenter) {
                this.isAssignWWc = false
            }

            // 覆盖层使用参数
            this.renderOverlay.scale = viewport.scale
            this.renderOverlay.windowCenter = viewport.voi.windowCenter
            this.renderOverlay.windowWidth = viewport.voi.windowWidth

            // 方向使用的参数
            this.renderMarkers.rotationDegrees = viewport.rotation
            this.renderMarkers.isFlippedVertically = viewport.vflip
            this.renderMarkers.isFlippedHorizontally = viewport.hflip

            // 标尺使用的参数
            this.scaleOverlay.scale = viewport.scale
            this.scaleOverlay.columnPixelSpacing = image.columnPixelSpacing
            this.scaleOverlay.rowPixelSpacing = image.rowPixelSpacing

            // 在鼠标松开时，使用的参数
            this.watchViewportRelative = {
                scale: viewport.scale,
                x: viewport.translation.x,
                y: viewport.translation.y,
                index: this.imageIdIndex,
                width: viewport.displayedArea ? viewport.displayedArea.brhc.x : 0,
                height: viewport.displayedArea ? viewport.displayedArea.brhc.y : 0,
                columnPixelSpacing: viewport.displayedArea ? viewport.displayedArea.columnPixelSpacing : 1,
                rowPixelSpacing: viewport.displayedArea ? viewport.displayedArea.rowPixelSpacing : 1,
            }

            // 当这个enabledElement的layer有两个 时，判断为融合图像 的其中一个layer发生了变化
            if (layers.length === 2) {
                //   默认layers顺序是PT在第一，CT在第二 
                this.layerRenderCallback(layers[1])
            }
            // 实时变化的同步值
            this.watchViewportAbsolute.invert = viewport.invert;
            this.watchViewportAbsolute.windowWidth = viewport.voi.windowWidth;
            this.watchViewportAbsolute.windowCenter = viewport.voi.windowCenter;
            this.watchViewportAbsolute.hflip = viewport.hflip;
            this.watchViewportAbsolute.vflip = viewport.vflip;
            this.watchViewportAbsolute.rotation = viewport.rotation;
            this.watchViewportAbsolute.colormap = viewport.colormap;
            this.watchViewportAbsolute.offsetRotation = viewport.offsetRotation;

            // const imagePlane = cornerstone.metaData.get('imagePlaneModule', event.detail.image.imageId);
            this.applyInfo = {
                modality: image.data ? (image.data.string('x00080060') || '') : this.state.sModality,
            }
            // console.log(event.detail.image.data.string('x00200037') || '')

        },

        /**
         * 融合图像的次级layer发生了变化的回调
         */
        layerRenderCallback(layerData) {
            if (layerData.image === undefined) {
                return;
            }
            const modality = layerData.image.data.string('x00080060') || 'CT'
            const viewport = layerData.viewport

            this.$emit('renderCallBack', {
                ...layerData.viewport,
                windowWidth: viewport.voi.windowWidth,
                windowCenter: viewport.voi.windowCenter,
                x: viewport.translation.x,
                y: viewport.translation.y,
                width: viewport.displayedArea ? viewport.displayedArea.brhc.x : 0,
                height: viewport.displayedArea ? viewport.displayedArea.brhc.y : 0,
                columnPixelSpacing: viewport.displayedArea ? viewport.displayedArea.columnPixelSpacing : 1,
                rowPixelSpacing: viewport.displayedArea ? viewport.displayedArea.rowPixelSpacing : 1,
            }, {
                modality,
                el: this.element, orientation: this.state.orientation, layoutId: this.state.layoutId
            }, 'absolute')
        },

        // 选中设置
        onSelectCurrentCtrl() {
            setTimeout(() => {
                this.$emit('onSelectCurrentCtrl')
            }, 0);
        },
        onSelectCurrentShift() {
            setTimeout(() => {
                this.$emit('onSelectCurrentShift')
            }, 0);
        },
        // 设置当前选中窗口
        setViewportActive(e) {
            this.emitSetViewportActive(e.detail.event.button, this.selectToolState)
            if (this.selectToolState) {
                this.selectToolState = false;
                return;
            }
            // 调用触发清除
            this.$emit('clearSelectTool');
        },
        // 点击选中工具
        clickSelectTools(event) {
            // 设置选中状态
            this.selectToolState = true;
            // 触发清除
            this.$emit('clearSelectTool');
            // 触发选中
            const { toolData } = event.detail
            toolData.select = true;
            // 点击工具然后在拖拽，不会在触发窗口选中事件
            setTimeout(() => {
                this.selectToolState = false;
            }, 200);
            // 体测量数据选中时触发
            if (toolData.toolName === 'LesionArea') {
                this.$emit('selectTool', toolData);
            }
        },
        // 鼠标松开
        setViewportActiveCallBack(e) {
            this.emitSetViewportActive(e.button) // 设置选中该窗口
            // 调用回调
            this.syncCallBack('relative')
            
            // 显示微调
            if (this.showRotationTools && this.$refs.RotationTools) {
                this.$refs.RotationTools.renderCallBack();
            }
        },
        emitSetViewportActive(button, selectToolState) {
            clearTimeout(this.timerSelect)
            // 如果点击的是工具，并且工具栏已经显示不在触发选中操作
            if (selectToolState && this.showAction) {
                return;
            }
            this.timerSelect = setTimeout(() => {
                this.$emit('setViewportActive', button)
            }, 0);
        },
        // 加载完成
        onImageLoaded(e) {
            // console.log('loaded')
            // 统计加载数量
            // this.numImagesLoaded++;
        },
        // 加载进度
        onImageProgress(e) {
            // 加载进度
            // if (this.imageIds[this.imageIdIndex] == 'dicomweb:' + e.detail.url){
            this.imageProgress = e.detail.percentComplete;
            // }
        },
        // 设置反片
        setPTDefaultInvert(img) {
            const seriesModule = cornerstone.metaData.get('generalSeriesModule', img.imageId) || {};
            // 包含这几种 dicom 反色
            let inverts = ['PE', 'PET', 'PT', 'NM']
            if (seriesModule.modality && inverts.includes(seriesModule.modality.toLocaleUpperCase())) {
                const viewport = cornerstone.getViewport(this.element);
                viewport.invert = !viewport.invert;

                const desc = img.data.string('x0008103e') || null;
                const classUid = img.data.string('x00080016') || null;
                const imageType = img.data.string('x00080008') || null;
                if (this.$fun.isCaptrue(desc, classUid, false, imageType)) {
                    return
                }
                cornerstone.setViewport(this.element, viewport);
            }
        },
        // 设置默认窗位
        setDefaultWwwc(img, imageId) {

            // 有重建属性
            let voi = { windowWidth: undefined, windowCenter: undefined }
            // 是否是 PT
            if (this.state.sModality == 'PT' || img.data?.string('x00080060')) {
                const imageInfo = VoiMapping.getVoi(imageId);
                voi = { windowWidth: imageInfo.windowWidth, windowCenter: imageInfo.windowCenter };
            }

            // 需要设置
            if (voi.windowWidth && voi.windowWidth != 500) {
                const viewport = cornerstone.getViewport(this.element);
                viewport.voi.windowWidth = voi.windowWidth;
                viewport.voi.windowCenter = voi.windowCenter;
                cornerstone.setViewport(this.element, viewport);
            }
        },
        onResize() {
            cornerstone.resize(this.element);
        },
        // 设置当前活动层
        // async setActiveLayer(layerId) {
        //     await cornerstone.loadAndCacheImage(this.layerIds[0]);
        //     let layer = cornerstone.getActiveLayer(this.element);
        //     layer.options.opacity = 0.5;
        //     cornerstone.updateImage(this.element);
        //     this.$nextTick(() => {
        //         this.layers = cornerstone.getLayers(this.element)
        //         cornerstone.setActiveLayer(this.element, layerId || this.layers[1].layerId)
        //     })
        // },

        getCodeViewportModality() {
            let modality = ['PT', 'NM'].includes(this.state.sModality) ? 'PT' : 'CT';
            if (this.isFuse) {
                modality = 'FUSE'
            }
            return modality + this.state.orientation
        },
        getSliceSeriesId(imageId) {
            if (this.toolType === 'slice') {
                // 如果是 dicomweb 的，不裁剪
                if (imageId.includes("dicomweb")) {
                    return this.state.ctUid
                }
                const [scheme, seriesNumber] = imageId.split(':');
                return this.state.seriesId.replace(':', seriesNumber)
            }
            return this.state.seriesId;
        },

        setActiveTool(name) {
            this.$emit('update:activeTool', name)
        },
        setCrosshairsTool() {
            this.$emit('update:crosshairsTool', !this.crosshairsTool)
        },
        setIsOverlayVisible(name) {
            this.$emit('update:isOverlayVisible', name)
        },
        onClickIsShowRemark(val) {
            this.$emit('onClickIsShowRemark', val)
        },
        // setRemark(remark) {
        //     this.$emit('setRemark', remark)
        // },
        onMouseRight(e) {
            if (e.button !== 2) { return }
            let isMore = 0
            document.onmousemove = (e) => {
                isMore += 1
            }
            document.onmouseup = () => {
                if (isMore <= 2) {
                    this.$emit('onMouseRight', e)
                } else {
                    this.$emit('onMouseRight', e, true)
                }
                document.onmousemove = document.onmouseup = isMore = null
            }
        },
        changeCoordinateOffsetInput() {
            // 图片url变化时触发 从url中获取偏移量参数 传入偏移组件中

            const imageId = this.imageIds[0]
            if (!imageId) return

            // 由总偏移量 分别计算x y方向的偏移量
            const [scheme, seriesNumber, iop, imagePositionPatient, imageClip, clipKey, offsetParams] = imageId.split(':');
            const iopArray = iop.split(',')
            const rowCosinesVec3 = vec3.fromValues(iopArray[0], iopArray[1], iopArray[2]);  // x
            const colCosinesVec3 = vec3.fromValues(iopArray[3], iopArray[4], iopArray[5]);  // y
            let zedCosinesVec3 = vec3.create()
            vec3.cross(zedCosinesVec3, rowCosinesVec3, colCosinesVec3);
            if (!offsetParams) return
            const paramsArray = offsetParams.split(',')
            const offset = vec3.fromValues(paramsArray[0], paramsArray[1], paramsArray[2]) // 总偏移向量

            // 把偏移量分解到两个方向上，获取偏移值
            const offsetX = vec3.dot(offset, rowCosinesVec3) / vec3.length(rowCosinesVec3)  // 在X轴投影 = a与b的点乘 / b的模
            const offsetY = vec3.dot(offset, colCosinesVec3) / vec3.length(colCosinesVec3)  // 在Y轴投影
            const offsetZ = vec3.dot(offset, zedCosinesVec3) / vec3.length(zedCosinesVec3)

            this.offsetX = offsetX
            this.offsetY = offsetY
            this.offsetZ = offsetZ
            this.coordinateToolsDisabled = false
        },
        onOffsetChange({ offsetX, offsetY }) {
            // 计算总偏移量 x y分别乘以方向向量再相加
            const imageId = this.imageIds[0]
            const [scheme, seriesNumber, iop] = imageId.split(':');
            const iopArray = iop.split(',')
            const rowCosinesVec3 = vec3.fromValues(iopArray[0], iopArray[1], iopArray[2]);  // x
            const colCosinesVec3 = vec3.fromValues(iopArray[3], iopArray[4], iopArray[5]);  // y
            let zedCosinesVec3 = vec3.create()
            vec3.cross(zedCosinesVec3, rowCosinesVec3, colCosinesVec3);

            let offset = vec3.add([0, 0, 0], vec3.scale([0, 0, 0], rowCosinesVec3, offsetX), vec3.scale([0, 0, 0], colCosinesVec3, offsetY))
            offset = vec3.add(offset, offset, vec3.scale([0, 0, 0], zedCosinesVec3, this.offsetZ))
            const isCt = !['PT', 'NM'].includes(this.state.sModality);

            this.$emit('onOffsetChange', isCt, offset.join(','), this.element)
            // console.log(offset)
            // this.$emit('onOffsetChange', isCt, '33,33,33', this.element)
            this.coordinateToolsDisabled = true
        },
        resetCoordOffset() {
            this.$emit('onOffsetChange', true, '0,0,0', this.element)
            this.$emit('onOffsetChange', false, '0,0,0', this.element)
        },
        // 获取上一次的缩放比例显示
        getRatioRescale(defaultViewport) {
            const scale = defaultViewport.scale;
            const canvasWidth = this.element.clientWidth;
            const canvasHeight = this.element.clientHeight;
            const relWidthChange = canvasWidth / defaultViewport.oldCanvasWidth;
            const relHeightChange = canvasHeight / defaultViewport.oldCanvasHeight;
            const relChange = Math.sqrt(relWidthChange * relHeightChange);

            return relChange * scale
        }

    }
}
</script>
<style lang="scss" scoped>
.i-print-select {
    position: absolute;
    top: 3px;
    right: 4px;
    font-size: 13px;
    font-weight: bold;
    color: #409eff;
    background: #dcdfe6;
}

</style>