const DEFAULT_VALUES = {
    lineSize: 18,
    strokeColorColumn: '#00ff00',
    strokeColorRow: '#00ff00',
    strokeWidth: 1,
    strokeDashArray: '',
    padding: 10,
};
// 获取线条 xy 信息
function _getXYInfo(val, width, height, x, y, color){
    let colorFromConfig = color || DEFAULT_VALUES.strokeColorColumn
    if (val === 'large'){
        // 全屏线条
        return {
            top: {  
                x1: +x, y1: 0, 
                x2: +x, y2: +y - +DEFAULT_VALUES.padding
            },
            right: {
                x1: width, y1: +y,
                x2: +x + +DEFAULT_VALUES.padding, y2: +y
            },
            bottom: {
                x1: +x, y1: +height,
                x2: +x, y2: +y + +DEFAULT_VALUES.padding
            },
            left: {
                x1: 0, y1: +y,
                x2: +x - +DEFAULT_VALUES.padding, y2: +y
            },
            strokeWidth: DEFAULT_VALUES.strokeWidth,
            strokeDashArray: DEFAULT_VALUES.strokeDashArray,
            strokeColorRow: colorFromConfig,
            strokeColorColumn: colorFromConfig,
        }
    }else {
        // 小线条
        return {
            top: {  
                x1: x, y1: y - DEFAULT_VALUES.lineSize, 
                x2: x, y2: y - DEFAULT_VALUES.padding
            },
            right: {
                x1: x + DEFAULT_VALUES.lineSize, y1: y,
                x2: x + DEFAULT_VALUES.padding, y2: y
            },
            bottom: {
                x1: x, y1: y + DEFAULT_VALUES.lineSize,
                x2: x, y2: y + DEFAULT_VALUES.padding
            },
            left: {
                x1: x - DEFAULT_VALUES.lineSize, y1: y,
                x2: x - DEFAULT_VALUES.padding, y2: y
            },
            strokeWidth: 2.4,
            strokeDashArray: DEFAULT_VALUES.strokeDashArray,
            strokeColorRow: colorFromConfig,// DEFAULT_VALUES.strokeColorRow,
            // strokeColorRow: '#B11A1A',// DEFAULT_VALUES.strokeColorRow,
            strokeColorColumn: colorFromConfig,
        }
    }
}

export default function (node, width, height, x, y, showCrosshairs, crosshairsType, color) {
    requestAnimationFrame(() => {

        // console.log('渲染~') TODO 需要优化定位线效率
        // 获取本地缓存的定位线类型
        // console.log(val, width, height, x, y)
        // 不显示定位线，直接退出
        if (!showCrosshairs){
            node.innerHTML = ``;
            return;
        }
        const { top, right, bottom, left, strokeWidth, strokeDashArray, strokeColorRow, strokeColorColumn } = _getXYInfo(crosshairsType, width, height, x, y, color)
        // console.log(top, right, bottom, left, strokeWidth, strokeDashArray, strokeColorRow, strokeColorColumn)
        node.innerHTML = `
    <g id="container" stroke-dasharray="none" stroke="none" fill="none">
        <g>
        <!-- TODO: Why is this <svg> necessary?? </svg> If I don't include it, nothing renders !-->
        <svg version="1.1" viewBox="0 0 ${width} ${height}" width=${width} height=${height} style="width: 100%; height: 100%">
        <!-- Top !-->
        <line
        x1="${top.x1}"
        y1="${top.y1}"
        x2="${top.x2}"
        y2="${top.y2}"
        stroke="${strokeColorColumn}"
        stroke-width="${strokeWidth}"
        ></line>
        <!-- Right !-->
        <line
        x1="${right.x1}"
        y1="${right.y1}"
        x2="${right.x2}"
        y2="${right.y2}"
        stroke="${strokeColorRow}" 
        stroke-width=${strokeWidth}
        ></line>
        <!-- Bottom !-->
        <line
        x1="${bottom.x1}"
        y1="${bottom.y1}"
        x2="${bottom.x2}"
        y2="${bottom.y2}"
        stroke="${strokeColorColumn}" 
        stroke-width=${strokeWidth}
        ></line>
        <!-- Left !-->
        <line
        x1="${left.x1}"
        y1="${left.y1}"
        x2="${left.x2}"
        y2="${left.y2}"
        stroke="${strokeColorRow}" 
        stroke-width=${strokeWidth}
        ></line>
        </g>
    </g>
        `;
    })
}