<template>
    <el-dialog
        append-to-body
        ref="dialog"
        :title="newTitle"
        :visible.sync="visible"
        @close="closeDialog"
        @open="openDialog"
        width="860px"
        :close-on-click-modal="false"
        :close-on-press-escape="isClose"
        :show-close="isClose"
        custom-class="my-dialog open-series">
            <el-tabs id="tabs-base-tools" class="tabs-base-tools" v-model="activeTabs" type="card">
                <el-tab-pane label="序列" name="series">

                    <div class="c-main">
                        <div class="c-item-01">
                            <el-checkbox-group v-model="filterValue">
                                <el-checkbox label="可重建"></el-checkbox>
                                <el-checkbox label="不可重建"></el-checkbox>
                            </el-checkbox-group>
                            <el-button type="mini" @click="onClickGetData(false)" icon="el-icon-refresh" :loading="refreshLoading">刷 新</el-button>
                        </div>
                        <div class="c-item-02">
                            <el-scrollbar style="height: 100%" class="overflow-x-hide">
                                <div class="table-item" v-for="(item, index) in filterTable" :key="item.sStudyInstanceUID">
                                    <h4 class="table-title">
                                        <p class="table-title--p">日期：{{ item.iStudyDate }}</p>
                                        <p class="table-title--p">设备：{{ item.modality }}</p>
                                        <p class="table-title--p">核医学号：{{ item.sPatientID }}</p>
                                        <p class="table-title--p overflow">描述：{{ item.sStudyDescription }}</p>
                                    </h4>
                                    <i class="js-control-show el-icon-caret-top" @click="onClickShowHide(index, $event)"></i>
                                    <el-table  @row-contextmenu="(row, column, event) => rowContextMenu(event, index)"
                                        :ref="'multipleTable' + index" border @selection-change="(selection) => { sliceSelection(selection, index) }" @row-click="(row, column, e) => onClickRow(row, column, e, index)"
                                        :data="item.instanceList" tooltip-effect="dark" style="width: 100%">
                                        <!-- <el-table-column type="index" label="序号" width="50" align="center"></el-table-column> -->
                                        <el-table-column type="selection" width="55" align="center"></el-table-column>
                                        <el-table-column prop="sModality" label="设备" width="80" show-overflow-tooltip align="center" sortable></el-table-column>
                                        <el-table-column prop="iInstanceCount" label="数目" width="80" show-overflow-tooltip align="center" sortable></el-table-column>
                                        <el-table-column prop="seriesNumber" label="序号" width="90" show-overflow-tooltip align="center" sortable></el-table-column>
                                        <el-table-column prop="sSeriesDescription" width="180" label="序列描述" show-overflow-tooltip> </el-table-column>
                                        <el-table-column prop="imageType" label="图像描述" show-overflow-tooltip></el-table-column>
                                    </el-table>
                                </div>
                                <div class="tips" v-if="!filterTable.length">{{ filterValue.length ? '无数据' : '勾选可重建或不可重建筛选条件'  }}</div>
                            </el-scrollbar>
                        </div>
                        <div class="c-item-03">
                            <div class="left">
                                <PastPatient></PastPatient>
                            </div>
                            <div class="right">
                                <el-button type="small" v-if="limitType == 1" @click="onClickReSlice(0)" icon="el-icon-picture-outline" >阅 图</el-button>
                                <el-button type="small" v-if="limitType == 1" @click="onClickReSlice(1)" icon="el-icon-folder-opened" >重 建</el-button>
                                <el-button type="small" v-if="limitType == 2" @click="onClickReSlice(2)" icon="el-icon-folder-opened" >重建 MPR</el-button>
                                <el-button type="small" v-if="limitType == 6" @click="onClickReSlice(6)" icon="el-icon-folder-opened" >重建 VR</el-button>
                            </div>
                        </div>
                    </div>

                </el-tab-pane>
                <el-tab-pane label="重建" name="rebuild">
                    <tabsRebuild :originalSeries="originalSeries" :openVisible.sync="visible"></tabsRebuild>
                </el-tab-pane>
            </el-tabs>
            <div slot="title" class="c-title">
                <el-radio class="c-radio" v-model="openMode" :label="1">当前页</el-radio>
                <el-radio class="c-radio" v-model="openMode" :label="2">浏览器标签页</el-radio>
                <span class="c-info">{{ newTitle }}</span>
            </div>
            <v-contextmenu ref="contextmenu">
                <v-contextmenu-item @click="onClickAddMatch">添加匹配</v-contextmenu-item>
            </v-contextmenu>
            <!-- <layerMatchRebuild v-model="matchInfo.visible" :selectItem="matchInfo.item" :type="1"></layerMatchRebuild> -->
            <!-- 重建匹配弹窗页 -->
            <component :is="lazy.LayerMatchRebuild" v-model="matchInfo.visible" :selectItem="matchInfo.item" :type="1"></component>
    </el-dialog>
</template>
<script>
import PastPatient from './PastPatient.vue';
const LayerMatchRebuild = () => import('./components/layerMatchRebuild');
import { tabsRebuild } from './components'
import { mapState } from 'vuex'
import event from '$src/event.js'
export default {
    components: {
        tabsRebuild,
        PastPatient
    },
	props: {
        title: {
            type: String,
            default: "患者序列",
        },
		dialogVisible: {
			type: Boolean,
			default: false
		},
        limitType: {
            default: 1
        },
        isClose: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            activeTabs: 'series',
            visible: false,
            loading: false,
            refreshLoading: false,
            originalSeries: [],
            filterValue: ['可重建'],
            selectionCheck: {},
            openMode: 1,
            matchInfo: {
                rightIndex: 0,
                visible: false,
                item: {}
            },
            lazy: { },
        }
    },
	watch: {
		dialogVisible(later){
			if (later) {
				this.visible = this.dialogVisible;
			}
		},
        'openSlectSeries.refresh': {
            handler() {
                // 渲染自动重建组件
                this.$refs.dialog.rendered = true;
                this.openDialog()
            }
        }
    },
    computed: {
        ...mapState({
            openSlectSeries: 'openSlectSeries',
        }),
        filterTable() {
            return this.originalSeries.map(item => {
                // 用于拼接多个设备类型显示用
                let modality = []
                const instanceList = item.instanceList.filter(item => {
                    modality.push(item.sModality)
                    // 返回全部
                    if (this.filterValue.includes('不可重建') && this.filterValue.includes('可重建')) return true;
                    
                    // 过滤不可重建
                    if (this.filterValue.includes('不可重建')) return this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
                    
                    // 过滤可重建
                    if (this.filterValue.includes('可重建')) return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
                })

                console.log(instanceList)
                // 可重建，二次过滤，如果包含配置的内容就过滤，用 instanceList 中的 sSeriesDescription 属性判断


                // 截图的时候，设备类型放的是CT，所以这个MR的时候，会出现CT
                if (modality.includes('MR')) {
                    modality = modality.filter(item => {
                        return item != 'CT'
                    })
                }
                const res = Object.assign({}, item, {instanceList: instanceList}, {modality: Array.from(new Set(modality)).join('&')})
                return res
            }).filter(item => {
                // if (!this.filterValue.length) {
                //     return true
                // } 
                return item.instanceList.length
            })

        },
        seriesInfo() {
            return this.$store.state.seriesInfo
        },
        newTitle() {
           return `
                核医学号: ${this.seriesInfo.sPatientID || '__'} 　
                姓名: ${this.seriesInfo.sPatientName || '__'} 　
                年龄: ${this.seriesInfo.sPatientAge || '__'} 　
                性别: ${this.seriesInfo.sPatientSex || '__'} 　
           `
        }
    },
    methods: {
        // 点击打开重建匹配编辑弹窗
        onClickMetchRebuild() {
            if (!this.lazy.LayerMatchRebuild) {
                this.lazy.LayerMatchRebuild = LayerMatchRebuild;
            }
            this.matchInfo.visible = true;
        },

        checkSelectStatus(type) {
            let isPass = false
            let passMessage = '请选择'
            let passObj = {}
            for (const key in this.selectionCheck) {
                if (Object.hasOwnProperty.call(this.selectionCheck, key)) {
                    const select = this.selectionCheck[key]

                    if (type === 0) {
                        // 阅图
                        if (select && select.length >= 1){ 
                            isPass = true
                            passObj[key] = select.map(series => {
                                return series.uid
                            })
                        }else {
                            passMessage = '至少选择一组序列'
                        }
                    }else if (type === 1 || type === 2) {

                        if (select.length) {
                            console.log(select)
                            if ((select[0] && this.$fun.isCaptrue(select[0].sSeriesDescription, select[0].sSOPClassUID, true, select[0].imageType)) || 
                                (select[1] && this.$fun.isCaptrue(select[1].sSeriesDescription, select[1].sSOPClassUID, true, select[1].imageType)) ) 
                            {
                                passMessage = '存在不可重建序列';
                                break;
                            }
                            isPass = true;
                            passObj[key] = [];

                            const findModality = ['PT', 'NM'];
                            const ptList    = select.filter( item => findModality.includes(item.sModality.toLocaleUpperCase()) );
                            const notPtList = select.filter( item => !findModality.includes(item.sModality.toLocaleUpperCase()) );
                            
                            // 从不是 pt 开始找
                            if (notPtList.length) {
                                notPtList.forEach(item => {
                                    const groupId = { ct: item.uid, pet: null };
                                    const currentFirstDesc = item.sSeriesDescription ? item.sSeriesDescription.split(' ')[0] : '';
                                    // TODO 不知道查找 PT 的逻辑条件是什么？ 应该是查找与 ct、mr 相符融合 PT 图像
                                    const findPt = ptList.find(item => {
                                        return currentFirstDesc == (item.sSeriesDescription ? item.sSeriesDescription.split(' ')[0] : '');
                                    });
                                    
                                    if (findPt) {
                                        groupId.pet = findPt.uid;
                                    }else if (ptList[0]){
                                        groupId.pet = ptList[0].uid;
                                    }

                                    passObj[key].push(groupId);

                                })
                            }else if (ptList.length) {
                                // 只选了 pt、nm 的图像
                                ptList.forEach(item => {
                                    const groupId = { ct: null, pet: item.uid };
                                    passObj[key].push(groupId);
                                })
                            }

                        }else {
                            passMessage = '只可选择相同检查两组序列'
                        }
                    }else if (type === 6) {
                        // VTK VR
                        if (select.length === 1){ 
                            if (this.$fun.isCaptrue(select[0].sSeriesDescription, select[0].sSOPClassUID, true, select[0].imageType)) {
                                passMessage = '存在不可重建序列'
                                break
                            }
                            isPass = true
                            passObj[key] = select[0].uid
                        }else {
                            passMessage = '只可选择一组序列'
                        }
                    }
                }
            }
            return {
                isPass,
                passMessage,
                passObj
            }
        },
        /**
         * 打开新的网页
         * 打开新页面逻辑-
         * 1、 main.vue 中的 onSelectUid 方法参数存储到 localStorage
         * 2、打开页面带有 localStorage 的 key
         * 3、在 main.vue 中判断这个参数，有就尝试从 localStorage 中的信息，在通过 onSelectUid 打开
         */
        openNewPage(type, info) {
            const urlStr = window.location.hash.replace(/module=(0|1|2)/i, 'module='+type);
            const key    = 'open-new-page';
            // 读取 storage
            let arr = localStorage.getItem(key);
            if (!arr) {
                arr = [];
            }else {
                arr = JSON.parse(arr);
            }

            const pageId = this.$fun.onlyValue();
            const date   = + new Date();
            const day    = 60 * 60 * 24 * 1000;

            // 删除成功一天的数据
            arr.map((item, index) => {
                if ((date - item.date) > day) {
                    arr.splice(index, 1);
                }
            });

            // 加入
            arr.push({
                pageId,
                info,   // 动作信息（打开阅图、重建）
                date,
                requestList: this.$store.state.requestList // 请求信息，多列表请求
            });

            localStorage.setItem(key, JSON.stringify(arr));
            
            // 打开页面
            let a = document.createElement('a');
            a.href = urlStr + '&pageId=' + pageId;
            a.setAttribute('target', '_blank');
            document.body.append(a);
            a.click();
            a.remove();
        },
        // 点击确定
        onClickReSlice(type){
            const res = this.checkSelectStatus(type)
            if (!res.isPass) {
                this.$message({
                    message: res.passMessage,
                    type: 'warning'
                });
                return;
            }

            // 只有当前页面打开才关闭选择弹窗
            if (this.openMode === 1) {
                this.visible = false;
            }

            for (const key in res.passObj) {
                if (Object.hasOwnProperty.call(res.passObj, key)) {
                    const uids = res.passObj[key];
                    const idx  = Number(key.replace(/[^0-9]/ig, ''));

                    if (uids[0].constructor === String) {
                        // 阅图是数组(打开整个数组)
                        this.newPage(uids, type, idx);
                    }else {
                        // 重建传递过来的是数组对象，按照对象打开
                        uids.forEach((groupId, index) => {
                            this.newPage(groupId, type, idx, index);
                        })
                    }
                }
            }

        },
        /**
         * uids 打开的序列 uid
         * type 打开的组件模块类型
         * idx  检查下标
         * index = 0 只激活第一个tab
         */
        newPage(uids, type, idx, index) {
            // 这个数组是否有必要，要那么多？打开那么多
            const component = ['ReadDcm', 'ViewSlice', 'ViewVtkFuseMpr', 'Select', 'ViewContrast', 'ViewMRLayout', 'ViewVtkVR'];
            
            if (uids.pet === null) {
                delete uids.pet
            }

            if (this.openMode == 2) {
                // 打开新浏览器 tab
                this.openNewPage(type, { uids, component: component[type], seriesId: this.filterTable[idx].sStudyInstanceUID });
            }else {
                // 当前页面打开
                event.$emit('onSelectUid', uids, component[type], this.filterTable[idx].sStudyInstanceUID, undefined, false, index);
            }
        },
        closeDialog(){
			this.$emit('update:dialogVisible', false);
		},
        openDialog(){
            this.getAllSeries()
            this.activeTabs = 'series'
        },
        /**
         * 获取全部序列，存到 this.originalSeries
         */
        getAllSeries() {
            let arr = Array.from(this.$store.state.seriesMap.values())
            this.originalSeries = arr.map((item, index) => {
                this.$set(this.selectionCheck, 'multiple'+index, [])
                const instanceList = this.$fun.deepClone(Array.from(item.instanceList.values()))
                instanceList.data = null
                instanceList.imageIds = []
                instanceList.forEach(item => {
                    item.data = null
                    item.imageId = item.imageIds[0]
                    item.imageIds = []
                })
                const obj = Object.assign({}, 
                    { instanceList: instanceList },
                    item.seriesInfo,
                )
                return obj
            })
        },
        // 多选改变弹窗
        sliceSelection(val, index){
            this.selectionCheck['multiple'+index] = val
        },
        // 重建表格中点击行
        onClickRow(row, column, e, index){
            this.$refs['multipleTable'+index][0].toggleRowSelection(row);
        },
        onClickGetData(noHint = false){
            this.refreshLoading = true
            this.$store.dispatch('loadStudy').then(() => {
                this.refreshLoading = false
                this.getAllSeries()
                if (noHint === false) {
                    this.$message({
                        message: '刷新成功！',
                        type: 'success'
                    })
                }
            }).catch(err => {
                this.$message({
					type: 'error',
					message: err.msg
				})
                this.refreshLoading = false
            })
        },
        onClickShowHide(index, event) {
            try {
                const transform = event.target.style.transform
                const rotate = transform.replace(/[^0-9]/ig, '') === "90" ? "0" : "90"
                event.target.style.transform = 'rotate('+ rotate +'deg)'
                const dom = this.$refs['multipleTable' + index][0].$el.querySelector('.el-table__body-wrapper')
                dom.style.display = rotate == '90' ? 'none' : 'initial';
            } catch (error) {
                // error
            }

        },
        onClickAddMatch() {
            const studySeries = this.selectionCheck[`multiple${this.matchInfo.rightIndex}`];
            const len = studySeries.length;
            if (!len) {
                this.$message({
                    message: '请选择匹配序列。', type: 'info'
                })
                return;
            }


            let modalitys = []
            let uids = { ct: null, pet: null }
            studySeries.forEach(series => {
                modalitys.push(series.sModality.toLocaleUpperCase())
                if (series.sModality.toLocaleUpperCase() === 'CT' || series.sModality.toLocaleUpperCase() === 'MR' ){
                    uids.ct = series
                }else {
                    uids.pet = series
                }
            });

            // 条件满足,打开弹窗，添入到列表中
            if ((modalitys.includes('CT') || modalitys.includes('MR')) && (modalitys.includes('PET') || modalitys.includes('PT') || modalitys.includes('NM')) ){

                this.matchInfo.item = uids;
                this.onClickMetchRebuild();
                return;
            }

            if (uids.ct || uids.pet) {
                this.matchInfo.item = uids;
                this.onClickMetchRebuild();
                return;
            }

            this.$message({
                message: '请选择PT+CT 或 PT+MR 或 PT+ECT 或 单个序列', type: 'info'
            })

        },
        /* 右键事件打开菜单 */
		rowContextMenu(event, index){
            this.matchInfo.rightIndex = index
			event.preventDefault();
			this.contextmenuShow(event)
		},
        contextmenuShow(ev) {
			const postition = {
				top: ev.clientY,
				left: ev.clientX
			}
			this.$refs.contextmenu.show(postition)
        }
    }
}
</script>
<style lang="scss" scoped>
::v-deep .open-series{
    .el-dialog__body{
        padding: 0px !important;
        background: #fdfdfd;
    }
}
#tabs-base-tools{
    ::v-deep .el-tabs__nav .el-tabs__item{
        padding: 0px 20px !important;
        font-size: 14px;
    }
}
.c-title{
    padding-left: 10px;
    .c-radio{
        margin-right: 15px;
    }
    .c-info{
        font-size: 14px;
        font-weight: 700;
        padding-left: 5px;
        color: #2384d3;
        user-select: text;
    }
}
.c-main{
    display: flex;
    flex-direction: column;
    height: 600px;
    padding: 10px;
    .c-item-01 {
        padding-bottom: 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .c-item-02{
        flex: 1;
        overflow: auto;
        .tips {
            font-size: 16px;
            color: #999;
            text-align: center;
            padding: 40px 0px;
        }
    }
    .c-item-03{
        display: flex;
        justify-content: space-between;
        text-align: right;
        padding-top: 10px;
        .item {
            margin-top: 10px;
            margin-right: 30px;
        }
    }
}
.dialog-footer{
    button {
        margin-right: 20px;
        &:last-child{
            margin-right: 5px;
        }
    }
}
.table-item{
    position: relative;
    background: #727f8e;
    padding-left: 30px;
}
.table-title{
    display: flex;
    height: 30px;
    line-height: 30px;
    color: white;
    user-select: text;
}
.table-title--p{
    min-width: 140px;
    padding-right: 20px;
    &.overflow {
        width: 315px;
        overflow: auto;
    }
}
.js-control-show{
    font-size: 24px;
    color: white;
    position: absolute;
    left: 3px;
    top: 41px;
    cursor: pointer;
    transition: all 0.2s;
    &:hover{
        color: #fafafa;
    }

}
</style>