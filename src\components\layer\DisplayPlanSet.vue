<template>
    <div style="height: 100%">
        <el-dialog append-to-body title="重建显示方案" custom-class="displayplanset-table" :visible.sync="innerVisible"
            :close-on-click-modal="false" @open="openDialog" @close="closeDialog" width="720px">
            <div class="main-contain">
                <div class="main">
                    <div class="left-part">
                        <el-table ref="planTable" :data="tableData" highlight-current-row
                            @current-change="handleCurrentChange" style="width: 100%; height: 100%;">
                            <el-table-column prop="name" label="方案名称" align="center">
                            </el-table-column>

                        </el-table>
                    </div>
                    <div v-if="isDataViewShow" class="right-part">
                        <div class="head-name-input">

                            <span style="margin-right: 14px;">选择方案</span>

                            <el-input class="input" v-model="formData.name" size="small"></el-input>

                        </div>
                        <div class="radio-contain">
                            <el-radio-group class="radio-group" v-model="modality">
                                <el-radio v-for="item in modalityList" :key="item.name" :label="item.value">{{
                                    item.name
                                }}
                                </el-radio>
                            </el-radio-group>
                        </div>
                        <template v-if="modality === 'pt'">
                            <div class="card-box">
                                <div class="title">非投影</div>
                                <div class="card-row">
                                    <el-checkbox v-model="formData.pt.invert" :true-label="1" :false-label="0">反色
                                    </el-checkbox>
                                    <span class="select-label">伪彩</span>
                                    <el-select v-model="formData.pt.colormap" size="small">
                                        <el-option v-for="item in colormapsList" :key="item.id" :label="item.name"
                                            :value="item.id">
                                            {{ item.name }}
                                        </el-option>
                                    </el-select>
                                </div>
                                <div class="card-row">
                                    <span class="input-label">U</span>
                                    <el-input class="input" v-model="formData.pt.w" size="small"></el-input>
                                </div>
                                <div class="card-row">
                                    <span class="input-label">L</span>
                                    <el-input class="input" v-model="formData.pt.l" size="small"></el-input>
                                </div>

                            </div>
                            <div class="card-box">
                                <div class="title">投影（MIP）</div>
                                <div class="card-row">
                                    <!-- <el-checkbox v-model="formData.pt.mip.invert" :true-label="1" :false-label="0">反色
                                </el-checkbox> -->
                                    <!-- <span class="select-label">伪彩</span>
                                <el-select v-model="formData.pt.mip.colormap" size="small">
                                    <el-option v-for="item in colormapsList" :key="item.id" :label="item.name"
                                        :value="item.id">
                                        {{ item.name }}
                                    </el-option>
                                </el-select> -->
                                </div>
                                <div class="card-row">
                                    <span class="input-label">U</span>
                                    <el-input class="input" v-model="formData.pt.mip.w" size="small"></el-input>
                                </div>
                                <div class="card-row">
                                    <span class="input-label">L</span>
                                    <el-input class="input" v-model="formData.pt.mip.l" size="small"></el-input>
                                </div>
                            </div>
                        </template>
                        <template v-if="modality === 'ct'">
                            <div class="card-box">
                                <div class="title">非投影</div>
                                <div class="card-row">
                                    <el-checkbox v-model="formData.ct.invert" :true-label="1" :false-label="0">反色
                                    </el-checkbox>
                                    <span class="select-label">伪彩</span>
                                    <el-select v-model="formData.ct.colormap" size="small">
                                        <el-option v-for="item in colormapsList" :key="item.id" :label="item.name"
                                            :value="item.id">
                                            {{ item.name }}
                                        </el-option>
                                    </el-select>
                                </div>
                                <div class="card-row">
                                    <span class="input-label">Width</span>
                                    <el-input class="input" v-model="formData.ct.w" size="small"></el-input>
                                </div>
                                <div class="card-row">
                                    <span class="input-label">Center</span>
                                    <el-input class="input" v-model="formData.ct.l" size="small"></el-input>
                                </div>

                            </div>
                            <div class="card-box">
                                <div class="title">投影（MIP）</div>
                                <div class="card-row">
                                    <!-- <el-checkbox v-model="formData.ct.mip.invert" :true-label="1" :false-label="0">反色
                                </el-checkbox> -->
                                    <!-- <span class="select-label">伪彩</span>
                                <el-select v-model="formData.ct.mip.colormap" size="small">
                                    <el-option v-for="item in colormapsList" :key="item.id" :label="item.name"
                                        :value="item.id">
                                        {{ item.name }}
                                    </el-option>
                                </el-select> -->
                                </div>
                                <div class="card-row">
                                    <span class="input-label">Width</span>
                                    <el-input class="input" v-model="formData.ct.mip.w" size="small"></el-input>
                                </div>
                                <div class="card-row">
                                    <span class="input-label">Center</span>
                                    <el-input class="input" v-model="formData.ct.mip.l" size="small"></el-input>
                                </div>
                            </div>
                        </template>
                        <!-- 融合的图像基本可以通过调pt和ct实现，其他功能不实用 -->
                        <template v-if="modality === 'fuse'">
                            <div class="card-box">
                                <div class="title">非投影</div>
                                <div class="card-row">
                                    <!-- <el-checkbox v-model="formData.fuse.invert" :true-label="1" :false-label="0">反色
                                </el-checkbox> -->
                                    <span class="select-label">伪彩</span>
                                    <el-select v-model="formData.fuse.colormap" size="small">
                                        <el-option v-for="item in colormapsList" :key="item.id" :label="item.name"
                                            :value="item.id">
                                            {{ item.name }}
                                        </el-option>
                                    </el-select>
                                </div>
                                <!-- <div class="card-row">
                                <span class="input-label">W</span>
                                <el-input class="input" v-model="formData.fuse.w" size="small"></el-input>
                                <span class="input-label">Overlay W</span>
                                <el-input class="input" v-model="formData.fuse.overlayW" size="small"></el-input>
                            </div>
                            <div class="card-row">
                                <span class="input-label">L</span>
                                <el-input class="input" v-model="formData.fuse.l" size="small"></el-input>
                                <span class="input-label">Overlay L</span>
                                <el-input class="input" v-model="formData.fuse.overlayL" size="small"></el-input>
                            </div> -->

                            </div>
                            <!-- <div class="card-box">
                            <div class="title">投影</div>
                            <div class="card-row">
                                <el-checkbox v-model="formData.fuse.mip.invert" :true-label="1" :false-label="0">反色
                                </el-checkbox>
                                <span class="select-label">伪彩</span>
                                <el-select v-model="formData.fuse.mip.colormap" size="small">
                                    <el-option v-for="item in colormapsList" :key="item.id" :label="item.name"
                                        :value="item.id">
                                        {{ item.name }}
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="card-row">
                                <span class="input-label">W</span>
                                <el-input class="input" v-model="formData.fuse.mip.w" size="small"></el-input>
                                <span class="input-label">Overlay W</span>
                                <el-input class="input" v-model="formData.fuse.mip.overlayW" size="small"></el-input>
                            </div>
                            <div class="card-row">
                                <span class="input-label">L</span>
                                <el-input class="input" v-model="formData.fuse.mip.l" size="small"></el-input>
                                <span class="input-label">Overlay L</span>
                                <el-input class="input" v-model="formData.fuse.mip.overlayL" size="small"></el-input>
                            </div>
                        </div> -->
                        </template>
                    </div>
                </div>
                <div class="bottom">
                    <div class="left">
                        <el-button type="small" @click="onClickDel">删除</el-button>
                    </div>
                    <div class="right"> 
                        <el-button type="small" @click="onClickCreate">添加</el-button>
                        <el-button type="small" @click="onClickUpdate">修改</el-button>
                        <el-button type="small" @click="innerVisible = false">关闭</el-button>
                    </div>
                </div>
                <!-- <div class="save-tip">
                    *配置数据仅保存在本地，若需要长期保存请在“系统设置”中点击【配置方案 - 保存至服务器】 
                </div> -->
            </div>
        </el-dialog>
    </div>
</template>
<script>
import ModeDialog from "$src/mixin/ModeDialog.js";

import { dbDisplayPlan, defaultPlanData } from "$library/db";


const modalityList = [
    {
        name: 'PT / NM',
        value: 'pt'
    },
    {
        name: 'CT / MR',
        value: 'ct'
    },
    {
        name: '融合',
        value: 'fuse'
    }
]

const colormapsList = [
    { id: '', name: '默认' },

    { id: 'ge_color', name: 'ge_color' },
    { id: 'hot', name: 'Hot' },
    { id: 'hot2', name: 'Hot2' },
    { id: 'perfusion', name: 'Perfusion' },
    { id: 'rainbow', name: 'Rainbow' },
    { id: 'x_rain', name: 'x_rain' },
    { id: 'rainbow_brain', name: 'rainbow_brain' },
    { id: 'x_hot', name: 'X_hot' },
    { id: 'darkHot', name: 'darkHot' },

    { id: 'x_brain', name: 'x_brain' },
    { id: 'copper', name: 'Copper' },
    { id: 'hotMetalBlue', name: 'Hot Metal Blue' },
    { id: 'pet20Step', name: 'PET 20 Step' },
    { id: 'spectral', name: 'Spectral' },
    { id: 'gray', name: 'gray' },

    { id: 'blues', name: 'Blues' },
    { id: 'cool', name: 'Cool' },
    { id: 'jet', name: 'Jet' },
]


export default {
    name: 'DisplayPlanSet',
    mixins: [ModeDialog],
    components: {},
    props: {
        scheme: {
            type: [String, Number],
            default: 0
        }
    },
    data() {
        return {
            tableData: [
            ],
            modality: 'pt',
            formData: { ...defaultPlanData, name: '' },
            isDataViewShow: true,
            modalityList,
            colormapsList,
        };
    },
    methods: {
        handleCurrentChange(item) {
            if (!item) {
                return
            }
            this.selectedId = item.id;
            this.formData = this.$fun.deepClone(item)
            this.refreshDataView()

        },
        onClickUpdate() {
            const item = this.$fun.deepClone(this.formData)

            dbDisplayPlan.then((e) => {
                e.update(item).then(() => {
                    this.getData().then(() => {
                        let index = this.tableData.findIndex(i => i.id === this.selectedId)
                        index = index > -1 ? index : this.tableData.length - 1
                        this.$refs.planTable.setCurrentRow(this.tableData[index])
                        this.$store.dispatch('saveAllConfig')
                    })

                    // this.$message.success('修改成功')
                    
                });
            });

        },
        getData() {
            return new Promise((resolve, reject) => {
                dbDisplayPlan.then((e) => {
                    e.getGroup('displayPlan').then((e) => {
                        if (e.success) {
                            // 存储方案到 vuex
                            this.$store.commit('setSchemeRebuilds', e.data)
                            this.tableData = e.data
                            resolve(e.data)
                        }
                    });
                });

            })

        },
        // 点击创建布局
        onClickCreate() {
            // 当前输入的内容当做新的创建
            const data = this.$fun.deepClone(this.formData)
            delete data.id
            // data.name = '方案 ' + (this.tableData.length + 1)
            dbDisplayPlan.then((e) => {
                e.add(data).then(() => {
                    this.getData().then(() => {
                        this.$refs.planTable.setCurrentRow(this.tableData[this.tableData.length - 1])
                        this.$store.dispatch('saveAllConfig')
                    })
                })
            })

        },
        onClickDel() {
            if (this.tableData.length === 1) return
            let index = this.tableData.findIndex(i => i.id === this.selectedId)
            index = index > -1 ? index : this.tableData.length - 1
            index = Math.max(0, index - 1)
            dbDisplayPlan.then((e) => {
                e.del(this.selectedId).then(e => {
                    if (e.success) {
                        this.getData().then(() => {
                            this.$refs.planTable.setCurrentRow(this.tableData[index])
                            this.$store.dispatch('saveAllConfig')
                        })
                    }
                })
            })

        },

        closeDialog() {
            this.$emit('closeDialog')
        },
        openDialog() {
            this.$nextTick(() => {
                this.setCurrentItem()
            });
        },
        refreshDataView() {
            this.isDataViewShow = false
            this.$nextTick(() => {
                this.isDataViewShow = true
            })
        },
        setCurrentItem() {
            let index = 0
            if (this.scheme) {
                const idx = this.tableData.findIndex(item => item.id === this.scheme)
                if (idx != -1) {
                    index = idx
                }
            }
            this.$refs.planTable.setCurrentRow(this.tableData[index])
        }
    },
    mounted() {
        this.getData().then(() => {
            this.setCurrentItem()
        })
    },
};
</script>
<style lang="scss" scoped>
.main-contain {
    position: relative;
    width: 100%;
    height: 560px;
    display: flex;
    flex-direction: column;

    .main {
        position: relative;
        flex: 1;
        display: flex;
        width: 100%;
        flex-direction: row;
        background: #f6f6f6;

        .left-part {
            width: 250px;
            overflow: hidden;
            border: 1px solid #ccc;
        }

        .right-part {
            flex: 1;
            border: none;
            overflow: auto;
            margin: 0 0 0 20px;

            .head-name-input {
                position: relative;

                .input {
                    display: inline-block;
                    width: 150px;
                }
            }

            .radio-contain {
                position: relative;
                margin: 15px 0 25px;
            }

            .card-box {
                position: relative;
                width: 100%;
                height: 165px;
                margin: 20px 0 0 0;
                border: 1px solid #dbdbdb;

                .title {
                    position: absolute;
                    font-size: 14px;
                    padding: 0 5px;
                    top: -7px;
                    left: 20px;
                    background: #f6f6f6;
                }

                .card-row {
                    position: relative;
                    margin: 16px 24px;

                    .select-label {
                        margin: 0 14px 0 60px;
                    }

                    .input {
                        display: inline-block;
                        width: 100px;
                        margin: 0 14px 0 0;
                    }

                    .input-label {
                        display: inline-block;
                        min-width: 20px;
                        margin: 0 14px 0 0;
                    }
                }
            }
        }
    }

    .bottom {
        position: relative;
        flex: 0;
        width: 100%;
        height: 60px;
        margin: 15px 0 0 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .left {}
    }
    .save-tip {
        margin: 8px 0 0 0;
        font-size: 12px;
        height: 30px;
        text-align: right;
    }
}
</style>
<style lang="scss">
.displayplanset-table {

    .el-table__body tr.current-row>td {
        background-color: #1872da;
        color: #f6f6f6;
    }

    .el-table {
        background: #ccc;
    }

    .el-table td {
        padding: 6px 0;
    }
}
</style>
