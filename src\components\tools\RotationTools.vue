<template>
    <div ref="curDom" class="container" @dblclick="stopPropagate">
        <Coordinate2dTols 
            ref="Coordinate2dTols"
            :element="element" 
            :imageId="imageId"
            :offsetKey="offsetKey"
            :showInput="showInput"
            :showState="showState"
            @onClickAction="onClickAction" ></Coordinate2dTols>
        <div class="rotationTools-tools">
            <template v-if="!showInput">
                <div class="item-btn" title="切换输入方式" @click="toggleShowInput('toInput')">
                    <i class="iconfont iconleft-right"></i>
                </div>
                <div class="item-btn" title="逆时针旋转" @click="onClickAction('left')">
                    <i class="el-icon-refresh-left"></i>
                </div>
                <div class="item-btn" style="z-index: 0" @click="onClickAction('reset')">
                    <i class="reset-icon"></i>
                </div>
                <div class="item-btn" title="顺时针旋转" @click="onClickAction('right')">
                    <i class="el-icon-refresh-right"></i>
                </div>
                <div class="item-btn btn-sync" title="旋转同步">
                    <el-checkbox v-model="synchronize" @change="onChangeCheck"></el-checkbox>
                </div>
            </template>
            <template v-else>
                <div class="item-btn" title="切换输入方式" @click="toggleShowInput('toAction')">
                    <i class="iconfont iconleft-right"></i>
                </div>
                <div class="input-box">
                    <span>度</span>
                    <el-input-number 
                    :min="0" :max="360"
                    class="input" 
                    size="mini" 
                    controls-position="right"
                    v-model="offsetInput" 
                    @change="onEnterAction"
                    v-floorNumber></el-input-number>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
// 微调后，过滤旋转 = 0  ，平移 = {x: 0, y: 0}
function filterData(data, condition) {
    const filteredData = Object.keys(data).reduce((acc, key) => {
        if (condition === 'rotation') {
            if (data[key] !== 0) {
                acc[key] = data[key];
            }
        }else {
            if (data[key]?.x !== 0 && data[key]?.y !== 0) {
                acc[key] = data[key];
            }
        }
        return acc;
    }, {});
    return filteredData;
}
// 同步只同步旋转，没有同步平移
import Coordinate2dTols from './Coordinate2dTols.vue'
export default {
    components: {
        Coordinate2dTols
    },
    props: {
        element: {
            type: [Array, HTMLCollection, HTMLDivElement ],
            default: () => { [] }
        },
        imageId: {
            default: null
        },
        showState: {
            default: false
        }
    },
    data() {
        return {
            step: 1,
            showInput: false,
            offsetInput: 0,
            synchronize: true,
            timer: null
        }
    },
    computed: {
        offsetKey() {
            if (this.imageId.includes('mpr')) {
                // 重建的
                const [scheme, seriesNumber, imageOrientationPatient, , , fuseSeriesNumber] = this.imageId.split(':');
                return `${scheme}+${seriesNumber}+${imageOrientationPatient}+${fuseSeriesNumber}`; 
            }else {
                // 没有重建的
                const instanceStr = this.imageId.split('=')[1]
                if (!instanceStr) {
                    return ''
                }
                return `dicomweb+${instanceStr.slice(0, instanceStr.lastIndexOf('.'))}`
            }
        }
    },
    watch: {
        showState() {
            this.offsetInput = cornerstone.offsetRotationMap[this.offsetKey] || 0;
        }
    },
    methods: {
        renderCallBack() {
            if (this.$refs.Coordinate2dTols) {
                this.$refs.Coordinate2dTols.renderCallBack();
            }
        },
        onEnterAction() {
            this.onClickAction('input');
        },
        // 阻止事件冒泡
        stopPropagate(e) {
            e.stopPropagation();
        },
        onClickAction(status) {
            let synchronize = this.synchronize;
            
            let targetOffsetKey = ''
            let targetOffset    = 0
            // 获取保存中的偏移值
            let offsetVal = cornerstone.offsetRotationMap[this.offsetKey] || 0;

            if (status === 'left') {
                offsetVal -= this.step;
            }else if (status === 'right') {
                offsetVal += this.step;
            }else if (status === 'input') {
                offsetVal = this.offsetInput;
            }else {
                // 重置的时候，不管有没有同步，都要重置所有
                synchronize = true;
                offsetVal = 0;
            }

            if (offsetVal < 0) {
                offsetVal = 360 - this.step;
            }
            if (offsetVal >= 360) {
                offsetVal = 0;
            }

            // 更新到 input 点击左旋右旋需要
            this.offsetInput = offsetVal;

            // 更新保存的偏移值
            cornerstone.offsetRotationMap[this.offsetKey] = offsetVal;

            // 开启同步
            if (synchronize) {
                // 获取同步目标
                const soureInfo = cornerstoneTools.getToolState(this.element, 'stack')
                
                if (soureInfo !== undefined && soureInfo.data !== undefined && soureInfo.data.length >= 1){
                    // 获取融合层，获取是否是原图
                    if (soureInfo.data[1] && soureInfo.data[1].imageIds[0]?.includes('dicomweb')) {
                        const instanceStr = soureInfo.data[1].imageIds[0].split('=')[1]
                        targetOffsetKey = `dicomweb+${instanceStr.slice(0, instanceStr.lastIndexOf('.'))}`
                    }else {
                        // 重建图
                        const soureState = soureInfo.data[0].state;
                        const { ctUid, petUid } = soureState
                        const [ scheme, seriesNumber, imageOrientationPatient ] = this.imageId.split(':');
                        const targetUid = ctUid == seriesNumber ? petUid : ctUid
                        targetOffsetKey = `${scheme}+${targetUid}+${imageOrientationPatient}+ `
                    }

                    targetOffset = cornerstone.offsetRotationMap[targetOffsetKey] || 0
                    if (status === 'left') {
                        targetOffset -= this.step
                    }else if (status === 'right') {
                        targetOffset += this.step
                    }else if (status === 'input') {
                        targetOffset = this.offsetInput
                    } else {
                        targetOffset = 0
                    }

                    if (targetOffset < 0) {
                        targetOffset = 360 - this.step
                    }
                    if (targetOffset >= 360) {
                        targetOffset = 0
                    }

                    cornerstone.offsetRotationMap[targetOffsetKey] = targetOffset
                     
                }
            }

            // 更新 viewport 偏移
            const viewport = cornerstone.getViewport(this.element);
            // const beforeOffset = viewport.offsetRotation || 0;
            viewport.offsetRotation = offsetVal; // 新的偏移值
            cornerstone.setViewport(this.element, viewport);
            cornerstone.updateImage(this.element);

            // 更新其它同屏中相同 id 的旋转
            const tabDom = this.getContentDom();
            const elements = tabDom.getElementsByClassName('viewport-element');
            if (!elements) {
                return;
            }

            for (let index = 0; index < elements.length; index++) {
                const el = elements[index];
                
                if (!el){
                    continue;
                }

                const layers = cornerstone.getLayers(el);
                if (layers.length) {
                    layers.forEach(_ => {
                        const layer = cornerstone.getLayer(el, _.layerId);
                        const viewport = layer.viewport;

                        const image = layer.image;
                        // const [scheme, seriesNumber, imageOrientationPatient] = image.imageId.split(':');
                        // const tagetOffsetKey = `${scheme}${seriesNumber}${imageOrientationPatient}`; 
                        const renderOffsetKey = cornerstone.getOffsetRotationKey(image.imageId);

                        if (this.offsetKey == renderOffsetKey) {
                            viewport.offsetRotation = offsetVal;
                            cornerstone.updateImage(el);
                        }

                        if (synchronize && targetOffsetKey === renderOffsetKey) {
                            viewport.offsetRotation = targetOffset;
                            cornerstone.updateImage(el);
                        }
                    })
                }else {
                    const image = cornerstone.getImage(el);
                    if (!image) continue
                    // const [scheme, seriesNumber, imageOrientationPatient] = image.imageId.split(':');
                    // const tagetOffsetKey = `${scheme}${seriesNumber}${imageOrientationPatient}`; 
                    const renderOffsetKey = cornerstone.getOffsetRotationKey(image.imageId);

                    if (this.offsetKey == renderOffsetKey) {
                        const viewport = cornerstone.getViewport(el);
                        viewport.offsetRotation = offsetVal;
                        cornerstone.setViewport(el, viewport);
                        cornerstone.updateImage(el);
                    }

                    if (synchronize && targetOffsetKey === renderOffsetKey) {
                        const viewport = cornerstone.getViewport(el);
                        viewport.offsetRotation = targetOffset;
                        cornerstone.setViewport(el, viewport);
                        cornerstone.updateImage(el);
                    }
                }
            }

            this.storeData()
        },
        storeData() {
            try {
                const id = this.$parent.state.patientId
                
                clearTimeout(this.timer)
                this.timer = setTimeout(() => {
                    this.$Api.putKV({
                        key: `image-offsetMap+${id}`,
                        value: JSON.stringify({
                            offsetRotationMap: filterData(cornerstone.offsetRotationMap, 'rotation'),
                            translationMap: filterData(cornerstone.translationMap, 'translation')
                        })
                    })
                }, 1000)
            } catch (err) {
                console.log(err)
            }

        },
        toggleShowInput(actionType) {
            if (actionType === 'toInput') {
                this.offsetInput = cornerstone.offsetRotationMap[this.offsetKey] || 0;
            }
            this.showInput = !this.showInput;
        },
        getContentDom() {
            let dom = this.$refs.curDom;
            let contentDom;
            while (dom) {
               if (!dom || dom.className == 'c-content') {
                   contentDom = dom;
                   break;
               }
               dom = dom.parentElement;
            }
            return contentDom;
        },
        onChangeCheck() {
            this.$store.state.rotationSyncTool = this.synchronize
        }
    },
    mounted() {
        this.synchronize = this.$store.state.rotationSyncTool
    },
}
</script>
<style lang="scss" scoped>
.container {
    position: absolute;
    bottom: 2px;
    right: 2px;
    z-index: 4;
    transition: none;
    .rotationTools-tools {
        display: flex;
        width: 112px;
        height: 28px;
        background: #efefef;
    }
    .item-btn{
        width: 28px;
        height: 28px;
        background: #f5f9fc;
        border: 1px solid #d0d2d1;
        color: #142b4b;
        border-radius: 4px;
        cursor: pointer;
        z-index: 1;
        &:hover {
            background-color: #d1dbe7;
        }
        &.btn-sync {
            position: absolute;
            right: 0;
            bottom: 56px;
        }
        .el-checkbox{
            width: 100%;
            height: 100%;
            padding-top: 6px;
        }
        > i {
            display: inline-block;
            width: 100%;
            height: 100%;
            font-size: 22px;
            line-height: 28px;
            &:active {
                filter: invert(50%);
            }
        }
        .reset-icon {
            position: relative;
            width: 15px;
            height: 15px;
            font-size: 26px;
            margin: 6px auto;
            background: #111;
            border-radius: 50%;
            &:active {
                filter: invert(50%);
            }
        }
    }
    .input-box {
        width: 84px; 
        z-index: 2;
        display: flex;
        align-items: center;
        > span {
            display: inline-block;
            width: 18px;
        }
        .input{
            width: 66px;
            ::v-deep input{
                text-align:left;
                font-size: 16px;
            }
        }
    }
}
</style>