<template>
    <div style="height: 100%;display: flex;flex-direction: column;">
        <div class="c-content">
            <!-- <el-checkbox v-model="interpolationMode" :true-label="1" :false-label="0">图像插值</el-checkbox> -->
            <el-checkbox v-model="imageFilterMode" :true-label="1" :false-label="0">图像平滑</el-checkbox>
            <el-checkbox v-model="imageFilterSharpen" :true-label="1" :false-label="0">图像二次锐化</el-checkbox>
            <el-checkbox v-model="imageFilteWithoutCnS" :true-label="1" :false-label="0">冠矢位图像不处理</el-checkbox>
            
            <div class="item-01">
                <span style="padding-right: 32px;">切面插值方式</span>
                <el-select v-model="interpolationMode" size="small">
                    <el-option
                    v-for="item in interpolationModeOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="item-01">
                <span>MIP图像清晰度</span>
                <el-select v-model="imageSampleDistance"  size="small">
                    <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div class="item-01">
                <span>切面图像清晰度</span>
                <el-select v-model="imageClipClarity"  size="small">
                    <el-option
                    v-for="item in imageClipClarityOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </div>
        </div>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setImageSharpness',
    data() {
        return {
            storageKey: {
                imageSampleDistance: 'configs-imageSampleDistance',
                interpolationMode: 'configs-interpolationMode',
                imageFilterMode: 'configs-imageFilter2Mode', 
                imageFilterSharpen: 'configs-imageFilterSharpen', 
                imageFilteWithoutCnS: 'configs-imageFilteWithoutCnS', 
                imageClipClarity: 'configs-imageClipClarity', 
                
            },
            interpolationMode: 1,   // 0 邻近。 1 线性
            imageSampleDistance: 1, // 越大越模糊
            imageFilterMode: 1,
            imageFilterSharpen: 1,
            imageFilteWithoutCnS: 0,
            imageClipClarity: 900,
            options: [
                {
                    value: 1,
                    label: '高'
                },
                {
                    value: 3,
                    label: '中'
                },
                {
                    value: 5,
                    label: '低'
                }
            ],
            interpolationModeOption: [
                {
                    value: 0,
                    label: '邻近插值'
                },
                {
                    value: 1,
                    label: '线性插值'
                },
            ],
            imageClipClarityOptions: [
                {
                    value: 1500,
                    label: '高'
                },
                {
                    value: 1200,
                    label: '中'
                },
                {
                    value: 900,
                    label: '低'
                }
            ],
        }
    },
    mounted() {
        this.interpolationMode = getConfigByStorageKey(this.storageKey.interpolationMode);
        this.imageSampleDistance = getConfigByStorageKey(this.storageKey.imageSampleDistance);
        this.imageFilterMode = getConfigByStorageKey(this.storageKey.imageFilterMode);
        this.imageFilterSharpen = getConfigByStorageKey(this.storageKey.imageFilterSharpen);
        this.imageFilteWithoutCnS = getConfigByStorageKey(this.storageKey.imageFilteWithoutCnS);
        this.imageClipClarity = getConfigByStorageKey(this.storageKey.imageClipClarity);
    },
    methods: {
        onClickSave() {


            this.setDistance();
            this.setInterpolation();
            localStorage.setItem(this.storageKey.imageFilterMode, JSON.stringify(this.imageFilterMode));
            localStorage.setItem(this.storageKey.imageFilterSharpen, JSON.stringify(this.imageFilterSharpen));
            localStorage.setItem(this.storageKey.imageFilteWithoutCnS, JSON.stringify(this.imageFilteWithoutCnS));
            localStorage.setItem(this.storageKey.imageClipClarity, JSON.stringify(this.imageClipClarity));


            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        // MIP 采样
        setDistance(){
            // 设置新的缓存
            localStorage.setItem(this.storageKey.imageSampleDistance, JSON.stringify(this.imageSampleDistance));
            cornerstoneTools.store.state.imageSampleDistance = this.imageSampleDistance;
        },
        // 插值
        setInterpolation() {
            localStorage.setItem(this.storageKey.interpolationMode, JSON.stringify(this.interpolationMode));
            cornerstoneTools.store.state.interpolationMode = this.interpolationMode;
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    flex: 1;
    padding-top: 10px;
    > span{
        padding-right: 20px;
    }
    .item-01{
        display: flex;
        align-items: center;
        margin-top: 50px;
        > span{
            padding-right: 20px;
            label{
                font-size: 13px;
            }
        }
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>