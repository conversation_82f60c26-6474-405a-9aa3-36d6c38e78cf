import csTools from '$library/cornerstone/cornerstoneTools'

const external = csTools.external;

const { addToolState, removeToolState, getToolState, toolStyle, toolColors, getModule, store } = csTools;
const BaseTool = csTools.importInternal('base/BaseTool');
const { state } = store; 
const { eraserCursor } = csTools.importInternal('tools/cursors');
/**
 * @public
 * @class EraserTool
 * @memberof Tools
 *
 * @classdesc Tool for deleting the data of other Annotation Tools.
 * @extends Tools.Base.BaseTool
 */
export default class EraserTool extends BaseTool {
  constructor(props = {}) {
    const defaultProps = {
      name: 'Eraser',
      supportedInteractionTypes: ['Mouse', 'Touch'],
      svgCursor: eraserCursor,
    };

    super(props, defaultProps);

    this.preMouseDownCallback = this._deleteAllNearbyTools.bind(this);
    this.preTouchStartCallback = this._deleteAllNearbyTools.bind(this);
  }

  _deleteAllNearbyTools(evt) {
    const coords = evt.detail.currentPoints.canvas;
    const element = evt.detail.element;

    state.tools.forEach(function(tool) {
      const toolState = getToolState(element, tool.name);

      if (toolState) {
        // Modifying in a foreach? Probably not ideal
        toolState.data.forEach(function(data) {
          if (
            typeof tool.pointNearTool === 'function' &&
            tool.pointNearTool(element, data, coords)
          ) {
            removeToolState(element, tool.name, data);
            
            const image = external.cornerstone.getImage(element)
            csTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(image.imageId, tool.name, data);

            external.cornerstone.updateImage(element);
          }
        });
      }
    });

    const consumeEvent = true;

    return consumeEvent;
  }
}
