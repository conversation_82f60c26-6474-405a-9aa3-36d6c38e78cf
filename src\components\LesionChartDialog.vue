<template>
	<el-dialog
		append-to-body
		title="病灶评估"
		:visible.sync="innerVisible"
		:close-on-click-modal="false"
		@close="closeDialog"
		@open="openDialog"
		width="1020px"
		top="5vh"
	>
		<el-container direction="horizontal" class="dialog-modal">
			<el-aside width="160px" class="aside">
				<div
					v-for="(item, index) in totalDataList"
					:key="index"
					:class="{ 'btn-change-data': true, 'active': index === activeDataIndex }"
					@click="showContrastData(index)"
				>{{ item.lesionPositionName }}</div>
				<div
					:class="{ 'btn-change-data': true, 'active': isSummary, 'btn-summary': true }"
					@click="showSummary"
				>总览分析</div>
			</el-aside>
			<el-container v-show="isSummary" direction="vertical" class="main-content">
				<div class="table-contain">
					<table class="table summary-table" ref="summaryTable">
						<thead>
							<tr>
								<th rowspan="2">参数</th>
								<th :colspan="maxCheckNum">原发灶总和</th>
								<th :colspan="maxCheckNum">转移灶总和</th>
								<th :colspan="maxCheckNum">合计</th>
							</tr>
							<tr>
								<template v-for="m in 3">
									<th v-for="n in maxCheckNum" :key="m + `-` + n">检查{{ n }}</th>
								</template>
							</tr>
						</thead>
						<tbody v-if="summaryTableData[0]">
							<tr v-for="(val, key) in summaryTableData[0]" :key="key">
								<td>{{ key }}</td>
								<td v-for="(item, index) in summaryTableData" :key="key + index">{{ item[key] }}</td>
							</tr>
						</tbody>
					</table>
					<div class="line-div">
						<el-button type="primary" @click="summaryTableExcel">导出excel</el-button>
					</div>
				</div>

				<div class="chart-contain">
					<Echarts
						:options="check1ChartConfig"
						:opts="{
							width: '790px',
							height: '400px'
						}"
						@ready="check1ChartReady"
					/>
					<Echarts
						:options="check2ChartConfig"
						:opts="{
							width: '790px',
							height: '400px'
						}"
						@ready="check2ChartReady"
					/>
				</div>
			</el-container>
			<el-container v-show="!isSummary" direction="vertical" class="main-content">
				<div class="table-contain">
					<table class="table" ref="contrastTable">
						<thead>
							<tr v-if="contrastTableData[0]">
								<th width="200">参数</th>
								<th v-for="n in maxCheckNum" :key="n">检查{{ n }} （{{contrastTableData[0][`checkDate${n}`]}}）</th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item, index) in contrastTableData" :key="index">
								<td>{{ item.key }}</td>
								<td v-for="n in maxCheckNum" :key="n">{{ item[`val${n}`] }}</td>
							</tr>
						</tbody>
					</table>
					<div class="line-div">
						<el-button type="primary" @click="contrastTableExcel">导出excel</el-button>
					</div>
				</div>

				<div class="chart-contain">
					<Echarts
						:options="contrastChartConfig"
						:opts="{
							width: '790px',
							height: '400px'
						}"
						@ready="contrastChartReady"
					/>
				</div>

				<div class="picture-contain">
					<div v-for="(item, index) in contrastPicturesData" :key="index" class="img-box">
						<el-image class="img" :src="item.base64" :z-index="9999" :preview-src-list="[item.base64]"></el-image>
						<div class="img-title">{{ item.checkName }}</div>
					</div>
				</div>
			</el-container>
		</el-container>
	</el-dialog>
</template>
<script>
import Echarts from '$components/Echarts'
import { dbLesionAssess } from '$library/db';

const COLOR_TYPE_2 = ['#0080dd', '#40b927']
const COLOR_TYPE_3 = ['#0080dd', '#40b927', '#80cd5b']

const X_AXIS_TYPE_2 = ['检查1\n原发灶\n总和', '检查2\n原发灶\n总和',
	'检查1\n转移灶\n总和', '检查2\n转移灶\n总和',
	'检查1\n合计', '检查2\n合计']

const X_AXIS_TYPE_3 = ['检查1\n原发灶\n总和', '检查2\n原发灶\n总和', '检查3\n原发灶\n总和',
	'检查1\n转移灶\n总和', '检查2\n转移灶\n总和', '检查3\n转移灶\n总和',
	'检查1\n合计', '检查2\n合计', '检查3\n合计']

export default {
	name: 'LesionChartDialog',
	components: {
		Echarts
	},
	props: {
		visible: {
			type: Boolean,
			default: false
		}
	},

	data() {

		return {
			innerVisible: false,
			isSummary: false,
			activeDataIndex: -1,
			maxCheckNum: 2, // 检查次数 仅支持2、3
			totalDataList: [],
			contrastTableData: [],
			contrastPicturesData: [],
			summaryTableData: [],

			contrastChartConfig: {
				title: {
					text: ' ',
					subtext: '   '
				},
				tooltip: {
					trigger: 'axis'
				},
				legend: {
					data: []
				},
				label: {
					show: true,
					position: 'top',
					formatter: (params) => {
						// if (params.seriesIndex === 1) {
						// 	return params.seriesName + '\n'
						// 		+ this.getContrastPercent(this.contrastChartConfig.series, params.dataIndex)
						// }
						return (params.seriesName || '') + '\n' + params.value
					}
				},
				calculable: true,
				xAxis: {
					xAxisLabel: {
						hideOverlap: false
					},
					type: 'category',
					data: ['SUV_MAX', 'SUV_MIN', 'SUV_AVG', 'MTV_VOL', 'TLG', 'SUL',]
				},
				yAxis: [
					{
						type: 'value'
					}
				],
				colorBy: 'series',
				color: COLOR_TYPE_3,
				toolbox: {
					show: true,
					showTitle: false,
					right: '34',
					feature: {
						magicType: {
							type: ['line', 'bar']
						}
					},
					tooltip: {
						show: true,
						formatter: function (param) {
							return '<div>' + param.title + '</div>';
						} 
					}
				},
				series: [],
				toolbox: {
					show: true,
					showTitle: false,
					right: '34',
					feature: {
						magicType: {
							type: ['line', 'bar']
						}
					},
					tooltip: {
						show: true,
						formatter: function (param) {
							return '<div>' + param.title + '</div>';
						} 
					}
				},

			},

			check1ChartConfig: {
				title: {
					text: 'MTV',
					left: '40%'
				},
				barWidth: '50%',
				tooltip: {
					trigger: 'axis'
				},
				label: {
					show: true,
					position: 'top',
					formatter: (params) => {
						// if (params.dataIndex % 2 === 1) {
						// 	return params.value + '\n'
						// 		+ `${this.getSummaryPercent(this.check1ChartConfig.series, params.dataIndex)}`
						// }
						return params.value
					}
				},
				calculable: true,
				xAxis: {
					xAxisLabel: {
						hideOverlap: false
					},
					type: 'category',
					data: X_AXIS_TYPE_2
				},
				yAxis: [
					{
						type: 'value'
					}
				],
				colorBy: 'data',
				color: COLOR_TYPE_2,
				toolbox: {
					show: true,
					showTitle: false,
					right: '34',
					feature: {
						magicType: {
							type: ['line', 'bar']
						}
					},
					tooltip: {
						show: true,
						formatter: function (param) {
							return '<div>' + param.title + '</div>';
						} 
					}
				},
				series: []


			},
			check2ChartConfig: {
				title: {
					text: 'TLG',
					left: '40%'
				},
				barWidth: '50%',
				tooltip: {
					trigger: 'axis'
				},
				label: {
					show: true,
					position: 'top',
					formatter: (params) => {
						// if (params.dataIndex % 2 === 1) {
						// 	return params.value + '\n'
						// 		+ `${this.getSummaryPercent(this.check2ChartConfig.series, params.dataIndex)}`
						// }
						return params.value
					}
				},
				calculable: true,
				xAxis: {
					xAxisLabel: {
						hideOverlap: false
					},
					type: 'category',
					data: X_AXIS_TYPE_2
				},
				yAxis: [
					{
						type: 'value'
					}
				],
				colorBy: 'data',
				color: COLOR_TYPE_2,
				series: []

			},
			contrastChartInstance: null,
			check1ChartInstance: null,
			check2ChartInstance: null
		}
	},
	computed: {
	},
	watch: {
		visible: {
			handler(val) {
				this.innerVisible = val
			},
			immediate: true
		}
	},
	mounted() {
	},
	methods: {
		initDataList(list) {
			this.totalDataList = list
			if (this.activeDataIndex === -1) {
				this.showContrastData(0)
			}
		},
		initContrastData(index) {
			this.contrastTableData = []
			const targetData = this.totalDataList[index]
			if (!targetData) return
			const lesionList = targetData.lesionList || []
			const measurementsParamList = Object.keys(lesionList[0].measurements)

			this.contrastPicturesData = lesionList // 截图数据

			measurementsParamList.forEach(key => {
				if (this.maxCheckNum === 2) {
					this.contrastTableData.push({
						key,
						checkDate1: lesionList[0].checkDate,
						checkDate2: lesionList[1].checkDate,
						val1: lesionList[0].measurements[key],
						val2: lesionList[1].measurements[key]
					})
				} else {
					this.contrastTableData.push({
						key,
						checkDate1: lesionList[0].checkDate,
						checkDate2: lesionList[1].checkDate,
						checkDate3: lesionList[2].checkDate,
						val1: lesionList[0].measurements[key],
						val2: lesionList[1].measurements[key],
						val3: lesionList[2].measurements[key]
					})
				}
			})

			const packedSeries = [
				{
					name: lesionList[0].checkName,
					type: 'bar',
					data: this.contrastTableData.map(item => item.val1)
				},
				{
					name: lesionList[1].checkName,
					type: 'bar',
					data: this.contrastTableData.map(item => item.val2)
				},
			]
			if (this.maxCheckNum === 3) {
				packedSeries.push({
					name: lesionList[2].checkName,
					type: 'bar',
					data: this.contrastTableData.map(item => item.val3)
				})
			}
			this.contrastChartConfig.series = packedSeries
			this.contrastChartConfig.legend.data = packedSeries.map(item => item.name)
			this.contrastChartConfig.xAxis.data = this.contrastTableData.map(item => item.key)

			this.loopGetInnerData('contrastChartInstance',
				() => {
					this.contrastChartInstance.setOption(this.contrastChartConfig)
				}
			)

		},
		initSummaryData(list) {
			const keyList = ['MTV', 'TLG']

			const summaryDataObjArr = [] // [{mtv: ,tlg: }, ...]
			// 原发灶
			const firstArea = list.find(item => item.lesionTypeId == 1)
			if (!firstArea) return
			const firstAreaLesions = firstArea.lesionList
			// 转移灶
			const secondArea = list.find(item => item.lesionTypeId == 2)
			if (!secondArea) return
			const secondAreaLesions = secondArea.lesionList

			// firstAreaLesions.sort // 检查1 2 3 排序

			firstAreaLesions.forEach(item => {
				const dataObj = {}
				keyList.forEach(key => {
					dataObj[key] = item.measurements[key]
				})
				summaryDataObjArr.push(dataObj)
			})

			secondAreaLesions.forEach(item => {
				const dataObj = {}
				keyList.forEach(key => {
					dataObj[key] = item.measurements[key]
				})
				summaryDataObjArr.push(dataObj)
			})

			// 总计
			const sumDataObjList = []
			firstAreaLesions.forEach((item, index) => {
				const dataObj = {}

				keyList.forEach(key => {
					dataObj[key] = summaryDataObjArr
						.filter((_, _index) => _index % firstAreaLesions.length === index)
						.map(i => i[key])
						.reduce((a, b) => a + Number(b), 0)
						.toFixed(2)
				})
				sumDataObjList.push(dataObj)
			})

			sumDataObjList.forEach(item => {
				summaryDataObjArr.push(item)
			})

			this.summaryTableData = summaryDataObjArr

			this.check1ChartConfig.series = [{
				type: 'bar',
				data: summaryDataObjArr.map(item => item.MTV)
			}]
			this.check2ChartConfig.series = [{
				type: 'bar',
				data: summaryDataObjArr.map(item => item.TLG)
			}]

			if (this.maxCheckNum === 3) {
				this.check1ChartConfig.color = this.check2ChartConfig.color = COLOR_TYPE_3
				this.check1ChartConfig.xAxis.data = this.check2ChartConfig.xAxis.data = X_AXIS_TYPE_3
			}

			this.loopGetInnerData('check2ChartInstance',
				() => {
					this.check1ChartInstance.setOption(this.check1ChartConfig)
					this.check2ChartInstance.setOption(this.check2ChartConfig)
				}
			)

		},
		getContrastPercent(series, index) {
			const value1 = series[0].data[index] - 0
			const value2 = series[1].data[index] - 0
			const percent = ((value2 - value1) / value1 * 100)

			if (!isNaN(percent) && percent < Number.MAX_SAFE_INTEGER) {
				return percent.toFixed(1) + '%'
			} else {
				return ''
			}
		},
		getSummaryPercent(series, index) {
			const value1 = series[0].data[index - 1] - 0
			const value2 = series[0].data[index] - 0
			const percent = ((value2 - value1) / value1 * 100)

			if (!isNaN(percent) && percent < Number.MAX_SAFE_INTEGER) {
				return percent.toFixed(1) + '%'
			} else {
				return ''
			}
		},
		showContrastData(index) {
			this.activeDataIndex = index
			this.isSummary = false
			this.initContrastData(index)
		},
		showSummary() {
			this.isSummary = true
			this.activeDataIndex = -1
			this.check1ChartInstance.setOption(this.check1ChartConfig)
			this.check2ChartInstance.setOption(this.check2ChartConfig)
		},
		contrastTableExcel() {
			const dom = this.$refs['contrastTable']
			const tableHtml = '<html><head><meta charset="UTF-8"></head><body><table>' + dom.innerHTML + '</table></body></html>';
			this.exportExcel(tableHtml, '病灶评估')
		},
		summaryTableExcel() {
			const dom = this.$refs['summaryTable']
			const tableHtml = '<html><head><meta charset="UTF-8"></head><body><table>' + dom.innerHTML + '</table></body></html>';
			this.exportExcel(tableHtml, '病灶评估总览分析')
		},
		exportExcel(tableHtml, fileName) {
			const excelBlob = new Blob([tableHtml], { type: 'application/vnd.ms-excel' });
			const linkEl = document.createElement('a')
			linkEl.href = URL.createObjectURL(excelBlob);
			linkEl.download = fileName + '.xls';
			linkEl.click();
		},
		openDialog() {
			dbLesionAssess.then(e => {
				const condition = this.$store.state.seriesInfo.sAccessionNumber;
				e.getGroup(condition).then(e => {
					if (e.success) {
						this.list = e.data;
						if (!this.list.length) {
							// 无数据
							this.$message.error('无病灶数据，请先测量病灶并保存数据');
						} else {
							const lesionListMaxLeng = this.list.reduce((_, curr) => {
								return curr.lesionList.length > _ ? curr.lesionList.length : _
							}, 0)
							this.maxCheckNum = lesionListMaxLeng > 2 ? 3 : 2
							this.initDataList(this.list)
							this.initSummaryData(this.list)
						}
					}
				})
			})

		},
		closeDialog() {
			this.isSummary = false
			this.activeDataIndex = -1
			this.$emit('close')
		},
		contrastChartReady(instance) {
			this.contrastChartInstance = instance
		},
		check1ChartReady(instance) {
			this.check1ChartInstance = instance
		},
		check2ChartReady(instance) {
			this.check2ChartInstance = instance
		},
		loopGetInnerData(key, cb) {
			setTimeout(() => {
				if (!this[key]) {
					this.loopGetInnerData(key, cb)
				} else {
					cb(this[key])
				}
			}, 200);
		}
	}
}
</script>
<style lang="scss" scoped>
.dialog-modal {
	max-height: 720px;
	overflow-y: scroll;
}
.aside {
	border: 1px solid rgb(193, 205, 228);
	.btn-change-data {
		position: relative;
		width: 158px;
		min-height: 36px;
		line-height: 36px;
		padding: 0 0 0 14px;
		font-size: 16px;

		color: rgb(5, 0, 78);
		background: #f1f6fe;
		cursor: pointer;
		transition: 0.12s;
		&:hover {
			background: #c0c4f8;
		}
		&.active {
			color: #007dff;
			border-left: 5px solid;
			border-right: 5px solid;
		}
	}
	.btn-summary {
		color: rgb(150, 0, 0);
	}
}

.main-content {
	position: relative;
	.table-contain {
		position: relative;
		margin: 0 0 10px 10px;
		border: 1px solid rgb(193, 205, 228);
		.table {
			position: relative;
			width: 100%;
			text-align: center;
			thead {
				th {
					vertical-align: middle;
					padding: 12px 0;
					color: #909399;
					font-weight: 500;
					font-weight: bold;
					border-right: 1px solid #ebeef5;
					border-bottom: 1px solid #ebeef5;
				}
			}
			tbody {
				td {
					vertical-align: middle;
					padding: 12px 0;
					border-right: 1px solid #ebeef5;
					border-bottom: 1px solid #ebeef5;
				}
			}
		}
	}
	.chart-contain {
		position: relative;
		margin: 0 0 10px 10px;
		border: 1px solid rgb(193, 205, 228);
		padding-top: 20px;

		.half {
			position: relative;
			float: left;
		}
	}
	.picture-contain {
		position: relative;
		margin: 0 0 10px 10px;
		border: 1px solid rgb(193, 205, 228);
		.img-box {
			position: relative;
			width: 188px;
			height: 260px;
			margin: 10px 0 10px 10px;
			float: left;
			.img {
				position: relative;
				width: 100%;
				height: 240px;
			}
			.img-title {
				position: relative;
				width: 100%;
				text-align: center;
			}
		}
	}

	.line-div {
		position: relative;
		padding: 4px;
	}
}
</style>