import macro from 'vtk.js/Sources/macro';
import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';
import vtkCoordinate from 'vtk.js/Sources/Rendering/Core/Coordinate'; // 各种坐标系位置，转换
// import crosshairsWidget from '$library/cornerstone/tools/crosshairsWidget'
import getConfigByStorageKey from '$library/utils/configStorage.js';

let instanceId = 1;

function getWidgetNode(svgContainer, widgetId) {
  let node = svgContainer.querySelector(`#${widgetId}`);
  if (!node) {
    node = document.createElement('g');
    node.setAttribute('id', widgetId);
    svgContainer.appendChild(node);
  }
  return node;
}

// ----------------------------------------------------------------------------

function vtkSVGCrosshairsWidget(publicAPI, model) {
  model.classHierarchy.push('vtkSVGCrosshairsWidget');
  model.widgetId = `vtkSVGCrosshairsWidget-${instanceId++}`;
  publicAPI.render = (svgContainer, scale) => {
    requestAnimationFrame(() => {

      const node = getWidgetNode(svgContainer, model.widgetId);
      let { point, strokeColor, lineSize, strokeWidth, strokeDashArray, padding } = model;
      if (point[0] === null || point[1] === null) {
        return;
      }
      const width = parseInt(svgContainer.getAttribute('width'), 10);
      const height = parseInt(svgContainer.getAttribute('height'), 10);

      const p = point.slice();
      p[0] = point[0] * scale;
      p[1] = height - point[1] * scale;


      const crosshairsType = getConfigByStorageKey('configs-crosshairs-type');

      const isShortLine = crosshairsType.MIP !== 'large'
      const isArrowline = crosshairsType.MIP === 'arrowline'

      if (isShortLine) {
        strokeWidth = 2.6
      }
 
      const customColor =  crosshairsType.toolColor || strokeColor;

      const normalCrosshair = `
      <!-- Right !-->
          <line
            x1="${isShortLine ? point[0] + lineSize : width}"
            y1="${p[1]}"
            x2="${point[0] + padding}"
            y2="${p[1]}"
            stroke-dasharray="${strokeDashArray}"
            stroke="${isShortLine ? '#B11A1A' : customColor}"
            stroke-width=${strokeWidth}
          ></line>
          <!-- Left !-->
          <line
            x1="${isShortLine ? point[0] - lineSize : 0}"
            y1="${p[1]}"
            x2="${point[0] - padding}"
            y2="${p[1]}"
            stroke-dasharray="${strokeDashArray}"
            stroke="${isShortLine ? '#B11A1A' : customColor}"
            stroke-width=${strokeWidth}
          ></line>

            <!-- Top !-->
            <line
            x1="${p[0]}"
            y1="${isShortLine ? height - point[1] - lineSize : 0}"
            x2="${p[0]}"
            y2="${height - point[1] - padding}"
            stroke="${customColor}"
            stroke-dasharray="${strokeDashArray}"
            stroke-width="${strokeWidth}"
          ></line>
            <!-- Bottom !-->
            <line
              x1="${p[0]}"
              y1="${isShortLine ? height - point[1] + lineSize : height}"
              x2="${p[0]}"
              y2="${height - point[1] + padding}"
              stroke-dasharray="${strokeDashArray}"
              stroke="${customColor}"
              stroke-width=${strokeWidth}
            ></line>
        `
      const arrowlineCrosshair = `
         <path d="M0,${p[1]-0} L30,${p[1]-0} M10,${p[1]-10} L10,${p[1]+10} L30,${p[1]-0}" stroke="${customColor}" stroke-width="2" fill="${customColor}" />
      <line
            x1="${0}"
            y1="${p[1]}"
            x2="${width}"
            y2="${p[1]}"
            stroke-dasharray=""
            stroke="${customColor}"
            stroke-width=${1}
          ></line>
      `

      const mipCrosshairDisplay = getConfigByStorageKey('configs-mipCrosshairDisplay')

      if (mipCrosshairDisplay === '2') { // 固定隐藏
        model.display = false
      } else if (mipCrosshairDisplay === '1') { // 固定显示
        model.display = true
      }else {
        model.display = model.crosshairsTool.mipShow
      }

      if (model.display) {
        // crosshairsWidget(node, width, height, p[0], p[1])
        // 在改变层厚会出现 NaN，其它有时候也会
        if (isNaN(point[0]) || isNaN(point[1])) {
          return
        }

        node.innerHTML = `
        <g id="container">
        <g>
        <svg version="1.1" viewBox="0 0 ${width} ${height}" width=${width} height=${height} style="width: 100%; height: 100%">
            ${isArrowline ? arrowlineCrosshair : normalCrosshair}
          
        </g>
        </g>
              `;
      } else {
        node.innerHTML = '';
      }
    })
  };

  publicAPI.resetCrosshairs = (apis, apiIndex) => {
    const api = apis[apiIndex];

    if (!api.svgWidgets.crosshairsWidget) {
      // If we aren't using the crosshairs widget, bail out early.
      return;
    }

    // Get viewport and get its center.
    const renderer = api.genericRenderWindow.getRenderer();
    const view = renderer.getRenderWindow().getViews()[0];
    const dims = view.getViewportSize(renderer);
    const dPos = vtkCoordinate.newInstance();

    dPos.setCoordinateSystemToDisplay();

    dPos.setValue(0.5 * dims[0], 0.5 * dims[1], 0);
    let worldPos = dPos.getComputedWorldValue(renderer);

    const camera = renderer.getActiveCamera();
    const directionOfProjection = camera.getDirectionOfProjection();
    const halfSlabThickness = api.getSlabThickness() / 2;

    // Add half of the slab thickness to the world position, such that we select
    //The center of the slice.

    for (let i = 0; i < worldPos.length; i++) {
      worldPos[i] += halfSlabThickness * directionOfProjection[i];
    }
    publicAPI.moveCrosshairs(worldPos, apis, apiIndex);
  };

  publicAPI.moveCrosshairs = (worldPos, apis, apiIndex) => {
    if (worldPos === undefined || apis === undefined) {
      console.error(
        'worldPos, apis must be defined in order to update crosshairs.'
      );
    }
    if (apiIndex !== undefined && apiIndex !== null) {

      // 触发
      // console.log(api[apiIndex])
      // api[apiIndex].触发生命周期
      // console.log(worldPos)
      // api.watchModifiedNum = [api.watchModifiedNum[0]+=1, worldPos]


      // if (apis[apiIndex].addCrosshairsModified) {

      // }
      // apis[apiIndex].addCrosshairsModified = (fun => {
      //   fun && fun(worldPos)
      // })


    }
    model.worldPos = worldPos
    // Set camera focal point to world coordinate for linked views
    apis.forEach((api, viewportIndex) => {
      if (!api) { return }
      api.set('cachedCrosshairWorldPosition', worldPos);

      // We are basically doing the same as getSlice but with the world coordinate
      // that we want to jump to instead of the camera focal point.
      // I would rather do the camera adjustment directly but I keep
      // doing it wrong and so this is good enough for now.
      // const renderWindow = api.genericRenderWindow.getRenderWindow();
      // 变化代码，注释
      // const istyle = renderWindow.getInteractor().getInteractorStyle();
      // const sliceNormal = istyle.getSliceNormal();
      // const transform = vtkMatrixBuilder // MAT4变换矩阵
      //   .buildFromDegree()
      //   .identity()
      //   .rotateFromDirections(sliceNormal, [1, 0, 0]);
      // const mutatedWorldPos = worldPos.slice();
      // transform.apply(mutatedWorldPos);
      // const slice = mutatedWorldPos[0];

      // 设置切片（设置后，MIP会改变切片位置）
      // istyle.setSlice(slice);

      // renderWindow.render();

      const renderer = api.genericRenderWindow.getRenderer();
      const wPos = vtkCoordinate.newInstance(); // vtk 内置的坐标位置转换
      wPos.setCoordinateSystemToWorld(); // 设置系统坐标到世界坐标

      if (!worldPos || !worldPos.length) {
        return;
      } 
      wPos.setValue(...worldPos);        // 应该是跟上面调用的函数有关系，这样设置后就能拿到画布 x y 坐标

      const displayPosition = wPos.getComputedDisplayValue(renderer); // 拿到计算出来的值，即 x，y 画布位置
      const { svgWidgetManager } = api;
      api.svgWidgets.crosshairsWidget.setPoint( // 旋转工具，设置选中点
        displayPosition[0],
        displayPosition[1]
      );

      svgWidgetManager.render();
    });
  };

  publicAPI.updateCrosshairForApi = api => {
    if (!api.svgWidgets.crosshairsWidget) {
      // If we aren't using the crosshairs widget, bail out early.
      return;
    }

    const renderer = api.genericRenderWindow.getRenderer();
    let cachedCrosshairWorldPosition = api.get('cachedCrosshairWorldPosition');
    
    // 工具没开启的时候，读取不到这个值
    if (!cachedCrosshairWorldPosition) { return }

    const wPos = vtkCoordinate.newInstance();
    wPos.setCoordinateSystemToWorld();
    try {
      wPos.setValue(...cachedCrosshairWorldPosition);
    } catch (error) {
    }

    const doubleDisplayPosition = wPos.getComputedDoubleDisplayValue(renderer);
    const dPos = vtkCoordinate.newInstance();
    dPos.setCoordinateSystemToDisplay();

    dPos.setValue(doubleDisplayPosition[0], doubleDisplayPosition[1], 0);
    let worldPos = dPos.getComputedWorldValue(renderer);

    const camera = renderer.getActiveCamera();
    const directionOfProjection = camera.getDirectionOfProjection();
    const halfSlabThickness = api.getSlabThickness() / 2;

    // Add half of the slab thickness to the world position, such that we select
    //The center of the slice.

    for (let i = 0; i < worldPos.length; i++) {
      worldPos[i] += halfSlabThickness * directionOfProjection[i];
    }
    publicAPI.moveCrosshairs(worldPos, [api]);
  };
}

// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
  point: [null, null],
  worldPos: [0, 0, 0 ],
  strokeColor: '#00ff00',
  strokeWidth: 1,
  strokeDashArray: '',
  padding: 12,
  display: true,
  lineSize: 22,
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {


  Object.assign(model, DEFAULT_VALUES, initialValues);

  macro.obj(publicAPI, model);
  macro.get(publicAPI, model, ['widgetId']);
  macro.setGet(publicAPI, model, [
    'strokeColor',
    'strokeWidth',
    'strokeDashArray',
    'display',
  ]);
  macro.setGetArray(publicAPI, model, ['point'], 2);
  macro.setGetArray(publicAPI, model, ['worldPos'], 3);
  macro.setGet(publicAPI, model, ['padding']);

  model.crosshairsTool = initialValues

  vtkSVGCrosshairsWidget(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(extend, 'vtkSVGCrosshairsWidget');

// ----------------------------------------------------------------------------

export default { newInstance, extend };
