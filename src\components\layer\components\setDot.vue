<template>
    <div style="height: 100%;display: flex;flex-direction: column;">
        <div class="c-content">
            <span>点测量半径值</span>
            <el-input-number v-model="dotRadius" :min="1" :max="24" label="" size="small"></el-input-number>
        </div>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setDot',
    data() {
        return {
            storageKey: 'configs-dot',
            dotRadius: 6
        }
    },
    mounted() {
        this.dotRadius = getConfigByStorageKey(this.storageKey);
    },
    methods: {
        onClickSave() {
            // 设置新的缓存
            localStorage.setItem(this.storageKey, JSON.stringify(this.dotRadius));
            cornerstoneTools.store.state.circleRadius = this.dotRadius;

            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    flex: 1;
    padding-top: 10px;
    > span{
        padding-right: 20px;
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>