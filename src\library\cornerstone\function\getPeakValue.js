import csTools from '$library/cornerstone/cornerstoneTools'
const external = csTools.external;

/**
 * 获取峰值
 * @param {*} element        元素
 * @param {*} roiCoordinates 坐标
 * @param {*} pixelSpacing   像素间距
 * @returns 
 */
export default function(element, roiCoordinates, pixelSpacing) {
    // 取最大值坐标
    const startMaxPixelCoordinate = getMaxPixelCoordinate(
        element,
        roiCoordinates.left,
        roiCoordinates.top,
        roiCoordinates.width,
        roiCoordinates.height
    );

    // 获取像素数据距离
    const mm = 10; // 10毫米
    const distanceY = Math.round(mm / pixelSpacing.rowPixelSpacing) / 2;
    
    // 半圆坐标
    const endMaxPixelCoordinate = {
        x: startMaxPixelCoordinate.x,
        y: startMaxPixelCoordinate.y - distanceY,
    };

    // 获取圆边界信息
    const circleCoordinates = getCircleCoords(
        startMaxPixelCoordinate,
        endMaxPixelCoordinate
    );

    // 通过边界获取全部像素
    const pixels = external.cornerstone.getPixels(
        element,
        circleCoordinates.left,
        circleCoordinates.top,
        circleCoordinates.width,
        circleCoordinates.height
    );
    // 峰值（均值）
    const peak = calculateEllipsePeak(
        pixels,
        circleCoordinates
    );

    return peak
}

// 在图像坐标中检索圆的边界
function getCircleCoords(startHandle, endHandle) {
    const { distance } = external.cornerstoneMath.point;
    const radius = distance(startHandle, endHandle);

    return {
        left: Math.floor(Math.min(startHandle.x - radius, endHandle.x)),
        top: Math.floor(Math.min(startHandle.y - radius, endHandle.y)),
        width: radius * 2,
        height: radius * 2,
    };
}

// 如果点在椭圆内则返回true
function pointInEllipse(ellipse, location) {
    const xRadius = ellipse.width / 2;
    const yRadius = ellipse.height / 2;

    if (xRadius <= 0.0 || yRadius <= 0.0) {
        return false;
    }

    const center = {
        x: ellipse.left + xRadius,
        y: ellipse.top + yRadius,
    };

    /* 圆方程的一般形式
     *
     * X^2/a^2 + Y^2/b^2 <= 1
     */

    const normalized = {
        x: location.x - center.x,
        y: location.y - center.y,
    };

    const inEllipse =
        (normalized.x * normalized.x) / (xRadius * xRadius) +
        (normalized.y * normalized.y) / (yRadius * yRadius) <=
        1.0;

    return inEllipse;
}

// 计算感兴趣的峰值。
function calculateEllipsePeak(sp, ellipse) {
    let sum = 0;
    let count = 0;
    let index = 0;

    for (let y = ellipse.top; y < ellipse.top + ellipse.height; y++) {
        for (let x = ellipse.left; x < ellipse.left + ellipse.width; x++) {
            const point = {
                x,
                y,
            };
            // 在区域中
            if (pointInEllipse(ellipse, point)) {
                sum += sp[index];
                count++;
            }

            index++;
        }
    }

    if (count === 0) {
        return 0.0;
    }
    // 全部数据值 / 数量
    const peak = sum / count;
    return peak;
}


// 获取最大值像素的坐标
function getMaxPixelCoordinate(element, x, y, width, height) {
    if (element === undefined) {
        throw new Error("getStoredPixels: parameter element must not be undefined");
    }

    x = Math.round(x);
    y = Math.round(y);
    const enabledElement = external.cornerstone.getEnabledElement(element);
    const storedPixels = [];
    let index = 0;
    const pixelData = enabledElement.image.getPixelData();
    const result = {
        value: 0,
        coordinate: {},
    };

    for (let row = 0; row < height; row++) {
        for (let column = 0; column < width; column++) {
            const spIndex = (row + y) * enabledElement.image.columns + (column + x);
            if (pixelData[spIndex] > result.value) {
                result.value = pixelData[spIndex];
                result.coordinate = { x: column + x, y: row + y };
            }
            storedPixels[index++] = pixelData[spIndex];
        }
    }
    // 返回像素坐标
    return result.coordinate;
}
