import macro from 'vtk.js/Sources/macro';

import csTools from '$library/cornerstone/cornerstoneTools'
import getConfigByStorageKey from '$library/utils/configStorage.js'

let instanceId = 1;

function getWidgetNode(svgContainer, widgetId) {
  let node = svgContainer.querySelector(`#${widgetId}`);
  if (!node) {
    node = document.createElement('div');
    node.setAttribute('id', widgetId);
    node.setAttribute('style', `position: absolute; left: 0; top: 0; width: 0; height: 0; z-index: 2;`);
    svgContainer.appendChild(node);
  }
  return node;
}

// ----------------------------------------------------------------------------

function vtkSVGMarkWidget(publicAPI, model) {
  model.classHierarchy.push('vtkSVGMarkWidget');
  model.widgetId = `vtkSVGMarkWidget-${instanceId++}`;
  const storageList = {}

  publicAPI.render = (svgContainer, scale) => {  // scale = 1
    requestAnimationFrame(() => {

      if (!model.api) return
      const node = getWidgetNode(svgContainer, model.widgetId);
      node.innerHTML = ''

      const storageId = 'getStorageId()'
      
      const storageDataList = storageList[storageId] || []

      const width = parseInt(svgContainer.clientWidth, 10);
      const height = parseInt(svgContainer.clientHeight, 10);
      const textOffsetTop = -16
      const textOffsetLeft = -16
      
      const configInfo = getConfigByStorageKey('configs-tools');
      const toolsColor = getConfigByStorageKey('configs-toolsColor');
      const customFontFamily = configInfo.fontFamily
      const customColor =  toolsColor['TextMarker'] || csTools.toolColors.getFillColor() || '#00FF00'

      const renderer = model.api.genericRenderWindow.getRenderer();
      const addLabelNode = (item) => {
        const labelNode = document.createElement('div')
        const labelNodeHandler = document.createElement('i')
        const originPoint = item.markPoint.slice(); 

        
        
        const p = [...originPoint];
        // p[0] = p[0];
        // p[1] = height - p[1];
        const showedText =   `${item.markText || ''}` 

        labelNode.setAttribute('style', `
          position: absolute;
          display: inline-block;
          white-space: nowrap;
          margin: 0;
          top: ${(0.5 + p[1] / 200) * height}px;
          left: ${(0.5 + p[0] / 200) * width}px;
          width: fit-content;
          height: fit-content;
          bottom: 0;
          right: 0;
            font-size  : ${configInfo.fontSize}px;
            font-weight : bold;
            font-family: ${customFontFamily};
            text-shadow: 1px 1px 1px black;
            filter: drop-shadow(0 0 2px black);
            color       : ${customColor};`)
        labelNode.innerText = showedText

        labelNodeHandler.setAttribute('class','markspan el-dialog__close el-icon el-icon-close')   
        labelNodeHandler.setAttribute('title','删除标注')   
        labelNodeHandler.setAttribute('style', `position: absolute;
        top: -5px;
        right: -15px;
        width: 12px;
        height: 12px;
        font-size: 12px;
        cursor: pointer;`)

        labelNodeHandler.addEventListener('mousedown', (e) => {
          e.stopPropagation()
          e.preventDefault();
          storageDataList.forEach((item, index) => {
            if (item.markPoint[0] == originPoint[0] && item.markPoint[1] == originPoint[1] ) {
              storageDataList.splice(index, 1)
            }
          })
          labelNode.remove()
          return
        })

        labelNode.appendChild(labelNodeHandler)

        node.appendChild(labelNode)

      }

      storageDataList.forEach(item => {
        addLabelNode(item)
      })

      const { 
        markPoint,
        markText,
        api
      } = model;

      if (markPoint[0] && markPoint[1]) {
        addLabelNode({ 
          markPoint,
          markText,
        })
      }

    })
  };

  publicAPI.addMarkPointData = (p, api) => {
    if (!model.api) model.api = api 

    // 世界位置转化为ComputedDoubleDisplayValue 可以直接用在切片上定位
    const { svgWidgetManager, genericRenderWindow } = api;
    
    const markText = prompt('请输入标注内容', '标注')

    const pointData = {
      markPoint: p,
      markText
    } 
    const storageId = 'getStorageId()'


    if (!storageList[storageId]) storageList[storageId] = []
    const storageDataList = storageList[storageId]

    storageDataList.push(pointData)

    svgWidgetManager.render(); 


  }


  publicAPI.moveMark = (p, api) => {
    if (!model.api) model.api = api  
    api.svgWidgets.markWidget.setMarkPoint(
      ...p
    ); 
    api.svgWidgets.markWidget.setMarkText(
      '标注'
    );
    
    const { svgWidgetManager } = api;

    svgWidgetManager.render();

  };

  publicAPI.cancelTempPoint = (api) => {
    if (!model.api) model.api = api
    model.markPoint = [null, null]
    model.api.svgWidgetManager.render();
  };


  publicAPI.deletePointStorage = (p, api) => {
    const storageId = 'getStorageId()' 
    const list = storageList[storageId] || []
    list.forEach((item, index) => {
      if (positionIsEqual(item.markPoint, p)) {
        list.splice(index, 1)
      }
    })
    
  };

  publicAPI.clearPointStorage = () => {
    const storageId = 'getStorageId()'
    storageList[storageId] = []
    if (!model.api) return  
    const { svgWidgetManager } = model.api;
    svgWidgetManager.render();
  };

  // function getStorageId() {
  //   const renderer = model.api.genericRenderWindow.getRenderer();
  //   const camera = renderer.getActiveCamera()
  //   const directionId = camera.getDirectionOfProjection().reduce((prev, curr) => String(prev) + (+curr).toFixed(5), '')
  //   const positionId = camera.getPosition().reduce((prev, curr) => String(prev) + String(Math.round(+curr)), '') // bugfix: 取整，否则初次取值和初次滚动后的值有偏差(每次滚动坐标变动1)
  //   return directionId + positionId
  // }

  function positionIsEqual(a, b, diff = 20) { 
    if (!a || !b || !a.every || !b.every) {
      return false
    }
    return a.every((v, i) => {
      return Math.abs(v - b[i]) < diff
    })
  }

}

// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
  markPoint: [null, null],
  markText: '',
  api: null
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {
  Object.assign(model, DEFAULT_VALUES, initialValues);

  macro.obj(publicAPI, model);
  macro.get(publicAPI, model, ['widgetId']);
  macro.setGet(publicAPI, model, [
    'markText',
    'api'
  ]);

  macro.setGetArray(publicAPI, model, ['markPoint'], 2);

  vtkSVGMarkWidget(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(
  extend,
  'vtkSVGMarkWidget'
);

// ----------------------------------------------------------------------------

export default { newInstance, extend };
