import { cloneDeep } from "lodash-es";
import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js';

import getConfigByStorageKey from '$library/utils/configStorage.js'

const showRebuild = getConfigByStorageKey('configs-show-rebuild')
const mipMagnify = showRebuild.mipMagnify

function daysInMonth(m, y) {
	// m is 0 indexed: 0-11
	switch (m) {
	  case 2:
		return y % 4 == 0 && y % 100 || y % 400 == 0 ? 29 : 28;
  
	  case 9:
	  case 4:
	  case 6:
	  case 11:
		return 30;
  
	  default:
		return 31;
	}
}
export default {
	// 保留两位小数
	formatNumberPrecision(number, precision) {
		if (number !== null) {
			return parseFloat(number).toFixed(precision);
		}
	},

	// 获取压缩情况
	getCompression(imageId) {
		const generalImageModule =
			cornerstone.metaData.get('generalImageModule', imageId) || {};
		const {
			lossyImageCompression,
			lossyImageCompressionRatio,
			lossyImageCompressionMethod,
		} = generalImageModule;

		if (lossyImageCompression === '01' && lossyImageCompressionRatio !== '') {
			const compressionMethod = lossyImageCompressionMethod || 'Lossy: ';
			const compressionRatio = formatNumberPrecision(
				lossyImageCompressionRatio,
				2
			);
			return compressionMethod + compressionRatio + ' : 1';
		}

		return 'Lossless/Uncompressed';
	},

	/**
	 * 获取方向
	 * @param {*} rowCosines                     行余弦
	 * @param {*} columnCosines 				 列余弦
	 * @param {*} rotationDegrees 				 当前旋转角度
	 * @param {*} isFlippedVertically 			 垂直翻转
	 * @param {*} isFlippedHorizontally          水平翻转
	 * @returns 
	 */
	getOrientationMarkers(rowCosines, columnCosines, rotationDegrees, isFlippedVertically, isFlippedHorizontally) {

		if (rowCosines === null || columnCosines === null || rowCosines === undefined || columnCosines === undefined) {
			return {
				top: '',
				left: ''
			}
		}
		const {
			getOrientationString,
			invertOrientationString,
		} = cornerstoneTools.orientation;

		const rowString = getOrientationString(rowCosines);
		const columnString = getOrientationString(columnCosines);
		const oppositeRowString = invertOrientationString(rowString);
		const oppositeColumnString = invertOrientationString(columnString);

		const markers = {
			top: oppositeColumnString,
			left: oppositeRowString,
		};

		// 图像有水平、翻转变化
		if (isFlippedVertically) {
			markers.top = invertOrientationString(markers.top);
		}
		if (isFlippedHorizontally) {
			markers.left = invertOrientationString(markers.left);
		}

		// 角度变化
		if (rotationDegrees === 90 || rotationDegrees === -270) {
			return {
				top: markers.left,
				left: invertOrientationString(markers.top),
			};
		} else if (rotationDegrees === -90 || rotationDegrees === 270) {
			return {
				top: invertOrientationString(markers.left),
				left: markers.top,
			};
		} else if (rotationDegrees === 180 || rotationDegrees === -180) {
			return {
				top: invertOrientationString(markers.top),
				left: invertOrientationString(markers.left),
			};
		}
		return markers;
	},
	// 获取 url信息
	searchParse() {
		let resultObj = {};
		let searchUrl = window.location.hash.split('?', 2)[1];
		if (searchUrl && searchUrl.length > 1) {
			let search = searchUrl;
			let items = search.split('&');
			for (let index = 0; index < items.length; index++) {
				if (!items[index]) {
					continue;
				}
				let kv = items[index].split('=');
				resultObj[kv[0]] = typeof kv[1] === "undefined" ? "" : kv[1];
			}
		}
		return resultObj;
	},

	/**
	 * 填充2d视图
	 * @param {*} view           vtk view
	 * @param {*} normal         法线
	 * @param {*} newZPosition   新的图像 z 轴位置
	 */
	fill2DView(view, normal, newZPosition = {}) {
		// Based this code: https://github.com/Kitware/paraview-glance/issues/230#issuecomment-445779222

		const bounds = view.getRenderer().computeVisiblePropBounds();

		// 新的 z 轴位置
		if (newZPosition && newZPosition.max != undefined) {
			bounds[5] = newZPosition.max;
			bounds[4] = newZPosition.min;
		}

		const dim = [
			(bounds[1] - bounds[0]) / 2,
			(bounds[3] - bounds[2]) / 2,
			(bounds[5] - bounds[4]) / 2,
		];

		const w = view.getContainer().clientWidth;
		const h = view.getContainer().clientHeight;
		const r = w / h;

		const position = ['x', 'y', 'z'];

		let viewName = 'x';
		let idx = normal.indexOf(1);
		if (normal.length && idx !== -1) {
			viewName = position[idx];
		}

		let x;
		let y;
		if (viewName === 'x') {
			x = dim[1];
			y = dim[2];
		} else if (viewName === 'y') {
			x = dim[0];
			y = dim[2];
		} else if (viewName === 'z') {
			x = dim[0];
			y = dim[1];
		}
		
		// 设置相机焦点、位置
	    const distance = view.getRenderer().getActiveCamera().getDistance();
		let xPoint = (bounds[0] + bounds[1]) / 2;
		let yPoint = (bounds[2] + bounds[3]) / 2;
		let zPoint = (bounds[5] + bounds[4]) / 2;
		view.getRenderer().getActiveCamera().setFocalPoint(xPoint, yPoint, zPoint);
		view.getRenderer().getActiveCamera().setPosition(0, -distance, zPoint);

		if (r >= x / y) {
			// use width
			view.getRenderer().getActiveCamera().setParallelScale(y + 1);
		} else {
			// use height
			let value = x / r + 1

			// 图像宽度比高度高（不是全身的）
			if (y < x) {
				// 按照 Y 尽量缩放
				const ratio = (x / y) - 1.4
				if (ratio >= 0) {
					value = (y / r + 1) / 0.8
				}else {
					value = y / r + 1
				}

				if ((x / 1.4) > y) {
					value = x / r + 1
				}
			}
            // 配置 true 用这个方式放大（按照高度放大百分之80%）
            if (mipMagnify) {
                value = (y + 1) / 0.8
            }
			view.getRenderer().getActiveCamera().setParallelScale(value);  // x / r + 1
		}
		view.resize();
	},

	/**
	 * 
	 * @param {*} sourceStackIdx 当前序列的下标
	 * @param {*} sourceStackLen 当前序列的长度
	 * @param {*} targetStackLen 目标序列的长度
	 * @returns 目标序列的下标
	 */
	getIndexBySeriesNumber(sourceStackIdx, sourceStackLen, targetStackLen) {

		sourceStackIdx += 1;
		let scaleValue = targetStackLen / sourceStackLen;
		// let targetNewIdx = Math.round(sourceStackIdx * scaleValue) - 1 <= 0 ? 0 : Math.round(sourceStackIdx * scaleValue) - 1
		return Math.round(sourceStackIdx * scaleValue) - 1 || 0;
	},
	
	/**
	 * 是否是截屏图
	 * @param {*} desc       序列描述
	 * @param {*} ClassUid   二次截图
	 * @param {*} isBody     是否判断全身（全是不能重建，也不算截图）
	 * @returns 
	 */
	isCaptrue(desc, ClassUid, isBody = false, imageType){

		// 如果 ClassUid 是 1.2.840.10008.5.1.4.1.1.7 就当做截图

        if (ClassUid === '1.2.840.10008.5.1.4.1.1.7'){

            // imageTypeLower.includes('screen')
            // 如果没有描述就当做原图
            // if (!imageType && desc.includes('DXA')) {
            //     return false
            // }

            // if (desc == 'injector') {
            //     return true
            // }

			const filters = ['3D', 'LOCALIZER', 'PATIENT_INFO', 'EXECUTED_SURVIEW'];
			// 截图图像类型包含这些就不当作截图看
			if ( imageType && filters.some(s => imageType.includes(s)) ) {
				return false;
			}
		  	return true;
		}
		
		if (desc == null || ClassUid == null || desc == undefined || ClassUid == undefined){
		  return false;
		}
	  
		// 描述中存在 screen 就当做截图
		const _desc = desc.toLocaleLowerCase()

		// 全是不能重建，也不算截图
		if (isBody && (_desc.includes('scout') || _desc.includes('total'))) {
			return true;
		}

		if (_desc.includes('screen')){
			return true;
		}

		return false;
	},

	deepClone: cloneDeep,

	// 深拷贝
	_deepClone(target) {
		// 定义一个变量
		let result;
		// 如果当前需要深拷贝的是一个对象的话
		if (typeof target === 'object') {
			// 如果是一个数组的话
			if (Array.isArray(target)) {
				result = []; // 将result赋值为一个数组，并且执行遍历
				for (let i in target) {
					// 递归克隆数组中的每一项
					result.push(this.deepClone(target[i]))
				}
				// 判断如果当前的值是null的话；直接赋值为null
			} else if (target === null) {
				result = null;
				// 判断如果当前的值是一个RegExp对象的话，直接赋值    
			} else if (target.constructor === RegExp) {
				result = target;
			} else {
				// 否则是普通对象，直接for in循环，递归赋值对象的所有值
				result = {};
				for (let i in target) {
					result[i] = this.deepClone(target[i]);
				}
			}
			// 如果不是对象的话，就是基本数据类型，那么直接赋值
		} else {
			result = target;
		}
		// 返回最终结果
		return result;
	},

	/**
	 * 通过 iop 获取其法线 
	 * @param {*} iop  tag [0020,0037]
	 * @returns [x,y,z] 那个位置最大代表该图像偏向那一面 x == 矢，y == 冠，z == 轴
	 */
	getNormal(iop, getAngle = false){
		// 第一组向量
		let ax = iop[0],
			ay = iop[1],
			az = iop[2];
		// 第二组向量
		let bx = iop[3],
			by = iop[4],
			bz = iop[5];
		
		let out = [0,0,0];

		// 叉乘 -- 取绝对值
		out[0] = Math.abs(ay * bz - az * by);
		out[1] = Math.abs(az * bx - ax * bz);
		out[2] = Math.abs(ax * by - ay * bx);

		if (getAngle){
			const angle = ['sagittal', 'coronal', 'axial']
			let max = out[0];
			//声明了个变量 保存下标值
			let index = 0;
			for (let i = 1; i < out.length; i++) {
				if (max < out[i]) {
					max = out[i];
					index = i;
				}
			}
			// z轴的时候，存在倾斜严重，不使用 z轴
			// if (index === 2) {
			// 	if (out[0] > 0.4) {
			// 		index = 0
			// 	}else if (out[1] > 0.4) {
			// 		index = 1
			// 	}
			// }
			return angle[index]
		}
		return out;
	},
	isItalic(iop) {
		// 第一组向量
		let ax = iop[0],
			ay = iop[1],
			az = iop[2];
		// 第二组向量
		let bx = iop[3],
			by = iop[4],
			bz = iop[5];
		
		let out = [0,0,0];

		// 叉乘 -- 取绝对值
		out[0] = Math.abs(ay * bz - az * by);
		out[1] = Math.abs(az * bx - ax * bz);
		out[2] = Math.abs(ax * by - ay * bx);
		if ((out[0] > 0 && out[0] < 1) || (out[0] > 1 && out[1] < 1) || (out[2] > 0 && out[2] < 1)) {
			return true
		}
		return false
	},

	// 获取一个唯一值
	onlyValue(){
		return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
			let r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
			return v.toString(16);
		});
	},
	/**
	 * 通过 canvas 将截图转化成指定大小的图 
	 * @param {String} imageSrc 输入图片的链接
	 * @param {Function} callback 回调函数
	 * @param {Number} w 输出图片的width
	 * @param {Number} h 输出图片的wheight
	 * @param {String} format 输出图片的格式，jpeg或png
	 * @returns Void
	 */
	getCanvasScreenshot(imageSrc, callback, w = 1024, h = 1024, format = 'png') {
		const canvasContain = document.createElement('div')
		const canvas = document.createElement('canvas');
		canvas.setAttribute('width', w)
		canvas.setAttribute('height', h)
		canvasContain.appendChild(canvas)
		const ctx = canvas.getContext('2d');
		const img = new Image();
		img.onload = () => {
			ctx.drawImage(img, 0, 0, w, h); 
			const src = canvas.toDataURL("image/" + format, 1);
			callback(src)
		};
		img.src = imageSrc;
	},
	isValidDate(d, m, y) {
		// make year is a number
		if (isNaN(y)) {
		  return false;
		}
	  
		return m > 0 && m <= 12 && d > 0 && d <= daysInMonth(m, y);
	},
	// 转换ww wl成为 U L值
	transformVoiWLToUL(ww, wl, imageId, seriesUID){
		let w = ww
		let l = wl

		const { patientWeight, correctedDose, decayFactor } = getPatientWeightAndCorrectedDose(imageId, seriesUID);
                    
		const transitionWW = (1000 * patientWeight * w) / (correctedDose * decayFactor);
		const transitionWL = (1000 * patientWeight * l) / (correctedDose * decayFactor);

		const temp = transitionWW / 2;
		const u = transitionWL + temp;
			l = transitionWL - temp;
		return {u, l};  
	},
	// 转换U L成为 ww wl值
	transformULtoVoiWL(ww, wl, imageId, seriesUID){
		let w = ww
		let l = wl

		if (!imageId) return {w, l};
		try {

			let { patientWeight, correctedDose, decayFactor } = getPatientWeightAndCorrectedDose(imageId, seriesUID);

			if (patientWeight && correctedDose) {
				let transitionWW = (ww * (correctedDose * decayFactor)) / 1000 / patientWeight;
				let transitionWL = (wl * (correctedDose * decayFactor)) / 1000 / patientWeight;
	
				w = transitionWW - transitionWL;
				l = (transitionWW + transitionWL) / 2;
			}
			
		} catch (error) {
			console.error(error)
		}
		
		return {w, l};
	},
	triggerLayoutFullscreen() {
		const isHideTool = this.$store.state.hideTools
		const isLayoutFill = this.$store.state.layoutFill
        const isHideRragTools = this.$store.state.hideRragTools
		const state = isHideTool && isLayoutFill && isHideRragTools

		this.$store.commit('SET_HIDETOOLS', !state)
        this.$store.commit('SET_HIDERAGTOOLS', !state)
		this.$store.commit('setLayoutFill', !state)
        
		localStorage.setItem('configs-layoutFill', !state)
		this.$nextTick(() => {
			const myEvent = new Event('resize')
			window.dispatchEvent(myEvent)
		})
		setTimeout(() => {
			const myEvent = new Event('resize')
			window.dispatchEvent(myEvent)
		}, 0);
	},
	triggerShortcutVisible() {
		const shortcutVisible = this.$store.state.shortcutVisible		
		this.$store.commit('setShortcutVisible', !shortcutVisible)  
	}
}
