<template>
    <section class="c-viewport" :style="styleObj">
        <div oncontextmenu="return false" ref="element" :style="styleObj">
            <canvas class="cornerstone-canvas" ref="canvas"/>
            <ViewportSign class="sign" :iIsGraphic="iIsGraphic" :iIsPrinted="iIsPrinted"></ViewportSign>
        </div>
        <p v-show="isShowLen">{{ imageIdsLen }}</p>
        <div class="i-shade"></div>
    </section>
</template>
<script>
import ViewportSign from '$components/ViewportSign'
export default {
    components: {
        ViewportSign
    },
    props: {
        imageIds: {                // 图像
            type: Array,
            default: () => {
                return []
            },
            studyUID: {
                type: String,
                default: '',
            },
            seriesUID: {
                type: String,
                default: ''
            }
        },
        iIsGraphic: {
            type: [Number, String],
            default: 0
        },
        iIsPrinted: {
            type: [Number, String],
            default: 0
        },
        isShowLen: {
            type: <PERSON>olean,
            default: true
        },
        styleObj: {
            type: Object,
            default: () => {
                return {
                    width: '100px',
                    height: '100px'
                }
            }
        }
    },
    data() {
        return {
            element: null,
            cornerstoneOptions: {
            },
            imageIdsLen: 0
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.element = this.$refs.element;
            cornerstone.enable(this.element, this.cornerstoneOptions);
            this.init();
        })
    },
    methods: {
        async init(){
            let imageIds = this.imageIds.slice(0)
            // 判断是否多帧
            const frameImageIds = await this.loadFrameImage()
            if (frameImageIds.length) {
                imageIds =  frameImageIds
            }

            const imageIndex = Math.floor(imageIds.length / 2);

            this.imageIdsLen = imageIds.length
            // 如果序列某一张用 requestPoolManager 请求过，压缩不会突然崩..
            cornerstoneTools.requestPoolManager.addRequest(
                {},
                imageIds[imageIndex],
                'thumbnail',
                false,
                () => {
                    // 加载图像
                    cornerstone.loadAndCacheImage(imageIds[imageIndex]).then(image => {
                        // 设置 viewport 默认值
                        let defViewport = cornerstone.getDefaultViewport(this.element, image);
                        // 显示
                        cornerstone.displayImage(this.element, image, defViewport);

                        cornerstone.reset(this.element);
                        this.setDefaultInvert(image);
                        cornerstone.resize(this.element);
                    });
                },
                () => {}
            );
            cornerstoneTools.requestPoolManager.startGrabbing();
        },
        // 加载多帧
        async loadFrameImage() {
            let imageIds = [];
            if (this.imageIds.length === 1 && !this.imageIds[0].includes('?frame=') && !this.imageIds[0].includes('http://')) {
                let url = this.imageIds[0].slice(9)
                await cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.load(url, cornerstoneWADOImageLoader.internal.xhrRequest).then((dataSet) => {
                    let numFrames = dataSet.intString('x00280008');
                    if (numFrames && numFrames > 1){
                        for(let i=0; i < numFrames; i++) {
                            const imageId = 'dicomweb:' + url + "?frame=" + i;
                            imageIds.push(imageId);
                        }
                        // 展开多帧图像，改变外部图像ids
                        this.$emit('onFrameImages', imageIds);
                    }
                })
            }
            return imageIds;
        },
        // 设置反片
        setDefaultInvert(img) {
            const seriesModule = cornerstone.metaData.get('generalSeriesModule', img.imageId) || {};
            // 包含这几种 dicom 反色
            let inverts = ['PE', 'PET', 'PT', 'NM']
            if ( seriesModule.modality && inverts.includes( seriesModule.modality.toLocaleUpperCase() ) ){
                const viewport = cornerstone.getViewport(this.element);
                viewport.invert = true;

                const desc = img.data.string('x0008103e') || null;
                const classUid = img.data.string('x00080016') || null;
                const imageType = img.data.string('x00080008') || null;
                if (this.$fun.isCaptrue(desc, classUid, false, imageType)){
                    return
                }
                cornerstone.setViewport(this.element, viewport);
            }
        },
    },
}
</script>
<style lang="scss">
.c-viewport{
    position: relative;

    margin: 0 auto;
    margin-bottom: 10px;
    > p {
        position: absolute;
        right: 0px;
        bottom: 0px;
        background: black;
        color: white;
        font-size: 12px;
        padding: 2px;
    }
    .i-shade{
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        border: 1px solid #ebeef5;
    }
    .sign {
        position: absolute;
        top: 2px;
        left: 2px;
    }
}
</style>