<template>
    <div style="height: 100%;display: flex;flex-direction: column;">
        <div class="c-content">
            <div class="item">
                <span>阅图显示其它设备:</span>
                <div class="box">
                    <el-checkbox-group v-model="showReadModality">
                        <el-checkbox v-for="item in modalitys" :label="item"></el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
            <div class="item">
                <span>阅图翻页方式:</span>
                <div class="box">
                    <el-radio-group class="radio-group" v-model="readDcmSeting.isSrollAll">
                        <el-radio v-for="item in pageSeting" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="item">
                <span>阅图多选同步:</span>
                <div class="box">
                    <el-radio-group class="radio-group" v-model="readDcmSeting.selectSync">
                        <el-radio v-for="item in optionSelectSync" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="item">
                <span>原厂截图序列合并:</span>
                <div>
                    <el-switch
                        v-model="readDcmSeting.mergeMri"
                        active-color="#13ce66"
                        inactive-color="#eeeeee"
                        :active-value="true"
                        :inactive-value="false">
                    </el-switch>
                </div>
                <div v-if="readDcmSeting.mergeMri">
                    <p style="padding: 20px 0 10px;">不需要合并序列描述 <el-button type="primary" size="mini" @click="onClickAdd" plain>添加</el-button></p>
                    <div class="not-box">
                            <div v-for="(item, index) in mergeNo" :key="index" style="margin-right: 10px;display: flex;align-items: center; margin-bottom: 10px;">
                                <el-input v-model="item.value" size="mini" placeholder="序列描述" style="width: 90px; margin-right: -1px;"></el-input>
                                <el-button size="mini" @click="onClickDel(index)">删除</el-button>
                            </div>
                    </div>
                </div>
            </div>

        </div>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setShowRead',
    data() {
        return {
            modalitys: ['NM', 'PT', 'CT', 'MR', 'CR', 'OT'],
            storageKey: 'configs-show-read-modality',
            showReadModality: [],

            storageKeyPageSeting: 'configs-readDcmSeting',
            pageSeting: [
                {
                    value: 0,
                    name: '整页'
                },
                {
                    value: 1,
                    name: '单页'
                }
            ],
            optionSelectSync: [
                {
                    value: true,
                    name: '同步'
                },
                {
                    value: false,
                    name: '不同步'
                }
            ],
            readDcmSeting: {
                isSrollAll: true,
                selectSync: false,
                mergeMri: false,
                mergeNo: [],
            },
            mergeNo: [],
        }
    },
    mounted() {
        this.showReadModality = getConfigByStorageKey(this.storageKey);
        this.readDcmSeting = getConfigByStorageKey(this.storageKeyPageSeting);

        // 默认值,以前保存过，所有要添加默认值
        if (!this.readDcmSeting.mergeNo) {
            this.readDcmSeting.mergeNo = ['screen', 'pages']
        }
        this.mergeNo = this.readDcmSeting.mergeNo.map(item => {
            return {
                value: item,
            }
        })
    },
    methods: {
        onClickSave() {

            localStorage.setItem(this.storageKey, JSON.stringify(this.showReadModality));

            this.readDcmSeting.mergeNo = this.mergeNo.map(item => {
                return item.value
            })
            localStorage.setItem(this.storageKeyPageSeting, JSON.stringify(this.readDcmSeting));

            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        onClickDel(index) {
            this.mergeNo.splice(index, 1);
        },
        onClickAdd() {
            this.mergeNo.push({
                value: '',
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    flex: 1;
    padding-top: 10px;
    .item {
        margin-bottom: 40px;
        > span{
            font-size: 15px;
            display: block;
            padding-bottom: 20px;
        }
    }
    .not-box {
        display: flex;
        flex-wrap: wrap;
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>