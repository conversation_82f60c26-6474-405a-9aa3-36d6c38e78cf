<template>
    <div class="mpr-item image-area" 
        :style="styles" 
        v-loading="loading"
        :class="{'i-active': activeSelect, 'xx-full-screen': fullScreen}"
        element-loading-background="rgba(122,163,192,0.2)"
        :element-loading-text="loadingText">
        <div class="item-list-action" @click="setViewportActive(0)" v-if="isFun">
            <h6>{{desc}}</h6>
            <div>
                <div class="item-action" :class="{'i-disable': !layer1series || originalImage != 1}" @click="onClickMIP">
                    <span>MIP</span>
                    <!-- <i class="el-icon-caret-bottom"></i>
                    <ul class="s-ul">
                        <li @click="onClickMIP('y')"><span>100mm</span></li>
                    </ul> -->
                </div>
                <div class="item-action" :class="{'i-disable': modalityTypeFake != 'CT' || originalImage == 3}">
                    <span>融合</span>
                    <i class="el-icon-caret-bottom"></i>
                    <ul class="s-ul i-width-auto">
                        <li v-for="item in fuseLayers" :class="{ 'i-select': layer2series && layer1series === item.seriesUID }" :key="item.seriesUID" @click="onClickFuse(item)"><span>{{ item.seriesDesc }} </span> <span>{{ item.count || 0 }}张</span></li>
                    </ul>
                </div>
                <div class="item-action" :class="{'i-disable': !layer1series || originalImage != 1}">
                    <span>MPR</span>
                    <i class="el-icon-caret-bottom"></i>
                    <ul class="s-ul">
                        <li @click="onClickSliceFace('y')" :class="{'i-active': sliceFace == 'y' && viewType != 'vtk'}"><span>Coronal</span><span>冠状位</span></li>
                        <li @click="onClickSliceFace('x')" :class="{'i-active': sliceFace == 'x' && viewType != 'vtk'}"><span>Sagittal</span><span>矢状位</span></li>
                        <li @click="onClickSliceFace('z')" :class="{'i-active': sliceFace == 'z' && viewType != 'vtk'}"><span>Axial</span><span>横截面</span></li>
                    </ul>
                </div>
            </div>
            <!-- <PopperLayer>
                <template slot>
                    <li class="popover-lis--item">顺时针 90</li>
                    <li class="popover-lis--item">顺时针 120</li>
                </template>
            </PopperLayer> -->
        </div>
        <div class="item-view" @dblclick="onDblclickViewport" @mousedown="onContextShow">

            <ViewMPRCornerstone v-if="viewType == 'cs' && !loading"
                ref="mprViewport"
                :id="id"
                :activeSelect="activeSelect"
                :patientId="patientId"
                :showAction="showAction"
                :propIsOverlayVisible.sync="isOverlayVisible"
                :propActiveTool.sync="activeTool"
                :propCrosshairsTool.sync="crosshairsTool.mipShow"
                :modalityTypeFake.sync="modalityTypeFake"
                :originalImage.sync="originalImage"
                @setCrosshairs="setCrosshairs"
                @clearSelectTool="clearSelectTool"
				@selectTool="selectTool"
                @onToolRenderCompleted="onToolRenderCompleted"
                @setViewportActive="setViewportActive"
                @onNewImage="onNewImage"
                @renderCallBack="renderCallBack"
                :layer1series="layer1series"
                :layer2series="layer2series"
                :curSeriesChange="curSeriesChange"
                :sliceFace.sync="sliceFace">
            </ViewMPRCornerstone>
            <ViewMPRVtk v-else-if="viewType == 'vtk' && !loading"
                ref="vtkViewport"
                :id="id"
                :activeSelect="activeSelect"
                :tabId="tabId"
                :activeTool.sync="activeTool"
                :crosshairsTool.sync="crosshairsTool"
                :layer1series="layer1series"
                :layer2series="layer2series"
                :propIsOverlayVisible.sync="isOverlayVisible"
                :isFun="isFun"
                @setViewportActive="setViewportActive"
                @vtkChangeCsIndex="vtkChangeCsIndex"
                @getVtkVolumeInfo="getVtkVolumeInfo"
                @setFullscreen="onDblclickViewport"
                @emitApi="emitApi"
            ></ViewMPRVtk>
        </div>
        <div noprint="true" class="c-select-shade c-select-shade-top"></div>
        <div noprint="true" class="c-select-shade c-select-shade-right"></div>
        <div noprint="true" class="c-select-shade c-select-shade-bottom"></div>
        <div noprint="true" class="c-select-shade c-select-shade-left"></div>
    </div>
</template>
<script>
/**
 * 工作：
 * 1、下载图像..
 * 2、区分显示 cornerstone vtk 视图
 * appState 体存放地方
 */
import ViewMPRCornerstone from './ViewMPRCornerstone.vue'
import ViewMPRVtk from './ViewMPRVtk.vue'

// 重建图像中非横截面不重建.只下图..
import appState from '$library/cornerstone/mpr/store/appState.js';
export default {
    components: {
        ViewMPRCornerstone,
        ViewMPRVtk
    },
    props: {
        imageInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        id: {
            type: String
        },
        tabId: {
            type: String
        },
        fuseLayers: {
            type: Array,
            default: () => {
                return []
            }
        },
        styles: {
            type: Object,
            default: () => {
                return {}
            }
        },
        activeSelect: {         // 选中当前组件
            type: Boolean,
            default: false
        },
        fullScreen: {
            type: Boolean,
            default: false
        },
        showAction: {
            type: Boolean,
            default: false
        },
        propActiveTool: {    // 当前工具
            type: String,
            default: 'Wwwc'
        },
        propCrosshairsTool: {
            type: Object,
            default: () => { }
        },
        propIsOverlayVisible: {
            type: Boolean,
            default: true
        },
        isFun: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            desc: '',  // 描述
            percentage: { loadCount: 0, total: 0 }, // 加载进度
            patientId: '',       // 
            layer1series: '',  // 第一层序列 id
            layer2series: '',  // 第二层序列 id
            sliceFace: 'z',    // z = 横截面 y 冠状位, x 矢状位
            viewType: 'none',  // cs vtk none
            modalityTypeFake: '',
            curSeriesChange: false,  // 拖拽或者打开是 false。点击融合，点击正切位 true
            originalImage: 1,  // 1 重建图，2可融合不重建原始图，3 不重建原始图
        }
    },
    watch: {
        imageInfo: {
            handler(later, bef) {
                const before = bef || {}
                if (!later.studyUID || !later.seriesUID) {
                    return;
                }
                if (before.studyUID != later.studyUID || before.seriesUID != later.seriesUID) {
                    this.loadImage()
                }else {
                    // // 拖的是一样的。有可能是其它vtk显示，或者非横截面显示
                    // const dragSliceFase = this.getSliceFase(this.layer1Meta.imagePlaneModule)

                    // // 显示的正切不一样，设置为默认正切位
                    // if (dragSliceFase != this.sliceFace) {
                    //     this.onClickSliceFace(dragSliceFase)
                    // }

                }
            },
            deep: true,
            immediate: true,
        },
        styles: {
            handler() {
                setTimeout(() => {
                    if (this.$refs.mprViewport) {
                        cornerstone.resize(this.$refs.mprViewport.$el.querySelector('.viewport-element'))
                    }
                }, 0);
            },
            deep: true
        },
        viewType: {
            handler(later, before) {
                if (
                    (later == 'vtk' && before == 'cs') ||
                    (later == 'cs' && before == 'vtk')
                ) {
                    // cs 显示改为 vtk 显示，向外告知
                    this.$emit('changeViewType', later)
                }
            }
        }
    },
    computed: {
        loading() {
            // 加载
            return this.percentage.total && this.percentage.loadCount != this.percentage.total
        },
        loadingText() {
            // 加载提示
            if (!this.percentage.total || !this.percentage.loadCount) return 0 + '%'
            const val = Math.floor((this.percentage.loadCount / this.percentage.total) * 100)
            return val + '%'
        },
        activeTool: {
            get: function() {
                return this.propActiveTool
            },
            set: function(val) {
                this.$emit('update:propActiveTool', val)
            }
        },
        crosshairsTool: {
            get: function() {
                return this.propCrosshairsTool
            },
            set: function(val) {
                this.$emit('update:propCrosshairsTool', val)
            }
        },
        isOverlayVisible: {
            get: function() {
                return this.propIsOverlayVisible
            },
            set: function(val) {
                this.$emit('update:propIsOverlayVisible', val)
            }
        }
    },
    methods: {
        /**
         * 初始体相应序列。如果有体返回空。没有体，返回需要下载的序列 id
         */
        setVolumesSeries(studyUID, seriesUID) {
            
            if (!studyUID && !seriesUID) {
                return []
            }

            // 没有这个体信息，设置空对象
            if (!appState[seriesUID]) {
                appState[seriesUID] = {}
            }

            // 没有体数据, 添加体数据相应的序列
            if (!appState[seriesUID].vtkVolumes) {
                const instanceList = this.$store.state.seriesMap.get(studyUID).instanceList
                
                if (instanceList.get(seriesUID)) {
                    const imageIds = instanceList.get(seriesUID).imageIds
                    appState[seriesUID].series = imageIds
                    return appState[seriesUID].series
                }else {
                    appState[seriesUID].series 
                }

            }

            return []
        },
        loadImage() {
            this.curSeriesChange = false
            // 第一层序列，第二层序列
            this.layer1series = this.imageInfo.seriesUID  || ''
            this.layer2series = this.imageInfo.seriesUID1 || ''
            this.desc         = this.imageInfo.desc       || ''
            const studyId     = this.imageInfo.studyUID   || this.imageInfo.studyUID1

            this.$emit('selectStudyId', studyId)

            this.patientId = this.$store.state.seriesMap.get(studyId).seriesInfo.key

            // 设置体相应序列，如果没有体，返回序列
            let imageIds1 = this.setVolumesSeries(this.imageInfo.studyUID, this.imageInfo.seriesUID)
            let imageIds2 = this.setVolumesSeries(this.imageInfo.studyUID1, this.imageInfo.seriesUID1)

            // 把序列拼接，在同一下载
            const allImageIds = imageIds1.concat(imageIds2)

            // 设置进度条总数
            this.percentage.total = allImageIds.length
            this.percentage.loadCount = 0

            // 下载序列
            const ImagePromise = allImageIds.map((imageId) => {
                // cornerstone 下载
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1  // 进度条进度+1
                });
            });
            // 同一下载
            Promise.all(ImagePromise).then(() => {
                // 下载完成
                // this.builderImageIds()
                // 需要显示视图类型（cornerstone vtk）
                this.viewType = this.imageInfo.viewType || 'cs'
                // 关闭进度条
                this.percentage.total = 0
                this.percentage.loadCount = 0

                // 按照第一个切片位置分割
                const groupByLocationSeries = (data) => {
                    if (data.length === 0) return [];

                    const anchorValue = data[0].sliceLocation + ''

                    const groups = []
                    let groupIndex = -1

                    data.forEach(item => {
                        if ((item.sliceLocation + '') == anchorValue) {
                            groupIndex += 1
                            groups[groupIndex] = []
                        }
                        groups[groupIndex].push(item.imageId)
                    })

                    return groups;
                };
                if (this.desc.includes('DWI')) {
                    // 包含 dwi 的图像就读取看看是否可以拆开
                    const metaDataMap = [];

                    const data = appState[this.imageInfo.seriesUID]
                    data.series.forEach(imageId => {
                        // TODO: Retrieve this from somewhere other than Cornerstone
                        const metaData = cornerstone.metaData.get('imagePlaneModule', imageId);
                        // metaDataMap.set(imageId, metaData);
                        metaDataMap.push(Object.assign({imageId}, metaData))
                    });


                    const result = groupByLocationSeries(metaDataMap);

                    const ids = []
                    if (result.length >= 2) {
                        result.forEach((item, index) => {
                            const id = this.imageInfo.seriesUID+'-'+index
                            appState[id] = {
                                series: item
                            }

                            ids.push(id)
                        })
                    }
                    this.$emit('setNewlayout', { 
                        seriesUID: this.imageInfo.seriesUID, 
                        studyUID: this.imageInfo.studyUID, 
                        list: ids 
                    })
                }
            })
        },
        // 点击改变切面
        onClickSliceFace(type) {
            this.curSeriesChange = true
            // 改变的是一样的，就不在执行
            if (this.viewType == 'cs' && type == this.sliceFace) {
                return
            }
            this.viewType = 'cs'
            this.sliceFace = type
        },
        onClickMIP() {
            if (!this.layer1series) {
                return
            }
            this.viewType = 'vtk'
        },
        // 点击融合
        onClickFuse(item) {
            this.curSeriesChange = true
            if (this.layer1series == item.seriesUID) {
                // 点击的融合是当前融合层
                // 退出融合
                this.layer1series = this.layer2series
                this.layer2series = ''

                this.$emit('setLayerFuse', { 
                    fuse: false
                })


            }else {
                this.layer2series = this.layer2series || this.layer1series
                this.layer1series = item.seriesUID


                this.$emit('setLayerFuse', { 
                    fuse: true,
                    studyUID: item.studyUID, 
                    seriesUID: item.seriesUID
                })
            }

            let imageIds = this.setVolumesSeries(item.studyUID, item.seriesUID)

            // 设置进度条总数
            this.percentage.total = imageIds.length
            this.percentage.loadCount = 0

            // 下载
            // 下载序列
            const ImagePromise = imageIds.map((imageId) => {
                // cornerstone 下载
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1  // 进度条进度+1
                });
            });

            // 同一下载
            Promise.all(ImagePromise).then(() => {
                // 下载完成
                // 关闭进度条
                this.percentage.total = 0
                this.percentage.loadCount = 0

                setTimeout(() => {
                    this.$emit('onClickFuse')
                }, 0)
            })
        },
        onContextShow(e) {
            if (e.button !== 2) { return }
            let isMore = 0
            document.onmousemove = (e) => {
                isMore += 1
            }
            document.onmouseup = () => {
                if (isMore <= 2) {
                    this.$emit('onContextShow', e)
                }else {
                    this.$emit('onContextShow', e, true)
                }
                document.onmousemove = document.onmouseup = isMore = null
            }
        },
        /**
         * 双击全屏
         */
        onDblclickViewport(){
            this.$emit('onDblclickViewport', this.element)
        },
        renderCallBack(sourceViewport, clayData, type, isSend = false){
            this.$emit('renderCallBack', sourceViewport, clayData, type, isSend)
        },
        onNewImage(obj, lifecycle) {
            this.$emit('onNewImage', obj, lifecycle)
        },
        setViewportActive(val) {
            this.$emit('setViewportActive', val)
        },
        clearSelectTool() {
            this.$emit('clearSelectTool')
        },
        selectTool(toolData) {
            this.$emit('selectTool', toolData)
        },
        onToolRenderCompleted(sourceElement, sourceOrientation, status) {
            this.$emit('onToolRenderCompleted', sourceElement, sourceOrientation, status)
        },
        setCrosshairs() {
            this.$emit('setCrosshairs')
        },
        vtkChangeCsIndex(worldPos, uid, camera, volume) {
            this.$emit('vtkChangeCsIndex', worldPos, uid, camera, volume)
        },
        getVtkVolumeInfo(info) {
            this.$emit('getVtkVolumeInfo', this.id, info)
        },
        emitApi(api) {
            this.$emit('emitApi', api)
        }
    },
    mounted() {
        // window.seriesMap = this.$store.state.seriesMap
        // window.appState = appState
    },
}
</script>
<style lang="scss">
.mpr-item{
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
    box-sizing: border-box;

    .c-select-shade{
        position: absolute;
        background: #444;
        z-index: 3;
        &.c-select-shade-top{
            width: 100%;
            height: 1px;
        }
        &.c-select-shade-right{
            width: 1px;
            height: 100%;
            right: 0px;
        }
        &.c-select-shade-bottom{
            width: 100%;
            height: 1px;
            bottom: 0px;
        }
        &.c-select-shade-left{
            width: 1px;
            height: 100%;
            left: 0px;
        }
    }
    &.i-active {
        .c-select-shade {
            background: #409eff;
            &.c-select-shade-top{
                height: 3px;
            }
            &.c-select-shade-right{
                width: 3px;
            }
            &.c-select-shade-bottom{
                height: 3px;
            }
            &.c-select-shade-left{
                width: 3px;
            }
        }
    }
    
    .item-list-action{
        display: flex;
        font-size: 13px;
        height: 16px;
        line-height: 16px;
        color: #cecece;
        background: #212121;
        border-bottom: 1px solid #444;
        z-index: 3;
        > h6{
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            padding-left: 2px;
        }
        > div{
            display: flex;
        }
        .item-action{
            position: relative;
            cursor: pointer;
            padding: 0px 4px;
            border-left: 1px;
            border-right: 1px;
            border-style: solid;
            border-color: #142b4b;
            &.i-disable{
                cursor: no-drop;
            }
            &:not(.i-disable) {
                &:hover{
                    background: #5c5c5c;
                    .s-ul{
                        display: block;
                    }
                }
            }
            &:first-child{
                border-right: none;
            }
            .s-ul{
                display: none;
                width: 120px;
                position: absolute;
                right: 0px;
                color: #cecece;
                background: #000000;
                padding: 2px;
                z-index: 3;
                &.i-width-auto{
                    width: auto;
                    span {
                        &:first-child{
                            padding-left: 20px;
                            // min-width: 100px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        &:last-child{
                            width: 50px;
                            flex: none;
                        }
                    }
                }
                li {
                    position: relative;
                    display: flex;
                    justify-content: space-between;
                    height: 24px;
                    line-height: 24px;
                    margin-bottom: 2px;
                    &:last-child{
                        margin-bottom: 0px;
                    }
                    &:hover{
                        background: #303e53;
                    }
                    &.i-active{
                        background: #5c5c5c !important;
                        cursor: initial;
                        &:hover{
                            background: inherit;
                        }
                    }
                    &.i-select{
                        &::before{
                            font-size: 20px;
                            position: absolute;
                            top: 5px;
                            left: 5px;
                            content: "*";
                            color: #267aee;
                        }
                    }
                    span{
                        flex: 1;
                    }
                }
            }
        }
    }
    .item-view{
        flex: 1;
        height: 0px; // vtk 显示不加这个高会被撑开
    }
    &.xx-full-screen {
        position: absolute !important;
        width: 100% !important;
        height: 100% !important;
        z-index: 1002 !important;
    }
}
</style>