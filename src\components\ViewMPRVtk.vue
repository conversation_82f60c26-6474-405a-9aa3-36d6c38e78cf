<template>
    <View2D v-if="volumes.length"
        class="vtk-viewport"
        :onCreated='storeApi()'
        :scrollRotate="scrollRotate"
        :volumes="volumes"
        :imageId="imageId"
        :orientation="orientation"
        :isFun="isFun"
        :isOverlayVisible.sync="isOverlayVisible"
        @setViewportActive="setViewportActive">
         <ul noprint="true" class="c-action" v-if="isFun">
            <li @click="onClickRotate(0)" title="正面">F</li>
            <li @click="onClickRotate(180)" title="背面">B</li>
            <li @click="onClickRotate(271)" title="左侧">L</li>
            <li @click="onClickRotate(91)" title="右侧">R</li>
            <li @click="onClickVTKFullscreen" title="全屏"> <i class="el-icon-full-screen"></i> </li>
            <li @click="clearVtkWindowMark(index)" title="清除标注"> <i class="el-icon-delete"></i>
            </li>
        </ul>
    </View2D>
</template>
<script>
import { View2D, vtkSVGCrosshairsWidget, invertVolume, vtkInteractorStyleMPRSlice,
	vtkInteractorStyleMPRCrosshairs,
    vtkSVGProbeWidget,
    vtkInteractorStyleProbe,
    vtkSVGMarkWidget,
    vtkSVGArrowWidget,
    vtkInteractorStyleMark,
    vtkInteractorStyleArrow,
    vtkInteractorStyleMPRWindowLevel } from "$library/vtk";
import getVtkVolumeBySeriesUid from '$library/cornerstone/function/getVtkVolumeBySeriesUid.js'
import getConfigByStorageKey from '$library/utils/configStorage.js';

export default {
    components: {
        View2D
    },
    props: {
        layer1series: {
            type: String,
            default: ''
        },
        layer2series: {
            type: String,
            default: ''
        },
        activeSelect: {         // 选中当前组件
            type: Boolean,
            default: false
        },
        activeTool: {    // 当前工具
            type: String,
            default: 'NuclearCrosshairs'
        },
        crosshairsTool: {
            type: Object,
            default: () => { }
        },
        id: {
            type: String
        },
        tabId: {
            type: String
        },
        propIsOverlayVisible: {
            type: Boolean,
            default: true
        },
        isFun: {
            type: Boolean,
            default: true
        }
    },
    watch: {
        allSeries: {
            handler() {
                this.getVolumes()
            },
            immediate: true
        },
        activeTool: {
            handler(later) {
                if (later === 'Wwwc') {
                    this.vtkToggleWL('Wwwc')
                }else if (later === 'vtkInteractorStyleMPRSlice') {
                    this.vtkToggleToMPRSlice('vtkInteractorStyleMPRSlice')
                }else if (['DragProbe', 'TextMarker', 'ArrowAnnotate'].includes(later)) {
                    // this.vtkToggleToProbe('DragProbe')
                    this.vtkToggleToSomeMarks(later)
                }else {
                    this.vtkToggleToCrosshairs('NuclearCrosshairs')
                }
            }
        },
        'crosshairsTool.mipShow': {
            handler(later) {
                const showMipCross = !this.$store.state.hideReferenceLines[this.tabId] // 定位线按钮是否开启
                // 控制定位线显示与否
                const { svgWidgetManager, svgWidgets } = this.api;
                this.$nextTick(() => {
                    svgWidgets.crosshairsWidget.setDisplay(showMipCross)
                    svgWidgetManager.render()
                })

                if (later) {
                    this.vtkToggleToCrosshairs('NuclearCrosshairs')
                }
            }
        }
    },
    computed: {
        allSeries() {
            return this.layer1series + ',' + this.layer2series
        },
        isOverlayVisible: {
            get: function() {
                return this.propIsOverlayVisible
            },
            set: function(val) {
                this.$emit('update:propIsOverlayVisible', val)
            }
        }
    },
    data() {
        return {
            volumes: [],
            imageId: '',
            scrollRotate: true,
            vtkActiveTool: 'Wwwc',
            orientation: { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] },
            api: {},
            imageIdsGroup: []
        }
    },
    methods: {
        getVolumes() {
            if (!this.layer1series) {
                return
            }
            this.volumes = []
            getVtkVolumeBySeriesUid(this.layer1series,
                this.layer2series
            ).then(res => {
                setTimeout(() => {
                    let { volumes, imageId, imageIdsGroup } = res
                    this.volumes = volumes
                    this.imageId = imageId
                    this.imageIdsGroup = imageIdsGroup

                    this.$emit('getVtkVolumeInfo', {imageIdsGroup, volumes, imageId})

                    this.$forceUpdate()
                }, 200);
            })
        },
        storeApi() {
            return (api) => {
                this.api = api;
                this.$emit('emitApi', api)
                const renderWindow = api.genericRenderWindow.getRenderWindow();
                
                api.addSVGWidget(
					vtkSVGCrosshairsWidget.newInstance(this.crosshairsTool),
					"crosshairsWidget"
				);

                api.addSVGWidget(
					vtkSVGProbeWidget.newInstance(),
					"probeWidget"
				);
                
                api.addSVGWidget(
                    vtkSVGMarkWidget.newInstance(),
                    "markWidget"
                );

                api.addSVGWidget(
                    vtkSVGArrowWidget.newInstance(),
                    "arrowWidget"
                );


				const istyle = vtkInteractorStyleMPRCrosshairs.newInstance({scrollRotate: this.scrollRotate});
				// 操作功能
				api.setInteractorStyle({
					istyle,
                    configuration: { apis: [api], apiIndex: 0 },
				});

                

                // 设置最大密度投影
				const mapper = api.volumes[0].getMapper();
				if (mapper.setBlendModeToMaximumIntensity) {
					mapper.setBlendModeToMaximumIntensity();
				}
                // 密谱图厚度 先快速加载一个小的厚度，再更新
                api.setSlabThickness(1);
                const targetThickness = api.getMaxLabThickness() // 最大厚度
                const intervalNum = 100; // 间隔时间

                setTimeout(() => {
                    api.setSlabThickness(Math.round(targetThickness));
                }, intervalNum);


                this.$fun.fill2DView(api.genericRenderWindow, api.getSliceNormal());
            
                // 是否反片
                cornerstone.loadAndCacheImage(api.imageId).then(image => {
                    const seriesModule = cornerstone.metaData.get('generalSeriesModule', image.imageId);
                    // PT、NM 才默认反片
                    const isInvert = seriesModule && ['PT', 'NM'].includes(seriesModule.modality);

                    api.changeInvert(isInvert) 

                    // 渲染窗口
                    renderWindow.render();

                    this.vtkToggleToCrosshairs('NuclearCrosshairs', true)

                    // 如果, 一开始显示的时候工具就是调窗,就设置它
                    this.$nextTick(() => {
                        if (this.activeTool === 'Wwwc') {
                            this.vtkToggleWL('Wwwc')
                        }
                    })
                });

            }
        },
        vtkToggleToMPRSlice(name) {
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name
            const { svgWidgetManager, svgWidgets } = this.api
            svgWidgets.crosshairsWidget.setDisplay(false)
            svgWidgetManager.render()
            
            this.$nextTick(() => {
                const istyle = vtkInteractorStyleMPRSlice.newInstance({scrollRotate: this.scrollRotate});
                this.api.setInteractorStyle({
                    istyle,
                    configuration: { apis: [this.api], apiIndex: 0 },
                });
            })

        },
        // 定位线
		vtkToggleToCrosshairs(name, reset = false) {
            if (this.activeTool === this.vtkActiveTool && !reset) {
                return
            }
            this.vtkActiveTool = name
            const apis = [this.api];
            const api  = this.api;
            
            const { svgWidgetManager, svgWidgets } = this.api;
            // console.log(svgWidgets.crosshairsWidget.setDisplay(false)) // 可以获取、设置 svg
            if (reset) {
                this.$nextTick(() => {
                    const defCrosshairShow = getConfigByStorageKey('configs-crosshairs-show'); // 默认显示定位线
                    svgWidgets.crosshairsWidget.setDisplay(defCrosshairShow)
                    svgWidgets.crosshairsWidget.resetCrosshairs(apis, 0)
                    svgWidgetManager.render();

                    if (!api.crosshairsModified) {
                        api.crosshairsModified = true
                        api.svgWidgets.crosshairsWidget.onModified(res => {
                            const worldPos = res.getWorldPos();
                            // 触发执行事件，改变 cornerstone 
                            // this.vtkChangeCsIndex(worldPos)

                            // 这些是用来计算三维定位坐标的参数
                            const renderer = api.genericRenderWindow.getRenderer(); 
                            const camera = renderer.getActiveCamera(); 

                            // 触发执行事件，改变 cornerstone 
                            this.$emit('vtkChangeCsIndex', worldPos, this.id, camera, api.volumes[0])
                        });
                    }
                })
            }else {
                const istyle = vtkInteractorStyleMPRCrosshairs.newInstance({scrollRotate: this.scrollRotate});
                // 操作功能
                api.setInteractorStyle({
                    istyle,
                    configuration: { apis, apiIndex: 0 },
                })
                svgWidgets.crosshairsWidget.setDisplay(true)
                svgWidgetManager.render()
            }

		},
        vtkToggleWL(name) {
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name

            let istyle = vtkInteractorStyleMPRWindowLevel.newInstance({scrollRotate: this.scrollRotate});
            
            const callbacks = {
                setOnLevelsChanged: voi => {
                    const { windowWidth, windowCenter } = voi;

                    this.api.updateVOI(windowWidth, windowCenter)
                    this.api.genericRenderWindow.getRenderWindow().render();
                }
            }
            
            this.api.setInteractorStyle({
                istyle,
                callbacks,
                configuration: { apis: [this.api], apiIndex: 0 },
            });
        }, 
        vtkToggleToSomeMarks(name) {
            
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name
            const apis = [this.api];
            if(!this.activeSelect) return
            apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets } = api;
                svgWidgetManager.render();
                let interactor
                switch (name) {
                    case 'TextMarker':
                        interactor = vtkInteractorStyleMark
                        break;
                    case 'DragProbe':
                        interactor = vtkInteractorStyleProbe
                        break;
                    case 'ArrowAnnotate':
                        interactor = vtkInteractorStyleArrow
                        break;
                
                    default:
                        break;
                }
                let istyle = interactor.newInstance({scrollRotate: this.scrollRotate});

                api.setInteractorStyle({
                	istyle,
                	configuration: { apis, apiIndex, endCallback: () => {
                        if (['TextMarker', 'ArrowAnnotate'].includes(name)) {
                            this.activeTool = 'Airtools';
                        }
                    } },
                }); 

            });

        },
        // 点击其它面
        onClickRotate(angle){
            const renderWindow = this.api.genericRenderWindow.getRenderWindow();
            renderWindow.getInteractor().getInteractorStyle().getViewport().rotateAbsolute(0, angle);
            renderWindow.render();
            api.svgWidgetManager.render()
        },
        setViewportActive(val) {
            this.$emit('setViewportActive', val)
        },
        onClickVTKFullscreen() {
            this.$emit('setFullscreen')
        },
        clearVtkWindowMark() {
            this.api.svgWidgets.markWidget.clearPointStorage()
            this.api.svgWidgets.probeWidget.clearPointStorage()
            this.api.svgWidgets.arrowWidget.clearPointStorage()
        },
    },
}
</script>
<style lang="scss" scoped>
.vtk-viewport{
    &:hover{
        .c-action{
            display: block;
        }
    }
}
.c-action{
    display: none;
    position: absolute;
    top: 44%;
    right: 2px;
    margin-top: -64px;
    height: 128px;
    z-index: 1;
    li{
        width: 30px;
        height: 30px;
        line-height: 30px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background: white;
        cursor: pointer;
        margin-bottom: 2px;
        &:hover{
            background: #eee;
        }
        i{
            font-size: 18px;
        }
    }
}
</style>