<template>
    <el-dialog
        append-to-body
        :title="title"
        :visible.sync="innerVisible"
        @close="closeDialog"
        @open="openDialog"
        :close-on-click-modal="false"
        width="720px"
        custom-class="my-dialog">
        <div class="c-main">
            <div class="c-left">
                <div class="item table-box">
                    <el-table border ref="tableRow" :data="rebuildMate" :row-class-name="tableRowClassName" @row-contextmenu="rowContextMenu" @row-click="onClickRow" size="mini" highlight-current-row
                    tooltip-effect="dark" style="width: 100%" height="100%">
                        <el-table-column label="位置" prop="sort" width="50"></el-table-column>
                        <el-table-column label="检查部位" prop="checkPart" width="80" show-overflow-tooltip></el-table-column>
                        <el-table-column label="名称" prop="customName" width="100" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{ scope.row.customName || scope.row.checkPart }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="显示方案" show-overflow-tooltip min-width="80">
                            <template slot-scope="scope">
                                <span>{{ getSchemeName(scope.row.scheme) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="序号" width="70">
                            <template slot-scope="scope">
                                <span>{{ scope.row.series1.seriesNumber }}|{{ scope.row.series2.seriesNumber }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="序列一" show-overflow-tooltip width="70">
                            <template slot-scope="scope">
                                <span>{{ scope.row.series1.checkModality + '：' + scope.row.series1.seriesDesc + '；' + scope.row.series1.imageDesc }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="序列二" show-overflow-tooltip width="70">
                            <template slot-scope="scope">
                                <span>{{ (scope.row.series2.checkModality || '') + '：' + (scope.row.series2.seriesDesc || '') + '；' + (scope.row.series2.imageDesc || '') }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="item-line">
                    <div class="form-item" style="width: 200px">
                        <span>检查部位</span>
                        <el-select v-model="item.checkPart" size="mini" style="width: 100px" placeholder="">
                            <el-option
                            v-for="item in options.checkPart"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="form-item" style="width: 220px">
                        <span>自定义名称</span>
                        <el-input size="mini" style="width: 130px" ref="customName"
                            v-model="item.customName">
                        </el-input>
                    </div>
                    <div class="form-item">
                        <span>位置</span>
                        <el-input size="mini" style="width: 64px" v-floorNumber
                            v-model="item.sort">
                        </el-input>
                    </div>
                </div>
                <div class="item-box">
                    <h6>序列一</h6>
                    <div class="box-content">
                        <div class="form-item" style="width: 200px">
                            <span>检查类型</span>
                            <el-select v-model="item.series1.checkModality" size="mini" style="width: 100px" placeholder="">
                                <el-option
                                v-for="item in options.checkModality"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="form-item" style="width: 220px">
                            <span>图像数目</span>
                            <el-select v-model="item.series1.mathSymbol" size="mini" style="width: 60px" placeholder="">
                                <el-option
                                v-for="item in options.mathSymbol"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                            <el-input size="mini" style="width: 70px;margin-left: 10px" v-floorNumber
                                v-model="item.series1.mathValue">
                            </el-input>
                        </div>
                        <div class="form-item">
                            <span>序号</span>
                            <el-input size="mini" style="width: 62px" v-floorNumber
                                v-model="item.series1.seriesNumber">
                            </el-input>
                        </div>
                        <div class="form-item">
                            <span>序列描述</span>
                            <el-input size="mini" style="width: 454px"
                                v-model="item.series1.seriesDesc">
                            </el-input>
                        </div>
                        <div class="form-item">
                            <span>图像描述</span>
                            <el-input size="mini" style="width: 454px"
                                v-model="item.series1.imageDesc">
                            </el-input>
                        </div>
                    </div>
                </div>
                <div class="item-line">
                    <div class="form-item" style="width: 200px">
                        <el-checkbox v-model="twoSeries">两个序列</el-checkbox>
                    </div>
                    <div class="form-item" style="width: 280px">
                        <span>显示方案</span>
                        <el-select v-model="item.scheme" size="mini" style="width: 140px" placeholder="">
                            <el-option
                            v-for="item in options.scheme"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                            </el-option>
                        </el-select>
                        <el-button type="mini" @click="onClickDisplayPlan" style="border-left: none;">...方案</el-button>
                    </div>
                </div>
                <div class="item-box">
                    <h6>序列二</h6>
                    <div class="box-content">
                        <div class="form-item" style="width: 200px">
                            <span>检查类型</span>
                            <el-select v-model="item.series2.checkModality" :disabled="!twoSeries" size="mini" style="width: 100px" placeholder="">
                                <el-option
                                v-for="item in options.checkModality"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="form-item" style="width: 220px">
                            <span>图像数目</span>
                            <el-select v-model="item.series2.mathSymbol" :disabled="!twoSeries" size="mini" style="width: 60px" placeholder="">
                                <el-option
                                v-for="item in options.mathSymbol"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                            <el-input size="mini" style="width: 70px;margin-left: 10px" v-floorNumber :disabled="!twoSeries"
                                v-model="item.series2.mathValue">
                            </el-input>
                        </div>
                        <div class="form-item">
                            <span>序号</span>
                            <el-input size="mini" style="width: 62px" v-floorNumber :disabled="!twoSeries"
                                v-model="item.series2.seriesNumber">
                            </el-input>
                        </div>
                        <div class="form-item">
                            <span>序列描述</span>
                            <el-input size="mini" style="width: 454px" :disabled="!twoSeries"
                                v-model="item.series2.seriesDesc">
                            </el-input>
                        </div>
                        <div class="form-item">
                            <span>图像描述</span>
                            <el-input size="mini" style="width: 454px" :disabled="!twoSeries"
                                v-model="item.series2.imageDesc">
                            </el-input>
                        </div>
                    </div>
                </div>
                <div class="item-footer">
                    <el-button type="small" @click="onClickAdd" icon="el-icon-plus">添 加</el-button>
                    <el-button type="small" @click="onClickUpdate" icon="el-icon-check" :disabled="item.index === undefined">修 改</el-button>
                    <el-button type="small" @click="innerVisible = false" icon="el-icon-close">关 闭</el-button>
                </div>

            </div>
            <div class="c-right">
                <div class="item-box">
                    <h6>相关设置</h6>
                    <div class="box-content right-box">
                        <span>默认重建数目</span>
                        <el-input size="mini" style="width: 100%" v-floorNumber
                            v-model="autoRebuildAbout.number">
                        </el-input>
                        <!-- <el-checkbox v-model="autoRebuildAbout.multipleNotAuto" :title="autoRebuildAbout.number + '次以上的检查不自动重建'">多次检查时手选</el-checkbox> -->
                        <el-button type="mini" @click="onClickAbout" icon="el-icon-check" >保 存</el-button>
                    </div>
                </div>
                <div class="item-box right-botton">
                    <span>数目差最大值</span>
                        <el-input size="mini" style="width: 100%" v-floorNumber :disabled="!twoSeries"
                        v-model="item.differ.number">
                    </el-input>
                    <span>序号差最大值</span>
                    <el-input size="mini" style="width: 100%" v-floorNumber :disabled="!twoSeries"
                        v-model="item.differ.series">
                    </el-input>
                </div>
            </div>


            <v-contextmenu ref="contextmenu">
                <v-contextmenu-item @click="onClickDel">删除</v-contextmenu-item>
            </v-contextmenu>
            <component :is="lazy.DisplayPlanSet" v-model="displayPlanSetVisible" @closeDialog="closePlanSet" :scheme="item.scheme"></component>
        </div>
        <!-- <div slot="footer" style="font-size: 12px;">
            *配置数据仅保存在本地，若需要长期保存请在“系统设置”中点击【配置方案 - 保存至服务器】
        </div> -->

    </el-dialog>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js';
const DisplayPlanSet = () => import('$components/layer/DisplayPlanSet');
import ModeDialog from "$src/mixin/ModeDialog.js";
import { dbDisplayPlan } from "$library/db";
export default {
    mixins: [ModeDialog],
    props: {
        selectItem: {
            type: Object,
            default: () => {}
        },
        type: {
            type: [Number, String],
            default: 0
        }
    },
    data() {
        return {
            title: '序列匹配',
            rebuildMateKey: 'configs-rebuild-mate',
            autoRebuildAboutKey: 'configs-rebuild-about',
            autoRebuildAbout: {
                number: 3,
                multipleNotAuto: true
            },
            rebuildMate: [],
            itemMenu: {},
            item: {
                scheme: 0,
                checkPart: '全身',
                customName: '',
                sort: '',
                differ: {
                    number: '',
                    series: '',
                },
                series1: {
                    checkModality: 'CT',
                    mathSymbol: 0,
                    mathValue: 0,
                    seriesNumber:'',
                    seriesDesc: '',
                    imageDesc: ''
                },
                series2: {
                    checkModality: 'PT',
                    mathSymbol: 0,
                    mathValue: 0,
                    seriesNumber:'',
                    seriesDesc: '',
                    imageDesc: ''
                }
            },
            options: {
                checkPart: [
                    { value: '全身', label: '全身'},
                    { value: '头部', label: '头部'}
                ],
                checkModality: [
                    { value: 'CT', label: 'CT'},
                    { value: 'PT', label: 'PT'},
                    { value: 'MR', label: 'MR'},
                    { value: 'NM', label: 'NM'}
                ],
                mathSymbol: [
                    { value: 0, label: '>='},
                    { value: 1, label: '='},
                ],
                scheme: []
            },
            twoSeries: true,
            displayPlanSetVisible: false,
            lazy: { },

        }
    },
    methods: {
        getSchemeName(id) {
            const item = this.options.scheme.find(item => item.id == id);
            if (item) {
                return item.name;
            }
            return '';
        },
        // 获取方案
        getScheme(loadData = false) {
            dbDisplayPlan.then((e) => {
                e.getGroup('displayPlan').then((e) => {
                    if (e.success) {
                        this.options.scheme = [ {id: 0, name: ' '} ]
                        if (e.data) {
                            this.options.scheme = [...this.options.scheme, ...e.data]
                        }
                        if (loadData) {
                            this.openDialog();
                        }
                    }
                });
            });
        },
        // 显示方案设置
        onClickDisplayPlan() {
            if (!this.lazy.DisplayPlanSet) {
                this.lazy.DisplayPlanSet = DisplayPlanSet;
            }
            this.displayPlanSetVisible = true
        },
        closePlanSet() {
            this.getScheme()
        },
        closeDialog() {
			this.$emit('update:dialogVisible', false);
		},
        // 打开弹窗
        async openDialog() {
            this.autoRebuildAbout = getConfigByStorageKey(this.autoRebuildAboutKey)
            this.rebuildMate      = getConfigByStorageKey(this.rebuildMateKey)

            // 赋值添加打开
            if (this.type === 1) {
                
                // 位置
                const lastItem = this.rebuildMate[this.rebuildMate.length - 1];
                if (lastItem) {
                    const num = Number(lastItem.sort)
                    if (num >= 0) {
                        this.item.sort = num + 1;
                    }
                }else {
                    this.item.sort = 1
                }

                if (this.selectItem.ct && (!this.selectItem.ct.seriesNumber || !this.selectItem.ct.imageType)) {
                    const image = await cornerstone.loadAndCacheImage(this.selectItem.ct.imageId);
                    this.selectItem.ct.imageType = image.data.string('x00080008') || ''
                    this.selectItem.ct.seriesNumber = image.data.string('x00200011') || ''
                }


                if (this.selectItem.pet && (!this.selectItem.pet.seriesNumber || !this.selectItem.pet.imageType)) {
                    const image = await cornerstone.loadAndCacheImage(this.selectItem.pet.imageId);
                    this.selectItem.pet.imageType = image.data.string('x00080008') || ''
                    this.selectItem.pet.seriesNumber = image.data.string('x00200011') || ''
                }

                if (this.selectItem.ct && this.selectItem.pet) {
                    // 序列一
                    this.item.series1.checkModality = this.selectItem.ct.sModality;
                    this.item.series1.mathValue = this.selectItem.ct.iInstanceCount;
                    this.item.series1.seriesNumber = this.selectItem.ct.seriesNumber;
                    this.item.series1.seriesDesc = this.selectItem.ct.sSeriesDescription;
                    this.item.series1.imageDesc = this.selectItem.ct.imageType.replace(/\\/g, '|');

                    // 序列二
                    this.item.series2.checkModality = this.selectItem.pet.sModality;
                    this.item.series2.mathValue = this.selectItem.pet.iInstanceCount;
                    this.item.series2.seriesNumber = this.selectItem.pet.seriesNumber;
                    this.item.series2.seriesDesc = this.selectItem.pet.sSeriesDescription;
                    this.item.series2.imageDesc = this.selectItem.pet.imageType.replace(/\\/g, '|');

                    this.twoSeries = true
                }else {
                    const series = this.selectItem.ct || this.selectItem.pet

                    this.item.series1.checkModality = series.sModality;
                    this.item.series1.mathValue = series.iInstanceCount;
                    this.item.series1.seriesNumber = series.seriesNumber;
                    this.item.series1.seriesDesc = series.sSeriesDescription;
                    this.item.series1.imageDesc = series.imageType.replace(/\\/g, '|')

                    this.item.series2 = {}

                    this.twoSeries = false
                }

                this.$nextTick(() => {
                    this.$refs.customName.focus()
                })
                return;
            }
            this.$nextTick(() => {
                if (this.rebuildMate[0]) {
                    this.item = this.$fun.deepClone(this.rebuildMate[0])

                    const exist = this.options.scheme.find(item => item.id == this.item.scheme)
                    if (!exist) {
                        this.item.scheme = 0
                    }

                    if (!this.item.series2 || !Object.keys(this.item.series2)) {
                        this.twoSeries = false
                    }else {
                        this.twoSeries = true
                    }

                    this.$refs.tableRow.setCurrentRow(this.rebuildMate[0])
                }
            })
        },
        // 点击行
        onClickRow(row) {
            this.item = this.$fun.deepClone(row)

            // 关联的方案被删除了，显示为 0
            const exist = this.options.scheme.find(item => item.id == this.item.scheme)
            if (!exist) {
                this.item.scheme = 0
            }

            if (!this.item.series2 || !Object.keys(this.item.series2).length) {
                this.twoSeries = false
            }else {
                this.twoSeries = true
            }
        },
        // 点击添加
        onClickAdd() {
            const newItem = this.$fun.deepClone(this.item)
            // 没有勾选二个序列， 删除第二个序列信息
            if (!this.twoSeries) {
                newItem.differ = {}
                newItem.series2 = {}
            }
            this.rebuildMate.push(newItem)
            this.$nextTick(() => {
                const lastItem = this.rebuildMate[this.rebuildMate.length - 1];
                this.$refs.tableRow.setCurrentRow(lastItem)
                this.item = this.$fun.deepClone(lastItem)
                this.updateStorage()
            })
        },
        // 点击修改
        onClickUpdate() {
            const index = this.item.index;
            const newItem = this.$fun.deepClone(this.item)
            // 没有勾选二个序列， 删除第二个序列信息
            if (!this.twoSeries) {
                newItem.differ = {}
                newItem.series2 = {}
            }
            this.rebuildMate.splice(index, 1, newItem)
            this.$nextTick(() => {
                this.$refs.tableRow.setCurrentRow(this.rebuildMate[index])
                this.updateStorage()
            })
        },
        // 点击删除
        onClickDel() {
            this.rebuildMate.splice(this.itemMenu.index, 1)

            // 点击的是选中的，设置其它选中
            if (this.item.index === this.itemMenu.index) {
                
                this.$nextTick(() => {
                    if (this.rebuildMate[0]) {
                        this.$refs.tableRow.setCurrentRow(this.rebuildMate[0])
                        this.item = this.$fun.deepClone(this.rebuildMate[0])
                    }else {
                        this.item.index = undefined
                    }
                    if (!this.item.series2 || !Object.keys(this.item.series2).length) {
                        this.twoSeries = false
                    }else {
                        this.twoSeries = true
                    }
                })
            }
            this.updateStorage()
        },
        /* 把每一行的索引放进row */
        tableRowClassName({row, rowIndex}) {
            row.index = rowIndex;
        },
        /* 右键事件打开菜单 */
		rowContextMenu(row, column, event){
            this.itemMenu = this.$fun.deepClone(row)
			event.preventDefault();
			this.contextmenuShow(event)
		},
		/* 显示右键菜单 */
		contextmenuShow (ev) {
			const postition = {
				top: ev.clientY,
				left: ev.clientX
			}
			this.$refs.contextmenu.show(postition)
		},

        // 点击相关保存
        onClickAbout() {
            localStorage.setItem(this.autoRebuildAboutKey, JSON.stringify(this.autoRebuildAbout))
            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        updateStorage() {
            localStorage.setItem(this.rebuildMateKey, JSON.stringify(this.rebuildMate))
            // this.$message.success('操作成功！')

            this.$store.dispatch('saveAllConfig')
        }
    },
    mounted() {
        this.getScheme(true)
    },
}
</script>
<style lang="scss" scoped>
.c-main{
    height: 586px;
    display: flex;

    .item-box{
        position: relative;
        padding: 10px;
        margin: 0px 0px 10px;
        border: 1px solid #dcdfe6;
        >h6{
            font-size: 14px;
            position: absolute;
            top: -10px;
            left: 10px;
            background: #fbfbfb;
        }
        .box-content{
            display: flex;
            flex-wrap: wrap;
            .form-item{
                margin-bottom: 14px;
                &:last-child{
                    margin-bottom: 0px;
                }
            }
        }
    }

    .c-left{
        flex: 1;
        .table-box{
            width: 532px;
            height: 150px;
            border: 1px solid #eee;
            border-bottom: 0px;
            margin-right: 10px;
        }
        .item-line{
            display: flex;
            padding: 10px;
        }
        .item-footer{
            display: block;
            margin-top: 14px;
            text-align: right;
            line-height: 32px;
            .c-tip {
                position: absolute;
                left: 20px;
                font-size: 13px;
            }
        }
    }
    .c-right{
        width: 144px;
        margin-left: 6px;
        .item-box{
            position: relative;
            top: 10px;
        }
        .right-box{
            height: 150px;
            > span{
                padding-top: 10px;
            }
            > button{
                width: 100%;
                align-self: center;
            }
        }
        .right-botton{
            left: -6px;
            width: 150px;
            height: 160px;
            padding-top: 20px;
            margin-top: 100px;
            border-left: white;
            > span{
                display: inline-block;
                padding-bottom: 6px;
            }
            > div{
                padding-bottom: 20px;
                &:last-child{
                    margin-bottom: 0px;
                }
            }
        }
    }
}
.form-item{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    span{
        display: inline-block;
        padding-right: 8px;
    }
}
</style>