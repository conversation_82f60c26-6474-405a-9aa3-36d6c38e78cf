/**
 * vtk 操作方法
 */

import vtkMath from 'vtk.js/Sources/Common/Core/Math'

import { vtkSVGCrosshairsWidget, invertVolume, vtkInteractorStyleMPRSlice,
    vtkSVGProbeWidget,
    vtkInteractorStyleProbe,
    vtkSVGMarkWidget,
    vtkSVGArrowWidget,
    vtkInteractorStyleMark,
    vtkInteractorStyleArrow,
	vtkInteractorStyleMPRCrosshairs, vtkInteractorStyleMPRWindowLevel, View2D } from "$library/vtk";
import getVtkVolumeBySeriesUid from '$library/cornerstone/function/getVtkVolumeBySeriesUid.js'

import appState from '$library/cornerstone/mpr/store/appState.js';

import vtkVolumeMapper from "vtk.js/Sources/Rendering/Core/VolumeMapper";
import vtkVolume from "vtk.js/Sources/Rendering/Core/Volume";

import findNearIndex from '$library/cornerstone/tools/math/findNearIndex.js';
import VoiMapping from '$library/cornerstone/function/VoiMapping.js';
import setClipPlane from '$library/vtk/lib/data/setClipPlane.js';
// import CoordsEvent from '$library/cornerstone/mpr/store/CoordsEvent.js';

import getConfigByStorageKey from '$library/utils/configStorage.js'


export default {
    components: {
        View2D
    },
    data() {
        return {
            viewports: [],               // 视图渲染数据
            activeViewportIndex: 0,      // 当前选中视图
            activeTool: 'Airtools',      // 当前工具
            toolRenderLast: false,       // 用于控制，在工具渲染后，不在触发点选状态（直接赋值选中）
            showAction: -1,              // 没有用
            apis: [],                    // 组件(vtk)中的方法 apis
            scrollRotate: true,          // 控制滚轮旋转
            isCrosshairsCallback: [], // 控制vtk定位线回调
            vtkActiveTool: 'Wwwc',
            // coords: null,

        }
    },
    mounted() {
        this.isCrosshairsCallback = []
        // 需要优化 定位线 TODO 
        // this.coords = new CoordsEvent.getInstance();
    },
    methods: {
        // // 设置当前选中的视窗
		// setViewportActive(index){
        //     this.activeViewportIndex = index

        //     if (this.toolRenderLast) {
        //         this.toolRenderLast = false
        //         this.showAction = this.activeViewportIndex
        //         return;
        //     }
        //     // 选中出现工具
		// 	this.showAction = this.activeTool === 'Airtools' && this.showAction === index ? -1 : index
		// },
        // 点击其它面
        onClickRotate(angle, index){
            const api = this.apis[index];
            if(!api){
                return;
            }
            const renderWindow = api.genericRenderWindow.getRenderWindow();
            renderWindow.getInteractor().getInteractorStyle().getViewport().rotateAbsolute(0, angle);
            renderWindow.render();
            api.svgWidgetManager.render()
        },
        // 保存组件中的方法到 apis 中
		storeApi(viewportIndex) {
			return (api) => {
				this.apis[viewportIndex] = api;
                const apis = this.apis;
                
                const sourceGroup = api.group
                // 获取渲染的窗口
				const renderWindow = api.genericRenderWindow.getRenderWindow();
                
                // 添加 svg 工具- 定位线名称 crosshairsWidget...用于控制显示等
				api.addSVGWidget(
					vtkSVGCrosshairsWidget.newInstance(this.crosshairsTool),
					"crosshairsWidget"
				);
                api.addSVGWidget(
					vtkSVGProbeWidget.newInstance(),
					"probeWidget"
				);

                api.addSVGWidget(
                    vtkSVGMarkWidget.newInstance(),
                    "markWidget"
                );

                api.addSVGWidget(
                    vtkSVGArrowWidget.newInstance(),
                    "arrowWidget"
                );

				const istyle = vtkInteractorStyleMPRCrosshairs.newInstance({scrollRotate: this.scrollRotate});
				// 操作功能
                const filterApis = []
                for (let index = 0; index < apis.length; index++) {
                    const api = apis[index];
                    if (!api || api.group !== sourceGroup) {
                        filterApis.push(undefined)
                    }else {
                        filterApis.push(api)
                    }
                }
				api.setInteractorStyle({
					istyle,
					configuration: { apis: filterApis, apiIndex: viewportIndex },
				});

                // 设置最大密度投影
				const mapper = api.volumes[0].getMapper();
				if (mapper.setBlendModeToMaximumIntensity) {
					mapper.setBlendModeToMaximumIntensity();
				}

                const key = this.groupUid[sourceGroup].ptId + '&' + this.groupUid[sourceGroup].noPtId;
                const clipMip = appState[key].clipMip;
                // 设置裁剪
                // if (clipMip) {
                //     setClipPlane(mapper, clipMip);
                // }

                // 存入，在重置使用
                const newZPosition = clipMip ? {max: clipMip.max, min: clipMip.min} : {};
                api.newZPosition = newZPosition;

                // 层厚 --- 200会出现一根线
                // 密谱图厚度 先快速加载一个小的厚度，再更新
                api.setSlabThickness(1);
                const targetThickness = api.getMaxLabThickness() // 最大厚度
                const intervalNum = 100; // 间隔时间

                setTimeout(() => {
                    api.setSlabThickness(Math.round(targetThickness));
                }, intervalNum);

                // 缩放到适应窗口
                this.$fun.fill2DView(api.genericRenderWindow, api.getSliceNormal(), newZPosition);

                // 是否反片
                cornerstone.loadAndCacheImage(api.imageId).then(image => {
                    const seriesModule = cornerstone.metaData.get('generalSeriesModule', image.imageId);
                    // PT、NM 才默认反片
                    const isInvert = seriesModule && ['PT', 'NM'].includes(seriesModule.modality);

                    api.changeInvert(isInvert)

                    // 渲染窗口
                    renderWindow.render();
                    // const apiObj = apis[viewportIndex];
                    // apiObj.svgWidgets.crosshairsWidget.resetCrosshairs(apis, viewportIndex);
                    this.vtkToggleToCrosshairs('NuclearCrosshairs', true)
                });
                
            }
        },
        // 窗宽窗位
		vtkToggleWL(name) {
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name
			const apis = this.apis;

			apis.forEach((api, apiIndex) => {
				let istyle = vtkInteractorStyleMPRWindowLevel.newInstance({scrollRotate: this.scrollRotate});
				
				const callbacks = {
					setOnLevelsChanged: voi => {
                        const { windowWidth, windowCenter } = voi;
                        api.updateVOI(windowWidth, windowCenter)
                        api.genericRenderWindow.getRenderWindow().render();
					}
				}
				
				api.setInteractorStyle({
					istyle,
					callbacks,
					configuration: { apis, apiIndex },
				});
			});
		},
        // 点测量
        vtkToggleToSomeMarks(name) {
            const activeIndex = this.activeViewportIndex
            
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.activeTool = name;
            const apis = this.apis;
            apis.forEach((api, apiIndex) => {
                if (activeIndex != apiIndex) return
                const { svgWidgetManager, svgWidgets } = api;
                svgWidgetManager.render();
                let interactor
                switch (name) {
                    case 'TextMarker':
                        interactor = vtkInteractorStyleMark
                        break;
                    case 'DragProbe':
                        interactor = vtkInteractorStyleProbe
                        break;
                    case 'ArrowAnnotate':
                        interactor = vtkInteractorStyleArrow
                        break;
                
                    default:
                        break;
                }
                let istyle = interactor.newInstance({scrollRotate: this.scrollRotate});

                api.setInteractorStyle({
                	istyle,
                	configuration: { apis, apiIndex, endCallback: () => {
                        if (['TextMarker', 'ArrowAnnotate'].includes(name)) {
                            this.activeTool = 'Airtools';
                        }
                    } },
                }); 

            });

        },
        vtkToggleToMPRSlice(name) {
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name
            const apis = this.apis;
            apis.forEach((api, apiIndex) => {
				
                const { svgWidgetManager, svgWidgets } = api;

                svgWidgets.crosshairsWidget.setDisplay(false)
                svgWidgetManager.render();

               this.$nextTick(() => {
                    const istyle = vtkInteractorStyleMPRSlice.newInstance({scrollRotate: this.scrollRotate});
                    api.setInteractorStyle({
                        istyle,
                        configuration: { apis, apiIndex },
                    });
               })

			});
        },
        // 定位线
		vtkToggleToCrosshairs(name, reset = false) {
            if (this.activeTool === this.vtkActiveTool && !reset) {
                return
            }
            this.vtkActiveTool = name
            const apis = this.apis;
            apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets, group } = api;
                if (reset) {
                    // setTimeout(() => {
                        const ipp = this.synchronizerCrosshairs[group].patientPoint
                        svgWidgets.crosshairsWidget.setDisplay(true) 
                        if (ipp) {
                            // 初始化mip图的定位线 从cs图的定位同步器中取世界坐标点
                            svgWidgets.crosshairsWidget.moveCrosshairs([ipp.x, ipp.y, ipp.z], [api], 0)
                        } else {
                            svgWidgets.crosshairsWidget.resetCrosshairs(apis, apiIndex)
                        }


                        // 这个 onModified 回调，调用一次会加一次，疯狂叠加
                        if (!this.isCrosshairsCallback[group]){
                            this.isCrosshairsCallback[group] = true
                            api.svgWidgets.crosshairsWidget.onModified(res => {
                                // 这些是用来计算三维定位坐标的参数
                                const renderer = api.genericRenderWindow.getRenderer(); 
                                const camera = renderer.getActiveCamera(); 

                                const worldPos = res.getWorldPos();
                                // 触发执行事件，改变 cornerstone 
                                this.vtkChangeCsIndex(worldPos, group, camera, api.volumes[0])
                            });
                        }
                    // }, 10);
                }else {
                    const istyle = vtkInteractorStyleMPRCrosshairs.newInstance({scrollRotate: this.scrollRotate});
                    // 操作功能
                    const sourceGroup = api.group
                    const filterApis = []
                    for (let index = 0; index < apis.length; index++) {
                        const api = apis[index];
                        if (!api || api.group !== sourceGroup) {
                            filterApis.push(undefined)
                        }else {
                            filterApis.push(api)
                        }
                    }
                    api.setInteractorStyle({
                        istyle,
                        configuration: { apis: filterApis, apiIndex },
                    });

                    // svgWidgets.crosshairsWidget.setDisplay(true)
                    svgWidgetManager.render()

                }
                // console.log(svgWidgets.crosshairsWidget.setDisplay(false)) // 可以获取、设置 svg
                // svgWidgets.crosshairsWidget.setDisplay(true)
                // svgWidgetManager.render();


                // // 这个 onModified 回调，调用一次会加一次，疯狂叠加
                // if (!this.isCrosshairsCallback){
                //     this.isCrosshairsCallback = true  
                //     api.svgWidgets.crosshairsWidget.onModified(res => {
                //         const worldPos = res.getWorldPos();
                //         // 触发执行事件，改变 cornerstone 
                //         this.coords.dispatchEvent('dispatchCornerstoneCoords', worldPos, this.showCrosshairs)
                //     });
                // }

                
            });

		},

        // 点击vtk视图时候,设置其它 cornerstone 视图
        vtkChangeCsIndex(worldPos, group,  camera, volume) {

            // cs 改变 vtk vtk会重新反过来改变 cs 截断执行
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'vtk') {
                this.vtkAndCsRenderState.curRender = 'vtk'
                this.setvtkAndCsRenderThrottle()
            }
            // 如果是 cornerstone 渲染，就不在返回渲染 cs
            if (this.vtkAndCsRenderState.curRender == 'cs') {
                return
            }
            
            // 不知道为什么 数组结构，会在嵌套一层数组
            let ippVec3 = []
            if (worldPos[0].length){
                ippVec3 = [...worldPos[0]]
            }else {
                ippVec3 = worldPos
            }

            const updateAllScrollToIndex = (new3dPo, _toIndex) => {
                // 更新所有viewport的图片位置
                let allAngle = ['x', 'y', 'z']
                this.viewports.forEach((viewport, i) => {
                    if (viewport.group !== group) {
                        return;
                    }
                    const targetElement = this.getEnabledElement(i)
                    if (targetElement === undefined) {
                        return;
                    }
                    const stackToolDataSource = cornerstoneTools.getToolState(targetElement, 'stack');
                    if (stackToolDataSource === undefined) {
                        return;
                    }

                    const stackData = stackToolDataSource.data[0];
                    const orientation = stackData.state.orientation // 去掉之后如果只有一个(冠或矢)图像的情况，定位线会不对

                    const targetAngleIdx = allAngle.indexOf(orientation)

                    // 相同角度只变化第一个
                    if (targetAngleIdx != -1) {
                        allAngle.splice(targetAngleIdx, 1)
                        // 遍历序列中的全部图像。在元素的堆栈中找到离所选位置最近的图像平面
                        let index = findNearIndex(stackData.imageIds, orientation, new3dPo)
                        if (!index) index = _toIndex // MR图像无位置数据 
                        if (!index) return
                        // 滚动到对应位置
                        cornerstoneTools.scrollToIndex(targetElement, index)
                    }
                    // bugfix： 当布局为1个横断cs+1个vtk图  2组（2x2）时 点击vtk定位不同步到cs图的十字线
                    const tool = cornerstoneTools.getToolForElement(targetElement, 'NuclearCrosshairs')
                    // 用这个方法更新十字线位置
                    tool && tool.getInitialPoint(targetElement, this.synchronizerCrosshairs[group])
                });
            }

            const switchOn = getConfigByStorageKey('configs-mipClickLocation') 
            if (!switchOn) {
                updateAllScrollToIndex({ x: ippVec3[0], y: ippVec3[1], z: ippVec3[2] })
                return
            }

            // vtk图像点击后三维定位功能，从vtk的viewport数据中获取点击那个切面的图像数据，计算出三维点
            const vtkViewportIndex = this.viewports.findIndex(item => item.group === group && item.vtk) 
            const inputData = volume.getMapper().getInputData() 
            const bounds = volume.getBounds();
            const Spacings = inputData.getSpacing()
            if (vtkViewportIndex > -1) {
                const sliceType = this.originSeries[group].sliceType // viewport里预先赋值的图像信息
                let orientation = sliceType == 'coronal' ? 'y' : (sliceType == 'sagittal' ? 'x' : 'z')
                const originSeriesZ = this.originSeries[group][orientation] // viewport里预先赋值的图像信息
                if (originSeriesZ) {
                    const targetImageIds = originSeriesZ.two || originSeriesZ.one // 体数据的源图id的数组，默认one是CT two是PT
                    const toIndex = findNearIndex(targetImageIds, orientation, {x: ippVec3[0], y: ippVec3[1], z: ippVec3[2]})
                    const targetImageId = targetImageIds[toIndex]
                    cornerstone.loadAndCacheImage(targetImageId).then(image => {
                         

                        // 参考cornerstone3d的方法，获取点击坐标所在射线穿过的点的集合，计算suv值最大的点
                        let _vec = [0,0,0]
                        let cameraPosition = [0, 0, 0]
                        const directionOfProjection = camera.getDirectionOfProjection();
                        cameraPosition = camera.getPosition();
                        cameraPosition[0] = directionOfProjection[0] * 65536
                        cameraPosition[1] = directionOfProjection[1] * 65536
                        cameraPosition[2] = ippVec3[2]
                        vtkMath.subtract(ippVec3, cameraPosition, _vec);
                        const [xMin, xMax, yMin, yMax, zMin, zMax] = bounds;

                        let pickedPoint = [];
                        const step     = 1;
                        for (let n = 0; n < 2; n++) {
                            for (let pointT = bounds[n*2]; pointT <= bounds[n*2 + 1]; pointT = pointT + Spacings[n]) {
                                // 6.1 Calculating the point x location
                                let point = [0, 0, 0];
                                point[n] = pointT
                                // 6.2 Calculating the point y,z location based on the line equation
                                let idxArr = [0, 1, 2]
                                const t = (pointT - cameraPosition[n]) / _vec[n];
                                idxArr = idxArr.filter(j => j !== n)
                                point[idxArr[0]] = t * _vec[idxArr[0]] + cameraPosition[idxArr[0]];
                                idxArr.shift()
                                point[idxArr[0]] = t * _vec[idxArr[0]] + cameraPosition[idxArr[0]];
    
                                // 6.3 Checking if the points is inside the bounds
                                if (point[0] > xMin &&
                                    point[0] < xMax &&
                                    point[1] > yMin &&
                                    point[1] < yMax &&
                                    point[2] > zMin &&
                                    point[2] < zMax) {
                                    pickedPoint.push(point)
                                }
                            }
                        }

                        let maxValue2Dpoint = {x: 0, y: 0};
                        let maxValue2D = 0;
                        let x,y,value;
                        pickedPoint.forEach(p => { 
                            value = inputData.getScalarValueFromWorld(p)
                            if (value > maxValue2D) {
                                maxValue2D = value
                                maxValue2Dpoint = { x: p[0], y: p[1], z: p[2] }
                            }
                        })

                        const new3dPoint = maxValue2Dpoint
                        
                        // 将三维坐标点传给十字线同步器
                        this.$set(this.synchronizerCrosshairs[group], 'patientPoint', new3dPoint)

                        updateAllScrollToIndex(new3dPoint, toIndex)
                    })
                } 
            }
        },
        /**
         * 获取 vtk 容积
         * 获取vtk容积通过 下标
         * seriesId 序列id
         */
        getVtkVolumeBySeriesUid,
        // 把 vtk 数据放入到 viewport 数组中进行渲染
        async setViewportVtkMIP(status, group, index){
            setTimeout(() => {
                // this.viewports[index] = {}

                this.viewports[index].orientation = { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] }

                const getMIPvolumeData = () => {
                    if (status == 11) {
                        return this.getVtkVolumeBySeriesUid(this.groupUid[group].ptId, this.groupUid[group].noPtId)
                    }
                    return this.getVtkVolumeBySeriesUid(status == 7 ? this.groupUid[group].ptId : this.groupUid[group].noPtId)
                }
                const getVtkClipZ = () => {
                    if (status == 6) {
                        return null
                    }
                    const ctUid  = this.groupUid[group].noPtId;
                    const petUid = this.groupUid[group].ptId;
                    if (!ctUid || !petUid || !appState[ctUid] || !appState[petUid]) {
                        return null
                    }
                    if (!appState[ctUid].vtkVolumes) {
                        return
                    }
                    const volDataExtentCT = appState[ctUid].vtkVolumes.vtkImageData.getExtent()
                    // const volDataExtentPT = appState[this.petUid].vtkVolumes.vtkImageData.getExtent()
                    const volDataOriginCT = appState[ctUid].vtkVolumes.vtkImageData.getOrigin()
                    const volDataOriginPT = appState[petUid].vtkVolumes.vtkImageData.getOrigin()
                    const volDataSpacingCT = appState[ctUid].vtkVolumes.vtkImageData.getSpacing()

                    const extent = (volDataExtentCT[5] - volDataExtentCT[4]) * volDataSpacingCT[2]
                    const diff = (volDataOriginPT[2] - volDataOriginCT[2])
                    // console.log( extent, diff)
                    return {
                        extent,
                        diff
                    }
                }
                getMIPvolumeData().then(res => {
                    if (res) {
                        let {volumes, imageId, imageIdsGroup} = res
                        this.viewports[index].volumes = volumes
                        this.viewports[index].imageId = imageId
                        this.viewports[index].imageIdsGroup = imageIdsGroup
                        this.viewports[index].modality = status == 7 ? 'PT' : 'CT'
                        this.viewports[index].vtkClipZ = getVtkClipZ()
                    }
                    // 异步加载完成，在渲染vtk mip图
                    this.$forceUpdate();
                })

                // this.viewports[index].layoutId = seriesId
                // 放外面-vue会先渲染
                this.viewports[index].vtk = true
                this.viewports[index].uid = this.$fun.onlyValue();
                // 设置布局原始矩阵
                // this.setLayoutOriginalMatrix(false);
            }, 200);
        },
    },
}