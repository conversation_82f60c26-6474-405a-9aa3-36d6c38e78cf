<template>
    <div class="hint">
        <h2>Oops!</h2>
        <h4>信息失效</h4>
        <el-button @click="onClickBack" type="success" size="small">返回</el-button>
    </div>
</template>
<script>
export default {
    name: 'Error',
    created() {
       const userInfo = sessionStorage.getItem("userInfo")
       if (userInfo) {
           this.onClickBack()
       }
    },
    methods: {
        onClickBack() {
            let urlQuery = sessionStorage.getItem('urlObj')
            if (urlQuery) {
                urlQuery = JSON.parse(urlQuery)
            }else {
                urlQuery = {}
            }
            this.$router.push({path: '/', query: urlQuery});
        }
    }
}
</script>
<style lang="scss" scoped>
.hint{
    width: 200px;
    height: 200px;
    position: absolute;
    left: 50%;
    top: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f9fc;
    border-radius: 20px;
    margin: -100px 0px 0px -100px;
    box-shadow: #3c40434d 0px 1px 2px 0px, #3c404326 0px 2px 6px 2px;
    h2 {
        font-size: 32px;
        padding-bottom: 20px;
    }
    h4 {
        font-size: 24px;
        margin-bottom: 20px;
    }
}
</style>
