import macro from 'vtk.js/Sources/macro';
import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';

import vtkCoordinate from 'vtk.js/Sources/Rendering/Core/Coordinate';
import csTools from '$library/cornerstone/cornerstoneTools'

const calculateSUV = csTools.importInternal('util/calculateSUV');

let instanceId = 1;

function getWidgetNode(svgContainer, widgetId) {
  let node = svgContainer.querySelector(`#${widgetId}`);
  if (!node) {
    // const width = parseInt(svgContainer.getAttribute('width'), 10);
    // const height = parseInt(svgContainer.getAttribute('height'), 10);
    node = document.createElement('g');
    node.setAttribute('id', widgetId);
    node.setAttribute('style', `position: absolute; left: 0; top: 0; z-index: 1;`);
    svgContainer.appendChild(node);
  }
  return node;
}

// ----------------------------------------------------------------------------

function vtkSVGProbeWidget(publicAPI, model) {
  model.classHierarchy.push('vtkSVGProbeWidget');
  model.widgetId = `vtkSVGProbeWidget-${instanceId++}`;
  const storageList = {}

  publicAPI.render = (svgContainer, scale) => {
    requestAnimationFrame(() => {

      if (!model.api) return
      const node = getWidgetNode(svgContainer, model.widgetId);
      const storageId = getStorageId()
      
      const storageDataList = storageList[storageId] || []

      let totalSvgHtml = ''

      const width = parseInt(svgContainer.getAttribute('width'), 10);
      const height = parseInt(svgContainer.getAttribute('height'), 10);
      const spacing = 2
      const padding = 7
      const textOffsetTop = -7
      const textOffsetLeft = 7
      const customColor = csTools.toolColors.getFillColor() || '#00FF00'


      const renderer = model.api.genericRenderWindow.getRenderer();
      const appendSVGHTML = (item) => {
        const originPoint = item.probePoint.slice();
        const probePointSUV = item.probePointSUV;

        // 世界位置转化为ComputedDoubleDisplayValue 可以直接用在切片上定位
        const wPos = vtkCoordinate.newInstance();
        wPos.setCoordinateSystemToWorld();
        wPos.setValue(...originPoint);
        const p = wPos.getComputedDoubleDisplayValue(renderer);

        p[0] = p[0] * scale;
        p[1] = height - p[1] * scale;

        const showedText = item.isSUV ? `SUV ${Number(probePointSUV).toFixed(2)}`
          : `HU ${probePointSUV}`

        const svgHtml = `<!-- Right !-->
        <line
          x1="${p[0] + spacing}"
          y1="${p[1]}"
          x2="${p[0] + spacing + padding}"
          y2="${p[1]}"          
          stroke-width="2"
          stroke="${customColor}" 
        ></line>
        <!-- Left !-->
        <line
          x1="${p[0] - spacing - padding}"
          y1="${p[1]}"
          x2="${p[0] - spacing}"
          y2="${p[1]}"          
          stroke-width="2"
          stroke="${customColor}" 
        ></line>

          <!-- Top !-->
          <line
          x1="${p[0]}"
          y1="${p[1] - spacing}"
          x2="${p[0]}"
          y2="${p[1] - spacing - padding}"
          stroke-width="2"
          stroke="${customColor}" 
        ></line>
          <!-- Bottom !-->
          <line
            x1="${p[0]}"
            y1="${p[1] + spacing}"
            x2="${p[0]}"
            y2="${p[1] + spacing + padding}"            
            stroke-width="2"
            stroke="${customColor}" 
          ></line>
          <text 
          x="${p[0] + textOffsetLeft}"
          y="${p[1] + textOffsetTop}" 
          style=" 
              font-size  : 18;
              font-weight : bold;
              text-shadow: 1px 1px 1px black;
              filter: drop-shadow(0 0 2px black);
          "
          fill="${customColor}">${showedText}
          </text>`

        totalSvgHtml += svgHtml
      }

      storageDataList.forEach(item => {
        appendSVGHTML(item)
      })

      const {
        probePointSUV,
        probePoint,
        isSUV,
        api
      } = model;

      if (probePoint[0] !== null && probePoint[1] !== null && probePoint[2] !== null) {
        appendSVGHTML({
          probePointSUV,
          probePoint,
          isSUV,
        })
      }

      node.innerHTML = `
        <g id="container" fill-opacity="1" stroke-dasharray="none" stroke="none" stroke-opacity="1" fill="none">
          <g>
            <svg version="1.1" viewBox="0 0 ${width} ${height}" width=${width} height=${height} style="width: 100%; height: 100%">
            ${totalSvgHtml}
          </g>
        </g>

        `;
    })
  };

  publicAPI.addProbePointData = (worldPos, probePointValue, api) => {
    if (!model.api) model.api = api

    const { svgWidgetManager } = api;

    cornerstone.loadImage(api.imageId).then(image => {
      const pointData = {
        probePoint: worldPos
      }
      let suvValue = calculateSUV(image, probePointValue - image.intercept) / image.slope
      if (isNaN(suvValue)) {
        suvValue = probePointValue + image.intercept
        pointData.isSUV = false
      } else {
        pointData.isSUV = true
      }
      pointData.probePointSUV = suvValue

      const storageId = getStorageId()


      if (!storageList[storageId]) storageList[storageId] = []
      const storageDataList = storageList[storageId]

      storageDataList.push(pointData)

      svgWidgetManager.render();
    })


  }


  publicAPI.moveProbe = (worldPos, probePointValue, api) => {
    if (!model.api) model.api = api
    model.probePointValue = probePointValue 

    const { svgWidgetManager } = api;
    api.svgWidgets.probeWidget.setProbePoint(
      ...worldPos
    );

    cornerstone.loadImage(api.imageId).then(image => {
      let suvValue = calculateSUV(image, probePointValue - image.intercept) / image.slope
      if (isNaN(suvValue)) {
        suvValue = probePointValue + image.intercept
        model.isSUV = false
      } else {
        model.isSUV = true
      }
      model.probePointSUV = suvValue

      svgWidgetManager.render();
    }, () => {
      svgWidgetManager.render();
    })
  };

  publicAPI.cancelTempPoint = (api) => {
    if (!model.api) model.api = api
    model.probePoint = [null, null, null]
    model.api.svgWidgetManager.render();
  };


  publicAPI.deletePointStorage = (worldPos, api) => {
    const storageId = getStorageId() 
    const list = storageList[storageId] || []
    list.forEach((item, index) => {
      if (positionIsEqual(item.probePoint, worldPos)) {
        list.splice(index, 1)
      }
    })
    model.api.svgWidgetManager.render();
  };

  publicAPI.clearPointStorage = () => {
    const storageId = getStorageId()
    storageList[storageId] = []
    if (!model.api) return  
    const { svgWidgetManager } = model.api;
    svgWidgetManager.render();
  };

  function getStorageId() {
    if (!model.api) return 'getStorageId()'
    const renderer = model.api.genericRenderWindow.getRenderer();
    const camera = renderer.getActiveCamera()
    const directionId = camera.getDirectionOfProjection().reduce((prev, curr) => String(prev) + (+curr).toFixed(5), '')
    const positionId = camera.getPosition().reduce((prev, curr) => String(prev) + String(Math.round(+curr)), '') // bugfix: 取整，否则初次取值和初次滚动后的值有偏差(每次滚动坐标变动1)
    return directionId + positionId
  }

  function positionIsEqual(a, b, diff = 10) { 
    if (!a || !b || !a.every || !b.every) {
      return false
    }
    return a.every((v, i) => {
      return Math.abs(v - b[i]) < diff
    })
  }
}

// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
  probePoint: [null, null, null],
  probePointValue: 0,
  probePointSUV: null,
  isSUV: false,
  api: null
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {
  Object.assign(model, DEFAULT_VALUES, initialValues);

  macro.obj(publicAPI, model);
  macro.get(publicAPI, model, ['widgetId']);
  macro.setGet(publicAPI, model, [
    'probePoint',
    'probePointValue',
    'api'
  ]);

  macro.setGetArray(publicAPI, model, ['probePoint'], 3);

  vtkSVGProbeWidget(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(
  extend,
  'vtkSVGProbeWidget'
);

// ----------------------------------------------------------------------------

export default { newInstance, extend };
