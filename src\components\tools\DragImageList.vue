<template>
    <section class="content" v-loading="loading" v-show="!hideRragTools">
        <header class="c-drag-header">
            <span>序列</span>
            <div style="position: relative;">
                <el-button size="mini" class="i-button" @click="onClickOpen">打开</el-button>
                <div class="i-badge" v-if="checkboxList.length">{{checkboxList.length}}</div>
            </div>
        </header>
        <div class="detail-list">
            <el-scrollbar style="height: 100%" class="overflow-x-hide" ref="scrollDiv">
                <div style="height: 100%">
                    <template v-for="study in imageList">
                        <header class="c-item-header">
                            <p>{{study.sStudyDescription || study.sPatientID}}</p>
                            <p>{{study.instanceList.length || 0}} series</p>
                        </header>
                        <draggable
                        v-model="study.instanceList"
                        group="mr-layout"
                        @unchoose="dragUnchoose"
                        :move="dragMove">
                            <div class="item" 
                            @click="onClickCheckBox(item, study.sStudyInstanceUID)"
                            v-for="item in study.instanceList"
                            :desc="item.sSeriesDescription"
                            :iInstanceCount="item.iInstanceCount" 
                            :sModality="item.sModality"
                            :seriesNumber="item.sSeriesNumber"
                            :studyUID="study.sStudyInstanceUID" 
                            :seriesUID="item.sSeriesInstanceUID"
                            :title="item.sSeriesDescription">
                                <section class="i-desc">{{ item.sSeriesDescription }}</section>
                                <section class="c-box">
                                    <div class="i-checkbox" :class="{'i-select': item.checkbox}"></div>
                                    <BaseViewport 
                                    @onFrameImages="onFrameImages($event, study.sStudyInstanceUID, item.sSeriesInstanceUID)"
                                    :imageIds.asyn="item.imageIds" 
                                    :studyUID="study.sStudyInstanceUID" 
                                    :seriesUID="item.sSeriesInstanceUID"></BaseViewport>
                                </section>
                            </div>
                        </draggable>
                    </template>
                </div>
            </el-scrollbar>
        </div>

    </section>
</template>
<script>
import draggable from 'vuedraggable'
import BaseViewport from '$components/tools/components/BaseViewport'
export default {
    components: {
        draggable,
        BaseViewport,
    },
    data() {
        return {
            loading: false,
            toEndIndex: -1,
            imageList: [],
            checkboxList: [],
            fuseLayers: []
        }
    },
    computed: {
        hideRragTools() {
            return this.$store.state.hideRragTools;
        },
    },
    mounted() {
        // 进入的时候，没有数据，获取数据
		if (!this.$store.state.seriesMap.size) {
            try {
                this.loading = true;
                this.$store.dispatch('loadStudy').then(() => {
                    this.getAllSeries();
                    this.loading = false;
                }).catch(err => {
                    let msg = '重建失败！请联系管理员';
                    if (err) {
                        msg = err.msg;
                    }
                    this.$message({ type: 'error', message: msg });
                    console.log(err);
                    this.loading = false;
                })
            } catch (err) {
                this.loading = false;
            }
        }else {
            this.getAllSeries();
        }
    },
    methods: {
        getAllSeries() {
            let arr = Array.from(this.$store.state.seriesMap.values())
            let fuseModality = ['PT', 'NM']
            this.fuseLayers = []
            this.imageList = arr.map(item => {
                const instanceList = this.$fun.deepClone(Array.from(item.instanceList.values())).filter(item => {
                    return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
                })
                instanceList.data = null
                instanceList.imageIds = []
                instanceList.forEach(item => {
                    item.data = null
                    item.imageId = item.imageIds[0]
                    item.checkbox = false

                    if (fuseModality.includes(item.sModality)) {
                        this.fuseLayers.push({
                            modality: item.sModality,
                            studyUID: item.sStudyInstanceUID,
                            seriesUID: item.sSeriesInstanceUID,
                            seriesDesc: item.sSeriesDescription,
                            count: item.iInstanceCount
                        })
                    }

                })
                const obj = Object.assign({}, 
                    { instanceList: instanceList },
                    item.seriesInfo,
                )
                return obj
            }).filter(item => item.instanceList.length)

            // 可融合 NM PT 列表
            this.$emit('fuseData', this.fuseLayers)

            // 不更新，element-ui 的滚动条不出现
            this.$nextTick(() => {
                this.$refs.scrollDiv.update()
            })
        },
        onFrameImages(imageIds, studyUID, seriesUID) {
            
            // 多帧展开后更新到 store 
            let imageLen  = imageIds.length
            let seriesMap = this.$store.state.seriesMap.get(studyUID).instanceList
            const series = seriesMap.get(seriesUID)

            series.imageIds = imageIds
            series.iInstanceCount = imageLen

            seriesMap.set(seriesUID, series)

            this.fuseLayers.forEach(layer => {
                if (layer.seriesUID === seriesUID) {
                    layer.count = imageLen
                }
            });

            this.$emit('fuseData', this.fuseLayers)
        },
        onClickCheckBox(item, studyUID){
            item.checkbox = !item.checkbox
            if (item.checkbox) {
                // 添加选中
                this.checkboxList.push({
                    studyUID,
                    seriesUID: item.sSeriesInstanceUID,
                    desc: item.sSeriesDescription,
                    modality: item.sModality
                })
            }else {
                // 删除
                const findIdx = this.checkboxList.findIndex(check => check.studyUID === studyUID && check.seriesUID === item.sSeriesInstanceUID)
                if (findIdx != -1) {
                    this.checkboxList.splice(findIdx, 1)
                }
            }
        },
        dragUnchoose(e) {
            // 拖拽的 检查id、序列id
            const studyUID  = e.item.getAttribute('studyUID')
            const seriesUID = e.item.getAttribute('seriesUID')
            const desc      = e.item.getAttribute('desc')
            // const sModality = e.item.getAttribute('sModality')
            // const iInstanceCount = e.item.getAttribute('iInstanceCount')
            // const seriesNumber = e.item.getAttribute('seriesNumber')
            
            // 获取放置目标位置下标
            let parent = e.originalEvent.target
            let idx  = parent.getAttribute('index')

            while (parent && idx === null) {
                parent = parent.parentNode
                if (parent.getAttribute('id') == 'app') {
                    return
                }
                idx  = parent.getAttribute('index')
            }
            this.toEndIndex = (idx !== null && idx >= 0) ? idx : -1

            // 拖拽完成，返回拖拽项信息
            if (this.toEndIndex >= 0) {
                this.$emit('dragSelect', this.toEndIndex, { studyUID, seriesUID, desc, viewType: 'cs' })
            }
        },
        dragMove() {
            return false
        },
        onClickOpen() {
            this.imageList.forEach(study => {
                study.instanceList.forEach(item => {
                    item.checkbox = false
                })
            });
            this.$emit('openSelect', this.checkboxList)
            this.checkboxList.splice(0)
        }
    },
}
</script>
<style lang="scss" scoped>
.content{
    width: 160px;
    text-align: left;
    background: #F5F9FC;;
    color: #142B4B;
    border-right: 1px solid #c1c2c3;
    box-shadow: #0000001a 0px 1px 3px 0px, #0000000f 0px 1px 2px 0px;
    .c-drag-header{
        height: 28px;
        line-height: 28px;
        background: #cddee6;
        color: #606266;
        padding: 0px 4px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #c8d0db;
        > span{
            flex: 1;
            text-align: center;
        }
    }
    .c-item-header{
        background: #547495;
        border-bottom: 1px solid #F5F9FC;
        color: white;
        p {
            display: block;
            text-align: center;
            line-height: 24px;
        }
    }
    .detail-list{
        overflow: hidden;
        height: 100%;
        height: calc(100% - 32px);
        position: relative;
        .item{
            border: 1px solid #D2D2D2;
            margin: 0px 4px;
            margin-bottom: 4px;
            background: #f5f7fa;
            &:hover{
                background: #f5f7fa;
            }
            .i-desc{
                font-size: 14px;
                text-align: center;
                margin: 8px 2px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                display:-webkit-box; 
                -webkit-box-orient:vertical;
                -webkit-line-clamp:2; 
            }
            .c-box{
                position: relative;
                width: 100%;
                height: 100%;
                .i-checkbox{
                    position: absolute;
                    top: 50%;
                    left: 4px;
                    width: 18px;
                    height: 18px;
                    line-height: 20px;
                    border: 1px solid #DCDFE6;
                    border-radius: 50%;
                    margin-top: -10px;
                    background: #fff;
                    cursor: pointer;
                    &:hover{
                        border-color: #409EFF;
                    }
                    &::after{
                        box-sizing: content-box;
                        content: "";
                        border: 1px solid #FFF;
                        border-left: 0;
                        border-top: 0;
                        width: 4px;
                        height: 8px;
                        left: 5px;
                        position: absolute;
                        top: 3px;
                        transform: rotate(45deg) scaleY(0);
                        transition: transform .15s ease-in .05s;
                        transform-origin: center;
                    }
                    &.i-select{
                        background-color: #409EFF;
                        &::after{
                            transform: rotate(45deg) scaleY(1);
                        }
                    }
                }
            }
        }
    }
}
.i-button{
    width: 64px;
    height: 21px;
    line-height: 6px;
    margin-left: 0px;
    font-size: 13px;
}
.overflow-x-hide {
    ::v-deep .el-scrollbar__bar.is-vertical{
        right: 0px;
    }
}
.i-badge{
    position: absolute;
    left: 0px;
    top: 3px;
    font-size: 12px;
    background: #9abdcd;
    color: white;
    padding: 0px 2px;
    height: 21px;
    line-height: 21px;
}
</style>