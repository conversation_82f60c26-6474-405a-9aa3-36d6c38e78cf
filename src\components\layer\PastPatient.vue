<template>
    <div>
        <el-popover
        placement="top"
        width="1050"
        trigger="click"
        v-model="visible">
            <div class="base-info">
                <h4>当前信息 <span class="close el-icon-close" @click="visible = false"></span></h4>
                <div>
                    <p>
                        <span class="label">姓名:</span><span class="value">{{ patientInfo.sName || '--' }}</span>
                        <span class="label">性别:</span><span class="value">{{ patientInfo.sSexText || '--' }}</span>
                        <span class="label">年龄:</span><span class="value">{{ patientInfo.sAge || '--' }}</span>
                    </p>
                    <p>
                        <span class="label">病例号:</span><span class="value">{{ patientInfo.sMedicalRecordNO || '--' }}</span>
                        <span class="label">核医学号:</span><span class="value">{{ patientInfo.sNuclearNum || '--' }}</span>
                        <span class="label">身份证号:</span><span class="value">{{ patientInfo.sIdNum || '--' }}</span>
                    </p>
                </div>
                <div class="c-condition">
                    <h4>筛选条件 <span class="close el-icon-close" @click="visible = false"></span></h4>
                    <div>
                        <el-checkbox v-model="checkbox.hasIdNum" :true-label="1"
                        :false-label="0" @change="onChangeHisReportConditions">身份证号</el-checkbox>
                        <el-checkbox v-model="checkbox.hasRecordNo" :true-label="1"
                        :false-label="0" @change="onChangeHisReportConditions">病历号</el-checkbox>
                        <el-checkbox v-model="checkbox.hasNuclearNum" :true-label="1"
                        :false-label="0" @change="onChangeHisReportConditions">核医学号</el-checkbox>
                    </div>
                </div>
            </div>
            <h4 class="title">历史报告</h4>
            <el-table :data="dataList" height="250"
            ref="multipleTable" v-loading="tableLoading"
            @selection-change="selection"
            @row-click="onClickRow">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column width="160" property="sProjectName" label="检查项目"></el-table-column>
                <el-table-column width="140" property="dAppointmentTime" label="检查日期">
                    <template slot-scope="scope">
                        {{ formatTime(scope.row.dAppointmentTime) }}
                    </template>
                </el-table-column>
                <el-table-column width="120" property="sName" label="姓名"></el-table-column>
                <el-table-column width="50" property="sSexText" label="性别" align="center"></el-table-column>
                <el-table-column width="60" property="sAge" label="年龄" align="center"></el-table-column>
                
                <el-table-column width="120" property="sMedicalRecordNO" label="病例号"></el-table-column>
                <el-table-column width="120" property="sNuclearNum" label="核医学号"></el-table-column>
                
                <el-table-column property="sIdNum" label="身份证号"></el-table-column>
            </el-table>
            <div class="c-action">
                <el-button type="primary" size="small" @click="onClickLoad" :disabled="!multipleSelection.length" :loading="loading">{{ multipleSelection.length ? '载入' : '请选择' }}</el-button>
            </div>
            <el-badge slot="reference" :value="reportNum" class="item" :hidden="!reportNum">
                <el-button icon="el-icon-picture-outline" size="small">
                    历史报告
                </el-button>
            </el-badge>
        </el-popover>
    </div>
</template>
<script>
import Api from '$api'
import { format } from 'date-fns';
export default {
    data() {
        return {
            visible: false,
            loading: false,
            tableLoading: false,
            dataList: [],
            multipleSelection: [],
            patientInfo: {},
            checkbox: JSON.parse(localStorage.getItem('dicom-historyRelatedCondition')) || { hasIdNum: 1 }
        }
    },
    computed: {
        reportNum() {
            return  this.dataList.length || 0
        }
    },
    methods: {
        formatTime(date) {
            // return date ? this.$moment(date).format('YYYY-MM-DD') : '--'
            return format(date || 0, 'yyyy-MM-dd HH:mm')
        },
        onChangeHisReportConditions () {
            localStorage.setItem('dicom-historyRelatedCondition', JSON.stringify(this.checkbox));
            this.getDataList()
        },
        onClickLoad() {
            
            this.loading = true
            this.multipleSelection.forEach(item => {
                const params = {
                    sId: item.sId,
                }
                this.$store.commit('addRequestList', params)
            })
            this.$store.dispatch('loadStudy').then(() => {
                this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: true, refresh: true })
                this.loading = false
                this.visible = false
                this.$refs.multipleTable.clearSelection()

                this.$notify({
                    title: '历史报告',
                    message: '关联图像载入成功！',
                });
            })
        },
        selection(val) {
            this.multipleSelection = val
        },
        onClickRow(row) {
            this.$refs['multipleTable'].toggleRowSelection(row);
        },
        getDataList() {
            Api.findPatient({
                sId: this.$store.state.sPatientId
            }).then(res => {
                this.tableLoading = true
                if (res.success) {
                    this.patientInfo = res.data
                    const params = {
                        sSex: this.patientInfo.sSex,
                        sName: this.patientInfo.sName,
                        sPatientInfoId: this.patientInfo.sId
                    }

                    if (this.checkbox.hasRecordNo) {
                        params.sMedicalRecordNO = this.patientInfo.sMedicalRecordNO
                    }
                    if (this.checkbox.hasNuclearNum) {
                        params.sNuclearNum = this.patientInfo.sNuclearNum
                    }
                    if (this.checkbox.hasIdNum) {
                        params.sIdNum = this.patientInfo.sIdNum
                    }

                    Api.queryPastPatients(params).then(res => {
                        if (res?.data?.pastPatientInfos) {
                            this.dataList = res.data.pastPatientInfos
                        }
                    }).finally(() => {
                        setTimeout(() => {
                            this.tableLoading = false
                        }, 100);
                    })
                }
            })
        }
    },
    mounted() {
        this.getDataList()        
    }
}
</script>
<style lang="scss" scoped>
h4 {
    display: flex;
    align-items: center;
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 10px;
    &::before {
        content: '';
        display: inline-block;
        width: 4px;
        height: 15px;
        background: #409eff;
        margin-right: 10px;
    }
}
.c-action {
    margin-top: 20px;
    text-align: right;
}
.base-info {
    position: relative;
    padding-bottom: 20px;
    .close {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        border: 1px solid #606266;
        border-radius: 50%;
        &:hover {
            box-shadow: 1px 1px 10px 6px #eee;
        }
    }
    p {
        font-size: 15px;
        padding-bottom: 10px;
        .label {
            display: inline-block;
            width: 100px;
            padding-right: 10px;
            text-align: right;
            color: #9b9d9f;
        }
        .value {
            display: inline-block;
            width: 180px;
        }
    }
}
.c-condition {
    display: flex;
    align-items: center;
    padding: 20px 0 10px;
    h4 {
        margin-bottom: 0;
        padding-right: 10px;
    }
}
</style>