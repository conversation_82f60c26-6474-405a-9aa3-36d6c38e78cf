<template>
    <div class="container">
        <p class="headline">
            <strong>模板选择：</strong><i @click="innerVisible = false" class="close el-icon-close"></i>
            <span class="condition"><i 
            :class="{active: condition == null}" @click="onClickCondition(null)">全部</i>/<i 
            :class="{active: condition == 3}" @click="onClickCondition(3)">纯图</i></span>
        </p>
        <div class="btns">
            <el-scrollbar ref="scrollDiv" class="scrollMenuBox overflow-x-hide">
                <el-button v-for="(template, idx) in filterData"
                    :key="idx"
                    :class="{active: selectTemplate.sTemplateId === template.sTemplateId}"
                    class="btn"
                    size="medium"
                    :title="template.sTemplateName"
                    @click="onClick(template)"><i class="icon-select"></i> {{ template.sTemplateName }}</el-button>
            </el-scrollbar>
            <span class="tip" v-if="!list.length">注：未配置打印模板</span>
        </div>
        <template v-if="controlPrint.showType == 2">
            <div v-if="controlPrint.isShowParams">
                <div class="c-params">
                    <el-select v-if="isTemOnline"
                        v-model="selectTemplate.sPrinterName"
                        size="small"
                        placeholder="请选择打印机"
                        :disabled="!selectTemplate.sTemplateId"
                        style="flex: 2; margin-right: 10px;"
                        @change="onHandleChange()">
                        <el-option v-for="(name, index) in optionsTemLoc.sPrinterNameOptions"
                            :key="index"
                            :label="name"
                            :value="name"></el-option>
                    </el-select>
                    <div v-else style="flex: 2; margin-right: 10px;color:red;line-height: 2;text-align: center;">客户端未启动</div>
                    <el-select v-model="selectTemplate.sFileType"
                        size="small"
                        placeholder="输出类型"
                        :disabled="!selectTemplate.sTemplateId"
                        style="width: 140px; margin-right: 10px;"
                        @change="onHandleChange()">
                        <el-option v-for="fileType in optionsTemLoc.sFileTypeOptions"
                            :key="fileType.sValue"
                            :label="fileType.sValue"
                            :value="fileType.sValue"></el-option>
                    </el-select>
                    <el-input-number v-model="selectTemplate.iCopies"
                        controls-position="right"
                        size="small"
                        :min="1"
                        :disabled="!selectTemplate.sTemplateId"
                        style="width: 140px;"
                        @change="onHandleChange()"></el-input-number>
                </div>
            </div>
            <div class="action-button">
                <el-button size="small" type="primary" @click="onPrintOrPreview(2)" v-if="controlPrint.isShowPriviewBtn">预览打印</el-button>
                <el-button size="small" type="primary" @click="onPrintOrPreview(1)" v-if="controlPrint.isShowPrintBtn">直接打印</el-button>
                <el-button size="small" type="primary" @click="onPrintOrPreview(3)" v-if="controlPrint.isShowDownloadBtn">下载报告</el-button>
            </div>
        </template>
    </div>
</template>
<script>
export default {
    model: {
        prop: 'visible',
        event: 'toggle'
    },
    props: {
        visible: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        printSelect: {
            default: []
        },
        patientId: '',
        seriesInfo: {
            default: () => {}
        }
    },
    computed: {
        innerVisible: {
            get: function() {
                return this.visible;
            },
            set: function(val) {
                this.$emit('toggle', val);
            }
        },
        filterData() {
            if (this.condition == null) {
                return this.list
            }
            const data = this.list.filter(item => item.iType === this.condition) || []
            return data
        }
    },
    watch: {
        innerVisible: {
            handler() {
                if (this.innerVisible) {
                    this.getData();
                    this.getPrinterNames();
                    setTimeout(() => {
                        this.$refs.scrollDiv.update();
                    }, 100);
                }else {
                    // 在模式1的时候，要清除选择
                    if (this.controlPrint.showType == 1) {
                        this.selectTemplate = {}
                    }
                }
            },
            immediate: true
        }
    },
    data() {
        return {
            list: [],
            selectTemplate: {},
            isTemOnline: true,
            optionsTemLoc: {
                sFileTypeOptions: [
                    { sValue: 'pdf' },
                    { sValue: 'docx' }
                ],
                sPrinterNameOptions: [],
            },
            controlPrint: {
                showType: 1,
                iPrintType: 2,
                isOneTemplate: false,
                isShowDownloadBtn: false,
                isShowParams: false,
                isShowPrintBtn: false,
                isShowPriviewBtn: false
            },
            conditionKey: 'print-filter',
            condition: null,
        }
    },
    mounted() {
        const item = localStorage.getItem(this.conditionKey)
        this.condition = JSON.parse(item)
    },
    methods: {
        // 加载提示
        loadFindTip(text) {
            return this.$loading.service({
                lock: true,
                text: text || '文件生成中...',
                // spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.5)',
                customClass: 'my-loading'
            });
        },
        onClickCondition(val) {
            this.condition = val
            localStorage.setItem(this.conditionKey, val)
            this.selectTemplate = {}
        },
        // 点击选中模板
        onClick(item) {
            this.selectTemplate = item;
            if (!this.selectTemplate.sFileType) {
                this.selectTemplate.sFileType = 'pdf'
            }
            // 模式1
            if (this.controlPrint.showType == 1) {
                this.onPrintOrPreview(this.controlPrint.iPrintType)
            }
        },
        onHandleChange() {
            if(!this.selectTemplate.sTemplateId) {
                this.$message.warning('请选择打印模板！')
                return
            }
            let jsonData = this.selectTemplate;
            jsonData.iWorkStationId = this.$store.state.sWorkStationId;
            this.$Api.saveWorkStationPrint(jsonData).then(res => {
                if (res.success) {
                    // this.$message.success(res.msg);
                    this.selectTemplate.sId = res.data.sId;
                    this.selectTemplate.iCopies = res.data.iCopies;
                    this.selectTemplate.sFileType = res.data.sFileType;
                    this.selectTemplate.sPrinterName = res.data.sPrinterName;
                    return
                }
                this.$message.error(res.msg);
            }).catch((err) => {
                console.log(err);
            })
        },
        async getPrinterNames() {
            this.isTemOnline = true;
            await this.$Api.getPrinterNames().then(res => {
                if (res.success) {
                    this.optionsTemLoc.sPrinterNameOptions = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
                this.isTemOnline = false;
            })
        },
        // 获取模板列表
        async getData() {
            // 获取打印设置
            await this.$Api.getListOfWorkStation({
                sWorkStationId: this.$store.state.sWorkStationId,
                iModuleId: 6
            }).then(res => {
                if (res.success){
                    const obj = res.data.find(item => item.iModuleId == 6)
                    if (obj) {
                        const printShow = JSON.parse(obj.sPrintShow)
                        this.controlPrint = printShow
                        return
                    }
                    // this.$message({
                    //     message: '获取打印控制失败，接口-/print/workStationPrintShow/listOfWorkStation',
                    //     type: 'error',
                    //     duration: 2000
                    // });
                }
            })

            // if (this.list.length) {
            //     return;
            // }
            // 获取打印模板
            this.$Api.getTemplate({
                sDeviceTypeId: this.$store.state.deviceTypeId,
                sWorkStationId: this.$store.state.sWorkStationId,
                iClassify: 1,
                iModuleId: 6,
            }).then(res => {
                if (res.success){
                    this.list = res.data
                    if (
                        this.controlPrint.showType == 1 &&
                        this.controlPrint.isOneTemplate && 
                        this.list.length == 1) {
                        this.onClick(this.list[0])
                    }
                    return;
                }
                this.$message({
                    message: '获取打印模板失败！',
                    type: 'error',
                    duration: 2000
                });
			})
        },
        // 打印
        onPrintOrPreview(iOpenType) {
            // 单个打印
            if (!this.selectTemplate.sTemplateId) {
                this.$message.info('请先选择打印模板！');
                return;
            }
            this.createFileAndPrintOrPreview(iOpenType);
            this.innerVisible = false;
        },
        // 创建文件跟打印文件
        async createFileAndPrintOrPreview(iOpenType) {
            let sPatientId = this.patientId
            // 点击打开，在选择序列，在点阅图的图像，有 ReadDcm 标识，替换成 seriesInfo 中的患者id
            if (sPatientId.includes('ReadDcm')) {
                sPatientId = this.seriesInfo.key
            }
            let jsonData = {
                sPatientId,
                sTemplateId: this.selectTemplate.sTemplateId,
                sWorkStationId: this.$store.state.sWorkStationId,
                sFileType: this.selectTemplate.sFileType || 'pdf',
                iOpenType: 2,
                webPrintImages: []
            }
            if (this.printSelect.length) {
                jsonData.webPrintImages = this.printSelect.filter(item => item.sopInstanceUID)
            }
            
            let loading = this.loadFindTip('文件生成中，大约需45秒时间');
            // 生成文件
            await this.$Api.createImagePrint(jsonData).then(async res => {
                loading.close();
                if (res.success) {

                    let iPrintMode = iOpenType || 2;
                    if (iPrintMode == 1) {
                        // todo iPrintMode = 1 打印模式=‘打印’ 直接打印
                        const data = res.data
                        data.iCopies = this.selectTemplate.iCopies || ''
                        this.silentPrint(data);
                        this.$emit('reloadScreen')
                        return
                    }
                    if (iPrintMode == 3) {
                        // todo  iPrintMode = 3  打印模式=‘下载’ 下载报告
                        this.downloadFile(res.data);
                        this.$emit('reloadScreen')
                        return
                    }
                    // todo  iPrintMode = 2  打印模式=‘预览’ 
                    if (res.data.sFileType === 'pdf') {
                        // 文件类型 = pdf
                        const apricotUrl = window.configs.protocol + '//' + window.configs.reportServe;
                        window.open(apricotUrl + `/printFile/webPreviewFile?sFileId=` + res.data.sFileId);
                        this.$emit('reloadScreen')
                        return
                    }
                    // 文件类型 =d ocx
                    this.wordPreview(res.data);
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                loading.close()
            })
        },
        // a标签下载
        downloadFile(params) {
            let a = document.createElement('a');
            const apricotUrl = window.configs.protocol + '//' + window.configs.reportServe;
            let downURL = `${apricotUrl}/printFile/webDownloadFile?sFileId=${params.sFileId}`
            a.href = downURL;
            a.setAttribute('target', '_blank');
            document.body.append(a);
            a.click();
            a.remove();
        },
        // 静默打印
        async silentPrint(data) {
            let jsonData = {
                sFileId: data.sFileId,
                printerName: data.sPrinterName,
                copies: data.iCopies || 1
            }
            let loading = this.loadFindTip('文件加载中...');
            this.$Api.silentPrint(jsonData).then(res => {
                loading.close()
                if (res && !res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.$message.success('正在打印文件！');
            }).catch(() => {
                this.$message.error('打印失败！')
                loading.close()
            })
        },
        // word 预览
        async wordPreview(data) {
            let jsonData = {
                sFileId: data.sFileId
            }
            let loading = this.loadFindTip('文件加载中...');
            this.$Api.wordPreview(jsonData).then(res => {
                loading.close()
                if (res && !res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.$message.success('正在打开文件！');
                this.$emit('reloadScreen')
            }).catch(() => {
                this.$message.error('预览失败！')
                loading.close()
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.container {
    height: 425px;
    .headline {
        margin: 0 10px 10px;
        font-size: 15px;
        position: relative;
        .close {
            position: absolute;
            right: 0;
            cursor: pointer;
            &:hover {
                color: #409EFF;
            }
        }
        .condition {
            display: inline-block;
            padding: 0 10px;
            > i {
                display: inline-block;
                padding: 0 5px;
                margin: 0 5px;
                cursor: pointer;
                border: 1px solid #eee;
                border-radius: 4px;
                font-size: 14px;
                &.active {
                    color: white;
                    background: #409eff;
                }
                &:hover {
                    color: white;
                    background: #5fa4eb;
                }
            }
        }
    }
    .btns{
        position: relative;
        margin: 0;
        border: 1px solid #f0f0f0;
        border-radius: 5px;
        width: 100%;
        .tip{
            position: absolute;
            top: 40px;
            left: 40%;
            color: #999;
        }
        .scrollMenuBox {
            margin: 15px;
            height: auto;
            width: calc(100% - 31px);
            height: 250px;
            overflow: hidden;
            ::v-deep .el-scrollbar__wrap{
                overflow-x: hidden;
            }
        }
        .btn {
            width: 100%;
            margin-bottom: 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;
            margin-left: 0px;
            &.active{
                color: #3a8ee6;
                border-color: #3a8ee6;
                .icon-select {
                    position: absolute;
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    background: #409eff;
                    left: 10px;
                }
            }
        }
    }
    .c-params {
        display: flex;
        width: 480px;
        margin: 20px 0 20px 24px;
    }
    .action-button{
        display: flex;
        height: 40px;
        line-height: 40px;
        align-items: center;
        justify-content: space-evenly;
        margin-top: 10px;
    }

}
</style>