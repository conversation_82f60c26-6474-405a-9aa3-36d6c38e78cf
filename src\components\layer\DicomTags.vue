<template>
    <el-dialog append-to-body :title="title" :visible="visible" @close="closeDialog" :close-on-click-modal="false"
        width="940px" custom-class="my-dialog config-set-dialog" style="user-select: text;">
             <el-table
                :data="list"
                height="600"
                style="width: 100%; margin-bottom: 20px;"
                row-key="id"
                border
                default-expand-all
                :default-sort="{prop: 'tag',order: 'ascending'}"
                :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                <el-table-column prop="tag" label="Element" sortable align="center" width="156"></el-table-column>
                <el-table-column prop="name" label="description" width="260"></el-table-column>
                <el-table-column prop="vr" label="VR" width="75" align="center"></el-table-column>
                <el-table-column prop="vm" label="VM" width="75" align="center"></el-table-column>
                <el-table-column prop="length" label="Length" width="75" align="center"></el-table-column>
                <el-table-column prop="value" label="Value"></el-table-column>
            </el-table>
    </el-dialog>
</template>
<script>
import TAG_DICT from '$library/utils/dataDictionary.js'
export default {
    props: {
        title: {
            type: String,
            default: "DICOM Tags",
        },
        visible: {
            type: Boolean,
            default: false
        },
    },
    watch: {
        visible: {
            immediate: true,
            handler() {
                if (this.visible) {
                    this.getData()
                }
            }
        }
    },
    data() {
        return {
            list: [],
            only: '',
        }
    },
    mounted() {
        this.only = this.$fun.onlyValue().replace(/-/g, '');
    },
    methods: {
        closeDialog() {
            this.$store.state.dicomTagsVisible = false;
        },
        gbkconvert(gbkStr) {
            return new Promise(resolve => {
                const script = document.createElement('script');
                script.className = 'gbkconvert' + this.only;
                script.src = 'data:text/javascript;charset=gbk,gbkconvertCb' + this.only + '("' + gbkStr + '");';
                document.body.appendChild(script);
                window['gbkconvertCb' + this.only] = (res) => {
                    setTimeout(() => {
                        resolve(res);
                    }, 100);
                };
            })
        },
        clearNode() {
            setTimeout(() => {
                let node = document.getElementsByClassName('gbkconvert' + this.only)
                for (let item of node) {
                    item.remove()
                }
                window['gbkconvertCb' + this.only] = null
            }, 4000);
        },
        getData() {
            this.list = []
            const chineseTag = ['x00080081', 'x00081040', 'x00189311', 'x00080080', 'x00100010', 'x00321060']
            const element = this.$store.state.dicomTagsElement
            const image = cornerstone.getImage(element)

            let id = 1
            if (image && image.data) {
                const getTag = (tag) => {
                    let group = tag.substring(1,5);
                    let element = tag.substring(5,9);
                    let tagIndex = ("("+group+","+element+")").toUpperCase();
                    let attr = TAG_DICT[tagIndex];
                    return attr;
                }
                const tree = (dataSet, prefix = '') => {
                    const list = []
                    const elements = dataSet.elements || {}
                    for (const key in elements) {
                        if (Object.hasOwnProperty.call(elements, key)) {
                            const element = elements[key];
                            let data = getTag(element.tag)

                            // data.value = image.data.string(element.tag)

                            if (data !== undefined) {
                                data = this.$fun.deepClone(data)
                                data.length = element.length

                                data.id = prefix + element.tag

                                if (element.items && element.items[0]) {
                                    data.children = tree(element.items[0].dataSet, data.id)
                                }

                                if (['UL'].includes(element.vr)) {
                                    data.value = dataSet.uint32(element.tag)
                                } else if (['US'].includes(element.vr)) {
                                    data.value = dataSet.uint16(element.tag)
                                }else if (['SL', 'SS'].includes(element.vr)) {
                                    data.value = dataSet.float(element.tag)
                                }else if (['SQ', 'OB', 'OW', 'OB|OW'].includes(element.vr)) {
                                    data.value = ''
                                }else {
                                    try {
                                        if (chineseTag.includes(element.tag)) {
                                            data.value = decodeURI(escape(dataSet.string(element.tag)))
                                        }else {
                                            data.value = dataSet.string(element.tag)
                                        }
                                    } catch (error) {
                                        this.gbkconvert(escape(dataSet.string(element.tag))).then(res => {
                                            data.value = res;
                                            list.push(data)
                                        })
                                        continue;
                                    }
                                }
                                list.push(data)
                            }

                        }
                    }
                    return list
                }
                this.list = tree(image.data)
                this.clearNode()
            }
        }
    }
}
</script>