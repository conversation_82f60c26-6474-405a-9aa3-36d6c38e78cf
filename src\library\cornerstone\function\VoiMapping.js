import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js'
import getConfigByStorageKey from '$library/utils/configStorage.js'
import $fun from '$library/utils/function.js'

const showRebuild = getConfigByStorageKey('configs-show-rebuild')
const value = showRebuild.petU || 10

const newVoiMapping = () => {

    /**
     * 获取 voi
     * @param {*} imageId cornerstone 图像id
     * @param {*} u pet u 值
     * @returns 
     */
    function getVoi(imageId, seriesUID, u = value) {
        const generalSeriesModule = cornerstone.metaData.get(
            'generalSeriesModule',
            imageId
        );
        if (generalSeriesModule && generalSeriesModule.modality === 'PT') {
            const { patientWeight, correctedDose } = getPatientWeightAndCorrectedDose(imageId, seriesUID);

            if (patientWeight && correctedDose) {
                const { w } = $fun.transformULtoVoiWL(u, 0, imageId, seriesUID)
        
                return {
                    windowWidth: w,
                    windowCenter: w / 2
                }
            }
        }

        const voiLutModule = cornerstone.metaData.get(
            'voiLutModule',
            imageId
        );

        if (voiLutModule) {
            return {
                windowWidth: voiLutModule.windowWidth ? voiLutModule.windowWidth[0] : undefined,
                windowCenter: voiLutModule.windowCenter ? voiLutModule.windowCenter[0] : undefined
            }
        }

        return {
            windowWidth: 500,
            windowCenter: 2000
        }

    }
    /**
     * 窗宽窗位转换成为 vtk 映射区间位置值
     * @param {*} windowWidth 
     * @param {*} windowCenter 
     * @returns 
     */
    function toLowHighRange(windowWidth, windowCenter) {
        const lower = windowCenter - windowWidth / 2.0;
        const upper = windowCenter + windowWidth / 2.0;
      
        return { lower, upper };
    }

    /**
     * vtk Range 转换成为窗宽窗位
     * getProperty().getRGBTransferFunction(0).getRange()
     * @param {*} low 
     * @param {*} high 
     * @returns 
     */
    function toWindowLevel(low, high) {
        const windowWidth = Math.abs(low - high);
        const windowCenter = low + windowWidth / 2;
      
        return { windowWidth, windowCenter };
    }

    return {
        getVoi,
        toLowHighRange,
        toWindowLevel
    }
}
const VoiMapping = newVoiMapping();

export default VoiMapping