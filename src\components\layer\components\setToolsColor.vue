<template>
    <div class="c-content">
        <ul>
            <li>
                <span>字体大小</span>
                <el-input-number v-model="currentConfigs.fontSize" :min="8" :max="24" label="" size="small"></el-input-number>
                <span> &nbsp; </span>
                <span>文字行距</span>
                <el-input-number v-model="currentConfigs.padding" :min="0" :max="24" label="" size="small"></el-input-number>
            </li>

            <li>
                <span>文字阴影</span>
                    <el-switch
                        v-model="textShadow"
                        active-color="#13ce66"
                        inactive-color="#eeeeee"
                        :active-value="1"
                        :inactive-value="0">
                    </el-switch>
                <span> &nbsp; </span>

                <span>字体加粗</span>
                <el-switch
                    v-model="currentConfigs.fontWeight"
                    active-color="#13ce66"
                    inactive-color="#eeeeee"
                    active-value="bold"
                    inactive-value="normal">
                </el-switch>
                <span> &nbsp; </span>

                
                <span>字体 </span>
                <el-select size="small"
                    v-model="currentConfigs.fontFamily"
                     >
                     <el-option v-for="item in options.fontFamily" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </li>
            <li>
                <el-popover placement="right" :width="100" trigger="click">
                    <template #reference>
                        <el-button style="margin-right: 16px">自定义工具颜色</el-button>
                    </template> 
                    <table style="width: 100%; ">
                        <tbody>
                            <tr v-for="(val, key) in toolsColor" :key="key"  style="border: 1px solid #aaa;" >
                                <td style="text-align: center;vertical-align: middle;">{{ toolNameMap[key] }}</td>
                                <td style="text-align: center;">
                                    <el-color-picker v-model="toolsColor[key]" :predefine="predefineColors" size="small"></el-color-picker>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </el-popover>
            </li>
            <li>
                <span>工具大小</span>
                <el-input-number v-model="currentConfigs.toolWidth" :min="1" :max="10" label="" size="small"></el-input-number>
            </li>
            <li>
                <span>工具颜色</span>
                <el-color-picker v-model="currentConfigs.toolColor" :predefine="predefineColors" size="small"></el-color-picker>
            </li>
            <li>
                <span>测量长度单位</span>
                <el-select v-model="lengthUnit" size="small" style="width: 120px">
                  <el-option v-for="item in options.lengthUnit" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                
                
            </li>
            <li>
                <span>测量值精确度</span>
                <el-select v-model="measurementAccuracy" size="small" style="width: 120px">
                  <el-option v-for="item in options.measurementAccuracy" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </li>
            <!-- <li>
                <span>选中颜色</span>
                <el-color-picker v-model="currentConfigs.activeColor" :predefine="predefineColors" size="small"></el-color-picker>
            </li> -->
        </ul>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setToolsColor',
    data() {
        return {
            currentConfigs: {},                 // 当前值
            lengthUnit: 'cm',
            measurementAccuracy: '2',
            textShadow: 0,
            predefineColors: [
                '#FF0000',
                '#00FF00',
                '#FFFF00',
                '#00A2A2',
                '#FF0080',
            ],
            storageKey: 'configs-tools',
            storageKey1: 'configs-lengthUnit',
            textShadowStorageKey: 'configs-textShadow',
            options: {
                measurementAccuracy: [
                    { value: '1', label: '小数点后1位' },
                    { value: '2', label: '小数点后2位' }
                ],
                lengthUnit: [
                    { value: 'cm', label: '厘米' },
                    { value: 'mm', label: '毫米' }
                ],
                fontFamily: [
                    { value: 'SimHei' , label: '黑体' }, 
                    { value: 'SimSun' , label: '宋体' }, 
                    { value: 'FangSong' , label: '仿宋' }, 
                    { value: 'KaiTi' , label: '楷体' },
                    { value: 'Microsoft YaHei' , label: '微软雅黑体' },  
                ],
            },
            toolsColor: {
                Length: '',
                ArrowAnnotate: '',
                TextMarker: '',
                CircleRoi: '',
                EllipticalRoi: '',
                RectangleRoi: '',
            },
            toolNameMap: {
                Length: '直线',
                ArrowAnnotate: '箭头',
                TextMarker: '标注',
                CircleRoi: '圆',
                EllipticalRoi: '椭圆',
                RectangleRoi: '矩形',
            }

        }
    },
    mounted() {
        this.currentConfigs = getConfigByStorageKey(this.storageKey);
        this.lengthUnit = getConfigByStorageKey(this.storageKey1);
        this.textShadow = getConfigByStorageKey(this.textShadowStorageKey);
        this.measurementAccuracy = getConfigByStorageKey('configs-measurementAccuracy');
        
        Object.assign(this.toolsColor, getConfigByStorageKey('configs-toolsColor'))
        for (const key in this.toolsColor) {
            if (!Object.hasOwnProperty.call(this.toolNameMap, key)) {
                delete this.toolsColor[key]
            }
        }
        
        if (!this.currentConfigs.padding) this.currentConfigs.padding = 1
    },
    methods: {
        onClickSave(){

            // 设置新的缓存
            localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfigs))

            localStorage.setItem(this.storageKey1, JSON.stringify(this.lengthUnit))
            
            localStorage.setItem(this.textShadowStorageKey, JSON.stringify(this.textShadow))
            
            localStorage.setItem('configs-toolsColor', JSON.stringify(this.toolsColor))
            
            localStorage.setItem('configs-measurementAccuracy', JSON.stringify(this.measurementAccuracy))

            
            // 更新
            cornerstoneTools.store.state.lengthUnit = this.lengthUnit

            cornerstoneTools.textStyle.setFont(`${this.currentConfigs.fontWeight} ${this.currentConfigs.fontSize}px ${this.currentConfigs.fontFamily}, Helvetica, Arial, sans-serif`);
            cornerstoneTools.textStyle.setPadding(this.currentConfigs.padding || 1);
            cornerstoneTools.toolStyle.setToolWidth(this.currentConfigs.toolWidth);
            cornerstoneTools.toolColors.setToolColor(this.currentConfigs.toolColor);
            cornerstoneTools.toolColors.setActiveColor(this.currentConfigs.toolColor);
            // 触发渲染
            this.triggerUpdateView();
            this.$message({
                message: '保存成功！',
                type: 'success'
            });
            // this.$emit('close')
        },
        // 浏览器触发渲染 canvas
        triggerUpdateView(){
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent)
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    > ul{
        flex: 1;
        li{
            height: 70px;
            display: flex;
            align-items: center;
            padding-bottom: 30px;
            span{
                padding-right: 10px;
            }
        }
    }
    > footer{
        height: 40px;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}
</style>