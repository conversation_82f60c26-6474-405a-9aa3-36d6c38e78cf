import BroadcastCenter from './model.js'
import event from '$src/event.js'

function newBroadcastManager() {
    const broadcast = new BroadcastCenter(_onMessage);
    let lastData = null;   // 最后获取到的信息
    let tabId   = null;   // 打开的 tab
    let scrollState = false; // 广播滚动状态， true 为广播滚动

    // 广播接收信息
    function _onMessage(data) {
        lastData = data;
        
        if (data.fun) {
            // 滚动操作事件的广播
            if (data.fun === 'broadcastScroll') {
                scrollState = true;
            }
            event.$emit(data.fun, data);
        }
        // 执行 .vue 中的方法
    };

    // 发送广播信息
    function _postMessage(data) {
        // console.log(info);
        broadcast.postMessage(data);
    };
    
    // 对外传递数据
    /**
     * 
     * @param {*} info      数据信息（原始方法参数）
     * @param {*} sendTabId 发生方 id 值
     */
    function postMessage(data) {
        _postMessage(data);
    };

    // 设置打开的 tab id
    function setTabId(id) {
        if (id != tabId) {
            tabId = id;
        }
        // postMessage({}, tabId);
    };

    // 判断是否是滚动操作，是就还原，且返回 true
    function isBroadcastScroll() {
        if (scrollState) {
            scrollState = false;
            return true;
        }
        return false;
    };

    return {
        setTabId,
        postMessage,
        isBroadcastScroll
    };
}

const broadcastManager = newBroadcastManager();

export default broadcastManager;
