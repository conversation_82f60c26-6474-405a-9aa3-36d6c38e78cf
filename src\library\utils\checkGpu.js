function getUnmaskedInfo() {
  const canvas = document.createElement("canvas")
  const gl = canvas.getContext("webgl") || canvas.getContext("experimental-webgl")
  const unMaskedInfo = {
      renderer: "",
      vendor: ""
  };
  
  const dbgRenderInfo = gl.getExtension("WEBGL_debug_renderer_info");
  if (dbgRenderInfo != null) {
      unMaskedInfo.renderer = gl.getParameter(dbgRenderInfo.UNMASKED_RENDERER_WEBGL);
      unMaskedInfo.vendor   = gl.getParameter(dbgRenderInfo.UNMASKED_VENDOR_WEBGL);
  }
  
  return unMaskedInfo;
}

let needLog = true
let gpuAvailable = null

function checkIfTurnGpuOn() {
  if (gpuAvailable !== null) {
    return gpuAvailable
  }

  const unMaskedInfo = getUnmaskedInfo()
  const gpuNameString = unMaskedInfo.renderer

  if (!/AMD|NVIDIA|INTEL|Apple|Vulkan/i.test(gpuNameString) && needLog) {
    console.error('未开启硬件加速！')
    gpuAvailable = false
    needLog = false
    return false
  }
  gpuAvailable = true
  return true
}

export {
  checkIfTurnGpuOn,
}