export default function setAndGetMetaData(img, metaData) {
    // 因为切出来的图像都没有 tags 值
    // Tools 等其它地方又需要。
    const dataSet = img.data || null;
    if (!dataSet) return;

    // 设置
    let data = {};
    data.generalSeriesModule = {
        modality: dataSet.string('x00080060'),
        seriesInstanceUID: dataSet.string('x0020000e'),
        seriesNumber: dataSet.intString('x00200011'),
        studyInstanceUID: dataSet.string('x0020000d'),
        seriesDate: dicomParser.parseDA(dataSet.string('x00080021')),
        seriesTime: dicomParser.parseTM(dataSet.string('x00080031') || ''),
        institutionName: (dataSet.string('x00080080') || ''),
        manufacturer: dataSet.string('x00080070'),

    }

    data.patientStudyModule = {
        patientAge: dataSet.intString('x00101010'),
        patientSize: dataSet.floatString('x00101020'),
        patientWeight: dataSet.floatString('x00101030') || 1,
        patientSex: dataSet.string('x00100040')
    }

    // VOT LUT Sequence
    data.modalityLutModule = {
        rescaleIntercept: dataSet.floatString('x00281052'),
        rescaleSlope: dataSet.floatString('x00281053'),
        rescaleType: dataSet.string('x00281054'),
    }
    
    // pet
    const radiopharmaceuticalInfo = dataSet.elements.x00540016;

    if (radiopharmaceuticalInfo) {
        const firstRadiopharmaceuticalInfoDataSet =
        radiopharmaceuticalInfo.items[0].dataSet;

        data.petIsotopeModule = {
            radiopharmaceuticalInfo: {
                radiopharmaceuticalStartTime: dicomParser.parseTM(
                firstRadiopharmaceuticalInfoDataSet.string('x00181072') || ''
                ),
                radionuclideTotalDose: firstRadiopharmaceuticalInfoDataSet.floatString(
                'x00181074'
                ),
                radionuclideHalfLife: firstRadiopharmaceuticalInfoDataSet.floatString(
                'x00181075'
                ),
                manufacturer: dataSet.string('x00080070'),
                decayFactor: dataSet.floatString('x00541321') || 1,
                acquisitionDate: dataSet.string('x00080022'),
                acquisitionTime: dataSet.string('x00080032'),
                seriesDate: (dataSet.string('x00080021') || ''),
                seriesTime: (dataSet.string('x00080031') || ''),
                startTime: firstRadiopharmaceuticalInfoDataSet.string('x00181072') || '' ,
                studyDate: dataSet.string('x00080020'),
            },
        };
    }

    data.geModule = {
        manufacturer: dataSet.string('x00080070'),
        measuredTracerActivityDate: dataSet.string('x00091039'),
        administreredDateTime: dataSet.string('x0009103b'),
        measuredTracerActivityDose: dataSet.float('x00091038'),
        postInjectionMeasuredDose: dataSet.float('x0009103c') || 0,
        acquisitionDate: dataSet.string('x00080022'),
        acquisitionTime: dataSet.string('x00080032'),
        decayFactor: dataSet.floatString('x00541321') || 1,
    }

    // 自定义的机器偏移值
    data.machineOffset = [
        dataSet.floatString('x00091000'),
        dataSet.floatString('x00091003'),
        dataSet.floatString('x00091006'),
        dataSet.floatString('x00091009'),
        dataSet.floatString('x0009100c'),
        dataSet.floatString('x0009100f')
    ]
    return data;
}