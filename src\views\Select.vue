<template>
    <div>
        <ImageSeries ref="imageSeries" :isClose="false" :dialogVisible="visible"/>
    </div>
</template>
<script>
import ImageSeries from '$components/layer/ImageSeries'
export default {
    components: {
        ImageSeries
    },
    data() {
        return {
            visible: false,
            loading: false,
            originalSeries: [],
            filterValue: ['可重建']
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.visible = true;
            // 获取 vuex 中的原始数据
            this.originalSeries = Array.from(this.$store.state.seriesMap.values())
            // 没有数据，尝试获取
            if (!this.originalSeries.length) {
                try {
                    this.$refs.imageSeries.onClickGetData(true);   
                } catch (error) {
                    // console.log(error)
                }
            }
        })
    }
}
</script>
<style lang="scss" scoped>
</style>