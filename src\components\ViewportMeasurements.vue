<template>
    <div saveimage="true" class="i-right-center i-overlay"><i v-if="visible" class="iconfont iconcelianggongju1"></i></div>
</template>
<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: true
        }
    }
}
</script>
<style lang="scss" scoped>
.i-overlay i{
    position: absolute;
    top: 34px;
    right: -2px;
    font-size: 16px;
    font-weight: bold;
    color: #409eff !important;
}
</style>