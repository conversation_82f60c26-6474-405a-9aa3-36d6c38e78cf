export default class BroadcastCenter {
    constructor(onMessage) {
        this.Broadcast = new BroadcastChannel('image');
        
        // 接收广播
        this.Broadcast.onmessage = (e) => {
            // console.log(e.data);
            onMessage(e.data);
        }
    }
    // 发送广播
    postMessage(info) {
        this.Broadcast.postMessage(info);
    }

    // 关闭
    close() {
        this.Broadcast.close();
    }
}