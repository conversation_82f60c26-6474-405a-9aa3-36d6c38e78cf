<template>
    <div class="c-box">
        <div class="c-left">
            <CustomSlider :WLUnitStype="WLUnitStype" ref="CustomSlider"
            :renderWindow="renderWindow"
            :renderLevel="renderLevel"
            :showWindow="showWLValue.ww"
            :showLevel="showWLValue.wl"
            :colormapId="colormapId"
            :invert="invert"
            @changeSlider="changeSlider"
            ></CustomSlider>
        </div>
        <div class="content-right">
            <el-dropdown class="i-dropdown" @command="onCommandWindow" trigger="click">
                <span class="el-dropdown-link">
                    <span class="text">
                        {{  isUsingPresetWL ? `[${isUsingPresetWL.index + 1}] ${isUsingPresetWL.name}` : '预设值' }} 
                    </span>
                    <i class="el-icon-caret-bottom el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown" class="i-dropdown g-window-value">
                    <el-dropdown-item v-for="(item, index) in windowWWwc" 
                        :key="item.id" :command="{ ...item, index}">
                        <span><label style="font-weight: bold">[{{ index+1 }}]</label> {{ item.name }}</span> 
                        <span>{{ item.ww+'/'+item.wl }}</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
            <el-dropdown class="i-dropdown" @command="onCommandColormaps" trigger="click"
             :class="{'i-gray': WLUnitStype === 'IMG' }">
                <span class="el-dropdown-link">
                    <span class="text">
                        {{colormapId === 'gray' ? '伪彩色' : colormapId }}
                    </span>
                    <i class="el-icon-caret-bottom el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown" class="g-window-value">
                    <el-dropdown-item :disabled="WLUnitStype === 'IMG' " command="gray"><span>清除配色</span><i class="iconfont iconban"></i></el-dropdown-item>
                    <el-dropdown-item :disabled="WLUnitStype === 'IMG' "
                        v-for="item in colormapsList"
                        :command="item.id"
                        :key="item.id">
                        <canvas :ref="item.id" width="66px" height="20px"></canvas> 
                        {{ item.name }}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>

            <p class="item-input">
                <el-input class="input" size="mini" v-model="showWLValue.ww" @keyup.enter.native="onEnterWindowLevel" v-floorNumber></el-input> <span>{{ showWLValue.wwUnit }}</span>
            </p>
            <p class="item-input">
                <el-input class="input" size="mini" v-model="showWLValue.wl" @keyup.enter.native="onEnterWindowLevel" v-floorNumber></el-input> <span>{{ showWLValue.wlUnit }}</span>
            </p>
            <el-button size="mini" style="width:109px;height:30px;line-height:14px;margin:2px 9px 1px; font-size:14px;" @click="onResetWwwc">重置窗位</el-button>
            <div class="item-input item-last">
                <div>
                    <p>层间隔</p>
                    <el-autocomplete v-model="showWLValue.thickness" size="mini" v-floorNumber style="width: 55px"
                        :fetch-suggestions="queryThickness" class="input" popper-class="custom-popper"
                        placeholder="" :disabled="showType != 'slice' || allowAngel != 'all' || !WLUnitStype || !!vtkApi" @select="onChangeNewImg($event, 'thickness')" @keyup.enter.native="onEnterNewImg('thickness')"
                        ></el-autocomplete>
                </div>
                <div>
                    <p>数目</p>
                    <el-autocomplete v-model="showWLValue.amount" size="mini" v-floorNumber style="width: 54px"
                        :fetch-suggestions="queryAmount" class="input" popper-class="custom-popper"
                        placeholder="" :disabled="showType != 'slice' || allowAngel != 'all' || !WLUnitStype || !!vtkApi" @select="onChangeNewImg($event, 'amount')" @keyup.enter.native="onEnterNewImg('amount')"
                        ></el-autocomplete>
                </div>
            </div>
            <!-- <span class="i-commit">重 置</span> -->
        </div>
        <div class="c-shade" v-if="!WLUnitStype"></div>
    </div>
</template>
<script>
import { debounce } from 'lodash-es'
import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js';

import VoiMapping from '$library/cornerstone/function/VoiMapping.js';

import CustomSlider from './components/CustomSlider'
import { getListByModality } from '$library/utils/configStorage.js'
import getConfigByStorageKey from '$library/utils/configStorage.js'

import customLocalMeta from '$library/cornerstone/mpr/store/customLocalMeta.js';
import event from '$src/event.js'

export default {
    components: {
        CustomSlider
    },
    props: {
        viewportElements: {
            type: [Array, HTMLCollection],
            default: () => { [] }
        },
        activeViewportIndex: {
            type: Number,
            default: 0
        },
        showType: {
            default: ''
        },
        tabId: {
            default: 'none',
        },
        allowAngel: {           // 允许点击角度
            default: 'all'      // 如果出现 X,Y,Z就禁止调整层厚，数目
        },
        isCtLayer: {            // 当前选取的视窗是CT图像
            default: false
        },
        activeViewIsFuse: {      // 当前选取的视窗是融合图像
            default: false
        },
        apis: {
            default: () => { return [] }
        },
    },
    data() {
        return {
            // 键盘码 1~9
            keyCodes: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
            windowWWwc: [],
            colormapsList: [
                // { id: 'gray', name: '测试'},
                { id: 'ge_color', name: 'ge_color' },
                { id: 'hot', name: 'Hot' },
                { id: 'hot2', name: 'Hot2' },
                { id: 'darkHot', name: 'darkHot' },

                { id: 'perfusion', name: 'Perfusion' },
                { id: 'rainbow', name: 'Rainbow' },
                { id: 'x_rain', name: 'x_rain' },
                { id: 'rainbow_brain', name: 'rainbow_brain' },
                { id: 'x_hot', name: 'X_hot' },

                { id: 'x_brain', name: 'x_brain' },
                { id: 'copper', name: 'Copper' },
                { id: 'hotMetalBlue', name: 'Hot Metal Blue' },
                { id: 'pet20Step', name: 'PET 20 Step' },
                { id: 'spectral', name: 'Spectral' },

                { id: 'blues', name: 'Blues' },
                { id: 'cool', name: 'Cool' },
                { id: 'jet', name: 'Jet' },
            ],
            thicknessList: [
                { value: '1.00', name: '1.00'},
                { value: '1.15', name: '1.15'},
                { value: '1.25', name: '1.25'},
                { value: '2.00', name: '2.00'},
                { value: '2.60', name: '2.60'},
                { value: '3.00', name: '3.00'},
                { value: '4.00', name: '4.00'},
                { value: '5.00', name: '5.00'},
                { value: '6.00', name: '6.00'},
                { value: '10.00', name: '10.00'}
            ],

            amountList: [
                { value: '45', name: '45'},
                { value: '80', name: '80'},
                { value: '120', name: '120'},
                { value: '200', name: '200'},
            ],
            showWLValue: {
                ww: 0,
                wl: 0,
                wwUnit: 'Width',
                wlUnit: 'Center',
                amount: '0',
                thickness: '0'
            },
            renderWindow: 0,
            renderLevel:  0,
            WLUnitStype: '',
            element: null,
            imageId: null,
            seriesUID: '',
            colormapId: '',
            invert: false,
            isUsingPresetWL: false,
            vtkApi: false
            // handleInterval: null
        }
    },
    watch: {
        
        activeViewportIndex: {
            handler(){
                // 获取当前点击元素的信息
                this.setActiveSelectElement();
            },
            immediate: true
        },
    },
    computed: {
        ctLayer() {
			if (!this.isCtLayer) return; // 是不是融合窗口才有的ct调窗
            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) return;
            const layers = cornerstone.getLayers(el);
            const layer = layers.find(item => item.options && item.options.isCtLayer)
            return layer
        }  
    },
    mounted() {
        event.$on('onKeyDown', this.onKeyDown)
        // canvas 填充颜色
        this.$nextTick(() => {
            this.colormapsList.forEach(_ => {
                this.updateColorbar(_.id)
            })
        })
    },
    beforeDestroy(){
        // 销毁事件
        this.bindInternalElementEventListeners(true);

        event.$off('onKeyDown', this.onKeyDown)
        // clearInterval(this.handleInterval)
    },
    methods: {
        setActiveSelectElement() {
            
            // 把上一个的监听渲染去掉
            this.bindVtkEventListeners(true);
            this.bindInternalElementEventListeners(true);
            this.vtkApi = false // 先清掉listener再清api
            this.seriesUID = ''

            this.$nextTick(() => {
                const el = this.getEnabledElement(this.activeViewportIndex);
                
                if (!el) {
                    // 没有选中 cornerstone 视窗
                    this.WLUnitStype = '';
                    const api = this.apis[this.activeViewportIndex]
                    if (api) {
                        // 选中vtk窗口
                        this.colormapId = api.getColormapId() || 'gray'
                        this.vtkApi = api
                        const voi = api.getVoi();
                        const imageId = api.imageId;
                        this.imageId = imageId
                        this.seriesUID = api.seriesUID

                        cornerstone.loadAndCacheImage(imageId).then((image) => {
                            const metaSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId)
                            const sModality = metaSeriesModule.modality
                            this.WLUnitStype = sModality
                            this.invert = sModality === 'PT' || sModality === 'NM'

                            this.setWLValue(voi.windowWidth, voi.windowCenter)

                            // 把上一个的监听渲染去掉
                            this.bindVtkEventListeners(true);
                            //  新的选中监听渲染
                            this.bindVtkEventListeners(); 

                            // 重置最大，最小值
                            const slider = this.$refs.CustomSlider;
                            slider.setWLValueZone();
                            slider.onEnterWindowLevel();
                        })
                    }
                    return;
                };
                this.getLoadCurImage(el).then(() => {
                    // 新的选中监听渲染
                    this.element = el;
                    this.bindInternalElementEventListeners();

                    if (!this.setViewportInfo(el)) return;
                    // this.getViewportWindowLevel(event.path[1]);
                    // 获取到的 窗宽窗位就是小数不对？ TODO
                    const viewport = this._getViewport(el);
                    this.setWLValue(viewport.voi.windowWidth, viewport.voi.windowCenter)
                    this.updateWindowColorBar(this.element)
                })

            })
        },

        async getLoadCurImage(el){
            const enabledElement = cornerstone.getEnabledElement( el );
            if (enabledElement) {
                const allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data) return

                const data = this.ctLayer ? allStack.data.find(item => item.isFuse) : allStack.data[0] 
                if (!data) return

                const img = data.imageIds[data.currentImageIdIndex];
                await cornerstone.loadAndCacheImage(img);
                return true;
            } else {
                console.log('error: getLoadCurImage getEnabledElement') 
            }
        },
        // 设置选择视图的信息
        setViewportInfo(el){
            const image = this.ctLayer ? this.ctLayer : cornerstone.getImage(el);
            if (!image) return false;
            this.imageId = image.imageId

            this.setThicknessAndAmount(el);
            if (this.ctLayer) {
                this.WLUnitStype = 'CT'
                return true
            }

            // 通过这二个 tag 判断是否是截屏图
            const desc = image.data && image.data.string('x0008103e') || null;
            const classUid = image.data && image.data.string('x00080016') || null;
            const imageType = image.data && image.data.string('x00080008') || null;
            if (image.color || this.$fun.isCaptrue(desc, classUid, false, imageType)) {
                this.WLUnitStype = 'IMG'
            }else {
                const seriesInfo = cornerstone.metaData.get('generalSeriesModule', image.imageId);
                this.WLUnitStype = seriesInfo.modality;
            }

            return true;
        },
        /**
         * 设置层厚、数量
         */
        async setThicknessAndAmount(el){

            // 获取数量
            const allStack = cornerstoneTools.getToolState(el, 'stack');
            if (!allStack || !allStack.data) return

            // 获取第一张、第二张图像 id
            const data = this.ctLayer ? allStack.data.find(item => item.isFuse) : allStack.data[0] 
            if (!data) return

            const img0 = data.imageIds[0]
            const img1 = data.imageIds[1]
            let nowThickness = '0.00'
            if (img0 && img1){
                // 加载图像
                await cornerstone.loadAndCacheImage(img0);
                await cornerstone.loadAndCacheImage(img1);

                // 获取信息
                let startMeta = cornerstone.metaData.get('imagePlaneModule', img0);
                let startMeta1 = cornerstone.metaData.get('imagePlaneModule', img1);
                
                // 通过获取到的信息，互减得到二张图位置间距
                if (startMeta.sliceLocation !== undefined && startMeta1.sliceLocation !== undefined){
                    nowThickness = Math.abs(startMeta.sliceLocation - startMeta1.sliceLocation).toFixed(2)
                }
            }else if (img0) {
                // 只切一张的时候
                nowThickness = this.showWLValue.thickness;
            }
            this.showWLValue.thickness = nowThickness
            this.showWLValue.amount = data.imageIds.length + ''
            
        },
        // 输入框改变 层厚、数量。改变新的图像
        onChangeNewImg(item, key){
            this.$emit('onChangeImg', item.value, key)
        },
        onEnterNewImg(key){
            this.$emit('onChangeImg', key === 'thickness' ? this.showWLValue.thickness : this.showWLValue.amount, key)
        },
        // 从视窗中通过下标获取 enabled 元素
		getEnabledElement(index){
            if (!this.viewportElements[index]) return;
			let el = this.viewportElements[index].getElementsByClassName('viewport-element')[0];
			if (!el) return;
            return el;
		},
        // 添加伪彩
        onCommandColormaps(command){
            const el = this.getEnabledElement(this.activeViewportIndex);
            if (!el) {
                const api = this.vtkApi
                if (!api) return
                this.colormapId = command
                api.changeMipColormap(command)
                return
            }
            const viewport = this._getViewport(el);
            if (!viewport) return;
            viewport.colormap = cornerstone.colors.getColormap(command);
            this._setViewport(el, viewport);
            cornerstone.updateImage(el, true); 
        },
        // canvas 颜色条
        updateColorbar(colormapId) {
            let colormap = cornerstone.colors.getColormap(colormapId);
            const lookupTable = colormap.createLookupTable();
            const canvas = this.$refs[colormapId][0];
            const ctx = canvas.getContext('2d');
            const height = canvas.height;
            const width = canvas.width;
            const colorbar = ctx.createImageData(66, 20); // 宽高

            lookupTable.setTableRange(0, width);
            
            for(let col = 0; col < width; col++) {
                const color = lookupTable.mapValue(col);
    
                for(let row = 0; row < height; row++) {
                    const pixel = (col + row * width) * 4;
                    colorbar.data[pixel] = color[0];
                    colorbar.data[pixel+1] = color[1];
                    colorbar.data[pixel+2] = color[2];
                    colorbar.data[pixel+3] = color[3];
                }
            }
    
            ctx.putImageData(colorbar, 0, 0);
        },

        updateWindowColorBar(el) {
            const viewport = this._getViewport(el);
            if (!viewport) {
                return
            }
            this.invert = viewport.invert
            const colormap = viewport.colormap
            const colormapId = colormap && colormap.getId ? colormap.getId() : colormap
            this.colormapId = colormapId || 'gray'
        },
        
        // 设置当前窗宽窗位值
        onCommandWindow(item){
            const { ww, wl } = item
            this.isUsingPresetWL = item
            let width = Number(ww);
            let center = Number(wl);

            if (this.WLUnitStype === 'PT'){
                const { w, l } = this.transformShowWLtoVoiWL(width, center)
                width = w;
                center = l;
            }

            if (this.vtkApi) {
                this._setVTKwindow(width, center);
                this.setWLValue(width, center);
                return
            }

            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) return;
            let viewport = this._getViewport(el);
            viewport.voi.windowWidth = width;
            viewport.voi.windowCenter = center;
            this._setViewport(el, viewport);
        },

        // 绑定事件监听
        bindInternalElementEventListeners(clear = false){
            const addOrRemoveEventListener = clear
            ? 'removeEventListener'
            : 'addEventListener';
            if (!this.element) return;
            // 图像渲染
            this.element[addOrRemoveEventListener](
                cornerstone.EVENTS.IMAGE_RENDERED,
                this.onImageRendered
            );
            this.element[addOrRemoveEventListener](
                'mouseup',
                this.onMouseupViewport
            );   
        },
        bindVtkEventListeners(clear = false) {
            if (!this.vtkApi) return;
            if (clear) {
                this.vtkApi.unwatchVOI()
            } else {
                this.vtkApi.watchVOI(({ windowWidth, windowCenter}) => {
                    this.setWLValue(windowWidth, windowCenter)
                })
            }
        },
        // 鼠标收起时事件
        onMouseupViewport(event){
            try {
                this.getViewportWindowLevel(event.path[1]);
            } catch (error) {
                // console.log(error)
            }
        },
        // 图像渲染
        onImageRendered(event) {
            const enableEle = this.getEnabledElement(this.activeViewportIndex);
            if (!enableEle) return
            const viewport = this.ctLayer ? this._getViewport(this.element) : event.detail.viewport;
            if (!this.WLUnitStype) {
                this.setViewportInfo(this.element);
            }
            if (event.srcElement === this.element){
                this.setWLValue(viewport.voi.windowWidth, viewport.voi.windowCenter)
                this.updateWindowColorBar(enableEle)
            }
        },
        // 获取视图
        getViewportWindowLevel(el){
            const viewport = this._getViewport(el);
            if (!viewport) return;
            this.setViewportInfo(el);
            this.setWLValue(viewport.voi.windowWidth, viewport.voi.windowCenter)
        },
        // 设置窗宽窗位值，设备判断过滤
        // 逻辑处理-显示出来的值
        setWLValue(ww, wl){
            // 赋值渲染的窗宽窗位 voi 值
            this.renderWindow = ww;
            this.renderLevel = wl;
            this.windowWWwc = getListByModality(this.WLUnitStype)
            if (this.vtkApi) {

                if (this.WLUnitStype === 'PT'){ 
                    this.windowWWwc = getListByModality('PT-MIP').length ? getListByModality('PT-MIP') : getListByModality('PT')
                    this.showWLValue.wwUnit = 'U';
                    this.showWLValue.wlUnit = 'L';
                    // 如果设置了 SUV 参数就用 SUV 参数值
                    let imageId = this.vtkApi.imageId
                    if (customLocalMeta.data[this.vtkApi.seriesUID]) {
                        imageId = `mpr:${this.vtkApi.seriesUID}`
                        customLocalMeta.setImageIdMetaData(imageId)
                    }
                    const { patientWeight, correctedDose } = getPatientWeightAndCorrectedDose(imageId, this.vtkApi.seriesUID);
                    if (patientWeight && correctedDose) {
                        const { u, l } = this.$fun.transformVoiWLToUL(ww, wl, imageId, this.vtkApi.seriesUID)
                        this.setShowWLValue(u, l)
                    }else {
                        this.setShowWLValue(ww, wl)
                        this.showWLValue.wwUnit = 'Width';
                        this.showWLValue.wlUnit = 'Center';
                    }
                    

                } else {
                    // CT MR
                    this.setShowWLValue(ww, wl)
                    this.showWLValue.wwUnit = 'Width';
                    this.showWLValue.wlUnit = 'Center';
                }

            } else {

                if (this.WLUnitStype === 'PT'){
                    this.showWLValue.wwUnit = 'U';
                    this.showWLValue.wlUnit = 'L';
                    // 修改才行
                    if (!this.imageId) return;
                    const { patientWeight, correctedDose } = getPatientWeightAndCorrectedDose(this.imageId);
                    
                    if (patientWeight && correctedDose) {
                        const { u, l } = this.$fun.transformVoiWLToUL(ww, wl, this.imageId, this.seriesUID)
                        this.setShowWLValue(u, l)
                    }else {
                       // CT MR
                        this.setShowWLValue(ww, wl)
                        this.showWLValue.wwUnit = 'Width';
                        this.showWLValue.wlUnit = 'Center'; 
                    }

                } else {
                    // CT MR
                    this.setShowWLValue(ww, wl)
                    this.showWLValue.wwUnit = 'Width';
                    this.showWLValue.wlUnit = 'Center';
                }
            }

            this.isUsingPresetWL = this.getPresetItem()
        },
        // 转换成为映射的voi ww wl值
        transformShowWLtoVoiWL(ww, wl){
            let w = ww
            let l = wl
            if (this.WLUnitStype === 'PT'){

                if (!this.imageId) return {w, l};

                let imageId = this.imageId
                // 如果是 vtk mip 图有设置 suv 参数修改 就从参数修改读 U,L值
                if (this.apis[this.activeViewportIndex] && customLocalMeta.data[this.apis[this.activeViewportIndex].seriesUID]) {
                    imageId = `mpr:${this.seriesUID}`
                }
                
                const { patientWeight, correctedDose } = getPatientWeightAndCorrectedDose(imageId, this.seriesUID);

                if (patientWeight && correctedDose) {
                    const _wl = this.$fun.transformULtoVoiWL(ww, wl, imageId, this.seriesUID)

                    w = _wl.w
                    l = _wl.l
                }
            }else {
                // CT 不需要转换
            }
            return {w, l};
        },
        // 设置显示出来的 wl 值
        setShowWLValue(ww, wl){
            this.showWLValue.ww = +Number(ww).toFixed(2);
            this.showWLValue.wl = +Number(wl).toFixed(2);
        },
        // 触发改变 窗宽窗位
        onEnterWindowLevel(){
            // 触发改变其它、改变窗宽窗位、颜色条
            const { w , l } = this.transformShowWLtoVoiWL(this.showWLValue.ww, this.showWLValue.wl); // 转换
            const command = w + '/' + l;

            let valueArr = command.split('/');
            let width = Number(valueArr[0]);
            let center = Number(valueArr[1]);

            if (this.vtkApi) {
                this._setVTKwindow(width, center);
                this.setWLValue(width, center);
                return
            }

            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) return;
            let viewport = this._getViewport(el);
            if (!viewport) return;
            viewport.voi.windowWidth = width;
            viewport.voi.windowCenter = center;
            this._setViewport(el, viewport);
        },
        changeSlider(ww, wl){
            this.showWLValue.ww = ww;
            this.showWLValue.wl = wl;
            this.onEnterWindowLevel();
        },

        queryThickness(queryString, cb) {
            cb(this.thicknessList);
        },
        queryAmount(queryString, cb) {
            cb(this.amountList);
        },
        // 键盘监听
        // 键盘事件方法，当前打开的
        onKeyDown(event, tabId){

            if (this.isCtLayer) return;

            if (this.tabId === tabId){
                
                // 查找键下标
                const idx = this.keyCodes.indexOf(event.key)
                // 不在内置键中
                if (idx === -1) return;

                // 没有该行预设值
                const item = this.windowWWwc[idx];
                if (!item) return;

                // 触发键盘强制同步，解决键盘设置有时候失灵
                this.$store.state.keyboardForceSync = true;
                // 调用添加预设值
                this.onCommandWindow({ ww: item.ww, wl: item.wl })

            }
        },
        // 重置窗宽窗位
        onResetWwwc() {
            if (this.vtkApi) {

                const api = this.vtkApi;
                const rgbTransferFunction = api.volumes[0].getProperty().getRGBTransferFunction(0)
                const range = api.volumes[0].getMapper().getInputData().getPointData().getScalars().getRange()

                // rgbTransferFunction.setRange(range[0], range[1]);

                cornerstone.loadAndCacheImage(api.imageId).then(() => {
                    let imageId = api.imageId
                    // 如果设置了 SUV 参数就用 SUV 参数值
                    if (customLocalMeta.data[api.seriesUID]) {
                        imageId = `mpr:${api.seriesUID}`
                        customLocalMeta.setImageIdMetaData(imageId)
                    }

                    const renderWindow = api.genericRenderWindow.getRenderWindow();
                    const { windowWidth, windowCenter } = VoiMapping.getVoi(imageId, api.seriesUID);
                    const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter)
                    rgbTransferFunction.setMappingRange(lower, upper);

                    renderWindow.render();
    
                    this.setWLValue(windowWidth, windowCenter)
                    this._setVTKwindow(windowWidth, windowCenter)
                });

                return
            }
            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) return;

            let obj = cornerstone.getEnabledElement(el)             // 获取开启的元素
            let viewport = this._getViewport(el);             // 获取视图
            if (!viewport) return;
            const image = this.ctLayer ? this.ctLayer.image : obj.image

            let ww = image.windowWidth
            let wc = image.windowCenter
            if (image.data.string('x00080060') === 'PT') {
                const imageInfo = VoiMapping.getVoi(image.imageId);
                ww = imageInfo.windowWidth
                wc = imageInfo.windowCenter
            }

            viewport.voi.windowWidth  =  ww;
            viewport.voi.windowCenter =  wc;

            // TODO 融合的PT调窗是hot 其他是gray
            if (!this.ctLayer && this.activeViewIsFuse){
                const colormapId = getConfigByStorageKey('configs-show-rebuild').colormap
                viewport.colormap = cornerstone.colors.getColormap(colormapId);
            } else {
                // 不是彩色图，彩色图无法上伪彩
                if (!obj.image.color) {
                    viewport.colormap = cornerstone.colors.getColormap('gray');
                }
            }
            this._setViewport(el, viewport);
            // 重置最大，最小值
            const slider = this.$refs.CustomSlider;
            slider.setWLValueZone();
            slider.onEnterWindowLevel();
        },
        getPresetItem() {
            const list = this.windowWWwc.map((item, index) => ({ ...item, index}))
            return list.find(item => {
                return +item.wl === +this.showWLValue.wl && +item.ww === +this.showWLValue.ww
            })
        },
        _getViewport(el) {
            if (this.ctLayer) { 
                const layer = this.ctLayer; 
                const viewport = layer.viewport; 
                return viewport
            } else {
                return cornerstone.getViewport(el);
            }
        },
        _setViewport(el, viewport) {
            if (this.ctLayer) { 
                const layer = this.ctLayer; 
                const layerViewport = layer.viewport;
                layerViewport.voi.windowWidth = viewport.voi.windowWidth;
                layerViewport.voi.windowCenter = viewport.voi.windowCenter;

                if (viewport) {
                    for (var attrname in viewport) {
                        if (viewport[attrname] !== null) {
                            layerViewport[attrname] = viewport[attrname];
                        }
                    }
                }  

                if (layerViewport.voi.windowWidth) {
                    layerViewport.voi.windowWidth = Math.max(viewport.voi.windowWidth, 0.000001);
                } 
                if (layerViewport.scale) {
                    layerViewport.scale = Math.max(viewport.scale, 0.0001);
                }
                layerViewport.rotation %= 360;

                if (layerViewport.rotation < 0) {
                    layerViewport.rotation += 360;
                }

                cornerstone.updateImage(el)
            } else {
                cornerstone.setViewport(el, viewport);
            }
        },
        _setVTKwindow: debounce(function (windowWidth, windowCenter) {
            const api = this.vtkApi
            if (!api) return
            // TODO: 没有触发interactor里的改变窗口 
            // publicAPI.setWindowLevel = (windowWidth, windowCenter) => {
            //     const lowHigh = toLowHighRange(windowWidth, windowCenter);

            //     model.levels.windowWidth = windowWidth;
            //     model.levels.windowCenter = windowCenter;

            //     model.volumeActor
            //     .getProperty()
            //     .getRGBTransferFunction(0)
            //     .setMappingRange(lowHigh.lower, lowHigh.upper);
            // };

            const rgbTransferFunction = api.volumes[0].getProperty().getRGBTransferFunction(0)
            const range = api.volumes[0].getMapper().getInputData().getPointData().getScalars().getRange()

            rgbTransferFunction.setRange(range[0], range[1]);

            cornerstone.loadAndCacheImage(api.imageId).then(() => {
                const renderWindow = api.genericRenderWindow.getRenderWindow();
                const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter)
                rgbTransferFunction.setMappingRange(lower, upper || 0.1);

                renderWindow.render();

                api.updateVOI(windowWidth, windowCenter)
            });
        }, 1 / 30)
    }
}

</script>
<style lang="scss" scoped>
.c-box{
    display: flex;
    height: 212px;
    padding: 8px;
    // padding-top: 8px;
    position: relative;
    .c-left{
        width: 110px;
        background: white;
    }
    .content-right{
        flex: 1;
        border-left: 0px;
        text-align: left;
    }
}
.el-dropdown-link{
    display: flex;
    align-items: center;
    justify-content: space-around;
    .text {
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.item-input{
    display: flex;
    align-items: center;
    margin: 1px 6px 1px 9px;
    font-size: 14px;
    &:last-child{
        padding-bottom: 0px;
    }
    .input{
        width: 65px;
        ::v-deep .el-input__inner{
            text-align: center;
        }
    }
    > span{
        padding-left: 4px;
    }
}
.item-last{
    display: flex;
    // justify-content: space-between;
    text-align: left;
    padding-top: 2px;
    p {
        padding-bottom: 2px;
    }
}
.i-commit{
    width: 111px;
    margin-left: 5px;
}
.c-shade{
    background: #c9ced8;
    opacity: 0.2;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 2;
}
.i-dropdown{
    min-height: 26px;
}
</style>