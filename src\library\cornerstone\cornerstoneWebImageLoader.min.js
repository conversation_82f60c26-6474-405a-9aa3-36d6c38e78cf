/*! cornerstone-web-image-loader - 2.1.1 - 2018-12-05 | (c) 2016 <PERSON> | https://github.com/cornerstonejs/cornerstoneWebImageLoader */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("cornerstoneWebImageLoader",[],t):"object"==typeof exports?exports.cornerstoneWebImageLoader=t():e.cornerstoneWebImageLoader=t()}(window,function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=5)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.external=void 0;var r,o=n(4),a=(r=o)&&r.__esModule?r:{default:r};var u=void 0,i={set cornerstone(e){u=e,(0,a.default)(u)},get cornerstone(){return u}};t.external=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.loadImage=function(e){var t=r.external.cornerstone,n=new XMLHttpRequest;return n.open("GET",e,!0),n.responseType="arraybuffer",i.beforeSend(n),n.onprogress=function(n){if(n.lengthComputable){var r=n.loaded,o=n.total,a=Math.round(r/o*100),u={imageId:e,loaded:r,total:o,percentComplete:a};t.triggerEvent(t.events,"cornerstoneimageloadprogress",u)}},{promise:new Promise(function(t,r){n.onload=function(){(0,o.default)(this.response).then(function(n){var r=(0,a.default)(n,e);t(r)},r)},n.send()}),cancelFn:function(){n.abort()}}},t.configure=function(e){i=e};var r=n(0),o=u(n(3)),a=u(n(2));function u(e){return e&&e.__esModule?e:{default:e}}var i={beforeSend:function(){}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=e.naturalHeight,u=e.naturalWidth;return{imageId:t,minPixelValue:0,maxPixelValue:255,slope:1,intercept:0,windowCenter:128,windowWidth:255,render:r.external.cornerstone.renderWebImage,getPixelData:function(){return(n=void 0,a===t?n=o.getContext("2d"):(o.height=e.naturalHeight,o.width=e.naturalWidth,(n=o.getContext("2d")).drawImage(e,0,0),a=t),n.getImageData(0,0,e.naturalWidth,e.naturalHeight)).data;var n},getCanvas:function(){return a===t?o:(o.height=e.naturalHeight,o.width=e.naturalWidth,o.getContext("2d").drawImage(e,0,0),a=t,o)},getImage:function(){return e},rows:n,columns:u,height:n,width:u,color:!0,rgba:!1,columnPixelSpacing:void 0,rowPixelSpacing:void 0,invert:!1,sizeInBytes:n*u*4}};var r=n(0),o=document.createElement("canvas"),a=void 0},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return new Promise(function(t,n){var r=new Image,o=new Uint8Array(e),a=new Blob([o]),u=window.URL||window.webkitURL,i=u.createObjectURL(a);r.src=i,r.onload=function(){t(r),u.revokeObjectURL(i)},r.onerror=function(e){u.revokeObjectURL(i),n(e)}})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){e.registerImageLoader("http",r.loadImage),e.registerImageLoader("https",r.loadImage)};var r=n(1)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.external=t.configure=t.loadImage=t.createImage=t.arrayBufferToImage=void 0;var r=i(n(3)),o=i(n(2)),a=n(1),u=n(0);function i(e){return e&&e.__esModule?e:{default:e}}var d={arrayBufferToImage:r.default,createImage:o.default,loadImage:a.loadImage,configure:a.configure,external:u.external};t.arrayBufferToImage=r.default,t.createImage=o.default,t.loadImage=a.loadImage,t.configure=a.configure,t.external=u.external,t.default=d}])});
//# sourceMappingURL=cornerstoneWebImageLoader.min.js.map