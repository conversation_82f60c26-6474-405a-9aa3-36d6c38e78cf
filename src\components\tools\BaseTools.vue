<template>
    <transition name="fade">
    <div class="c-right" style="box-shadow: rgb(0 0 0 / 10%) 0px 1px 3px 0px, rgb(0 0 0 / 6%) 0px 1px 2px 0px;" :class="[toolType === 'read' ? 'c-read' : 'c-slice']" ref="right" v-show="!menuRight">
        <!-- <div class="c-item-01"> <i @click="onClickRight" class="iconfont icondedent-right"></i> <span>工具栏</span> </div> -->
        <div class="scrollbar-parent">
            <el-scrollbar style="height: 100%" class="overflow-x-hide">
                <div class="inner-container">
                    <div class="inner-left">
                        <div class="click-right">
                            <i @click="onClickRight" class="iconfont icondedent-right"></i>
                        </div>
                        <div class="i-button range-bottom" @click="onClickOpenReadDicom(false)" title="打开阅图" v-contextmenu:contextmenu><i :class="buttonAnimation.openRead"></i><span>阅图</span></div>
                        <div class="i-button" @click="onClickOpenDicom" title="打开图像"><i class="el-icon-folder-opened elment-ui-icon"></i><span>打开</span></div>
                        
                        <!-- <div class="i-button i-more i-over-dropdown inner-left--maginBotton6" v-if="toolType === 'read' && projectName == 'Report' && !isClinic">
                            <el-dropdown @command="onCommandPrint" :hide-on-click="false">
                            <div class="el-dropdown-link">
                                <div>
                                    <i class="el-icon-printer elment-ui-icon"></i><span>打印</span>
                                </div>
                                <i class="el-icon-caret-bottom"></i>
                            </div>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="onClickPrint">纯图打印</el-dropdown-item>
                                    <el-dropdown-item command="onClickReportPrint">图文打印</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div> -->
                        <!-- 用 v-if 感觉比较快 -->
                        <template v-if="isRemark">
                            <el-popover
                            placement="bottom"
                            title="备注说明"
                            width="200"
                            trigger="click"
                            v-model="visiblePopover">
                                <el-input
                                    type="textarea" style="border-radius: 0px;"
                                    :rows="7" v-model="formRemark"
                                    placeholder="请输入内容" resize="none">
                                </el-input>
                                <el-button size="mini" type="primary" class="popover-button" @click="onClickRemarkSure">确定</el-button>
                                <span v-show="showShortcutKey" class="i-badge">S</span>
                                <div slot="reference" title="保存阅图 快捷键：字母 S" class="i-button" :class="{'range-bottom': isMuch}" @click="onClickSavePrint" id="print-save-tag" v-if="(toolType === 'slice' || !isMuch) && !isClinic"><i class="elment-ui-icon" :class="buttonAnimation.savePrint"></i><span style="font-size: 12px;">保存阅图</span></div>
                            </el-popover>
                        </template>
                        <div v-else-if="(toolType === 'slice' || !isMuch) && !isClinic" 
                        title="保存阅图 快捷键：字母 S" 
                        class="i-button" 
                        :class="{'range-bottom': isMuch}" 
                        @click="onClickNotRemarkSure" id="print-save-tag">
                            <span class="i-badge" v-show="showShortcutKey">S</span>
                            <i class="elment-ui-icon" :class="buttonAnimation.savePrint"></i><span style="font-size: 12px;">保存阅图</span>
                        </div>

                        <el-popover
                        v-model="innerVisiblePrint"
                        placement="left"
                        width="565"
                        trigger="click">
                            <slot name="print"></slot>
                        <div slot="reference" title="打印" class="i-button range-bottom" v-if="toolType === 'read' && projectName == 'Report'"><i class="el-icon-printer elment-ui-icon"></i><span style="font-size: 13px;">打印</span></div>
                        </el-popover>

                        <!-- <div class="i-button" @click="$emit('onClickPrint')" id="print-tag" v-if="toolType === 'read'"><i class="iconfont el-icon-picture"></i><span style="font-size: 12px;">纯图打印</span></div>
                        <div class="i-button inner-left--maginBotton6" @click="$emit('onClickReportPrint')" id="print-tag" v-if="toolType === 'read'"><i class="iconfont el-icon-picture"></i><span style="font-size: 12px;">图文打印</span></div> -->
                        <div class="i-button" style="margin-bottom: 20px;" v-if="clearImage" @click="onClearImage">
                            <div>
                                <i class="iconfont iconreset"></i><span>清除图像</span>
                            </div>
                        </div>
                        <div class="i-button i-more" @click="onCommandReset('all')">
                            <div>
                                <i class="iconfont iconreset"></i><span>重置</span>
                                <span v-show="showShortcutKey" class="i-badge">R</span>
                            </div>
                            <el-dropdown @command="onCommandReset" :hide-on-click="false">
                                <span class="el-dropdown-link">
                                    <i class="el-icon-caret-bottom"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-checkbox-group @change="onChangeResetModeVal" v-model="resetModeVal">
                                        <div class="dropdown-item">
                                            <el-checkbox label="rotation" title="勾选后,点击重置或者键盘R,将重置该项.如长期使用,请在系统设置-保存至服务器"><br /></el-checkbox>
                                            <el-dropdown-item command="rotation">重置旋转</el-dropdown-item>
                                        </div>
                                        <div class="dropdown-item">
                                            <el-checkbox label="size" title="勾选后,点击重置或者键盘R,将重置该项.如长期使用,请在系统设置-保存至服务器"><br /></el-checkbox>
                                            <el-dropdown-item command="size">重置大小</el-dropdown-item>
                                        </div>
                                        <div class="dropdown-item">
                                            <el-checkbox label="wwwc" title="勾选后,点击重置或者键盘R,将重置该项.如长期使用,请在系统设置-保存至服务器"><br /></el-checkbox>
                                            <el-dropdown-item command="wwwc">重置窗位</el-dropdown-item>
                                        </div>
                                        <div class="dropdown-item divided">
                                            <el-checkbox label="tools" title="勾选后,点击重置或者键盘R,将重置该项.如长期使用,请在系统设置-保存至服务器"><br /></el-checkbox>
                                            <el-dropdown-item command="tools" divided>重置标注</el-dropdown-item>
                                        </div>
                                    </el-checkbox-group>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                        <template v-for="item in toolsLayoutOne">
                            <template v-if="item.render === 'custom'">
                                <div v-if="item.toolName == 'customRotate'" class="i-button i-more" @click="onCommandRotate('right')">
                                    <div>
                                        <i class="iconfont iconrotate"></i><span>旋转</span>
                                    </div>
                                    <el-dropdown @command="onCommandRotate" :hide-on-click="false">
                                        <span class="el-dropdown-link">
                                            <i class="el-icon-caret-bottom"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown" class="g-window-value">
                                            <el-dropdown-item command="left"><i class="iconfont iconrotate-anticlockwise"></i>左旋转</el-dropdown-item>
                                            <el-dropdown-item command="right"><i class="iconfont iconrotate-clockwise"></i>右旋转</el-dropdown-item>
                                            <el-dropdown-item command="h" divided><div><i class="iconfont iconshuipingfanzhuan"></i><span>水平翻转</span></div></el-dropdown-item>
                                            <el-dropdown-item command="v"><i class="iconfont iconchuizhifanzhuan"></i>垂直翻转</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </template>
                            <template v-else>
                                <div class="i-button" :title="item.tipTitle" :key="item.toolName" v-show="!item.type || item.type === toolType"
                                @click="fun(item.method, item.methodParams)" 
                                :class="[activeTool == item.toolName ? 'i-active' : '']">
                                    <span v-show="item.hint && showShortcutKey" class="i-badge">{{ item.hint }}</span>
                                    <i class="iconfont" :class="item.icon"></i><span :style="item.style">{{ item.title }}</span>
                                </div>
                            </template>

                        </template>

                        <div v-if="toolType === 'read'" class="i-button" @click="onChangePage(1)" title="上一页,快捷键: PageUp">
                            <div>
                                <span class="i-badge" v-show="showShortcutKey">Up</span>
                                <i class="elment-ui-icon el-icon-arrow-up"></i>
                                <span>上翻</span>
                            </div> 
                        </div>
                        <div v-if="toolType === 'read'" class="i-button" @click="onChangePage(0)" title="下一页,快捷键: PageDown">
                            <div>
                                <span class="i-badge" v-show="showShortcutKey">Dn</span>
                                <i class="elment-ui-icon el-icon-arrow-down"></i>
                                <span>下翻</span>
                            </div> 
                        </div>

                        <!-- <div v-if="toolType === 'read' && !isClinic" class="i-button" style="margin-top: 2px;" @click="$emit('onClickDelScreen')" title="删除阅图"><i class="el-icon-delete elment-ui-icon"></i><span>删除</span></div> -->
                    
                        <div v-if="toolType === 'slice' && contrast" class="i-button" @click="showLesionChart" >
                            <div>
                                <i class="iconfont" :class="['iconth']"></i><span>病灶评估</span>
                            </div> 
                        </div>

                        
                        <!-- <el-dropdown class="i-button i-more" :hide-on-click="false" >
                            <div>
                                <i class="elment-ui-icon el-icon-setting"></i><span style="font-size: 12px;"> 设置</span>
                            </div>
                                <span class="el-dropdown-link">
                                </span>
                                <el-dropdown-menu slot="dropdown" class="g-window-value">
                                    <div class="dropdown-btn" @click="onClickWindow(false)">适应窗口大小</div>
                                    <div class="dropdown-btn" @click="onClickWindow(true)">适应图像大小</div>
                                    <div class="dropdown-btn" @click="onClickOverlayVisible">{{ isOverlayVisible ? '隐藏' : '显示' }}覆盖信息</div>
                                </el-dropdown-menu>
                        </el-dropdown> -->
                    </div>
                    <div class="inner-right">
                        <div class="layout-tool-contain">
                            <slot name="header">
                            </slot>
                        </div>
                        <section class="c-box c-box-02">
                            <div>
                                <LayoutBox @clickBox="onClickBox" :toolType="toolType" background="#666" :rows="layoutBox.rows" :columns="layoutBox.columns"></LayoutBox>
                            </div>
                            <ul>
                                <li @click="onClickChangeSeries('orientation', 'y', angleY)" :class="{'i-disable': angleY}" title="冠状位"><i :style="{backgroundImage:'url('+require('$assets/images/CT_Coronal.png')+')'}"></i></li>
                                <li @click="onClickChangeSeries('orientation', 'x', angleX)" :class="{'i-disable': angleX}" title="矢状位"><i :style="{backgroundImage:'url('+require('$assets/images/CT_Sagittal.png')+')'}"></i></li>
                                <li @click="onClickChangeSeries('orientation', 'z', angleZ)" :class="{'i-disable': angleZ}" title="横截面"><i :style="{backgroundImage:'url('+require('$assets/images/CT_Transverse.png')+')'}"></i></li>
                            </ul>
                            <ul>
                                <li @click="onClickChangeSeries('modality', 1)" title="PT/NM"><i :style="{backgroundImage:'url('+require('$assets/images/PET_Transverse.png')+')'}"></i></li>
                                <li @click="onClickChangeSeries('modality', 0)" title="CT/MR"><i :style="{backgroundImage:'url('+require('$assets/images/CT_Transverse2.png')+')'}"></i></li>
                                <li @click="onClickChangeSeries('modality', 2)" title="融合"><i :style="{backgroundImage:'url('+require('$assets/images/Fused_Transverse.png')+')'}"></i></li>
                            </ul>
                        </section>
                        
                        <div class="wwwc-tool-contain" style="border-top: 1px solid #d2d2dd;">
                            <el-tabs class="tabs-base-tools" v-model="activeTabs" type="card">
                                <el-tab-pane :label="activeViewIsFuse ? 'PT调窗' : '窗宽窗位'" name="wwwc">
                                    <GraySlider 
                                    ref="graySlider" 
                                    class="c-item-05"
                                    :tabId="tabId" 
                                    :allowAngel="allowAngel"
                                    @onChangeImg="onChangeImg"
                                    :showType="toolType"
                                    :activeViewportIndex="activeViewportIndex"
                                    :activeViewIsFuse="activeViewIsFuse"
                                    :viewportElements="viewportElements"
                                    :apis="apis"></GraySlider>
                                </el-tab-pane>
                                <el-tab-pane label="CT调窗" name="wwwc-ct" v-if="toolType === 'slice' && activeViewIsFuse ">
                                    <GraySlider 
                                    v-show="activeTabs === 'wwwc-ct' && activeViewIsFuse "
                                    ref="graySliderCT" 
                                    class="c-item-05"
                                    :isCtLayer="true"
                                    :tabId="tabId" 
                                    :allowAngel="allowAngel"
                                    @onChangeImg="onChangeImg"
                                    :showType="toolType"
                                    :activeViewportIndex="activeViewportIndex" 
                                    :activeViewIsFuse="activeViewIsFuse"
                                    :viewportElements="viewportElements"></GraySlider>
                                </el-tab-pane>
                                <el-tab-pane label="阅图文件" name="screen" v-if="toolType === 'slice' && groupId">
                                    <ScreenFile 
                                        ref="screenfile"
                                        :groupId="groupId" 
                                        @screenToRebuild="screenToRebuild"></ScreenFile>
                                </el-tab-pane>
                                <el-tab-pane name="tools" v-if="!remark.isCaptrue">
                                    <span slot="label" class="label-badge" v-contextmenu:rightMeasurements>测量值 <i class="i-badge-02" v-if="measurementLength">{{ measurementLength }}</i></span>
                                    <Measurements 
                                    :studyId="seriesId"
                                    ref="measurements"
                                    :activeViewportIndex="activeViewportIndex"
                                    @measurementLength="(len) => {measurementLength = len}"
                                    :viewportElements="viewportElements"></Measurements>
                                </el-tab-pane>
                            </el-tabs>
                        </div>

                        <EditRemarkList
                        :viewports="viewports" v-if="remark.isCaptrue"
                        @setRemark="setRemark" @selectThumb="selectThumb" :sSOPInstanceUID="remark.sSOPInstanceUID" :sRemarkCn="remark.sRemarkCn"></EditRemarkList>

                        <!-- 播放组件 -->
                        <PlayClip  v-if="!remark.isCaptrue" :activeViewportIndex="activeViewportIndex" ref="playClip" @playClip="playClip" :viewportElements="viewportElements"/>
                        <div class="c-item-03 tool-btns-contain">
                            <div class="i-button" v-for="item in toolsLayoutTwo" :title="item.tipTitle" :key="item.toolName" v-show="!item.type || item.type === toolType"
                            @click="onClickTool(item.toolName, item.method)" 
                            :class="[activeTool == item.toolName ? 'i-active' : '']">
                                <span v-show="item.hint && showShortcutKey" class="i-badge">{{ item.hint }}</span>
                                <i class="iconfont" :class="item.icon"></i><span :style="item.style">{{ item.title }}</span>
                            </div>
                        </div>

                        <MeasureThreshold v-if="!remark.isCaptrue"></MeasureThreshold>

                        <!-- <div class="c-item-05">
                            <div class="c-item-title">修改设置信息</div>
                            <div>
                                <div class="i-button" @click="onClickWindow(false)">适应窗口大小</div>
                                <div class="i-button" @click="onClickWindow(true)">适应图像大小</div>
                                <div class="i-button" @click="onClickOverlayVisible">{{ isOverlayVisible ? '隐藏' : '显示' }}覆盖信息</div>
                            </div>
                        </div> -->
                    </div>
                </div>
            </el-scrollbar>
        </div>

        <v-contextmenu ref="contextmenu">
            <v-contextmenu-item @click="onClickOpenReadDicom(true)"><i class="el-icon-refresh" style="padding-right:4px"></i>请求最新</v-contextmenu-item>
        </v-contextmenu>


        <v-contextmenu ref="rightMeasurements">
            <v-contextmenu-item @click="getNewToolList"><i class="el-icon-refresh" style="padding-right:4px"></i>刷新</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onClickDelTool"><i class="el-icon-delete" style="padding-right:4px"></i>全部删除</v-contextmenu-item>
             <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onclickSaveTool(toolType === 'read' || contrast ? 'active' : '')"><i class="el-icon-plus" style="padding-right:4px"></i>保存测量</v-contextmenu-item>
            <!-- <v-contextmenu-submenu > -->
                <!-- <span slot="title"><i class="el-icon-folder-checked" style="padding-right:4px"></i>保存</span> -->
                
                <!-- <v-contextmenu-item divider></v-contextmenu-item> -->
                <!-- <v-contextmenu-item @click="onclickSaveTool"></i>全部序列</v-contextmenu-item> -->
            <!-- </v-contextmenu-submenu> -->
        </v-contextmenu>

    </div>
    </transition>
</template>
<script>
import LayoutBox from '$components/tools/LayoutBox'
import PlayClip from '$components/tools/PlayClip'
import GraySlider from '$components/tools/GraySlider'
import Measurements from '$components/tools/Measurements'
import MeasureThreshold from '$components/tools/MeasureThreshold'

import event from '$src/event.js'

import EditRemarkList from './components/EditRemarkList.vue'
import ScreenFile from './components/ScreenFile.vue'

import getConfigByStorageKey from '$library/utils/configStorage.js';

import VoiMapping from '$library/cornerstone/function/VoiMapping.js';

import { throttle } from "lodash-es";

function getResetModeConfig() {
    const obj = getConfigByStorageKey('configs-default-reset-mode')
    if (toString.call(obj).slice(8, -1) === 'Array') {
        return obj
    }
    return ['rotation', 'size', 'wwwc']
}

export default {
    components: {
        LayoutBox,
        PlayClip,
        GraySlider,
        Measurements,
        EditRemarkList,
        ScreenFile,
        MeasureThreshold
    },
    props: {
        toolType: {
            type: String,
            default: 'read'
        },
        activeTool: {
            type: String,
            default: 'Wwwc'
        },
        crosshairsTool: {
            type: Boolean,
            default: false
        },
        activeViewportIndex: {
            type: Number,
            default: 0
        },
        viewportElements: {
            type: [Array, HTMLCollection],
            default: () => { [] }
        },
        isOverlayVisible: {
            type: Boolean,
            default: true
        },
        visiblePrint: {
            type: Boolean,
            default: false
        },
        tabId: {
            default: 'none',
        },
        allowAngel: {           // 允许点击角度
            default: 'all'
        },
        contrast: {             // 对比
            type: Boolean,
            default: false
        },
        lockSync: {
            type: Object,
            default: () => { return {} }
        },
        layoutBox: {
            type: Object,
            default: () => { return {rows: 7, columns: 7} }
        },
        remark: {
            type: Object,
            default: () => { return {
                sRemarkCn: '',
				sSOPInstanceUID: '',
				isCaptrue: false,
            } }
        },
        groupId: {
            type: String,
            default: ''
        },
        seriesId: {
            type: String,
            default: ''
        },
        apis: {
            default: () => { return [] }
        },
        isMuch: {
            type: Boolean,
            default: true
        },
        viewports: {  // 阅图，备注列表使用
            type: Array,
            default: () => []
        },
        clearImage: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            activeTabs: 'wwwc',
            projectName: window.configs.project.name,
            // FreehandScissors
            toolsRender: [

                {toolName: 'Wwwc', title: '调窗', icon: 'icontiaochuang', method: 'onClickTool', methodParams: ['Wwwc'], layout: 'one', hint: 'W'},
                {toolName: 'Pan', title: '平移', icon: 'iconyidong', method: 'onClickTool', methodParams: ['Pan'], layout: 'one', hint: ''},
                {toolName: 'RectangleCrop', title: '裁剪', icon: 'iconrectangle', method: 'onClickTool', methodParams: ['RectangleCrop'], layout: 'one', hint: '', type: 'read' },
                {toolName: 'Zoom', title: '缩放', icon: 'iconzoom', method: 'onClickTool', methodParams: ['Zoom'], layout: 'one', hint: ''},
                {toolName: 'Magnify', title: '透镜', icon: 'iconlens', method: 'onClickTool', methodParams: ['Magnify'], layout: 'one', hint: 'Z'},
                {toolName: 'Invert', title: '反片', icon: 'iconinvert', method: 'onClickInvert', layout: 'one', hint: ''},
                {toolName: 'StackScroll', title: '翻页', icon: 'iconcutPage', method: 'onClickTool', methodParams: ['StackScroll'], layout: 'one', hint: 'F'},
                {toolName: 'NuclearCrosshairs', title: '定位线', icon: 'iconmidpoint', method: 'onClickTool', methodParams: ['NuclearCrosshairs', 'setCrosshairs'], type: 'slice', layout: 'one', hint: 'P', tipTitle: '定位线 快捷键：字母 P'},
                {toolName: 'customRotate', render: 'custom', title: '旋转', layout: 'one' },
                {toolName: 'vtkInteractorStyleMPRSlice', title: 'MIP旋转', icon: 'iconmipRotate', method: 'onClickTool', methodParams: ['vtkInteractorStyleMPRSlice', 'setCrosshairs'], type: 'slice', layout: 'one', hint: ''},
                {toolName: 'rebuildContrast', title: '重建对比', icon: 'iconduibi_o', method: 'onClickTool', methodParams: ['rebuildContrast'], type: 'slice', layout: 'one', hint: ''},

                {toolName: 'Airtools', title: '选择', icon: 'iconjiantou', hint: 'A', tipTitle: '选择 快捷键：字母 A'},
                // {toolName: 'drag', title: '布局拖拽', icon: 'icongaibianfangxiang', type: 'read', style: "font-size: 12px", hint: ''},
                // {toolName: 'MprCrosshairs', title: '定位线', icon: 'iconmidpoint', type: 'slice', hint: ''},
                {toolName: 'ArrowAnnotate', title: '箭头', icon: 'iconarrows-1', hint: 'U', tipTitle: '箭头 快捷键：字母 U'},
                {toolName: 'Length', title: '直线', icon: 'iconline', hint: 'I', tipTitle: '直线 快捷键：字母 I'},
                // {toolName: 'Length', title: '直线', icon: 'iconline', hint: '', tipTitle: '直线 快捷键：字母 I'},
                {toolName: 'Bidirectional', title: '双向线', icon: 'iconzhongxin', hint: 'B',  tipTitle: '双向线 快捷键：字母 B'},
                {toolName: 'Angle', title: '角度', icon: 'iconangle', hint: ''},
                // {toolName: 'CobbAngle', title: 'Cobb', icon: 'iconcobb', hint: ''},
                // {toolName: 'Probe', title: '测值', icon: 'iconct-value'},
                {toolName: 'DragProbe', title: '点测量', icon: 'iconct-value', hint: ''}, // 拖拽
                {toolName: 'EllipticalRoi', title: '椭圆', icon: 'iconellipse', hint: ''},
                {toolName: 'CircleRoi', title: '圆', icon: 'iconyuanquan', hint: 'O', tipTitle: '圆 快捷键：字母 O'},
                {toolName: 'RectangleRoi', title: '矩形', icon: 'iconrectangle', hint: 'M', tipTitle: '矩形 快捷键：字母 M'},
                {toolName: 'FreehandRoi', title: '勾画', icon: 'iconbrush', hint: 'L', tipTitle: '勾画 快捷键：字母 L'},
                {toolName: 'EllipticalMeasure', title: '体测量', icon: 'iconbodyMeasure', hint: 'V', tipTitle: '体测量 快捷键：字母 V'},
                
                {toolName: 'TextMarker', title: '标注', icon: 'iconwenben', hint: 'T', tipTitle: '标注 快捷键：字母 T'},
                {toolName: 'FreehandLength', title: '自描线', icon: 'iconquxian' },
                {toolName: 'Eraser', title: '橡皮擦', icon: 'iconeraser', hint: ''},
            ],
            buttonAnimation: {
                openRead: 'iconfont iconportrait-film',
                savePrint: 'el-icon-picture'
            },
            measurementLength: 0,
            visiblePopover: false,
            formRemark: '',
            resetModeVal: getResetModeConfig()
        }
    },
    watch: {
        mouseEvents: {
            handler() {
                this.setHint();
            },
            deep: true,
            immediate: true
        },
        'remark.isCaptrue': {
            handler() {
                // if(this.activeTabs === 'remark' && !this.remark.isCaptrue) {
                //     this.activeTabs = 'wwwc';
                // }
                if (this.remark.isCaptrue && this.toolsRender[0]?.toolName != 'Airtools') {
                    this.toolsRender.unshift(
                        { toolName: 'Airtools', title: '选择', method: 'onClickTool', icon: 'iconjiantou', hint: 'A', tipTitle: '选择 快捷键：字母 A', layout: 'one' }
                    )
                }
            },
            immediate: true
        },
        activeViewIsFuse: {
            handler(value) {
                if(!value) {
                    this.activeTabs = 'wwwc';
                }
            },
            immediate: true
        },
    },
    computed: {
        showShortcutKey() {
            return this.$store.state.showShortcutKey
        },
        isRemark() {
            return this.$store.state.isRemark
        },
        menuRight() {
            return this.$store.state.hideTools;
        },
        toolsLayoutOne() {
            return this.toolsRender.filter(item => {
                return item.layout === 'one';
            })
        },
        toolsLayoutTwo() {
            return this.toolsRender.filter(item => {
                if (this.remark.isCaptrue) {
                    return false
                }
                return item.layout !== 'one';
            })
        },
        angleX() {
            return this.allowAngel === 'y' || this.allowAngel === 'z'
        },
        angleY() {
            return this.allowAngel === 'x' || this.allowAngel === 'z'
        },
        angleZ() {
            return this.allowAngel === 'x' || this.allowAngel === 'y'
        },
        mouseEvents() {
            // 鼠标右键、中间现在位置..
            return this.$store.state.mouseEvents
        },
        // 临床控制，页面
		isClinic() {
			return this.$store.state.currentModule === 'Report' && this.$store.state.imageReadOnly
		},
        activeViewIsFuse() {
            const el = (this.getEnabledElement(this.activeViewportIndex || 0))
            if (!el) return false
            const stack = cornerstoneTools.getToolState(el, 'stack')
            if (stack && Array.isArray(stack.data)) {
                return stack.data.findIndex(item => item.isFuse) > -1
            }
            return false
        },
        innerVisiblePrint: {
            get: function() {
                return this.visiblePrint;
            },
            set: function(val) {
                this.$emit('update:visiblePrint', val);
            }
        }
    },
    mounted() {
        event.$on('onKeyDown', this.onKeyDown)
    },
    beforeDestroy() {
        event.$off('onKeyDown', this.onKeyDown)
	},
    methods: {
        // 监听键盘
        onKeyDown(event, tabId) {
            if (this.tabId === tabId){
                switch (event.keyCode) {
                    case 112: // f1 弹出快捷键说明
                        this.$fun.triggerShortcutVisible.call(this)
                        break;
                    case 113: // f2 全屏
                        this.$fun.triggerLayoutFullscreen.call(this)
                        break;
                    case 67: // c 清除标注
                        this.$emit('clearMark')
                        break;
                    case 65: // a 选择
                        this.$emit('update:activeTool', 'Airtools')
                        break;
                    case 70: // f 翻页
                        this.$emit('update:activeTool', 'StackScroll')
                        break;
                    case 87: // w 调窗
                        this.$emit('update:activeTool', 'Wwwc')
                        break;
                    case 90: // Z 透镜
                        this.$emit('update:activeTool', 'Magnify')
                        break;
                    case 85: // u 箭头
                        this.$emit('update:activeTool', 'ArrowAnnotate')
                        break;
                    case 66: // b 双向线
                        this.$emit('update:activeTool', 'Bidirectional')
                        break;
                    case 73: // i 直线o
                        this.$emit('update:activeTool', 'Length')
                        break;
                    case 79: // o 圆
                        this.$emit('update:activeTool', 'CircleRoi')
                        break;
                    case 77: // M 矩形
                        this.$emit('update:activeTool', 'RectangleRoi')
                        break;
                    case 76: // L 勾画
                        this.$emit('update:activeTool', 'FreehandRoi')
                        break;
                    case 81: // q 清除窗宽窗位
                        this.onCommandReset('wwwc')
                        break;
                    case 86: // V 体测量
                        this.$emit('update:activeTool', 'EllipticalMeasure')
                        break;
                    case 84: // T 标注
                        this.$emit('update:activeTool', 'TextMarker')
                        break;
                    case 82: // R 重置
                        this.onCommandReset('all')
                        break;
                    case 80: // p 定位线
                        if (this.toolType === 'slice') {
                            this.onClickTool('NuclearCrosshairs', 'setCrosshairs')
                        }
                        break;
                    case 83: // s 保存
                        if ((this.toolType === 'slice' || !this.isMuch) && !this.isClinic) {
                            if (this.isRemark) {
                                this.onClickSavePrint();
                            }else {
                                this.onClickNotRemarkSure();
                            }
                        }
                        break;
                    case 33: // 上一页布局翻页
                        this.throttleChangePage('prev', true)
                        break;
                    case 34: // 下一页布局翻页
                        this.throttleChangePage('next', true)
                        break;
                    case 35: // 最后一张
                        this.onChangeImageIndex('end');
                        break;
                    case 36: // 第一张
                        this.onChangeImageIndex('home');
                        break;
                    case 38: // 上一页序列上一张
                        this.throttleChangePage('prev')
                        break;
                    case 40: // 下一页序列下一张
                        this.throttleChangePage('next')
                        break;
                    default:
                        break;
                }
            }
        },
        throttleChangePage: throttle(function (type, allPage) {
            if (this.toolType === 'read' || allPage) {
                this.onChangePage( type === 'prev' ? 1 : 0);
            }else {
                this.$refs.playClip.onClickToggle(type);
            }
        }, 41.66),
        onChangeImageIndex(type) {
            const el = (this.getEnabledElement(this.activeViewportIndex || 0))
            if (!el) return false

            let n = 0
            if (type === 'end') {
                const toolData = cornerstoneTools.getToolState(el, 'stack')
                if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0){
                    return;
                }
                n = toolData.data[0].imageIds.length - 1     // 总数
            }
            cornerstoneTools.scrollToIndex(el, n)
        },
        selectThumb(uid) {
            this.$emit('selectThumb', uid)
        },
        onAllRender(){
            // 触发跳窗方法
            this.$refs.graySlider?.setActiveSelectElement()
            this.getNewToolList()
        },
        getNewToolList() {
            // 触发测量值方法
            this.$refs.measurements?.getActiveSelectTool()
        },
        // 获取新的阅图列表
        getNewScreenFile() {
            if (this.$refs.screenfile) {
                this.$refs.screenfile.getData()
            }
        },
        onClickDelTool(attr) {
            this.$refs.measurements?.delAllTools(attr)
            if (attr === 'hide') {
                this.$refs.rightMeasurements.hide();
            }
        },
        removeAllTabTool(petUid, ctUid, thickness, isAnomalyFuse) {
            this.$refs.measurements.removeRebuildAllTool(petUid, ctUid, thickness, isAnomalyFuse);
        },
        removeSinglePageTool(element) {
            const { store, getToolState, removeToolState } = cornerstoneTools;
            const { state } = store;
            state.tools.forEach(function (tool) {
                if (
                    typeof tool.pointNearTool !== 'function'
                ) return
                const toolState = getToolState(element, tool.name);
                if (toolState) {

                    toolState.data.forEach(function (data) {

                        removeToolState(element, tool.name, data);
                        const image = cornerstone.getImage(element)

                        cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(image.imageId, tool.name, data);
                    })
                }
            })
            cornerstone.updateImage(element);
        },
        onclickSaveTool(attr) {
            this.$refs.measurements.saveTools(attr)
        },
        onClickRight(){
            this.$store.commit('SET_HIDETOOLS', true)
        },
        fun(funName, params){
            if (!funName) return;
            let p = [];
            if (params && params.length){
                p = params.slice(0);
            }
            this[funName](...p);
        },
		// 从视窗中通过下标获取 enabled 元素
		getEnabledElement(index){
            if (!this.viewportElements[index]) return;
			const el = this.viewportElements[index].getElementsByClassName('viewport-element')[0];
			if (!el) return;
            return el;
		},
		// 元素视图遍历
		viewportMap(callBack){
			for (let index = 0; index < this.viewportElements.length; index++) {
                const dom = this.getEnabledElement(index);
                if (dom) callBack && callBack(dom, index);
			}
		},
        // 旋转
        onCommandRotate(command){

            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) return;

            const viewport = cornerstone.getViewport(el);           // 获取视图信息
            if (command == 'h'){
                viewport.hflip = !viewport.hflip;
            }else if (command == 'v'){
                viewport.vflip = !viewport.vflip;
            }else if (command == 'left'){
                viewport.rotation -= 90;
            }else {
                viewport.rotation += 90;
            }
            cornerstone.setViewport(el, viewport);                  // 设置视图
        },
        // 设置工具
        onClickTool(name, fun){
            if (name === 'rebuildContrast') {
                this.$store.state.rebuildContrastShow = !this.$store.state.rebuildContrastShow
                return
            }
            
            // 定位线工具跟别的工具处理不同
            if (name === 'NuclearCrosshairs') {
                this.$emit('update:crosshairsTool', !this.crosshairsTool)
            }else {
                this.$emit('update:activeTool', name)
            }

            // 当前启用工具点击工具是一样的
            
            if (this.activeTool === name) {
                // 再次点击就关闭该工具
                this.$store.state.disabledTool = !this.$store.state.disabledTool
            }else if (fun) {
                switch (fun) {
                    case 'setCrosshairs':
                        // 开启线条绘制
                        // this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: false })
                        // this.$emit('setCrosshairs')
                        break;
                
                    default:
                        break;
                }
            }

        },
        onChangeResetModeVal() {
            localStorage.setItem('configs-default-reset-mode', JSON.stringify(this.resetModeVal));
        },
        onCommandReset(command){
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index);
                // const el = this.getEnabledElement(this.activeViewportIndex);
                if (!el) {
                    this.$emit('onReset', command, index)
                    continue;
                };

                switch (command) {
                    case 'rotation':
                        this.resetRotation(index)               // 重置旋转
                        break;
                    case 'size':
                        cornerstone.resize(el, true)       // 重置大小
                        this.$emit('syncSizeRender')       // 返回同步大小渲染
                        break;
                    case 'tools':
                        this.resetTools(index)                  // 重置工具
                        break;
                    case 'wwwc':
                        this.resetWwwc(index)                   // 重置窗位
                        break;
                    case 'all':
                        this.resetModeVal.forEach(code => {
                            if (code === 'size') {

                                const img = cornerstone.getImage(el)
                                if (!img) {
                                    return
                                }
                                // 大小
                                cornerstone.resize(el, true)
                                this.$emit('syncSizeRender')
                                // 反片
                                const viewport = cornerstone.getViewport(el);
                                viewport.invert = this.getDefaultInvert(el);
                                cornerstone.setViewport(el, viewport);

                            }else if (code === 'rotation') {
                                // 旋转
                                this.resetRotation(index)
                            }else if (code === 'wwwc') {
                                // 窗宽窗位
                                this.resetWwwc(index)
                            }else if (code === 'tools') {
                                // 工具
                                this.resetTools(index)
                            }
                        })
                        break;
                    default:
                        break;
                }
			}
			
        },
        // 获取反片状态
        getDefaultInvert(el){
            let invert = false
            const img = cornerstone.getImage(el)
            const seriesModule = cornerstone.metaData.get('generalSeriesModule', img.imageId)
            if (!seriesModule) {
                // 阅图(截图),没有这个meta
                return false;
            }
            const inverts = ['PE', 'PET', 'PT', 'NM'] // 反片类型
            // 获取到图像信息，且属于反片类型，且不是融合层 默认值反片
            if (seriesModule.modality && inverts.includes( seriesModule.modality.toLocaleUpperCase() ) && !cornerstone.getLayers(el).length){
                invert = true
            }
            return invert;
        },
        // 反片
        onClickInvert(){
            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) {
                this.$emit('onInvert')
                return;
            };
			const viewport = cornerstone.getViewport(el);
            if (!viewport) {
                return
            }
            viewport.invert = !viewport.invert;                     // 修改视图信息
            cornerstone.setViewport(el, viewport);                  // 设置视图
        },
        // 点击修改窗口
        onClickWindow(status){
            this.onWindowResize()
            if (status){
                // 重建、对比
                // if (this.toolType === 'slice') {
                    
                // }
                this.viewportMap(el => {
                    let viewport = cornerstone.getViewport(el);
                    if (!viewport) return;
                    viewport.scale = 1
                    cornerstone.setViewport(el, viewport);
                })
            }
        },
        // 窗口 reset
        onWindowResize(){
            this.viewportMap(el => {
                cornerstone.resize(el, true)
            })
        },
        // 隐藏显示覆盖层
        onClickOverlayVisible(){
            this.$emit('update:isOverlayVisible', !this.isOverlayVisible)
        },
         // 重置窗位image
        resetWwwc(clearIndex){
            const el = this.getEnabledElement(clearIndex != undefined ? clearIndex : this.activeViewportIndex);
			if (!el) return;

            const isImage = cornerstone.getImage(el)
            if (!isImage) {
                return
            }

            let obj = cornerstone.getEnabledElement(el)             // 获取开启的元素
            let viewport = cornerstone.getViewport(el);             // 获取视图
            const layers   = cornerstone.getLayers(el) || [];

            layers.forEach(enabledElement => {
                let ww = enabledElement.image.windowWidth
                let wc = enabledElement.image.windowCenter
                if (enabledElement.image.data.string('x00080060') === 'PT') {
                    const imageInfo = VoiMapping.getVoi(enabledElement.image.imageId);
                    // console.log('层', imageInfo.windowWidth)
                    // if (imageInfo.windowWidth && imageInfo.windowWidth != 500) {
                    ww = imageInfo.windowWidth
                    wc = imageInfo.windowCenter
                    // }
                }

                enabledElement.viewport.voi.windowWidth = ww;
                enabledElement.viewport.voi.windowCenter = wc;
                cornerstone.updateImage(el)
            })
            let ww = obj.image.windowWidth
            let wc = obj.image.windowCenter
            if (['PT', 'FUSE'].includes(obj.modality)) {
                const imageInfo = VoiMapping.getVoi(obj.image.imageId);
                if (imageInfo.windowWidth && imageInfo.windowWidth != 500) {
                    ww = imageInfo.windowWidth
                    wc = imageInfo.windowCenter
                }
            }
            viewport.voi.windowWidth = ww
            viewport.voi.windowCenter = wc
            cornerstone.setViewport(el, viewport);
        },
        // 重置旋转
        resetRotation(clearIndex){
            const el = this.getEnabledElement(clearIndex != undefined ? clearIndex : this.activeViewportIndex);
			if (!el) return;
            const isImage = cornerstone.getImage(el)
            if (!isImage) {
                return
            }

            let viewport = cornerstone.getViewport(el);
            viewport.rotation = 0;
            viewport.hflip = false;
            viewport.vflip = false;
            cornerstone.setViewport(el, viewport);
        },
        // 清除工具
        resetTools(clearIndex){
            const el = this.getEnabledElement(clearIndex != undefined ? clearIndex : this.activeViewportIndex);
			if (!el) return;
            const isImage = cornerstone.getImage(el)
            if (!isImage) {
                return
            }

            this.onClickDelTool('hide');

            cornerstoneTools.triggerEvent(el, cornerstoneTools.EVENTS.MEASUREMENT_REMOVED, {});
            cornerstone.updateImage(el, true)
        },
        onClickBox(command){
            this.$emit('onClickBox', command, true)
        },
        // 打开图像
        onClickOpenDicom(){
            this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: true })
        },
        // 打开阅图
        onClickOpenReadDicom(reload = false){
            // 打开阅图，以当前序列为基准
            if (this.seriesId) {
                const key = this.$store.state.seriesMap.get(this.seriesId).seriesInfo.key
                if (!key) {
                    return
                }
                event.$emit('openTabByOpenIds', key, reload)
            }else if (this.$store.state.seriesInfo.key) {
                // 没有打开图像，或者打开的是截图
                event.$emit('openTabByOpenIds', this.$store.state.seriesInfo.key, reload)
            }
        },
        onChangeImg(value, key){
            const el = this.getEnabledElement(this.activeViewportIndex);
            this.$emit('onChangeImg', value, key, el)
        },
        screenToRebuild(item) {
            this.$emit('screenToRebuild', item);
        },
        // 点击切换角度、设备
        onClickChangeSeries(type, value, isStop = false){
            if (isStop) return;
            this.$emit('onClickChangeSeries', type, value)
        },
        // 设置工具提示
        setHint(){
            const hint = {};
            hint[this.mouseEvents.mid] = '中';
            hint[this.mouseEvents.right] = '右';

            this.toolsRender.forEach(item => {
                const hintValue = hint[item.toolName];
                item.hint = hintValue || item.hint
            })
        },
        onClickSavePrint() {
            // 加载中
            if (this.buttonAnimation.savePrint === 'el-icon-loading') {
                // 不填备注
                this.visiblePopover = false;
                return
            }
            this.formRemark = ''
            // 不填备注
            setTimeout(() => {
                this.visiblePopover = true
            }, 0);
        },
        onClickNotRemarkSure() {
            if (this.buttonAnimation.savePrint === 'el-icon-loading') {
                return
            }
            this.formRemark = ''
            this.emitSavePrint()
        },
        // 点击备注确认
        onClickRemarkSure() {
            this.visiblePopover = false
            this.emitSavePrint()
        },
        emitSavePrint() {
            this.buttonAnimation.savePrint = 'el-icon-loading'
            this.$nextTick(() => {
                this.$emit('onClickSavePrint', () => {
                    this.buttonAnimation.savePrint = 'el-icon-picture'
                    // 刷新阅图列表
                    this.getNewScreenFile()
                }, this.formRemark)
            })
        },
        setRemark(remark) {
            this.$emit('setRemark', remark);
        },
        playClip(status, speed, callBack) {
            this.$emit('playClip', status, speed, callBack)
        },
        showLesionChart() {
            this.$emit('showLesionChart')
        },
        onChangePage(isUp) {
            this.$emit('onChangePage', isUp)
        },
        onClearImage() {
            this.$emit('onClearImage')
        }
    }
}
</script>
<style lang="scss" scoped>

.c-right  {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 308px;
    height: 100%;
    border-left: 1px solid #c1c2c3;
    background: white;
}

.scrollbar-parent{
    position: relative;
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

.inner-container {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;

}

.layout-tool-contain {
    position: relative;
    width: 100%;
    overflow: hidden;
    
}

.wwwc-tool-contain {
    position: relative;
    width: 100%;
    overflow: hidden;

}


.c-box-02 {
    position: relative;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    // border-bottom: 1px solid #d2d2d2;
    padding: 6px 0 0 14px;
    > div {
        padding: 0px 6px 0px 8px;
    }
    ul {
        padding: 0 4px;
        li {
            width: 36px;
            height: 36px;
            border: 1px solid #c0c4cc;
            cursor: pointer;
            margin-bottom: 2px;
            border-radius: 4px;
            padding: 0;
            box-sizing: border-box;
            background: #f5f7fa;
            transition: all 0.2s;
            &.i-disable{
                opacity: 0.5;
                cursor: no-drop;
            }
            i {
                display: inline-block;
                width: 100%;
                height: 100%;
                border-radius: 2px;
                background-size: cover;
            }
            &:last-child{
                margin-bottom: 6px;
            }
            &:hover{
                box-shadow: 2px 2px 4px 0px #c0c0c0;
            }
        }
    }
}
// 阅图模式
.c-read{
    .c-box-02 {
        padding: 6px 0;
        ul li
        {
            cursor: no-drop;
            i {
                opacity: 0.5;
            }
            &:hover{
                box-shadow: none;
            }
        }   

    }
    
}

.i-badge{
    min-width: 14px;
    min-height: 14px;
    position: absolute;
    right: 0px;
    top: 0px;
    font-size: 12px;
    line-height: 12px;
    background: #9abdcd;
    color: white;
    padding: 1px;
    border-radius: 0px 3px 0px 3px;
}
// .inner-container{
//     display: flex;
// }
.inner-left{
    float: left;
    // width: 62px;
    height: 100%;
    min-height: 950px; 
    padding: 8px 0px;
    background: #cddee6;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    flex: 0;
    .i-button{
        position: relative;
        box-sizing: border-box;
        float: left;
        width: 50px;
        height: 46px;
        color: #142B4B;
        border-radius: 3px;
        margin: 2px 6px 2px 6px;
        cursor: pointer;
        overflow: hidden;
        &:hover{
            background-color: #f5f9fc;
        }
        &.i-active{
            background: #9abdcd;
            color: white;
        }
        &.i-more{
            display: flex;
            align-items: center;
            > div:first-child{
                flex: 1;
            }
            > div:last-child{
                width: 16px;
                text-align: left;
            }
            .el-dropdown{
                position: absolute;
                right: 0;
            }
        }
        &.i-over-dropdown{
            >div.el-dropdown{
                width: 100%;
                .el-dropdown-link{
                    display: flex;
                    align-items: center;
                    > div{
                        flex: 1;
                        text-align: center;
                        color: #142B4B;
                    }
                    > i {
                        position: absolute;
                        right: 0;
                    }
                }
            }
        }
        .iconfont {
            font-size: 18px;
            display: block;
            height: 30px;
            line-height: 30px;
        }
    }
}
.inner-right {
    position: relative;
    width: 245px;
    > div {
        margin-bottom: 0px;
    }
}

.popover-lis--item{
    > i {
        left: 4px;
    }
    &.is-select{
        &::before{
            content: "√";
            display: block;
            position: absolute;
            left: 8px;
            top: 0px;
            border-radius: 50%;
        }
    }
}
.elment-ui-icon{
    font-size: 18px;
    display: block;
    height: 30px;
    line-height: 30px;
}
.label-badge{
    position: relative;
}
.i-badge-02{
    z-index: 2;
    position: absolute;
    background-color: #9abdcd;
    border-radius: 10px;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    height: 18px;
    width: 19px;
    line-height: 18px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
}
.popover-button{
    width: 100%;
    margin-top: 6px;
    text-align: center;
}
.click-right {
    width: 100%;
    border-bottom: 1px solid #bfd3dd;
    padding-bottom: 4px;
    i {
        cursor: pointer;
        width: 100%;
    }
}
.dropdown-btn {
    position: relative;
    width: 120px;
    height: 30px;
    line-height: 30px;
    list-style: none;
    padding: 0 6px;
    margin: 0;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    outline: 0;
    text-align: center;
    &:hover{
        background-color: #ecf5ff;
        color: #66b1ff;
    }
}



.inner-left {
    .range-bottom{
        margin-bottom: 10px;
    }
    .i-button {
        height: 38px;
        .iconfont {
            height: 24px;
            line-height: 24px;
        }
    }
}
.elment-ui-icon {
    height: 24px;
    line-height: 24px;
}
.inner-pc-plan .c-right .c-item-03 .i-button {
    margin-bottom: 4px;
}
.c-box-02 {
    padding-bottom: 0px;
}
@media screen and (min-height: 770px) {
    .inner-left {
        .i-button {
            height: 40px;
            margin: 6px 6px 2px 6px;
            .iconfont {
                height: 26px;
                line-height: 26px;
            }
        }
        .range-bottom{
            margin-bottom: 12px;
        }
    }
    .elment-ui-icon {
        height: 26px;
        line-height: 26px;
    }
}
@media screen and (min-height: 880px) {
    .inner-left {
        .range-bottom{
            margin-bottom: 14px;
        }
        .i-button {
            height: 45px;
            .iconfont {
                height: 30px;
                line-height: 30px;
            }
        }
    }
    .elment-ui-icon {
        height: 30px;
        line-height: 30px;
    }
}
@media screen and (min-height: 970px) {
    .inner-left {
        .range-bottom{
            margin-bottom: 40px;
        }
        .i-button {
            height: 46px;
        }
    }
    .inner-right > div {
        margin-bottom: 10px;
    }
    
}
@media screen and (min-height: 1080px) {
    .inner-left {
        .range-bottom{
            margin-bottom: 80px;
        }
        .i-button {
            height: 48px;
        }
    }
    .inner-pc-plan .c-right .c-item-03 .i-button {
        margin-bottom: 10px;
        height: 50px;
        .iconfont {
            height: 32px;
        }
    }
    .c-box-02 {
        padding-bottom: 10px;
    }
}
@media screen and (min-height: 1200px) {
    .inner-left {
        .i-button {
            height: 50px;
        }
    }
}
</style>