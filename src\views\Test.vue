<template>
    <div oncontextmenu="return false" class="inner-pc-plan pc-body">

        <section class="c-body" id="capture">
            <div ref="center" class="c-content">
                <CornerstoneViewport v-for="(item, index) in viewports" style="width: 300px;height:300px"
                    :key="item.uid"
                    :isOverlayVisible="false"
                    activeTool="CircleRoi"
                    :state="{uid: item.uid}"
                    :imageIds="item.imageIds"
                    :layerIds="item.layerIds">
                </CornerstoneViewport>
                <!-- <View2D  style="width: 300px;height:300px"
                :volumes="volumes"
                :onCreated="storeApi(0)"
                :orientation="{ sliceNormal: [0, 0, 1], viewUp: [0, -1, 0] }"
                /> -->
            </div>
            <div>
                <div @click="onClickTest">测试事件</div>
            </div>
        </section>

    </div>
</template>
<script>
import appstate from '$library/vtk/dcmFile/appstate.js'
import CornerstoneViewport from '$components/CornerstoneViewport.vue'
import tryGetVtkVolumeForSeriesNumber from '$library/cornerstone/mpr/tryGetVtkVolumeForSeriesNumber.js';

import html2canvas from "html2canvas"

import { View2D, vtkInteractorStyleMPRWindowLevel } from "$library/vtk";
import vtkVolumeMapper from "vtk.js/Sources/Rendering/Core/VolumeMapper";
import vtkVolume from "vtk.js/Sources/Rendering/Core/Volume";


import mprState from '$library/cornerstone/mpr/store/appState.js';
import draggable from 'vuedraggable'

import LesionChartDialog from '$components/LesionChartDialog'


export default {
    components: {
        CornerstoneViewport,
        View2D,
        draggable,
        LesionChartDialog
    },
    data() {
        return {
            cornerstoneEle: null,
            series: [],
            viewports: [],
            volumes: [],
            apis: [],
            uid: null,
            toEndIndex: -1,
            arr1: [
                { id: 1, name: 'www.itxst.com（不允许停靠）' },
                { id: 2, name: 'www.jd.com' },
                { id: 3, name: 'www.baidu.com' },
                { id: 5, name: 'www.google.com' },
                { id: 4, name: 'www.taobao.com（不允许拖拽）' }
            ],
            arr2: [
                { id: 5, name: 'aaaaa' },
                { id: 6, name: 'bbbbb' },
                { id: 7, name: 'ccccc' },
            ],
            LesionChartDialogVisible: false,
            LesionChartDialoglist: []
        }
    },
    mounted() {
        this.series = appstate.series.petNetwork;
        this.viewports.push({
            uid: 'test1',
            imageIds: appstate.series.petNetwork
        })
        console.log(this.viewports)
        // mprState.series[0] = this.series
        // mprState.vtkVolumes = []
        // this.loadGithubCornerstone()
        
        // 加载图像
        // this.loadImage().then()

    },
    methods: {
        onClickTest(){

            html2canvas(document.querySelector("#capture"), {
            allowTaint: true, useCORS: true
            }).then(canvas => {
                let dataURL = canvas.toDataURL("image/png");
                console.log(dataURL)
            })
            // const dom = document.getElementsByClassName('viewport-element');
            // const draw = cornerstoneTools.import('drawing/draw')
            // const drawLine = cornerstoneTools.import('drawing/drawLine');
            // const getNewContext = cornerstoneTools.import('drawing/getNewContext');

            // const iamgeObj = cornerstone.getEnabledElement(dom[0])
            // const context = getNewContext(iamgeObj.canvas);
            // draw(context, context => {
            //     drawLine(
            //         context,
            //         dom[0],
            //         {x: 250, y: 100},
            //         {x: 100, y: 100},
            //         { 
            //             // color,
            //             color: '#9ACD32',
            //             lineWidth: 1
            //         }
            //     );
                
            // });
        },
        loadGithubCornerstone(){
            const ctPromises = this.series.map((imageId) => {
                return cornerstone.loadAndCacheImage(imageId).then();
            });
            Promise.all(ctPromises).then(() => {
                this.loaderGithubCornerstoneSeries()
            })
        },
        async loaderGithubCornerstoneSeries(){

            const seriesModule = cornerstone.metaData.get('generalSeriesModule',this.series[0])
            this.uid = seriesModule.seriesInstanceUID;

            mprState[this.uid] = {}

            mprState[this.uid].series = this.series
            
            // mprState[this.uid].vtkVolumes = []

            const data = await this._getSliceSeries('ax', 3.27)
            // 冠状位
            this.viewports.push({
                sModality: 'CT',
                orientation: 'y',
                imageIds: data[0],
                uid: data[0][0],
            })
            // setTimeout(() => {
            //     const doms = document.getElementsByClassName('viewport-element');
            //     doms.forEach(el => {
            //         let viewport = cornerstone.getViewport(el);             // 获取视图
            //         if (!viewport) return;
            //         // cornerstoneTools.scrollToIndex(el, 20)
            //         let obj = cornerstone.getEnabledElement(el)  
            //         viewport.voi.windowWidth = 400;
            //         viewport.voi.windowCenter = 40;
            //         cornerstone.setViewport(el, viewport);
            //     });
            // }, 0);
            const { vtkVolume: imageDataObject } = await tryGetVtkVolumeForSeriesNumber(this.uid);

             const imageData = imageDataObject.vtkImageData;
            const range = imageData
                .getPointData()
                .getScalars()
                .getRange();

            const mapper = vtkVolumeMapper.newInstance();
            const vol = vtkVolume.newInstance();

            const rgbTransferFunction = vol
                .getProperty()
                .getRGBTransferFunction(0);

            mapper.setInputData(imageData);
            mapper.setMaximumSamplesPerRay(2000);
            rgbTransferFunction.setMappingRange(500, 3000);
            rgbTransferFunction.setRange(range[0], range[1]);
            vol.setMapper(mapper);
            this.volumes = [vol]
        },
        async _getSliceSeries(direction = 'coronal', thickness = 3){

            let ctData = {}
            let ptData = {}

            // 通过vtk容积，vtk方式获取图像的一些信息
            await this._getVtkVolume(this.uid, thickness).then(res => {
                ctData = res
            })
            // await this._getVtkVolume(1, thickness).then(res => {
            //     ptData = res
            // })

            let ctIds = [], ptIds = []
            let count = 0
            let start = 0
            switch (direction) {
                case 'sagittal':
                    count = ctData.xCount
                    start = ctData.xStart
                    for (let index = 0; index < count; index++) {
                        ctIds.push('mpr:' + this.uid + ':0,1,0,0,0,-1:' + start + ',0,0')
                        ptIds.push('mpr:1:0,1,0,0,0,-1:' + start + ',0,0')
                        start -= thickness
                    }
                    break;
                case 'coronal':
                    count = ctData.yCount
                    start = ctData.yStart
                    for (let index = 0; index < count; index++) {
                        ctIds.push('mpr:' + this.uid + ':1,0,0,0,0,-1:0,' + start + ',0')
                        ptIds.push('mpr:1:1,0,0,0,0,-1:0,' + start + ',0')
                        start -= thickness
                    }
                    break;
                default:
                    count = ctData.zCount
                    start = ctData.zStart
                    for (let index = 0; index < count; index++) {
                        ctIds.push('mpr:' + this.uid + ':1,0,0,0,1,0:0,0,' + start)
                        ptIds.push('mpr:1:1,0,0,0,1,0:0,0,' + start)
                        start -= thickness
                    }
                    break;
            }

            return [ctIds, ptIds]
        },
        async _getVtkVolume(seriesIndex = 0, thickness = 3){
            const { vtkVolume } = await tryGetVtkVolumeForSeriesNumber(seriesIndex);
            const vtkImageData = vtkVolume.vtkImageData;

            const [x0, y0, z0] = vtkImageData.getOrigin();  // x,y,z 原点
            const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
            const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

            let xStart = x0 + xSpacing * (xMax - xMin);  // x最大值(ipp 切片x最大位)
            let yStart = y0 + ySpacing * (yMax - yMin);  // y最大值
            let zStart = z0 + zSpacing * (zMax - zMin);  // z最大值

            // 算出有用的张数
            let xNmax = x0 + xSpacing * (xMin + xMax)
            let xCount = (xNmax - x0) / thickness;

            let yNmax = y0 + ySpacing * (yMin + yMax)
            let yCount = (yNmax - y0) / thickness;

            let zNmax = z0 + zSpacing * (zMin + zMax)
            let zCount = (zNmax - z0) / thickness;

            return { xCount, yCount, zCount, xStart, yStart, zStart}
        },
        // async loadImage(){
        //     cornerstone.loadAndCacheImage(this.series[0]).then(image => {
        //         cornerstone.displayImage(this.cornerstoneEle, image);

        //         var cornerstoneStack = {
        //             imageIds: [...this.series],
        //             currentImageIdIndex: 0,
        //         };

        //         cornerstoneTools.addStackStateManager(this.cornerstoneEle, ['stack']);
        //         cornerstoneTools.addToolState(this.cornerstoneEle, 'stack', cornerstoneStack);

        //         cornerstoneTools.addToolForElement(this.cornerstoneEle, cornerstoneTools[`StackScrollTool`])
        //         cornerstoneTools.addToolForElement(this.cornerstoneEle, cornerstoneTools[`WwwcTool`])
        //         cornerstoneTools.addToolForElement(this.cornerstoneEle, cornerstoneTools[`StackScrollMouseWheelTool`])
        //         cornerstoneTools.addToolForElement(this.cornerstoneEle, cornerstoneTools[`ZoomTool`])

        //         cornerstoneTools.setToolActive('StackScrollMouseWheel', { })
        //         cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 2 })
        //         cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 })
        //         // setTimeout(() => {
        //         //     let layers = cornerstone.getLayers(this.cornerstoneEle)
        //         //     cornerstone.setActiveLayer(this.cornerstoneEle, layers[1].layerId)
        //         // }, 2000);
        //     })
        // },

            // 保存控件Api
        storeApi(viewportIndex) {
            return (api) => {
                this.apis[viewportIndex] = api;

                const apis = this.apis;
            
                // 获取渲染的窗口
                const renderWindow = api.genericRenderWindow.getRenderWindow();


                // 设置最大密度投影
                // const mapper = api.volumes[0].getMapper();
                // if (mapper.setBlendModeToMaximumIntensity) {
                // mapper.setBlendModeToMaximumIntensity();
                // }

                // let istyle = vtkInteractorStyleMPRWindowLevel.newInstance();
                // api.setInteractorStyle({
                //     istyle,
                //     configuration: { apis, apiIndex: viewportIndex },
                // }); 
                // 层厚
                api.setSlabThickness(3);


                // 渲染窗口
                renderWindow.render();

            };
        },

    },
    computed: {
    } 
}
</script>
<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
}
</style>