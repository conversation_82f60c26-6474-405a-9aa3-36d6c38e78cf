<template>
    <div style="padding: 20px;">
        <h2>内存测试统计</h2>

        <div style="display: flex; gap: 30px; margin: 20px 0;">
            <div class="stat-card">
                <h3>原始信息</h3>
                <div class="stat-item">
                    <span class="label">原始序列数：</span>
                    <span class="value">{{ originalSeriesCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">下载图像数量：</span>
                    <span class="value">{{ totalImageCount }}</span>
                </div>
            </div>

            <div class="stat-card" style="min-width: 320px;">
                <h3>下载进度</h3>
                <div class="stat-item">
                    <span class="label">已下载图像：</span>
                    <span class="value">{{ percentage.loadCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">下载进度：</span>
                    <span class="value">{{ downloadProgress }}%</span>
                </div>
                <div class="stat-item">
                    <span class="label">当前下载序列：</span>
                    <span class="value">{{ currentDownloadingSeries || '未开始' }}</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>序列详情</h3>
                <div class="stat-item">
                    <span class="label">可重建序列数：</span>
                    <span class="value">{{ seriesListAll.length }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">下载错误次数：</span>
                    <span class="value">{{ errCount }}</span>
                </div>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button @click="startDownload" :disabled="isDownloading" style="padding: 10px 20px; font-size: 16px;">
                {{ isDownloading ? '下载中...' : '开始下载测试' }}
            </button>
        </div>

        <div v-if="seriesListAll.length > 0" style="margin-top: 20px;">
            <h3>序列列表</h3>
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <thead>
                    <tr style="background-color: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 8px;">序号</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">设备类型</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">序列描述</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">图像数量</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(series, index) in seriesListAll" :key="series.seriesUID">
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ index + 1 }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ series.modality }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ series.seriesDesc }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ series.count }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">
                            <span :style="{ color: getSeriesStatusColor(series.seriesUID) }">
                                {{ getSeriesStatus(series.seriesUID) }}
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
<script>
import appState from '$library/cornerstone/mpr/store/appState.js';
import { getNewImageIds, getVolumeSliceNumber } from '$library/cornerstone/function/getImageIds.js';

export default {

    data() {
        return {
            seriesListAll: [], // 序列数量
            errCount: 0,       // 下图错误次数
            percentage: {
                loadCount: 0   // 加载原图数量
            },
            buildImageList: [], // 可以重建的图像
            originalSeriesCount: 0, // 原始序列数
            totalImageCount: 0, // 总图像数量
            isDownloading: false, // 是否正在下载
            currentDownloadingSeries: '', // 当前下载的序列描述
            seriesStatusMap: new Map(), // 序列状态映射
        }
    },
    async mounted() {
        // 1、获取有图像整体信息（study）
        this.$store.commit('addRequestList')
        await this.$store.dispatch('loadStudy')

        let arr = Array.from(this.$store.state.seriesMap.values())

        // 计算原始序列数
        this.originalSeriesCount = arr.reduce((total, item) => {
            return total + item.instanceList.size;
        }, 0);

        // 2、所有序列合并为数组
        this.seriesListAll = []
        arr.map(item => {
            // 过滤保留可以重建的
            const instanceList = this.$fun.deepClone(Array.from(item.instanceList.values())).filter(item => {
                return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
            })
            console.log(item)
            instanceList.forEach(item => {
                this.seriesListAll.push({
                    modality: item.sModality,
                    studyUID: item.sStudyInstanceUID,
                    seriesUID: item.sSeriesInstanceUID,
                    seriesDesc: item.sSeriesDescription,
                    imageIds: item.imageIds,
                    count: item.iInstanceCount
                })
                // 初始化序列状态
                this.seriesStatusMap.set(item.sSeriesInstanceUID, '等待下载');
            })
        })

        // 计算总图像数量
        this.totalImageCount = this.seriesListAll.reduce((total, series) => {
            return total + series.count;
        }, 0);

        console.log('统计信息：', {
            originalSeriesCount: this.originalSeriesCount,
            rebuildableSeriesCount: this.seriesListAll.length,
            totalImageCount: this.totalImageCount
        });

        // 3、选择需要重建的序列
        // this.selectSeries()

        // 合并

    },
    methods: {
        // 开始下载测试
        startDownload() {
            if (this.isDownloading) return;

            // 重置统计信息
            this.percentage.loadCount = 0;
            this.errCount = 0;
            this.isDownloading = true;
            this.currentDownloadingSeries = '';

            // 重置所有序列状态
            this.seriesListAll.forEach(series => {
                this.seriesStatusMap.set(series.seriesUID, '等待下载');
            });

            // 开始下载
            this.selectSeries().finally(() => {
                this.isDownloading = false;
                this.currentDownloadingSeries = '下载完成';
            });
        },

        // 获取序列状态
        getSeriesStatus(seriesUID) {
            return this.seriesStatusMap.get(seriesUID) || '未知';
        },

        // 获取序列状态颜色
        getSeriesStatusColor(seriesUID) {
            const status = this.seriesStatusMap.get(seriesUID);
            switch (status) {
                case '下载中': return '#409EFF';
                case '下载完成': return '#67C23A';
                case '下载失败': return '#F56C6C';
                case '等待下载': return '#909399';
                default: return '#909399';
            }
        },
        // 步骤3
        async selectSeries() {
            let loadImage = []

            // 串行下载每个 series
            for (let i = 0; i < this.seriesListAll.length; i++) {
                const series = this.seriesListAll[i];

                // 更新当前下载序列信息
                this.currentDownloadingSeries = `${series.seriesDesc} (${i + 1}/${this.seriesListAll.length})`;
                this.seriesStatusMap.set(series.seriesUID, '下载中');

                // series 没有 imageIds，所有要从 Map 中获取
                loadImage = loadImage.concat(series.imageIds)

                if (!appState[series.seriesUID]) {
                    appState[series.seriesUID] = {}
                }
                if (!appState[series.seriesUID].series) {
                    appState[series.seriesUID].series = series.imageIds
                }

                try {
                    // 等待当前 series 下载完成后再进行下一个
                    await this.loadSeriesImages(series.imageIds, series.seriesUID);
                    this.seriesStatusMap.set(series.seriesUID, '下载完成');
                } catch (error) {
                    console.error(`Series ${series.seriesUID} 下载失败:`, error);
                    this.seriesStatusMap.set(series.seriesUID, '下载失败');
                }
            }

            console.log('原图下载完成！')

            // 下载体积图像
        },

        // 下载单个 series 的所有图像
        async loadSeriesImages(imageIds, seriesUID) {
            const errImageIds = [];

            let imagePromises = imageIds.map((imageId) => {
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });

            return this.loadPromiseAll(imagePromises, errImageIds, seriesUID);
        },
        // 统一下载图像
        loadPromiseAll(imageIds, errImageIds, seriesUID) {
            const errMaxValue = 6
            return new Promise((resolve, reject) => {
                Promise.all(imageIds).then(() => {
                    // 错误
                    if (errImageIds.length && this.errCount < errMaxValue) {
                        this.errCount += 1;
                        this.loadError(errImageIds, seriesUID).then(resolve).catch(reject);
                        return;
                    }
                    this.errCount = 0;
                    this.loaderSeries(seriesUID);
                    resolve(true);
                }).catch(() => {
                    if (errImageIds.length && this.errCount < errMaxValue) {
                        this.errCount += 1;
                        this.loadError(errImageIds, seriesUID).then(resolve).catch(reject);
                    } else {
                        reject(new Error(`Series ${seriesUID} 下载失败`));
                    }
                })
            });
        },
        async loadError(imageIds, seriesUID) {
            console.log('图像错误，再次下载');
            const errImageIds = [];
            const imagePromise = imageIds.map((imageId) => {
                // 错误的图像，加上不缓存标识，从服务器获取最新。
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });
            return this.loadPromiseAll(imagePromise, errImageIds, seriesUID);
        },
        async loaderSeries(seriesUID) {
            console.log('加载序列成功！', seriesUID)

            let thickness = 3
            const series = this.seriesListAll.find(item => item.seriesUID === seriesUID)

            if (series) {
                await cornerstone.loadAndCacheImage(series.imageIds[0]);
                await cornerstone.loadAndCacheImage(series.imageIds[1]);
                let startMeta = cornerstone.metaData.get('imagePlaneModule', series.imageIds[0]);
                let startMeta1 = cornerstone.metaData.get('imagePlaneModule', series.imageIds[1]);
                thickness = Math.abs(startMeta.sliceLocation - startMeta1.sliceLocation) || 2.5;
            }
            let ctData = await getVolumeSliceNumber(seriesUID, thickness)
            // 建体成功
            let [ ids ] = getNewImageIds('axial', ctData, thickness, seriesUID, '');

            this.buildImageList.push(ids)
        }
    },
    computed: {
        // 下载进度百分比
        downloadProgress() {
            if (this.totalImageCount === 0) return 0;
            return Math.round((this.percentage.loadCount / this.totalImageCount) * 100);
        }
    }
}
</script>

<style scoped>
.stat-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: #f9f9f9;
    min-width: 200px;
}

.stat-card h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.stat-item .label {
    color: #666;
}

.stat-item .value {
    font-weight: bold;
    color: #333;
}

button {
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button:hover:not(:disabled) {
    background-color: #66b1ff;
}
</style>
<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
}
</style>