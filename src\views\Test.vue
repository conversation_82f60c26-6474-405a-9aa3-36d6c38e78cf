<template>
    <div>
        123
    </div>
</template>
<script>
import appState from '$library/cornerstone/mpr/store/appState.js';

export default {

    data() {
        return {
            seriesListAll: [],
            errCount: 0,      // 下图错误次数
            percentage: {
                loadCount: 0 // 加载原图数量
            }
        }
    },
    async mounted() {
        // 1、获取有图像整体信息（study）
        this.$store.commit('addRequestList')
        await this.$store.dispatch('loadStudy')

        let arr = Array.from(this.$store.state.seriesMap.values())

        // 2、所有序列合并为数组
        this.seriesListAll = []
        arr.map(item => {
            // 过滤保留可以重建的
            const instanceList = this.$fun.deepClone(Array.from(item.instanceList.values())).filter(item => {
                return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
            })
            instanceList.forEach(item => {
                this.seriesListAll.push({
                    modality: item.sModality,
                    studyUID: item.sStudyInstanceUID,
                    seriesUID: item.sSeriesInstanceUID,
                    seriesDesc: item.sSeriesDescription,
                    count: item.iInstanceCount
                })
            })
        })

        console.log(this.seriesListAll)

        // 3、选择需要重建的序列
        this.selectSeries()

        // 合并

    },
    methods: {
        // 步骤3
        selectSeries() {
            let loadImage = []
            this.seriesListAll.forEach(series => {
                const allSeries = this.$store.state.seriesMap.get(series.studyUID).instanceList;
                const curSeries = allSeries.get(series.seriesUID)
                
                // series 没有 imageIds，所有要从 Map 中获取
                loadImage = loadImage.concat(curSeries.imageIds)

                if (!appState[series.seriesUID]) {
                    appState[series.seriesUID] = {}
                }
                if (!appState[series.seriesUID].series) {
                    appState[series.seriesUID].series = curSeries.imageIds
                }

                const errImageIds = [];
                
                let imagePromises = curSeries.imageIds.map((imageId) => {
                    return cornerstone.loadAndCacheImage(imageId).then(() => {
                        this.percentage.loadCount += 1
                    }).catch(() => {
                        errImageIds.push(imageId)
                    });
                });

                this.loadPromiseAll(imagePromises, errImageIds, series.seriesUID);
            })
        
        },
        // 统一下载图像
        loadPromiseAll(imageIds, errImageIds, seriesUID) {
            const errMaxValue = 6
            Promise.all(imageIds).then(() => {
                // 错误
                if (errImageIds.length && this.errCount < errMaxValue) {
                    this.errCount += 1;
                    this.loadError(errImageIds, seriesUID);
                    return;
                }
                this.errCount = 0;
                this.loaderSeries(seriesUID);
            }).catch(() => {
                if (errImageIds.length && this.errCount < errMaxValue) {
                    this.errCount += 1;
                    this.loadError(errImageIds, seriesUID);
                }
            })
        },
        async loadError(imageIds, seriesUID) {
            console.log('图像错误，再次下载');
            const errImageIds = [];
            const imagePromise = imageIds.map((imageId) => {
                // 错误的图像，加上不缓存标识，从服务器获取最新。
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });
            this.loadPromiseAll(imagePromise, errImageIds, seriesUID)
        },
        loaderSeries(seriesUID) {
            console.log('加载序列成功！', seriesUID)
        }
    },
    computed: {
    } 
}
</script>
<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
}
</style>