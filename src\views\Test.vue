<template>
    <div>
        123
    </div>
</template>
<script>
import appState from '$library/cornerstone/mpr/store/appState.js';
import { getNewImageIds, getVolumeSliceNumber } from '$library/cornerstone/function/getImageIds.js';

export default {

    data() {
        return {
            seriesListAll: [], // 序列数量
            errCount: 0,       // 下图错误次数
            percentage: {
                loadCount: 0   // 加载原图数量
            },
            buildImageList: [], // 可以重建的图像
        }
    },
    async mounted() {
        // 1、获取有图像整体信息（study）
        this.$store.commit('addRequestList')
        await this.$store.dispatch('loadStudy')

        let arr = Array.from(this.$store.state.seriesMap.values())

        // 2、所有序列合并为数组
        this.seriesListAll = []
        arr.map(item => {
            // 过滤保留可以重建的
            const instanceList = this.$fun.deepClone(Array.from(item.instanceList.values())).filter(item => {
                return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
            })
            console.log(item)
            instanceList.forEach(item => {
                this.seriesListAll.push({
                    modality: item.sModality,
                    studyUID: item.sStudyInstanceUID,
                    seriesUID: item.sSeriesInstanceUID,
                    seriesDesc: item.sSeriesDescription,
                    imageIds: item.imageIds,
                    count: item.iInstanceCount
                })
            })
        })

        console.log(this.seriesListAll)

        // 3、选择需要重建的序列
        this.selectSeries()

        // 合并

    },
    methods: {
        // 步骤3
        async selectSeries() {
            let loadImage = []

            // 串行下载每个 series
            for (let i = 0; i < this.seriesListAll.length; i++) {
                const series = this.seriesListAll[i];
                // const allSeries = this.$store.state.seriesMap.get(series.studyUID).instanceList;
                // const curSeries = allSeries.get(series.seriesUID)

                // series 没有 imageIds，所有要从 Map 中获取
                loadImage = loadImage.concat(series.imageIds)

                if (!appState[series.seriesUID]) {
                    appState[series.seriesUID] = {}
                }
                if (!appState[series.seriesUID].series) {
                    appState[series.seriesUID].series = series.imageIds
                }

                // 等待当前 series 下载完成后再进行下一个
                await this.loadSeriesImages(series.imageIds, series.seriesUID);
            }

            console.log('原图下载完成！')
        },

        // 下载单个 series 的所有图像
        async loadSeriesImages(imageIds, seriesUID) {
            const errImageIds = [];

            let imagePromises = imageIds.map((imageId) => {
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });

            return this.loadPromiseAll(imagePromises, errImageIds, seriesUID);
        },
        // 统一下载图像
        loadPromiseAll(imageIds, errImageIds, seriesUID) {
            const errMaxValue = 6
            return new Promise((resolve, reject) => {
                Promise.all(imageIds).then(() => {
                    // 错误
                    if (errImageIds.length && this.errCount < errMaxValue) {
                        this.errCount += 1;
                        this.loadError(errImageIds, seriesUID).then(resolve).catch(reject);
                        return;
                    }
                    this.errCount = 0;
                    this.loaderSeries(seriesUID);
                    resolve(true);
                }).catch(() => {
                    if (errImageIds.length && this.errCount < errMaxValue) {
                        this.errCount += 1;
                        this.loadError(errImageIds, seriesUID).then(resolve).catch(reject);
                    } else {
                        reject(new Error(`Series ${seriesUID} 下载失败`));
                    }
                })
            });
        },
        async loadError(imageIds, seriesUID) {
            console.log('图像错误，再次下载');
            const errImageIds = [];
            const imagePromise = imageIds.map((imageId) => {
                // 错误的图像，加上不缓存标识，从服务器获取最新。
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });
            return this.loadPromiseAll(imagePromise, errImageIds, seriesUID);
        },
        async loaderSeries(seriesUID) {
            console.log('加载序列成功！', seriesUID)

            let thickness = 3
            const series = this.seriesListAll.find(item => item.seriesUID === seriesUID)

            if (series) {
                await cornerstone.loadAndCacheImage(series.imageIds[0]);
                await cornerstone.loadAndCacheImage(series.imageIds[1]);
                let startMeta = cornerstone.metaData.get('imagePlaneModule', series.imageIds[0]);
                let startMeta1 = cornerstone.metaData.get('imagePlaneModule', series.imageIds[1]);
                thickness = Math.abs(startMeta.sliceLocation - startMeta1.sliceLocation) || 2.5;
            }
            let ctData = await getVolumeSliceNumber(seriesUID, thickness)
            // 建体成功
            let [ ids ] = getNewImageIds('axial', ctData, thickness, seriesUID, '');

            this.buildImageList.push(ids)
        }
    },
    computed: {
    } 
}
</script>
<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
}
</style>