<template>
    <div style="padding: 20px; height: 100%;">
        <h2 style="font-size: 32px; padding: 20px 0; background: #f5f5f5;">内存测试统计</h2>

        <div style="display: flex; gap: 30px; margin: 20px 0;">
            <div class="stat-card">
                <h3>原始信息</h3>
                <div class="stat-item">
                    <span class="label">原始序列数：</span>
                    <span class="value">{{ seriesListAll.length }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">原始图像数量：</span>
                    <span class="value">{{ totalImageCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">下载错误次数：</span>
                    <span class="value">{{ errCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">已下载图像：</span>
                    <span class="value">{{ percentage.loadCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">下载进度：</span>
                    <span class="value">{{ downloadProgress }}%</span>
                </div>
                <div class="stat-item">
                    <span class="label">当前下载序列：</span>
                    <span class="value">{{ currentDownloadingSeries || '未开始' }}</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>重建信息</h3>
                <div class="stat-item">
                    <span class="label">体数据：</span>
                    <span class="value">{{ percentage.volumeCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">体数据可生成图数量：</span>
                    <span class="value">{{ percentage.volumeImage }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">体数据生成图像成功：</span>
                    <span class="value">{{ percentage.loadBuildCount }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">体数据生成图像失败：</span>
                    <span class="value">{{ percentage.loadBuildCountErr }}</span>
                </div>
                <div class="stat-item">
                    <span class="label">重建图进度：</span>
                    <span class="value">{{ downloadProgressBuild }}%</span>
                </div>
                
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button @click="startDownload" :disabled="isDownloading" style="padding: 10px 20px; font-size: 16px; margin-right: 10px;">
                {{ isDownloading ? '下载中...' : '开始下载测试' }}
            </button>
            <button @click="showMemoryHistory" style="padding: 10px 20px; font-size: 16px; background-color: #67C23A;">
                查看内存记录
            </button>
        </div>
        
        <div class="stat-card" style="min-width: 300px;">
            <h3>内存使用情况</h3>
            <div class="stat-item">
                <span class="label">已用内存：</span>
                <span class="value">{{ formatMemorySize(memoryInfo.usedJSHeapSize) }}</span>
            </div>
            <div class="stat-item">
                <span class="label">总分配内存：</span>
                <span class="value">{{ formatMemorySize(memoryInfo.totalJSHeapSize) }}</span>
            </div>
            <div class="stat-item">
                <span class="label">内存限制：</span>
                <span class="value">{{ formatMemorySize(memoryInfo.jsHeapSizeLimit) }}</span>
            </div>
            <div class="stat-item">
                <span class="label">内存使用率：</span>
                <span class="value" :style="{ color: getMemoryUsageColor() }">{{ memoryUsagePercent }}%</span>
            </div>
        </div>

        <div v-if="seriesListAll.length > 0" style="margin-top: 20px;">
            <h3>序列列表</h3>
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <thead>
                    <tr style="background-color: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 8px;">序号</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">设备类型</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">序列描述</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">图像数量</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(series, index) in seriesListAll" :key="series.seriesUID">
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ index + 1 }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ series.modality }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ series.seriesDesc }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{{ series.count }}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">
                            <span :style="{ color: getSeriesStatusColor(series.seriesUID) }">
                                {{ getSeriesStatus(series.seriesUID) }}
                            </span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div>
            <div oncontextmenu="return false" ref="elementBuild" style="width: 400px; height: 400px;">
                <canvas class="cornerstone-canvas" ref="canvas"/>
            </div>
        </div>

        <!-- 内存历史记录弹窗 -->
        <div v-if="showHistoryDialog" class="dialog-overlay" @click="closeHistoryDialog">
            <div class="dialog-content" @click.stop>
                <div class="dialog-header">
                    <h3>内存使用历史记录</h3>
                    <button @click="closeHistoryDialog" class="close-btn">×</button>
                </div>
                <div class="dialog-body">
                    <div v-if="memoryHistory.length === 0" style="text-align: center; padding: 40px; color: #999;">
                        暂无内存记录
                    </div>
                    <div v-else>
                        <div v-for="(record, index) in memoryHistory" :key="index" class="memory-record">
                            <div class="record-header">
                                <h4>测试记录 {{ index + 1 }} - {{ record.timestamp }}</h4>
                                <div class="record-summary">
                                    总序列: {{ record.totalSeries }} | 总图像: {{ record.totalImages }} |
                                    最大内存: {{ formatMemorySize(record.maxMemory.usedJSHeapSize) }}
                                </div>
                            </div>
                            <div class="record-details">
                                <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
                                    <thead>
                                        <tr style="background-color: #f0f0f0;">
                                            <th style="border: 1px solid #ddd; padding: 4px;">原始序列</th>
                                            <th style="border: 1px solid #ddd; padding: 4px;">图像数</th>
                                            <th style="border: 1px solid #ddd; padding: 4px;">已用内存</th>
                                            <th style="border: 1px solid #ddd; padding: 4px;">总内存</th>
                                            <th style="border: 1px solid #ddd; padding: 4px;">使用率</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(snapshot, sIndex) in record.snapshots" :key="sIndex">
                                            <td style="border: 1px solid #ddd; padding: 4px;">{{ sIndex + 1 }}</td>
                                            <td style="border: 1px solid #ddd; padding: 4px;">{{ snapshot.loadedImages }}</td>
                                            <td style="border: 1px solid #ddd; padding: 4px;">{{ formatMemorySize(snapshot.memory.usedJSHeapSize) }}</td>
                                            <td style="border: 1px solid #ddd; padding: 4px;">{{ formatMemorySize(snapshot.memory.totalJSHeapSize) }}</td>
                                            <td style="border: 1px solid #ddd; padding: 4px;">{{ Math.round((snapshot.memory.usedJSHeapSize / snapshot.memory.jsHeapSizeLimit) * 100) }}%</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button @click="clearMemoryHistory" style="background-color: #F56C6C; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-right: 10px;">
                        清除所有记录
                    </button>
                    <button @click="closeHistoryDialog" style="background-color: #909399; color: white; padding: 8px 16px; border: none; border-radius: 4px;">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import appState from '$library/cornerstone/mpr/store/appState.js';
import { getNewImageIds, getVolumeSliceNumber } from '$library/cornerstone/function/getImageIds.js';
import event from '$src/event.js'
export default {

    data() {
        return {
            seriesListAll: [], // 序列数量
            errCount: 0,       // 下图错误次数
            percentage: {
                volumeCount: 0, // 体积数
                volumeImage: 0, // 体积图像
                loadBuildCount: 0, // 加载体积图成功
                loadBuildCountErr: 0, // 加载体积图失败
                loadCount: 0     // 加载原图数量
            },
            buildImageList: [], // 可以重建的图像
            totalImageCount: 0, // 总图像数量
            isDownloading: false, // 是否正在下载
            currentDownloadingSeries: '', // 当前下载的序列描述
            seriesStatusMap: new Map(), // 序列状态映射

            // 内存监控相关
            memoryInfo: {
                usedJSHeapSize: 0,
                totalJSHeapSize: 0,
                jsHeapSizeLimit: 0
            },
            memoryTimer: null, // 内存监控定时器
            currentTestRecord: null, // 当前测试记录
            memoryHistory: [], // 内存历史记录
            showHistoryDialog: false, // 显示历史记录弹窗
            completedSeriesCount: 0, // 已完成序列数

            taskNumber: 0,

            elementBuild: null,
        }
    },
    async mounted() {
        this.$nextTick(() => {
            this.elementBuild = this.$refs.elementBuild;
            cornerstone.enable(this.elementBuild, {});
        })
        // 1、获取有图像整体信息（study）
        this.$store.commit('addRequestList')
        await this.$store.dispatch('loadStudy')

        let arr = Array.from(this.$store.state.seriesMap.values())

        // 2、所有序列合并为数组
        this.seriesListAll = []
        arr.map(item => {
            // 过滤保留可以重建的
            const instanceList = this.$fun.deepClone(Array.from(item.instanceList.values())).filter(item => {
                return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
            })
            console.log(item)
            if (instanceList.length > 5) {
                return
            }
            instanceList.forEach(item => {
                this.seriesListAll.push({
                    modality: item.sModality,
                    studyUID: item.sStudyInstanceUID,
                    seriesUID: item.sSeriesInstanceUID,
                    seriesDesc: item.sSeriesDescription,
                    imageIds: item.imageIds,
                    count: item.iInstanceCount
                })
                // 初始化序列状态
                this.seriesStatusMap.set(item.sSeriesInstanceUID, '等待下载');
            })
        })

        // 计算总图像数量
        this.totalImageCount = this.seriesListAll.reduce((total, series) => {
            return total + series.count;
        }, 0);

        console.log('统计信息：', {
            rebuildableSeriesCount: this.seriesListAll.length,
            totalImageCount: this.totalImageCount
        });

        // 加载内存历史记录
        this.loadMemoryHistory();

        // 开始内存监控
        this.startMemoryMonitoring();

        // 3、选择需要重建的序列
        // this.selectSeries()

        // 合并

    },
    beforeDestroy() {
        // 清理内存监控定时器
        if (this.memoryTimer) {
            clearInterval(this.memoryTimer);
        }
    },
    methods: {
        updateLoadTabTask(type = 'add') {
            console.log('任务类型', type)
            if (type === 'add') {
                this.taskNumber += 1;
                event.$emit('updateLoadTabTask');
            } else if (type === 'del' && this.taskNumber) {
                this.taskNumber -= 1;
                event.$emit('updateLoadTabTask', 'del');
            }
        },
        // 开始下载测试
        startDownload() {
            if (this.isDownloading) return;

            // 清除上一次的内存记录缓存
            this.clearCurrentTestCache();

            // 重置统计信息
            this.percentage.loadCount = 0;
            this.percentage.volumeImage = 0;
            this.percentage.volumeCount = 0;
            this.percentage.loadBuildCount = 0;
            this.percentage.loadBuildCountErr = 0;

            this.errCount = 0;
            this.isDownloading = true;
            this.currentDownloadingSeries = '';
            this.completedSeriesCount = 0;

            // 重置所有序列状态
            this.seriesListAll.forEach(series => {
                this.seriesStatusMap.set(series.seriesUID, '等待下载');
            });

            // 初始化当前测试记录
            this.initCurrentTestRecord();

            // 开始下载
            this.selectSeries().finally(() => {
                this.isDownloading = false;
                this.currentDownloadingSeries = '下载完成';
                // 保存测试记录到历史
                this.saveTestRecord();
            });
        },

        // 获取序列状态
        getSeriesStatus(seriesUID) {
            return this.seriesStatusMap.get(seriesUID) || '未知';
        },

        // 获取序列状态颜色
        getSeriesStatusColor(seriesUID) {
            const status = this.seriesStatusMap.get(seriesUID);
            switch (status) {
                case '下载中': return '#409EFF';
                case '下载完成': return '#67C23A';
                case '下载失败': return '#F56C6C';
                case '等待下载': return '#909399';
                default: return '#909399';
            }
        },

        // 内存监控相关方法
        startMemoryMonitoring() {
            if (!window.performance || !window.performance.memory) {
                console.warn('浏览器不支持内存监控');
                return;
            }

            // 每秒更新一次内存信息
            this.memoryTimer = setInterval(() => {
                this.updateMemoryInfo();
            }, 1000);

            // 立即更新一次
            this.updateMemoryInfo();
        },

        updateMemoryInfo() {
            if (window.performance && window.performance.memory) {
                this.memoryInfo = {
                    usedJSHeapSize: window.performance.memory.usedJSHeapSize,
                    totalJSHeapSize: window.performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
                };
            }
        },

        formatMemorySize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        getMemoryUsageColor() {
            const percent = this.memoryUsagePercent;
            if (percent < 50) return '#67C23A'; // 绿色
            if (percent < 80) return '#E6A23C'; // 橙色
            return '#F56C6C'; // 红色
        },

        // 初始化当前测试记录
        initCurrentTestRecord() {
            this.currentTestRecord = {
                timestamp: new Date().toLocaleString(),
                totalSeries: this.seriesListAll.length,
                totalImages: this.totalImageCount,
                snapshots: [],
                maxMemory: {
                    usedJSHeapSize: 0,
                    totalJSHeapSize: 0,
                    jsHeapSizeLimit: 0
                }
            };
        },
        // 步骤3
        async selectSeries() {
            let loadImage = []

            // 串行下载每个 series
            for (let i = 0; i < this.seriesListAll.length; i++) {
                const series = this.seriesListAll[i];

                // 更新当前下载序列信息
                this.currentDownloadingSeries = `${series.seriesDesc} (${i + 1}/${this.seriesListAll.length})`;
                this.seriesStatusMap.set(series.seriesUID, '下载中');

                // series 没有 imageIds，所有要从 Map 中获取
                loadImage = loadImage.concat(series.imageIds)

                if (!appState[series.seriesUID]) {
                    appState[series.seriesUID] = {}
                }
                if (!appState[series.seriesUID].series) {
                    appState[series.seriesUID].series = series.imageIds
                }

                this.updateLoadTabTask();

                try {
                    // 等待当前 series 下载完成后再进行下一个
                    await this.loadSeriesImages(series.imageIds, series.seriesUID);
                    this.seriesStatusMap.set(series.seriesUID, '下载完成');
                    this.completedSeriesCount++;

                    // 记录当前序列完成时的内存快照
                    this.recordMemorySnapshot();
                } catch (error) {
                    console.error(`Series ${series.seriesUID} 下载失败:`, error);
                    this.seriesStatusMap.set(series.seriesUID, '下载失败');
                }
            }

            console.log('原图下载完成！')


            for (let i = 0; i < this.buildImageList.length; i++) {
                const imageIds = this.buildImageList[i];

                // 下载体积图像
                await this.loadBuildImages(imageIds)
            }

        },
        async loadBuildImages(imageIds = []) {
            let imagePromises = imageIds.map((imageId) => {
                return cornerstone.loadAndCacheImage(imageId).then((image) => {
                    this.percentage.loadBuildCount += 1
                    // const metaSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId)
                    // console.log(metaSeriesModule)

                    cornerstone.displayImage(image, this.elementBuild);
                }).catch(() => {
                    this.percentage.loadBuildCountErr += 1
                });
            });
            return new Promise((resolve, reject) => {
                Promise.all(imagePromises).then(() => {
                    
                    resolve(true);
                }).catch(() => {
                    reject(new Error(`Series ${seriesUID} 下载失败`));
                })
            });
        },










        // 下载单个 series 的所有图像
        async loadSeriesImages(imageIds, seriesUID) {
            const errImageIds = [];

            let imagePromises = imageIds.map((imageId) => {
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });

            return this.loadPromiseAll(imagePromises, errImageIds, seriesUID);
        },
        // 统一下载图像
        loadPromiseAll(imageIds, errImageIds, seriesUID) {
            const errMaxValue = 6
            return new Promise((resolve, reject) => {
                Promise.all(imageIds).then(() => {
                    // 错误
                    if (errImageIds.length && this.errCount < errMaxValue) {
                        this.errCount += 1;
                        this.loadError(errImageIds, seriesUID).then(resolve).catch(reject);
                        return;
                    }
                    this.errCount = 0;
                    this.loaderSeries(seriesUID);
                    resolve(true);
                }).catch(() => {
                    if (errImageIds.length && this.errCount < errMaxValue) {
                        this.errCount += 1;
                        this.loadError(errImageIds, seriesUID).then(resolve).catch(reject);
                    } else {
                        reject(new Error(`Series ${seriesUID} 下载失败`));
                    }
                })
            });
        },
        async loadError(imageIds, seriesUID) {
            console.log('图像错误，再次下载');
            const errImageIds = [];
            const imagePromise = imageIds.map((imageId) => {
                // 错误的图像，加上不缓存标识，从服务器获取最新。
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });
            return this.loadPromiseAll(imagePromise, errImageIds, seriesUID);
        },
        async loaderSeries(seriesUID) {
            console.log('加载序列成功！', seriesUID)

            let thickness = 3
            const series = this.seriesListAll.find(item => item.seriesUID === seriesUID)

            if (series) {
                await cornerstone.loadAndCacheImage(series.imageIds[0]);
                await cornerstone.loadAndCacheImage(series.imageIds[1]);
                let startMeta = cornerstone.metaData.get('imagePlaneModule', series.imageIds[0]);
                let startMeta1 = cornerstone.metaData.get('imagePlaneModule', series.imageIds[1]);
                thickness = Math.abs(startMeta.sliceLocation - startMeta1.sliceLocation) || 2.5;
            }
            let ctData = await getVolumeSliceNumber(seriesUID, thickness)
            // 建体成功
            this.percentage.volumeCount += 1
            let [ ids ] = getNewImageIds('axial', ctData, thickness, seriesUID, '');

            this.buildImageList.push(ids)

            this.percentage.volumeImage = this.buildImageList.flat().length

            this.updateLoadTabTask('del');
        },

        // 记录内存快照
        recordMemorySnapshot() {
            if (!this.currentTestRecord) return;

            const currentMemory = { ...this.memoryInfo };
            const snapshot = {
                loadedImages: this.percentage.loadCount,
                loadedVolume: this.percentage.volumeCount,
                loadedSeries: this.completedSeriesCount,
                memory: currentMemory
            };

            this.currentTestRecord.snapshots.push(snapshot);

            // 更新最大内存使用量
            if (currentMemory.usedJSHeapSize > this.currentTestRecord.maxMemory.usedJSHeapSize) {
                this.currentTestRecord.maxMemory = currentMemory;
            }
        },

        // 保存测试记录到历史
        saveTestRecord() {
            if (!this.currentTestRecord) return;

            this.memoryHistory.unshift(this.currentTestRecord);

            // 只保留最近10次记录
            if (this.memoryHistory.length > 10) {
                this.memoryHistory = this.memoryHistory.slice(0, 10);
            }

            // 保存到localStorage
            localStorage.setItem('memoryTestHistory', JSON.stringify(this.memoryHistory));
        },

        // 加载内存历史记录
        loadMemoryHistory() {
            try {
                const history = localStorage.getItem('memoryTestHistory');
                if (history) {
                    this.memoryHistory = JSON.parse(history);
                }
            } catch (error) {
                console.error('加载内存历史记录失败:', error);
                this.memoryHistory = [];
            }
        },

        // 清除当前测试缓存
        clearCurrentTestCache() {
            this.currentTestRecord = null;
        },

        // 显示内存历史记录
        showMemoryHistory() {
            this.showHistoryDialog = true;
        },

        // 关闭历史记录弹窗
        closeHistoryDialog() {
            this.showHistoryDialog = false;
        },

        // 清除所有内存记录
        clearMemoryHistory() {
            this.memoryHistory = [];
            localStorage.removeItem('memoryTestHistory');
            if (this.$message) {
                this.$message.success('内存记录已清除');
            } else {
                alert('内存记录已清除');
            }
        }
    },
    computed: {
        // 下载进度百分比
        downloadProgress() {
            if (this.totalImageCount === 0) return 0;
            return Math.round((this.percentage.loadCount / this.totalImageCount) * 100);
        },
        downloadProgressBuild() {
            if (this.percentage.volumeImage === 0) return 0;
            return Math.round(((this.percentage.loadBuildCount + this.percentage.loadBuildCountErr) / this.percentage.volumeImage) * 100);
        },
        // 内存使用率百分比
        memoryUsagePercent() {
            if (this.memoryInfo.jsHeapSizeLimit === 0) return 0;
            return Math.round((this.memoryInfo.usedJSHeapSize / this.memoryInfo.jsHeapSizeLimit) * 100);
        }
    }
}
</script>

<style scoped>
.stat-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: #f9f9f9;
    min-width: 400px;
}

.stat-card h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.stat-item .label {
    color: #666;
}

.stat-item .value {
    font-weight: bold;
    color: #333;
}

button {
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

button:hover:not(:disabled) {
    background-color: #66b1ff;
}

/* 弹窗样式 */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.dialog-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 1000px;
    max-height: 80%;
    display: flex;
    flex-direction: column;
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.dialog-header h3 {
    margin: 0;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

.dialog-body {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.dialog-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.memory-record {
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 20px;
    overflow: hidden;
}

.record-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

.record-header h4 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
}

.record-summary {
    color: #666;
    font-size: 14px;
}

.record-details {
    padding: 15px;
}
</style>
<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
}
</style>