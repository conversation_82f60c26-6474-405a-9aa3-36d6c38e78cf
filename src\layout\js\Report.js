import Api from '$api'
import store from '$src/store'
import Fun from '$library/utils/function'
export default class Report {
    static getInstance() {
        if (!Report.instance) {
            Report.instance = new Report();
        }
        return Report.instance;
    }
    constructor() {
        this.project = window.configs.project || { token_expired: 401 }
        this.title = '信息验证中'
        this.visible = false
        this.storeParams()
    }

    getUserInfo() {
        return new Promise((resolve, reject) => {
            // 请求获取信息
            Api.getUserInfo({sId: store.state.userId}).then(res => {
                if (res.success) {
                    store.state.userInfo = res.data
                    sessionStorage.setItem("userInfo", JSON.stringify(res.data))
                    resolve(true)
                }else {
                    resolve(false)
                }
            }).catch(() => {
                resolve(false)
            })
        })
    }
    storeParams() {
        let searchUrl = window.location.hash.split('?', 2)
        const urlObj = Fun.searchParse()
        // 心跳
        store.dispatch('setHeartbeat');
        if (searchUrl && searchUrl.length === 2) {
            // 存储 url 参数
            sessionStorage.setItem('urlParams', searchUrl[1])
            sessionStorage.setItem('urlObj', JSON.stringify(urlObj))
            sessionStorage.setItem('token', urlObj.token)
            // 情况 url 参数
            // window.location.href = searchUrl[0]
            store.commit('GET_INIT_PARAMS', urlObj)
        } else {
            // 如果是没参数的时候
            const token = sessionStorage.getItem('token')
            // 是否存储了token
            if (token) {
                // 存储到 store
                const urlObj = JSON.parse(sessionStorage.getItem('urlObj'))
                store.commit('GET_INIT_PARAMS', urlObj)
            }
        }
    }
    authorization() {
        this.title = '成功'
        this.visible = true
    }
}