<template>
    <div style="height: 100%;display: flex;flex-direction: column;">
        <div class="c-content">
            <span>圆拖拽测量方式</span>
            <el-switch
                v-model="diameter"
                active-color="#13ce66"
                active-text="直径"
                inactive-text="半径">
            </el-switch>
        </div>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setCircleRoi',
    data() {
        return {
            storageKey: 'configs-circleRoi',
            diameter: true
        }
    },
    mounted() {
        this.diameter = getConfigByStorageKey(this.storageKey);
    },
    methods: {
        onClickSave() {
            // 设置新的缓存
            localStorage.setItem(this.storageKey, JSON.stringify(this.diameter));
            cornerstoneTools.store.state.circleRoiDiameter = this.diameter;
            this.$message({
                message: '保存成功-刷新浏览器生效！',
                type: 'success'
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    flex: 1;
    padding-top: 10px;
    > span{
        position: relative;
        top: 1px;
        padding-right: 20px;
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>