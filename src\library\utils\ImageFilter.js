import '$library/cornerstone/cornerstonejs.3d.local2.js'

import vtkColorTransferFunction from '$library/vtk/lib/colorTransferFunction.js';

import vtkColorMaps from 'vtk.js/Sources/Rendering/Core/ColorTransferFunction/ColorMaps';
import vtkPiecewiseFunction from 'vtk.js/Sources/Common/DataModel/PiecewiseFunction';


import { debounce, throttle } from 'lodash-es';
import getConfigByStorageKey from '$library/utils/configStorage.js'
import { checkIfTurnGpuOn } from '$library/utils/checkGpu.js'
import { GPU } from './gpu.js';

const gpuMap = {}
const kernelFuncMap = {}
const kernelFuncList = []

const _innerColorMap = {}
let countercount = 0
function fpsMeter() {
  let prevTime = Date.now(),
    frames = 0;

  requestAnimationFrame(function loop() {
    const time = Date.now();
    frames++;
    if (time > prevTime + 1000) {
      let fps = Math.round((frames * 1000) / (time - prevTime));
      prevTime = time;
      frames = 0;

      // console.info('FPS: ', fps);
      document.title = fps
    }

    requestAnimationFrame(loop);
  });
}

// fpsMeter();
window.__fpsMeter = fpsMeter


const imageFilterMode = '0' // getConfigByStorageKey('configs-imageFilterMode')

// 算子
const KERNELS = ({
  nope: [
    0, 0, 0,
    0, 1, 0,
    0, 0, 0
  ].map(i => i),
  intp01: [
    0, 1, 0,
    1, 1, 1,
    0, 1, 0
  ].map(i => i / 5),
  intp02: [
    1, 1, 1,
    1, 1, 1,
    1, 1, 1
  ].map(i => i / 9),
  intp03: [
    0.0005, 0.0051, 0.0111, 0.0051, 0.0005,
    0.0051, 0.0529, 0.1139, 0.0529, 0.0051,
    0.0111, 0.1139, 0.2455, 0.1139, 0.0111,
    0.0051, 0.0529, 0.1139, 0.0529, 0.0051,
    0.0005, 0.0051, 0.0111, 0.0051, 0.0005,

  ],
  log: [
    2, 4, 4, 4, 2,
    4, 0, -8, 0, 4,
    4, -8, -24, -8, 4,
    4, 0, -8, 0, 4,
    2, 4, 4, 4, 2
  ].map(i => i * 1),
  edgeDetection: [
    0, 0, 1, 0, 0,
    0, 1, 2, 1, 0,
    1, 2, -16 - .4, 2, 1,
    0, 1, 2, 1, 0,
    0, 0, 1, 0, 0,
  ],
  edgeDetection2: [
    0, -1, 0,
    -1, 4, -1,
    0, -1, 0
  ],
  edgeDetection3: [
    -1, -1, -1,
    -1, 8, -1,
    -1, -1, -1
  ],
  edgeDetection4: [
    -1, 0, -1,
    0, 4, 0,
    -1, 0, -1
  ],
  sharpen: [
    0, -1, 0,
    -1, 5, -1,
    0, -1, 0
  ],
  sharpen2: [
    0, -1, 0,
    -1, 1 + 4, -1,
    0, -1, 0
  ].map(i => i / 1),
  sharpen3: [
    1, 1, 1,
    1, -9, 1,
    1, 1, 1
  ].map(i => i / -1),
  sharpen4: [
    -1, -1, -1,
    -1, 9, -1,
    -1, -1, -1
  ].map(i => i / 1),
  nope: [
    0, 0, 0,
    0, 1, 0,
    0, 0, 0
  ],
  normal: [
    0, 1, 0,
    1, 1, 1,
    0, 1, 0
  ].map(i => i / 5),
  normal2: [
    1, 1, 1,
    1, 1, 1,
    1, 1, 1,
  ].map(i => i / 9),
  normal3: [
    1, 1, 1, 1, 1,
    1, 1, 1, 1, 1,
    1, 1, 1, 1, 1,
    1, 1, 1, 1, 1,
    1, 1, 1, 1, 1,
  ].map(i => i / 25),



  gaussSmo: [1 / 16, 2 / 16, 1 / 16, 2 / 16, 4 / 16, 2 / 16, 1 / 16, 2 / 16, 1 / 16],
  smooth: [
    0.0025, 0.0125, 0.02, 0.0125, 0.0025,
    0.0125, 0.0625, 0.01, 0.0625, 0.0125,
    0.02, 0.01, 0.16, 0.01, 0.02,
    0.0125, 0.0625, 0.01, 0.0625, 0.0125,
    0.0025, 0.0125, 0.02, 0.0125, 0.0025
  ].map(i => i / 0.64), // ?
  smoothWithLighter: [
    0.0025, 0.0125, 0.02, 0.0125, 0.0025,
    0.0125, 0.0625, 0.05, 0.0625, 0.0125,
    0.02, 0.05, 0.16, 0.05, 0.02,
    0.0125, 0.0625, 0.05, 0.0625, 0.0125,
    0.0025, 0.0125, 0.02, 0.0125, 0.0025
  ].map(i => i / 0.64), // ?
  unsharpMasking: [
    1, 4, 6, 4, 1,
    4, 16, 24, 16, 4,
    6, 24, -476, 24, 6,
    4, 16, 24, 16, 4,
    1, 4, 6, 4, 1

  ].map(i => i / 256 * -1),
  unsharpMasking3: [
    0.05, 0.15, 0.05,
    0.15, -1 - 0.8, 0.15,
    0.05, 0.15, 0.05,
  ].map(i => i / 1 * -1),
  unsharpMasking31: [
    0.06, 0.18, 0.06,
    0.18, -1 - 0.96, 0.18,
    0.06, 0.18, 0.06,
  ].map(i => i / 1 * -1),
  unsharpMasking4: [
    0.0059, 0.0648, 0.0059,
    0.0648, 0.7172 - 2, 0.0648,
    0.0059, 0.0648, 0.0059,

  ].map(i => i * -1),
  unsharpMasking5: [
    0.0249, 0.1080, 0.0249,
    0.1080, 0.4686 - 2, 0.1080,
    0.0249, 0.1080, 0.0249,


  ].map(i => i * -1),
  unsharpMasking6: [
    0.0462, 0.1225, 0.0462,
    0.1225, -1 - 0.6748, 0.1225,
    0.0462, 0.1225, 0.0462,

  ].map(i => i * -1),
  unsharpMasking7: [
    0.0111, 0.0833, 0.0111,
    0.0833, 0.6223 - 2, 0.0833,
    0.0111, 0.0833, 0.0111,

  ].map(i => i * -1),

  unsharpMasking8: [
    -1, -1, -1,
    -1, 8 + 4.65, -1,
    -1, -1, -1,

  ].map(i => i / 4.65),

  unsharpMasking9: [   // 9
    -1, -1, -1,
    -1, 8 + 26.5, -1,
    -1, -1, -1,

  ].map(i => i / 24),

  unsharpMasking10: [   // 9
    0, -1, 0,
    -1, 4 + 1.25, -1,
    0, -1, 0,

  ].map(i => i / 1.25),

  unsharpMasking11: [   // 9
    -1, -1, -1,
    -1, 8 + 6, -1,
    -1, -1, -1,

  ].map(i => i / 6),

  unsharpMasking12: [   // 9
    -0.5, -2, -0.5,
    -2, 10 + 3, -2,
    -0.5, -2, -0.5,

  ].map(i => i / 3),



  gaussSmo5: [
    1, 4, 6, 4, 1,
    4, 16, 24, 16, 4,
    6, 24, 36, 24, 6,
    4, 16, 24, 16, 4,
    1, 4, 6, 4, 1

  ].map(i => i / 256),
  // 正gauss加反gauss = 模糊x1
  gaussSmo6: [
    1, 4, 7, 4, 1,
    4, 20, 33, 20, 4,
    7, 33, 55, 33, 7,
    4, 20, 33, 20, 4,
    1, 4, 7, 4, 1

  ].map(i => i / 337),  //  1
  gaussSmo7: [
    0.0013, 0.0086, 0.0161, 0.0086, 0.0013,
    0.0086, 0.0573, 0.1077, 0.0573, 0.0086,
    0.0161, 0.1077, 0.2022, 0.1077, 0.0161,
    0.0086, 0.0573, 0.1077, 0.0573, 0.0086,
    0.0013, 0.0086, 0.0161, 0.0086, 0.0013,

  ],
  gaussSmo8: [
    0.0003, 0.0035, 0.0083, 0.0035, 0.0003,
    0.0035, 0.0491, 0.1163, 0.0491, 0.0035,
    0.0083, 0.1163, 0.2758, 0.1163, 0.0083,
    0.0035, 0.0491, 0.1163, 0.0491, 0.0035,
    0.0003, 0.0035, 0.0083, 0.0035, 0.0003,
  ],
  gaussSmo9: [

    0.0778, 0.1233, 0.0778,
    0.1233, 0.1953, 0.1233,
    0.0778, 0.1233, 0.0778,

  ],
  gaussSmo10: [
    0.0005, 0.0217, 0.0005,
    0.0217, 0.9111, 0.0217,
    0.0005, 0.0217, 0.0005,
  ],
  gaussSmo11: [
    0.0111, 0.0833, 0.0111,
    0.0833, 0.6223, 0.0833,
    0.0111, 0.0833, 0.0111,
  ],
  gaussSmo12: [
    0.0000, 0.0002, 0.0009, 0.0002, 0.0000,
    0.0002, 0.0247, 0.1074, 0.0247, 0.0002,
    0.0009, 0.1074, 0.4661, 0.1074, 0.0009,
    0.0002, 0.0247, 0.1074, 0.0247, 0.0002,
    0.0000, 0.0002, 0.0009, 0.0002, 0.0000,
  ],
  gaussSmo13: [
    0.0249, 0.1080, 0.0249,
    0.1080, 0.4686, 0.1080,
    0.0249, 0.1080, 0.0249,
  ],

})



const getConvolution = (image, kernel) => {
  const kernelRadius = (Math.sqrt(kernel.length) - 1) / 2;
  const sizeId = String(image.width) + String(image.height) + `_sizeId_getConvolution` + kernelRadius
  // const sizeId = `${999}${777}`

  let kernelFunc = kernelFuncMap[sizeId]
  let _gpu = gpuMap[sizeId]
  if (!kernelFunc) {
    if (!_gpu) {
      gpuMap[sizeId] = new GPU();
      _gpu = gpuMap[sizeId]
    }
    kernelFunc = _gpu.createKernel(function (src, width, height, kernel, kernelRadius) {
      const kSize = 2 * kernelRadius + 1;
      const thisPixel = src[this.thread.y][this.thread.x]
      let r = 0, g = 0, b = 0;
      let i = -kernelRadius;
      if (this.thread.x > 2 && this.thread.x < width - 2
        && this.thread.y > 2 && this.thread.y < height - 2) {

          while (i <= kernelRadius) {
            const x = this.thread.x + i;
            // if (x < 1 || x >= width - 1) {
            //   i++;
            //   continue;
            // }
    
            let j = -kernelRadius;
            while (j <= kernelRadius) {
              const y = this.thread.y + j;
              // if (y < 1 || y >= height - 1) {
              //   j++;
              //   continue;
              // }
    
              const kernelOffset = (j + kernelRadius) * kSize + i + kernelRadius;
              const weights = kernel[kernelOffset];
              const pixel = src[y][x];
              r += pixel.r * weights;
              g += pixel.g * weights;
              b += pixel.b * weights;
              j++;
            }
            i++;
          }
        } else {
          r = thisPixel.r, g = thisPixel.g, b = thisPixel.b;
        }

      this.color(r, g, b);
    }).setOutput([image.width, image.height])
      .setGraphical(true);
    kernelFuncMap[sizeId] = kernelFunc
  }

  kernelFunc(image, image.width, image.height, kernel, kernelRadius)
  return kernelFunc.canvas
}

const getInterpolation = (image, size = 1500) => {

  const sizeId = String(size) + String(size) + `_sizeId_getInterpolationn`


  let kernelFunc = kernelFuncMap[sizeId]
  let _gpu = gpuMap[sizeId]
  if (!kernelFunc) {
    if (!_gpu) {
      gpuMap[sizeId] = new GPU();
      _gpu = gpuMap[sizeId]
    }
    function interpolationCalculate(z) {
      const absX = z >= 0 ? z : -z;
      const x2 = z * z;
      const x3 = absX * x2;
      const A = -2;
      /**
 * 采样公式的常数A取值,调整锐化与模糊
 * -0.5 三次Hermite样条
 * -0.75 常用值之一
 * -1 逼近y = sin(x*PI)/(x*PI)
 * -2 常用值之一
 */

      if (absX <= 1) {
        return 1 - (A + 3) * x2 + (A + 2) * x3;
      } else if (absX <= 2) {
        return -4 * A + 8 * A * absX - 5 * A * x2 + A * x3;
      }

      return 0;
    }
    kernelFunc = _gpu.createKernel(function (image, width, height, scaleX, scaleY) {
      let r = 0, g = 0, b = 0;

      const x = this.thread.x / scaleX;
      const y = this.thread.y / scaleY;


      // 源图像中的坐标（可能是一个浮点）
      const srcCol = Math.min(width - 1, y);
      const srcRow = Math.min(height - 1, x);
      const intCol = Math.floor(srcCol);
      const intRow = Math.floor(srcRow);
      // 计算u和v
      const u = srcCol - intCol;
      const v = srcRow - intRow;
      let weight = 0
      for (let m = -1; m <= 2; m += 1) {
        for (let n = -1; n <= 2; n += 1) {
          // 一定要正确区分 m,n和u,v对应的关系，否则会造成图像严重偏差（譬如出现噪点等）
          // F(row + m, col + n)S(m - v)S(n - u) 
          weight = interpolationCalculate(m - v) * interpolationCalculate(n - u);
          const point = image[intCol + n][intRow + m]
          r += point.r * weight;
          g += point.g * weight;
          b += point.b * weight;

        }
      }
      // return point
      this.color(r, g, b);


    }).setFunctions([interpolationCalculate])
      .setOutput([size, size])
      .setGraphical(true);
    kernelFuncMap[sizeId] = kernelFunc
  }

  kernelFunc(image, image.width, image.height, size / image.width, size / image.height)

  return kernelFunc.canvas
}


export default {
  getInterpolation: (imageCanvas, width) => {
    try {
      if (!checkIfTurnGpuOn()) {
        return imageCanvas
      }
      let intpCanvas = imageCanvas
      intpCanvas = getInterpolation(intpCanvas, width)

      const glops = { antialias: true }
      const gl = intpCanvas.getContext("webgl2", glops) || intpCanvas.getContext("webgl", glops) || intpCanvas.getContext("experimental-webgl", glops);
      if (gl) { gl.drawingBufferColorSpace = "srgb"; gl.unpackColorSpace = "srgb"; }
      return intpCanvas
    } catch (error) {
      console.log(error)
      return imageCanvas
    }
  },
  filterPTImage: (imageCanvas, enabledElement) => {
    if (+getConfigByStorageKey('configs-imageFilterSharpen') < 1) {
      return imageCanvas
    }
    try {
      if (!checkIfTurnGpuOn()) {
        return imageCanvas
      }
      let intpCanvas = imageCanvas
      // 平滑
      let colormap = enabledElement ? (enabledElement.viewport.colormap || enabledElement.options.colormap) : ''

      if (typeof colormap === 'string') {
        const colormapName = String(colormap).toLocaleLowerCase()

        if (['gray'].includes(colormapName)) {
          intpCanvas = getConvolution(intpCanvas, KERNELS['normal2'])
          intpCanvas = getConvolution(intpCanvas, KERNELS['unsharpMasking31'])
          intpCanvas = getConvolution(intpCanvas, KERNELS['normal2'])
          intpCanvas = getConvolution(intpCanvas, KERNELS['unsharpMasking31'])
          const glops = { antialias: true }
          const gl = intpCanvas.getContext("webgl2", glops) || intpCanvas.getContext("webgl", glops) || intpCanvas.getContext("experimental-webgl", glops);
          if (gl) { gl.drawingBufferColorSpace = "srgb"; gl.unpackColorSpace = "srgb"; }
        }
      }

      return intpCanvas
    } catch (error) {
      console.log(error)
      return imageCanvas
    }
  },
  filterCTImage: (imageCanvas, kernelName = 'unsharpMasking9') => {
    if (+getConfigByStorageKey('configs-imageFilterSharpen') < 1) {
      return imageCanvas
    }
    try {
      if (!checkIfTurnGpuOn()) {
        return imageCanvas
      }
      let intpCanvas = imageCanvas
      // intpCanvas = getConvolution(intpCanvas, KERNELS['gaussSmo13'])
      intpCanvas = getConvolution(intpCanvas, KERNELS['unsharpMasking5'])
      // intpCanvas = getConvolution(intpCanvas, KERNELS['gaussSmo9'])
      // intpCanvas = getConvolution(intpCanvas, KERNELS['unsharpMasking10'])
      // intpCanvas = getConvolution(intpCanvas, KERNELS['gaussSmo9'])
      // intpCanvas = getConvolution(intpCanvas, KERNELS['unsharpMasking10'])
      // intpCanvas = getConvolution(intpCanvas, KERNELS[kernelName])

      const glops = { antialias: true }
      const gl = intpCanvas.getContext("webgl2", glops) || intpCanvas.getContext("webgl", glops) || intpCanvas.getContext("experimental-webgl", glops);
      if (gl) { gl.drawingBufferColorSpace = "srgb"; gl.unpackColorSpace = "srgb"; }
      return intpCanvas
    } catch (error) {
      console.log(error)
      return imageCanvas
    }
  },
  filterFUSEPTImage: (imageCanvas) => {
    try {
      // if (!checkIfTurnGpuOn()) {
      //   return imageCanvas
      // }
      // let intpCanvas = imageCanvas
      // intpCanvas = getConvolution(intpCanvas, KERNELS['unsharpMasking6'])
      // const glops = { antialias: false }
      // const gl = intpCanvas.getContext("webgl2", glops) || intpCanvas.getContext("webgl", glops) || intpCanvas.getContext("experimental-webgl", glops);
      // if (gl) { gl.drawingBufferColorSpace = "srgb"; gl.unpackColorSpace = "srgb"; }
      return imageCanvas
    } catch (error) {
      console.log(error)
      return imageCanvas
    }
  },

  getVTKrenderCanvas2(enabledElement, image, context, width, height) {
    const bindingCANVAS = context.canvas
    let renderingEngine
    let viewportId = window._zerenderingEngineVpId
    const isPt = enabledElement.modality === 'PT' || image.windowWidth >= 15000
    const isCt = enabledElement.modality === 'CT' || image.windowWidth < 15000
    let imageClarity = 900
    let sizeStandard = Math.max(imageClarity, Math.max(width, height))
    let FixRatio = 1
    let CtRatio = 2
    let renderWidth = width 
    let renderHeight = height 


    imageClarity = +getConfigByStorageKey('configs-imageClipClarity') || imageClarity

    // renderWidth = width * enabledElement.viewport.scale
    // renderHeight = height * enabledElement.viewport.scale
    // const largerOne = Math.max(width * enabledElement.viewport.scale, height * enabledElement.viewport.scale)
    // if (largerOne > imageClarity) {
    //   renderWidth = width * enabledElement.viewport.scale * (imageClarity / largerOne)
    //   renderHeight = height * enabledElement.viewport.scale * (imageClarity / largerOne)
    // }
    // let ttt = document.getElementsByTagName('body')[0].getAttribute('ttt')
    // if(!ttt)ttt = 1
    // CtRatio = FixRatio = ttt
    if (Math.abs(width - height) < 0.1) {
      FixRatio = sizeStandard / Math.min(width, height)
      if (isPt) {
        renderHeight = renderWidth = width * FixRatio
      } else {
        renderHeight = renderWidth = width * Math.max(FixRatio, CtRatio)
      }
    } else {
      if (isPt) {
        FixRatio = sizeStandard / Math.min(width, height)
        renderHeight = height * FixRatio
        renderWidth = width * FixRatio
      } else {
        CtRatio = sizeStandard / Math.min(width, height)
        renderHeight = height * CtRatio
        renderWidth = width * CtRatio
      }
    }


    // 区分渲染容器，每个容器使用一个viewport实例
    if (!window._zerenderingEngineVpId) {
      viewportId = window._zerenderingEngineId + Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1)
      window._zerenderingEngineVpId = viewportId
      renderingEngine = new _ze_cornerstonejs_core__renderingEngine["default"](viewportId);
      window._zerenderingEngineMap[viewportId] = renderingEngine

      bindingCANVAS.setAttribute('viewportId', viewportId)
      const vpElement = document.createElement('div')

      vpElement.style.width = renderWidth + "px";
      vpElement.style.height = renderHeight + "px";
      vpElement.setAttribute('class', 'bindingCANVAS')
      // vpElement.style.visibility = 'hidden';
      // vpElement.style.position = 'absolute';
      // vpElement.setAttribute('id', `${viewportId}_wrap`);

      document.body.append(vpElement)
      window._zerenderingEngineElement = vpElement

      const newvp = {
        viewportId,
        type: 'stack',
        defaultOptions: {
          orientation: { viewPlaneNormal: [0, 0, 1], viewUp: [0, 1, 0] },
          suppressEvents: false
        },
        element: vpElement,
      }
      renderingEngine.setViewports([newvp])
      const offCanvas = renderingEngine.offScreenCanvasContainer.querySelector('canvas')
      const glops = { antialias: true }
      const gl = offCanvas.getContext("webgl2", glops) || offCanvas.getContext("webgl", glops) || offCanvas.getContext("experimental-webgl", glops);
      if (gl) { gl.drawingBufferColorSpace = "srgb"; gl.unpackColorSpace = "srgb"; }


      enabledElement.renderingTools.renderCanvas = vpElement.querySelector('canvas');
      enabledElement.renderingTools.viewportId = viewportId
    } else {
      renderingEngine = window._zerenderingEngineMap[viewportId]

      // const vpElement = document.body.querySelector(`#${viewportId}_wrap`);
      const vpElement = window._zerenderingEngineElement;
      let needResize = 0
      if (renderWidth != vpElement.clientWidth) {
        vpElement.style.width = renderWidth + "px";
        needResize = 1
      }

      if (renderHeight != vpElement.clientHeight) {
        vpElement.style.height = renderHeight + "px";
        needResize = 1
      }

      needResize && renderingEngine.resize();

      enabledElement.renderingTools.renderCanvas = vpElement.querySelector(`canvas`);
      enabledElement.renderingTools.viewportId = viewportId
    }

    const stackvp = (renderingEngine.getViewport(viewportId))
    if (!viewportId || !stackvp) {
      return enabledElement.renderingTools.renderCanvas
    }
    stackvp.renderImageObject(image)

    return enabledElement.renderingTools.renderCanvas

  },
  drawVtkCanvasSync(enabledElement) {
    const viewportData = enabledElement.viewport
    const image = enabledElement.image

    let viewportId = enabledElement.renderingTools.viewportId
    const renderingEngine = window._zerenderingEngineMap[viewportId]
    const stackvp = (renderingEngine.getViewport(viewportId))

    if (!viewportId || !stackvp) {
      return
    }


    let colormap = enabledElement.viewport.colormap || enabledElement.options.colormap;


    if (colormap && typeof colormap === 'string') {
      colormap = cornerstone.colors.getColormap(colormap);
    }

    if (!colormap) {
      throw new Error('renderPseudoColorImage: colormap not found.');
    }

    const colormapId = colormap.getId();

    if (enabledElement.renderingTools.colormapId !== colormapId) {
      enabledElement.renderingTools.colormapId = colormapId;
    }


    let { windowWidth, windowCenter } = viewportData.voi
    windowCenter = windowCenter - image.intercept || 0
    const voiRange = stackvp._getVOIRangeFromWindowLevel(windowWidth, windowCenter)




    const cfun = transferColormap2Func(colormap.getObj ? colormap.getObj() : colormap, viewportData.invert)

    cfun.setMappingRange(voiRange.lower / image.slope, voiRange.upper / image.slope );
    // cfun.setRange(voiRange.lower, voiRange.upper);

    let ActorEntry = stackvp.getActor(stackvp.id);
    let actor = ActorEntry.actor;
    actor.getProperty().setRGBTransferFunction(0, cfun);


    stackvp.setDisplayArea({
      imageArea: [0.90, 0.90],
      // imageCanvasPoint: {
      //   canvasPoint: [0, 1],
      //   imagePoint: [0, 1],
      // },
      // storeAsInitialCamera: true
    })

    stackvp.setProperties({
      interpolationType: 1,
    });

    try {
      const isPt = enabledElement.modality === 'PT' || image.windowWidth >= 15000
      const isFusePt = enabledElement.layerId
      const renderers = (renderingEngine.offscreenMultiRenderWindow.getRenderers())
      renderers[0].renderer.setBackground(isPt && !isFusePt ? [1, 1, 1] : [0, 0, 0])
    } catch (error) {

    }

    // const vtkImageData = actor.getMapper().getInputData();
    // const scalarData = vtkImageData.getPointData().getScalars()

    // console.log(vtkImageData.getPointData())
    // console.log(scalarData.setRange([0, 100000], 0))
    // scalarData.modified()
    // stackvp.setVOI(voiRange, {
    //   forceRecreateLUTFunction: false,
    //   voiUpdatedWithSetProperties: false,
    // })
    // stackvp.setProperties({
    //   // VOILUTFunction: SIGMOID,  
    //   invert: viewportData.invert, // get from colormap 
    //   voiRange: voiRange,
    //   interpolationType: 1,
    //   // rotation: 90
    //   // suppressEvents?: boolean
    // })

    // stackvp.setPan([viewportData.translation.x, viewportData.translation.y])
    // stackvp.setZoom(1)
    // stackvp.render()

    renderingEngine._setViewportsToBeRenderedNextFrame([viewportId])

    renderingEngine._renderFlaggedViewports()
    return viewportId

  },

  renderImagesSync(viewportIds) {
    viewportIds.forEach(viewportId => {
      if (!viewportId) return
      const renderingEngine = window._zerenderingEngineMap[viewportId]
      renderingEngine._setViewportsToBeRenderedNextFrame([viewportId])
      window.requestAnimationFrame(() => {
        renderingEngine._renderFlaggedViewports()
      })
    })
  },

  getSwitchValue() {
    try {
      if (!checkIfTurnGpuOn()) {
        return false
      }
      return +getConfigByStorageKey('configs-imageFilter2Mode') > 0
    } catch (error) {
      console.log(error)
      return false
    }
  },
  getCtCnSSwitchValue() {
    try {
      if (!checkIfTurnGpuOn()) {
        return false
      }
      return +getConfigByStorageKey('configs-imageFilteWithoutCnS') > 0
    } catch (error) {
      console.log(error)
      return false
    }
  },

}


function transferColormap2Func(colormapItem, invert = false) {
  const result = vtkColorTransferFunction.newInstance({
    clamping: false
  });

  // const result = {
  //   ColorSpace: 'RGB',
  //   Name: 'name',
  //   RGBPoints: [] // [[0,0,0]]
  // };
  // 将colormap数据转换为RGBTransferFunction数据。遍历colormap数据，并将其转换为vtk.js中颜色和位置的格式。根据colormap的每个颜色和位置，使用以下代码将它们添加到RGBTransferFunction实例中：
  // colormap.forEach((entry, index) => {
  // name: 'Gray',
  // numColors: 256,
  // colors: [[0,0,0,255],[2,0,0,255],]


  if (String(colormapItem.name).toLocaleLowerCase() == 'gray' && !invert) {
    // CT图像特殊处理
    const colormap = vtkColorMaps.getPresetByName('Grayscale')
    result.applyColorMap(colormap)
    return result
  }


  if (colormapItem.colors) {
    const _innerCid = colormapItem.name + (invert ? 1 : 0)

    if (colormapItem.name && _innerColorMap[_innerCid]) {
      return _innerColorMap[_innerCid]
    } else {
      let arr = []

      let invertColors = colormapItem.colors

      // if (String(colormapItem.name).toLocaleLowerCase() == 'gray') {
      //   invertColors = invertColors.map((a, i) => ([i, i, i, 255]))
      // }
      invertColors.forEach((entry, index) => {
        // arr.push(index / 255 * (invert ? -1 : 1))
        // arr.push(entry[0] / 255)
        // arr.push(entry[1] / 255)
        // arr.push(entry[2] / 255)
        arr.push({
          x: index / 255 * (invert ? -1 : 1),
          r: entry[0] / 255,
          g: entry[1] / 255,
          b: entry[2] / 255
        })
      })

      arr.forEach(({ x, r, g, b }) => {
        result.addRGBPointLong(x, r, g, b, 0.5, 0.1);
      })

      _innerColorMap[_innerCid] = result

      return result
    }

  } else {
    console.error('unknown colormap')
  }

}


function resetViewportsArray(viewportId) {

  // window._zerenderingVpArray = window._zerenderingVpArray.filter(item => item.viewportId !== viewportId)

  // debounce(function () { 

  // }, 100).call( )

  window._zerenderingEngineArray = window._zerenderingEngineArray.filter(item => item !== viewportId)
  const dom = document.body.querySelector('[data-viewport-uid=' + viewportId + ']')
  dom && dom.remove()

  // console.log(dom)
  throttle(function (viewportId) {
    const renderingEngine = window._zerenderingEngineMap[viewportId]
    renderingEngine && renderingEngine.destroy()
  }, 33).call(viewportId)

}

