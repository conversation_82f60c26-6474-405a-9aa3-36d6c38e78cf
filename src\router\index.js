import Vue from 'vue'
import VueRouter from 'vue-router'
import Index from '$src/layout'

Vue.use(VueRouter)
const routes = [
	{
		path: '/',
		name: 'Index',
		redirect: '/dcm',
		meta: {
			title: '首页'
		},
		component: Index,
		children: [
			{
				path: '/dcm',
				name: 'Main',
				meta: {
					title: '看图'
				},
				component: () => import('../views/Main.vue')
			},
		]
		// path: '/',
		// name: 'Main',
		// component: Main,
		// redirect: '/select',
		// children: [
		// 	{
		// 		path: '/Main',
		// 		name: 'Main',
		// 		meta: {
		// 			title: '首页'
		// 		},
		// 		component: MainModule
		// 	},
		// 	{
		// 		path: '/home',
		// 		name: 'Home',
		// 		meta: {
		// 			title: '阅图',
		// 		},
		// 		component: Home
		// 	},
		// 	{
		// 		path: '/mpr',
		// 		name: 'ViewVtkMpr',
		// 		meta: {
		// 			title: 'MPR重建',
		// 		},
		// 		component: ViewVtkMpr
		// 	},
		// 	{
		// 		path: '/fusemMpr',
		// 		name: 'ViewVtkFuseMpr',
		// 		meta: {
		// 			title: '融合重建',
		// 		},
		// 		component: ViewVtkFuseMpr
		// 	},
		// 	{
		// 		path: '/viewSlice',
		// 		name: 'ViewSlice',
		// 		meta: {
		// 			title: '重建',
		// 		},
		// 		component: ViewSlice
		// 	},
		// 	{
		// 		path: '/test',
		// 		name: 'Test',
		// 		meta: {
		// 			title: '测试',
		// 		},
		// 		component: () => import('../views/Test.vue')
		// 	},
		// 	{
		// 		path: '/select',
		// 		name: 'Select',
		// 		meta: {
		// 			title: '选择',
		// 		},
		// 		component: () => import('../views/Select.vue')
		// 	}
		// ]
	},
	// {
	// 	path: '/fuseMpr',
	// 	name: 'ViewVtkFuseMpr',
	// 	meta: {
	// 		title: '融合重建',
	// 	},
	// 	component: () => import('../views/ViewVtkFuseMpr.vue')
	// },
	// {
	// 	path: '/test',
	// 	name: 'Test',
	// 	meta: {
	// 		title: '测试',
	// 	},
	// 	component: () => import('../views/Test.vue')
	// },
	{
		path: '/error',
		name: 'Error',
		meta: {
			title: '没有权限',
		},
		component: () => import('../views/Error.vue')
	},

]

const router = new VueRouter({
	mode: 'hash',
	base: process.env.BASE_URL,
	routes
})

export default router
