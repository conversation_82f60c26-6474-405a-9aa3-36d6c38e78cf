import macro from 'vtk.js/Sources/macro';
import vtkInteractorStyleMPRSlice from './vtkInteractorStyleMPRSlice.js';
import Constants from 'vtk.js/Sources/Rendering/Core/InteractorStyle/Constants';
// import vtkWidgetManager from 'vtk.js/Widgets/Core/WidgetManager';
const { States } = Constants;

function vtkInteractorStyleMark(publicAPI, model) {
  model.classHierarchy.push('vtkInteractorStyleMark');

  function getCoordFromCalldata(callData) {
    const { apis, apiIndex } = model;
    const api = apis[apiIndex];
    const pos = callData.position;
    const renderer = callData.pokedRenderer;
    const size = renderer.getVTKWindow().getViews()[0].getSize()
    if (pos.x < 10 || pos.x > (size[0] - 10) || pos.y < 10 || pos.y > (size[1] - 10)) {
      return
    }

    const width = size[0]
      const height = size[1]
      const textOffsetTop = 8
      const textOffsetLeft = -16 


    return [( pos.x - 0.5 * width  - textOffsetLeft) / width * 200, 
    (0.5 * height - pos.y - textOffsetTop) / height * 200 ]
  }

  function moveMarkPoint(callData) {
    const { apis, apiIndex } = model;
    const thisApi = apis[apiIndex];
    const pos = getCoordFromCalldata(callData)
    if (!pos) {
      thisApi.svgWidgets.markWidget.cancelTempPoint(thisApi)
      return
    }


    // const wPos = vtkCoordinate.newInstance();
    // wPos.setCoordinateSystemToWorld();
    // wPos.setValue(...worldPos);
    // const renderer = genericRenderWindow.getRenderer();

    // const p = wPos.getComputedDoubleDisplayValue(renderer);

    thisApi.svgWidgets.markWidget.moveMark(
      pos,
      thisApi
    );
    model.cachedCallData = callData

    publicAPI.invokeInteractionEvent({ type: 'InteractionEvent' });
  }

  const superHandleMouseMove = publicAPI.handleMouseMove;
  // 鼠标放在视图层移动，触发的事件
  publicAPI.handleMouseMove = callData => {
    // if (model.state === States.IS_WINDOW_LEVEL) {
    moveMarkPoint(callData)
    // }

    if (superHandleMouseMove) {
      superHandleMouseMove(callData);
    }
  };

  function handleMouseUp(callData) {
    const { apis, apiIndex } = model;
    const thisApi = apis[apiIndex];

    // thisApi.svgWidgets.markWidget.setMarkDisplay(false)
    const pos = getCoordFromCalldata(callData)
    if (!pos) {
      thisApi.svgWidgets.markWidget.cancelTempPoint(thisApi)
      return
    }
    thisApi.svgWidgets.markWidget.addMarkPointData(
      pos,
      thisApi
    );
    thisApi.svgWidgets.markWidget.cancelTempPoint(thisApi)
  }

  function handleMouseRightClick(callData) {
    const { apis, apiIndex } = model;
    const thisApi = apis[apiIndex];

    const pos = getCoordFromCalldata(callData)
    if (!pos) {
      thisApi.svgWidgets.markWidget.cancelTempPoint(thisApi)
      return
    }
    thisApi.svgWidgets.markWidget.deletePointStorage(
      pos, 
      thisApi
    );
    thisApi.svgWidgets.markWidget.cancelTempPoint(thisApi)
  }

  const superHandleLeftButtonPress = publicAPI.handleLeftButtonPress;
  // 鼠标左键按下时的事件
  publicAPI.handleLeftButtonPress = callData => {
    // console.log('鼠标左键按 ')
    if (model.volumeActor) {
      publicAPI.startWindowLevel();
      // moveMarkPoint(callData)
    } else if (superHandleLeftButtonPress) {
      superHandleLeftButtonPress(callData);
    }
  };

  publicAPI.superHandleLeftButtonRelease = publicAPI.handleLeftButtonRelease;
  // 鼠标左键松开时的事件
  publicAPI.handleLeftButtonRelease = callData => {
    switch (model.state) {
      case States.IS_WINDOW_LEVEL:
        publicAPI.endWindowLevel();
        handleMouseUp(callData)
        break;
      default:
        publicAPI.superHandleLeftButtonRelease();
        break;
    }
  };
  
  // publicAPI.superHandleRightButtonPress = publicAPI.handleRightButtonPress;
  // publicAPI.handleRightButtonPress = callData => {
  //   debugger
  //   handleMouseRightClick(callData)
  //   publicAPI.superHandleRightButtonPress(callData);
  // };


  publicAPI.onEndInteractionEvent(() => {
    if (model.cachedCallData) moveMarkPoint(model.cachedCallData)

    model.endCallback && model.endCallback()
  })
}


// ----------------------------------------------------------------------------
// Object factory
// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {
  Object.assign(model, DEFAULT_VALUES, initialValues);

  // Inheritance
  vtkInteractorStyleMPRSlice.extend(publicAPI, model, initialValues);

  macro.setGet(publicAPI, model, [
    'apis',
    'apiIndex',
    'endCallback'
  ]);

  // Object specific methods
  vtkInteractorStyleMark(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(
  extend,
  'vtkInteractorStyleMark'
);

// ----------------------------------------------------------------------------

export default Object.assign({ newInstance, extend });


