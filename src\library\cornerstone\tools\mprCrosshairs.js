import {
	vec3
} from 'gl-matrix';
import csTools from '$library/cornerstone/cornerstoneTools'
const BaseTool = csTools.importInternal("base/BaseTool");
const imagePointToPatientPoint = csTools.import('util/imagePointToPatientPoint')
const { crosshairsCursor } = csTools.importInternal("tools/cursors");

import CoordsEvent from '$library/cornerstone/mpr/store/CoordsEvent.js';
import crosshairsWidget from './crosshairsWidget'

function waitForEnabledElementImageToLoad(element) {
	try {
		const enabledElement = csTools.external.cornerstone.getEnabledElement(
			element
		);

		if (!enabledElement.image) {
			return wait(250).then(() => waitForEnabledElementImageToLoad(element));
		}

		// EnabledElement's image is loaded.
		return enabledElement;
	} catch (ex) {
		// Is no longer, or never was an enabled element, stop polling
		return null;
	}
}
/**
 * 定位线工具
 */
export default class MprCrosshairsTool extends BaseTool {
	constructor(props = {}) {
		const defaultProps = {
			name: "MprCrosshairs",
			// mixins: ['enabledOrDisabledBinaryTool'],
			supportedInteractionTypes: ["Mouse", "Touch"],
			defaultStrategy: "default",
			configuration: {
				// renderer: renderActiveReferenceLine,
			},
			svgCursor: crosshairsCursor,
		};

		super(props, defaultProps);
		this.renderer = null;
		this.instanceId = "mpr-crosshairs";
		this.crossPoint = { x: 0, y: 0 };
		this.coords = null;
		this.showCrosshairs = true;
	}

	async enabledCallback(element, {} = {}) {
		console.log('end?')
		const enabledElement = await waitForEnabledElementImageToLoad(element);
		if (!enabledElement) {
			console.log("没有 load 图像");
			return;
		}

		this.forceImageUpdate(element);
	}
	activeCallback(element) {
		// 显示定位线
		this.showCrosshairs = true;
		setTimeout(() => {
			const node = this.getWidgetNode(element, this.instanceId);

			const enabledElement = csTools.external.cornerstone.getEnabledElement(
				element
			);
	
			if (!enabledElement.image) {
				return;
			}
			// 不存在像素点，创建初始点
			if (!node.getAttribute('crosshairs')){
				const width = parseInt(element.clientWidth / 2, 10);
				const height = parseInt(element.clientHeight / 2, 10);
				const pt = cornerstone.canvasToPixel(element, {x: width, y: height})
				node.setAttribute('crosshairs', JSON.stringify(pt))
			}
			// 渲染定位线
			this.renderLines(element)

			this.coords = new CoordsEvent.getInstance();

			this.coords.removeEventListener('dispatchCornerstoneCoords')
			// 添加执行的事件（vtk改变，触发cornerstone相应改变）
			this.coords.addEventListener('dispatchCornerstoneCoords', (worldPos, showCrosshairs) => {
				this.showCrosshairs = showCrosshairs;
				this.setCoordsByDispatchWorldPos(worldPos)
			})
		}, 0);
	}
	touchDragCallback(evt) {
		this.createLines(evt.detail);
		evt.preventDefault()
		evt.stopPropagation()
	}
	mouseDragCallback(evt) {
		this.createLines(evt.detail);
		evt.preventDefault()
		evt.stopPropagation()
	}
	postMouseDownCallback(evt) {
		this.createLines(evt.detail);
		evt.preventDefault()
		evt.stopPropagation()
	}
	disabledCallback(element) {
		const node = this.getWidgetNode(element, this.instanceId);
		node.innerHTML = ``;
		this.forceImageUpdate(element);
	}
	/**
	 * 获取当前 元素 的svg工具
	 * @param {*} svgContainer 
	 * @param {*} widgetId 
	 * @returns 
	 */
	getWidgetNode(svgContainer, widgetId) {
		let node = svgContainer.querySelector(`.${widgetId}`);
		if (!node) {
			node = document.createElement("g");
			node.setAttribute("class", widgetId);
			node.setAttribute(
				"style",
				"position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;"
			);
			svgContainer.appendChild(node);
		}
		return node;
	}
	/**
	 * 图像定位，其它图像变换切换逻辑
	 * @param {*} eventData 
	 * @returns 
	 */
	createLines(eventData) {
		const goodEventData =
			eventData && eventData.currentPoints && eventData.currentPoints.image;

		if (!goodEventData) {
			console.log("not Point");
			return;
		}

		// 获取图像平面数据
		const imagePlane =
			csTools.external.cornerstone.metaData.get(
				"imagePlaneModule",
				eventData.image.imageId
			) || {};
		
		// 获取点击图像 x，y 位置
		const imagePointXY = eventData.currentPoints.image;
		// 通过点击的位置转换成患者点
		const ipp = imagePointToPatientPoint(imagePointXY, imagePlane)
		const ippVec3 = vec3.fromValues(ipp.x, ipp.y, ipp.z)

		// cornerstone 操作后， 触发 vtk
		this.coords.dispatchEvent('dispatchVtkCoords', ippVec3)
		// this.drawLines(eventData.element, x, y);
		
		// 遍历其它序列元素
		const dom = document.getElementsByClassName("crosshairs-parent");
		for (const el of dom) {

			// 当前元素不操作
			if (el === eventData.element) {
				continue;
			}

			// 获取堆栈中的图像
			let allStack = cornerstoneTools.getToolState(el, 'stack');
			if (!allStack) return;
			const imageIds = allStack.data[0].imageIds;
			// 自己存的确定当前图像所在面（冠、矢、横）
			const orientation = allStack.data[0].state.orientation;

			// 图像位置
			let currentPoint = 0;
			let orientationIndex = 0;

			if (orientation === 'x') {
				// 矢状位置
				currentPoint = ippVec3[0];
				orientationIndex = 0;
			}else if (orientation === 'y') {
				// 冠状位
				currentPoint = ippVec3[1];
				orientationIndex = 1;
			}else {
				// 横截面
				currentPoint = ippVec3[2];
				orientationIndex = 2;
			}

			// 查找一个最近的点的序列位置
			const index = this.findRenderIndex(imageIds, currentPoint, orientationIndex)
			// 滚动到对应位置
			cornerstoneTools.scrollToIndex(el, index)
			
			// this.crossPoint = this.projectPatientPointToImagePlane(ippVec3, targetImagePlane)
			// this.drawLines(el, this.crossPoint.x, this.crossPoint.y);
		}
		// 赋值当前点中的点
		const node = this.getWidgetNode(eventData.element, this.instanceId);
		node.setAttribute('crosshairs', JSON.stringify( imagePointXY ))
		// 执行绘制渲染定位线
		this.renderLines(eventData.element)
	}

	// 或者线条
	drawLines(element, px, py) {
		const node = this.getWidgetNode(element, this.instanceId);

		// 获取 canvas 的大小
		const width = parseInt(element.clientWidth, 10);
		const height = parseInt(element.clientHeight, 10);

		// 像素点转为 canvas 点，因为是要绘制到 svg上面的
		const {
			x,
			y
		} = cornerstone.pixelToCanvas(element, {
			x: px,
			y: py
		});
		// 绘制线条
		crosshairsWidget(node, width, height, x, y, this.showCrosshairs)
	}
	/**
	 * 渲染执行绘制十字定位线逻辑
	 * @param element dom
	 * @returns 
	 */
	renderLines(element) {
		// console.log('renderLines')
		const node = this.getWidgetNode(element, this.instanceId);
		// 当前图像存储的点
		let pt   = node.getAttribute('crosshairs');
		if (pt){
			pt = JSON.parse(pt)
			// 通过 dom 元素获取图像
			let targetImage;
			try {
				targetImage = cornerstone.getImage(element);
			} catch (ex) {
				console.warn('target image is not enabled')
				return;
			}
			if (!targetImage) {
				return;
			}
			// 获取图像平面信息
			const imagePlane =
				csTools.external.cornerstone.metaData.get(
					"imagePlaneModule",
					targetImage.imageId
				) || {};
			const ipp = imagePointToPatientPoint(pt, imagePlane)
			const ippVec3 = vec3.fromValues(ipp.x, ipp.y, ipp.z)

			const dom = document.getElementsByClassName("crosshairs-parent");
			for (const el of dom) {
				// if (el === element) {
				// 	continue;
				// }
				let targetImage;
				try {
					targetImage = cornerstone.getImage(el);
				} catch (ex) {
					console.warn('target image is not enabled??')
					return;
				}
				if (!targetImage) {
					return;
				}
				const targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId);
				const crossPoint = this.projectPatientPointToImagePlane(ippVec3, targetImagePlane)
				// 调用绘制
				this.drawLines(el, crossPoint.x, crossPoint.y);
				// 存储最新点位置
				const node = this.getWidgetNode(el, this.instanceId);
				node.setAttribute('crosshairs', JSON.stringify(crossPoint))
			}
		}
	}

	// Tools 内置的一个算法 
	projectPatientPointToImagePlane(patientPoint, imagePlane) {
		const rowCosines = imagePlane.rowCosines;
		const columnCosines = imagePlane.columnCosines;
		const imagePositionPatient = imagePlane.imagePositionPatient;
	  
		const rowCosinesVec3 = vec3.fromValues(...rowCosines);
		const colCosinesVec3 = vec3.fromValues(...columnCosines);
		const ippVec3 = vec3.fromValues(...imagePositionPatient);
	  
		const point = vec3.create();
		vec3.sub(point, patientPoint, ippVec3);
	  
		const x = vec3.dot(rowCosinesVec3, point) / imagePlane.columnPixelSpacing;
		const y = vec3.dot(colCosinesVec3, point) / imagePlane.rowPixelSpacing;
		
		return { x, y };
	}

	/**
	 * 查找最近的图像，返回最近图像的下标
	 * @param {*} arr 序列
	 * @param {*} val 比较值
	 * @param {*} orientationIndex 1,2,3 [x,y,z]
	 * @returns 
	 */
    findRenderIndex(arr, val, orientationIndex) {

		let minValue = null;
		let index = null;
		// 遍历当前操作的图像序列
		arr.some((_, idx) => {
			// 绝对值
			const differ = Math.abs(this.getImageIppByString(_, orientationIndex) - val);
			// 差距越来越小，赋值最新差值
			if (differ < minValue || minValue === null){
				minValue = differ;
				index = idx;
			}else {
				// 差距值越来越大，直接退出
				return true;
			}
		})
		return index;
		// console.log('findRenderByIndex')
        // const idx = arr.findIndex(x => this.getImageIppByString(x, orientationIndex) < val)
		// if (idx == -1) return arr.length - 1 
        // if (idx == 0) return 0
		// return ((val - this.getImageIppByString( arr[idx], orientationIndex ) ) < (this.getImageIppByString( arr[idx - 1], orientationIndex) - val)) ? idx : idx - 1

    }
	// 获取 xyz ipp 值
	/**
	 * 
	 * @param {*} imageId 图像 id
	 * @param {*} index   xyz 0 = x, 1 = y, 2 = z
	 */
	getImageIppByString(imageId = '', index = 0){
		// 这个也能获取到 imagePositionPatient 位置
		if (imageId.includes('mpr')){
			// 一种获取方式 
			const arr = imageId.split(':');
			return arr[3].split(',').map(Number)[index];
		}

		const metaData = csTools.external.cornerstone.metaData.get('imagePlaneModule',imageId)
		return metaData.imagePositionPatient[index];

		
	}

	// 更新图像
	forceImageUpdate(element) {
		const enabledElement = csTools.external.cornerstone.getEnabledElement(
			element
		);

		if (enabledElement.image) {
			csTools.external.cornerstone.updateImage(element);
		}
	}

	// 图像渲染回调
	renderToolData(evt) {
		if(!evt.detail.image.imageId) return;

		this.renderLines(evt.detail.element)
		this.getWordCoordsByElementXY(evt.detail.element)
	}
	/**
	 * 通过 cornerstone 的图像位置，x,y 订阅发布改变 vtk 视图 
	 * @param {*} element 元素
	 * @returns 
	 */
	getWordCoordsByElementXY(element){
		const node = this.getWidgetNode(element, this.instanceId);
		// 当前图像存储的点
		let pt   = node.getAttribute('crosshairs');
		if (pt){
			pt = JSON.parse(pt)
			// 通过 dom 元素获取图像
			let targetImage;
			try {
				targetImage = cornerstone.getImage(element);
			} catch (ex) {
				console.warn('target image is not enabled')
				return;
			}
			if (!targetImage) {
				return;
			}
			// 获取图像平面信息
			const imagePlane =
				csTools.external.cornerstone.metaData.get(
					"imagePlaneModule",
					targetImage.imageId
				) || {};
			const ipp = imagePointToPatientPoint(pt, imagePlane)
			const ippVec3 = vec3.fromValues(ipp.x, ipp.y, ipp.z)
			
			// 发布订阅-发布
			if (!this.coords) return;
			this.coords.dispatchEvent('dispatchVtkCoords', ippVec3)
		}
		return;
	}
	/**
	 * 点击vtk视图时候,设置其它 cornerstone 视图
	 * @param {*} imagePos vtk 定位线位置
	 * @param {*} worldPos vtk 世界坐标位置
	 * @returns 
	 */
	setCoordsByDispatchWorldPos(worldPos){
		
		let ippVec3 = []
		// 不知道为什么 数组结构，会在嵌套一层数组
		if (worldPos[0].length){
			ippVec3 = [...worldPos[0]]
		}else {
			ippVec3 = worldPos
		}
		// 遍历序列元素
		const dom = document.getElementsByClassName("crosshairs-parent");

		const el = dom[0];
		if (!el) return;
		// 获取堆栈中的图像
		let allStack = cornerstoneTools.getToolState(el, 'stack');
		if (!allStack) return;
		const imageIds = allStack.data[0].imageIds;
		// 自己存的确定当前图像所在面（冠、矢、横）
		const orientation = allStack.data[0].state.orientation;

		// 图像位置
		let currentPoint = 0;
		let orientationIndex = 0;

		if (orientation === 'x') {
			// 矢状位置
			currentPoint = ippVec3[0];
			orientationIndex = 0;
		}else if (orientation === 'y') {
			// 冠状位
			currentPoint = ippVec3[1];
			orientationIndex = 1;
		}else {
			// 横截面
			currentPoint = ippVec3[2];
			orientationIndex = 2;
		}

		// 查找一个最近的点的序列位置
		const index = this.findRenderIndex(imageIds, currentPoint, orientationIndex)
		// 滚动到对应位置
		// console.log(index, currentPoint, orientationIndex)
		cornerstoneTools.scrollToIndex(el, index)
		
		// 赋值当前点中的点
		const node = this.getWidgetNode(el, this.instanceId);

		// 获取当前图像信息（plane）
		let targetImage;
		try {
			targetImage = cornerstone.getImage(el);
		} catch (ex) {
			console.warn('target image is not enabled??')
			return;
		}
		if (!targetImage) {
			return;
		}
		const targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId);
		const crossPoint = this.projectPatientPointToImagePlane(ippVec3, targetImagePlane)
		node.setAttribute('crosshairs', JSON.stringify( crossPoint ))
		// // 执行绘制渲染定位线
		this.renderLines(el)

	}
}