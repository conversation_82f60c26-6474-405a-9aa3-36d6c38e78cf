import { vec3, mat4 } from 'gl-matrix';
import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';

import vtkImageData from 'vtk.js/Sources/Common/DataModel/ImageData';
import vtkDataArray from 'vtk.js/Sources/Common/Core/DataArray';

import buildMetadata from '$library/vtk/lib/data/buildMetadata.js';
import sortDatasetsByImagePosition from '$library/vtk/lib/data/sortDatasetsByImagePosition.js';

import loadImageData from './loadImageData.js'

/**
 * 创建体
 * @param {*} imageIds 
 * @returns 
 */
export default async function getImageData(imageIds) {
	return new Promise((resolve, reject) => {
		const { metaData0, metaDataMap, imageMetaData0 } = buildMetadata(imageIds); // cornerstone 所有序列的元数据（类似 tags 值）
	
		const { rowCosines, columnCosines } = metaData0;                   // 行列余弦
		const rowCosineVec = vec3.fromValues(...rowCosines);               // 行余弦向量
		const colCosineVec = vec3.fromValues(...columnCosines);			   // 列余弦向量
		const scanAxisNormal = vec3.cross([], rowCosineVec, colCosineVec); // 行列余弦转换 x,y,z 法线

        const rowCosineSTD = [1,0,0]           // 行余弦向量
		const colCosineSTD = [0,1,0]		   // 列余弦向量
		const scanAxisNormalSTD = [0,0,1]
	
		let { spacing, origin, sortedDatasets } = sortDatasetsByImagePosition(
			scanAxisNormal,
			metaDataMap
		);  // 排序 

		const xSpacing = metaData0.columnPixelSpacing;  // 列像素间距
		const ySpacing = metaData0.rowPixelSpacing;     // 行像素间距
		const zSpacing = spacing;                       // z方向间距
		const xVoxels = metaData0.columns;              // 类似宽度
		const yVoxels = metaData0.rows;	                // 类似高度
		const zVoxels = metaDataMap.size;      			// 空间 z轴 size
		const signed = imageMetaData0.pixelRepresentation === 1; // 有无符号（图像tags标记出来的）
		const modality   = imageMetaData0.modality;
		const multiComponent = metaData0.numberOfComponents > 1;
		// TODO: Support numberOfComponents = 3 for RGB?
		if (multiComponent) {
			throw new Error('Multi component image not supported by this plugin.');
		}
		let pixelArray;
		switch (imageMetaData0.bitsAllocated) {
			case 8:
				if (signed) {
					throw new Error(
						'8 Bit signed images are not yet supported by this plugin.'
					);
				} else {
					throw new Error(
						'8 Bit unsigned images are not yet supported by this plugin.'
					);
				}
	
			case 16:
				// TODO 数据类型 Uint16Array Int16Array Float32Array
				// 有符号类型
				// if (signed){
				// 	// 最小像素值等于 0 用无符号 TODO
				// 	imageMetaData0.smallestPixelValue === 0
				// 	? pixelArray = new Uint16Array(xVoxels * yVoxels * zVoxels)
				// 	: pixelArray = new Int16Array(xVoxels * yVoxels * zVoxels);
				// }else {
				// 	pixelArray = new Uint16Array(xVoxels * yVoxels * zVoxels);
				// }
	
				// PT 的像素在运算 *斜率+截距 后超过 int16存储区域 32767 ~ -32768
				// if (signed && isPT != 'PT') {
				// 	pixelArray = new Int16Array(xVoxels * yVoxels * zVoxels)
				// } else {
				// 	// PT && PT 最小值小于 0
				// 	if (isPT === 'PT' && imageMetaData0.smallestPixelValue < 0) {
				// 		pixelArray = new Int32Array(xVoxels * yVoxels * zVoxels)
				// 	}else {
				// 		pixelArray = new Uint16Array(xVoxels * yVoxels * zVoxels)
				// 	}
				// }
				if (signed){
					if (modality === 'PT') {
						pixelArray = new Int32Array(xVoxels * yVoxels * zVoxels);
					}else {
						pixelArray = new Int16Array(xVoxels * yVoxels * zVoxels);
					}
				}else {
					if (modality === 'PT') {
						pixelArray = new Uint32Array(xVoxels * yVoxels * zVoxels);
					}else {
						pixelArray = new Uint16Array(xVoxels * yVoxels * zVoxels);
					}
				}
	
				// 有符号,无符号没有什么用.用这个条件做判断(最小像素值、PT)
				// 使用 int32位存储像素值
				// pixelArray = new Int32Array(xVoxels * yVoxels * zVoxels)
				
				// imageMetaData0.smallestPixelValue === 0 && imageMetaData0.modality === 'PT'
				// ? pixelArray = new Int32Array(xVoxels * yVoxels * zVoxels)
				// : pixelArray = new Int32Array(xVoxels * yVoxels * zVoxels);
	
				break;
		}
	
		const scalarArray = vtkDataArray.newInstance({
			name: 'Pixels',
			numberOfComponents: 1,
			values: pixelArray,
		});
	
		// 创建 vtk 图像数据
		const imageData = vtkImageData.newInstance();
	
		let direction = [...rowCosineSTD, ...colCosineSTD, ...scanAxisNormalSTD];
		// 设置图像的一些关键信息（三维人体坐标）
		imageData.setDimensions(xVoxels, yVoxels, zVoxels); // 大小
		imageData.setSpacing(xSpacing, ySpacing, zSpacing); // 间距

	
		// const machineOffset = [-0.517930, -3.844750, -9.072530 * -1]
		const machineOffset = (imageMetaData0.machineOffsetModule || []).map(i => i || 0)
	
		if (machineOffset[2]) {
			let [xMin, xMax, yMin, yMax, zMin, zMax] = imageData.getExtent();
			zMin += Math.floor(machineOffset[2] / zSpacing * -1)
			zMax += Math.floor(machineOffset[2] / zSpacing * -1)
			imageData.setExtent(xMin, xMax, yMin, yMax, zMin, zMax)
		}
		
		if (machineOffset.find(i => !!i)) { 
			origin[0] += machineOffset[0]
			origin[1] += machineOffset[1]
			// origin[2] += machineOffset[2] * -1
		}
	
		if (machineOffset[3]) {
			const { matrix } = vtkMatrixBuilder.buildFromDegree()
				.setMatrix([...rowCosineSTD, 0, ...colCosineSTD, 0, ...scanAxisNormalSTD, 0, 0,0,0,0])
				.translate(machineOffset[0], machineOffset[1], machineOffset[2])
				.rotateX(machineOffset[3])
				.rotateY(machineOffset[4])
				.rotateZ(machineOffset[5] * -1)
	
			direction = [
				matrix[0], matrix[1], matrix[2],
				matrix[4], matrix[5], matrix[6],
				matrix[8], matrix[9], matrix[10],
		   ]
		}
		
	
		imageData.setDirection(direction);                  // 方向
		imageData.setOrigin(...origin);                     // 原点左上角位置
		imageData.getPointData().setScalars(scalarArray);   // 设置图像数据
		pixelArray = null;

		const _publishPixelDataInserted = count => {
			imageDataObject.subscriptions.onPixelDataInserted.forEach(callback => {
				callback(count);
			});
		};
	
		const _publishPixelDataInsertedError = error => {
			imageDataObject.subscriptions.onPixelDataInsertedError.forEach(callback => {
				callback(error);
			});
		};
	
		const _publishAllPixelDataInserted = () => {
			imageDataObject.subscriptions.onAllPixelDataInserted.forEach(callback => {
				callback();
			});
			imageDataObject.isLoading = false;
			imageDataObject.loaded = true;
			imageDataObject.vtkImageData.modified();
	
			// Remove all subscriptions on completion.
			imageDataObject.subscriptions = {
				onPixelDataInserted: [],
				onPixelDataInsertedError: [],
				onAllPixelDataInserted: [],
			};

			resolve({
				vtkImageData: imageData,
				centerIpp,				// 切图的时候用到
				origin,
				orientation: [...rowCosineSTD, ...colCosineSTD],
			    oldDirection: [...rowCosineVec, ...colCosineVec, ...scanAxisNormal],
			});
		};
		const centerIpp = _getVolumeCenterIpp(imageData);
	
		const imageDataObject = {
			machineOffset,
			imageIds,
			centerIpp,
			metaData0,
			imageMetaData0,
			dimensions: [xVoxels, yVoxels, zVoxels],
			spacing: [xSpacing, ySpacing, zSpacing],
			origin,
			direction,
			vtkImageData: imageData,
			metaDataMap,
			sortedDatasets,
			loaded: false,
			subscriptions: {
				onPixelDataInserted: [],
				onPixelDataInsertedError: [],
				onAllPixelDataInserted: [],
			},
			onPixelDataInserted: callback => {
				imageDataObject.subscriptions.onPixelDataInserted.push(callback);
			},
			onPixelDataInsertedError: callback => {
				imageDataObject.subscriptions.onPixelDataInsertedError.push(callback);
			},
			onAllPixelDataInserted: callback => {
				imageDataObject.subscriptions.onAllPixelDataInserted.push(callback);
			},
			_publishPixelDataInserted,
			_publishAllPixelDataInserted,
			_publishPixelDataInsertedError,
		};
	
		// 加载图像数据，并把其设置到 imageData 里
		loadImageData(imageDataObject);
	
		// 删除体的tag信息
		// imageDataObject.imageIds = null;
		// imageDataObject.metaDataMap = null;
		// imageDataObject.sortedDatasets = null;
		// imageDataObject.metaData0 = null;
		// return imageDataObject; // 返回一个大对象（vtk图像对象，图像关键信息）
		// return {
		// 	vtkImageData: imageData,
		// 	centerIpp,				// 切图的时候用到
		// };
	})
}


/**
 * Calculates the center IPP for the volume. Useful for displaying
 * "best" slice on first render.
 * 获取中心点
 * @param {*} vtkImageData
 * @returns {Vec3} - Float32Array contain the volume's center IPP
 */
 function _getVolumeCenterIpp(vtkImageData){

    const [x0, y0, z0] = vtkImageData.getOrigin();
    const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing();
    const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent();

    const centerOfVolume = vec3.fromValues(
        x0 + xSpacing * 0.5 * (xMin + xMax),
        y0 + ySpacing * 0.5 * (yMin + yMax),
        z0 + zSpacing * 0.5 * (zMin + zMax)
    )

    return centerOfVolume;
}