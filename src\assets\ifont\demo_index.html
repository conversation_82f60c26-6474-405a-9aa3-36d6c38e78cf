<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1706250" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xeb5e;</span>
                <div class="name">对比_o</div>
                <div class="code-name">&amp;#xeb5e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb09;</span>
                <div class="name">箭头_上下切换</div>
                <div class="code-name">&amp;#xeb09;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">曲线</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb0d;</span>
                <div class="name">箭头_左右切换</div>
                <div class="code-name">&amp;#xeb0d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87a;</span>
                <div class="name">vr播放器</div>
                <div class="code-name">&amp;#xe87a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">上下移动箭头</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">3D旋转</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">体积小</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78c;</span>
                <div class="name">link</div>
                <div class="code-name">&amp;#xe78c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c6;</span>
                <div class="name">unlink</div>
                <div class="code-name">&amp;#xe8c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">测量工具1</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6bb;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xedda;</span>
                <div class="name">改变方向</div>
                <div class="code-name">&amp;#xedda;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea0c;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xea0c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea0d;</span>
                <div class="name">退出全屏</div>
                <div class="code-name">&amp;#xea0d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea32;</span>
                <div class="name">多平面重建</div>
                <div class="code-name">&amp;#xea32;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9cc;</span>
                <div class="name">重建</div>
                <div class="code-name">&amp;#xe9cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7df;</span>
                <div class="name">用户信息</div>
                <div class="code-name">&amp;#xe7df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">颜色字段</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xea1d;</span>
                <div class="name">转发,流转,旋转</div>
                <div class="code-name">&amp;#xea1d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">中间</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">下</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">上</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">文本</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">圆圈</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">后</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">前</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">左</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64b;</span>
                <div class="name">右</div>
                <div class="code-name">&amp;#xe64b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">移动</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">调窗</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">三维</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">设 置</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">ban</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe935;</span>
                <div class="name">中心</div>
                <div class="code-name">&amp;#xe935;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9f5;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe9f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9f6;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe9f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c8;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe6c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dd;</span>
                <div class="name">布局</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe86c;</span>
                <div class="name">stop-circle-o</div>
                <div class="code-name">&amp;#xe86c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9f4;</span>
                <div class="name">pause</div>
                <div class="code-name">&amp;#xe9f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d9;</span>
                <div class="name">pause</div>
                <div class="code-name">&amp;#xe7d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8b7;</span>
                <div class="name">th-large</div>
                <div class="code-name">&amp;#xe8b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe802;</span>
                <div class="name">reorder</div>
                <div class="code-name">&amp;#xe802;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe85a;</span>
                <div class="name">stop</div>
                <div class="code-name">&amp;#xe85a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe87b;</span>
                <div class="name">th</div>
                <div class="code-name">&amp;#xe87b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ca;</span>
                <div class="name">eraser</div>
                <div class="code-name">&amp;#xe6ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">垂直翻转</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">水平翻转</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe96b;</span>
                <div class="name">旋转</div>
                <div class="code-name">&amp;#xe96b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe971;</span>
                <div class="name">旋转</div>
                <div class="code-name">&amp;#xe971;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe92c;</span>
                <div class="name">中心点</div>
                <div class="code-name">&amp;#xe92c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe92e;</span>
                <div class="name">勾画</div>
                <div class="code-name">&amp;#xe92e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe92f;</span>
                <div class="name">重置</div>
                <div class="code-name">&amp;#xe92f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe930;</span>
                <div class="name">调窗</div>
                <div class="code-name">&amp;#xe930;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe931;</span>
                <div class="name">椭圆</div>
                <div class="code-name">&amp;#xe931;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe932;</span>
                <div class="name">旋转</div>
                <div class="code-name">&amp;#xe932;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe933;</span>
                <div class="name">矩形</div>
                <div class="code-name">&amp;#xe933;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe846;</span>
                <div class="name">箭头</div>
                <div class="code-name">&amp;#xe846;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9f3;</span>
                <div class="name">下一帧</div>
                <div class="code-name">&amp;#xe9f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b3;</span>
                <div class="name">dedent</div>
                <div class="code-name">&amp;#xe6b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe9f2;</span>
                <div class="name">dedent</div>
                <div class="code-name">&amp;#xe9f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8eb;</span>
                <div class="name">角度</div>
                <div class="code-name">&amp;#xe8eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe921;</span>
                <div class="name">翻图</div>
                <div class="code-name">&amp;#xe921;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe923;</span>
                <div class="name">播放</div>
                <div class="code-name">&amp;#xe923;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe924;</span>
                <div class="name">cobb</div>
                <div class="code-name">&amp;#xe924;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe925;</span>
                <div class="name">透镜</div>
                <div class="code-name">&amp;#xe925;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe926;</span>
                <div class="name">CT值</div>
                <div class="code-name">&amp;#xe926;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe927;</span>
                <div class="name">融合</div>
                <div class="code-name">&amp;#xe927;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe928;</span>
                <div class="name">缩放</div>
                <div class="code-name">&amp;#xe928;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe929;</span>
                <div class="name">反片</div>
                <div class="code-name">&amp;#xe929;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe92a;</span>
                <div class="name">直线</div>
                <div class="code-name">&amp;#xe92a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe92b;</span>
                <div class="name">下一帧</div>
                <div class="code-name">&amp;#xe92b;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1684306419689') format('woff2'),
       url('iconfont.woff?t=1684306419689') format('woff'),
       url('iconfont.ttf?t=1684306419689') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconduibi_o"></span>
            <div class="name">
              对比_o
            </div>
            <div class="code-name">.iconduibi_o
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontop-bottom"></span>
            <div class="name">
              箭头_上下切换
            </div>
            <div class="code-name">.icontop-bottom
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconquxian"></span>
            <div class="name">
              曲线
            </div>
            <div class="code-name">.iconquxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconleft-right"></span>
            <div class="name">
              箭头_左右切换
            </div>
            <div class="code-name">.iconleft-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconvr"></span>
            <div class="name">
              vr播放器
            </div>
            <div class="code-name">.iconvr
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcutPage"></span>
            <div class="name">
              上下移动箭头
            </div>
            <div class="code-name">.iconcutPage
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmipRotate"></span>
            <div class="name">
              3D旋转
            </div>
            <div class="code-name">.iconmipRotate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbodyMeasure"></span>
            <div class="name">
              体积小
            </div>
            <div class="code-name">.iconbodyMeasure
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlink"></span>
            <div class="name">
              link
            </div>
            <div class="code-name">.iconlink
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconunlink"></span>
            <div class="name">
              unlink
            </div>
            <div class="code-name">.iconunlink
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcelianggongju1"></span>
            <div class="name">
              测量工具1
            </div>
            <div class="code-name">.iconcelianggongju1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconai69"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.iconai69
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongaibianfangxiang"></span>
            <div class="name">
              改变方向
            </div>
            <div class="code-name">.icongaibianfangxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfull-screen"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.iconfull-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconexit-full-screen"></span>
            <div class="name">
              退出全屏
            </div>
            <div class="code-name">.iconexit-full-screen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconduopingmianzhongjian1"></span>
            <div class="name">
              多平面重建
            </div>
            <div class="code-name">.iconduopingmianzhongjian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconportrait-film"></span>
            <div class="name">
              重建
            </div>
            <div class="code-name">.iconportrait-film
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyonghuxinxi"></span>
            <div class="name">
              用户信息
            </div>
            <div class="code-name">.iconyonghuxinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanseziduan"></span>
            <div class="name">
              颜色字段
            </div>
            <div class="code-name">.iconyanseziduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconloop"></span>
            <div class="name">
              转发,流转,旋转
            </div>
            <div class="code-name">.iconloop
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongjian"></span>
            <div class="name">
              中间
            </div>
            <div class="code-name">.iconzhongjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxia"></span>
            <div class="name">
              下
            </div>
            <div class="code-name">.iconxia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshang"></span>
            <div class="name">
              上
            </div>
            <div class="code-name">.iconshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjiantou"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.iconjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwenben"></span>
            <div class="name">
              文本
            </div>
            <div class="code-name">.iconwenben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyuanquan"></span>
            <div class="name">
              圆圈
            </div>
            <div class="code-name">.iconyuanquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconhou"></span>
            <div class="name">
              后
            </div>
            <div class="code-name">.iconhou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqian"></span>
            <div class="name">
              前
            </div>
            <div class="code-name">.iconqian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzuo"></span>
            <div class="name">
              左
            </div>
            <div class="code-name">.iconzuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyou"></span>
            <div class="name">
              右
            </div>
            <div class="code-name">.iconyou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyidong"></span>
            <div class="name">
              移动
            </div>
            <div class="code-name">.iconyidong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontiaochuang"></span>
            <div class="name">
              调窗
            </div>
            <div class="code-name">.icontiaochuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconsanwei"></span>
            <div class="name">
              三维
            </div>
            <div class="code-name">.iconsanwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshezhi1"></span>
            <div class="name">
              设 置
            </div>
            <div class="code-name">.iconshezhi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconban"></span>
            <div class="name">
              ban
            </div>
            <div class="code-name">.iconban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongxin"></span>
            <div class="name">
              中心
            </div>
            <div class="code-name">.iconzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconai233-01-copy"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.iconai233-01-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconai212-01-copy"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.iconai212-01-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconai212"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.iconai212
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconai233"></span>
            <div class="name">
              布局
            </div>
            <div class="code-name">.iconai233
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconstop-circle"></span>
            <div class="name">
              stop-circle-o
            </div>
            <div class="code-name">.iconstop-circle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconpause-01-copy"></span>
            <div class="name">
              pause
            </div>
            <div class="code-name">.iconpause-01-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconpause"></span>
            <div class="name">
              pause
            </div>
            <div class="code-name">.iconpause
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconth-large"></span>
            <div class="name">
              th-large
            </div>
            <div class="code-name">.iconth-large
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconreorder"></span>
            <div class="name">
              reorder
            </div>
            <div class="code-name">.iconreorder
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconstop1"></span>
            <div class="name">
              stop
            </div>
            <div class="code-name">.iconstop1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconth"></span>
            <div class="name">
              th
            </div>
            <div class="code-name">.iconth
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconeraser"></span>
            <div class="name">
              eraser
            </div>
            <div class="code-name">.iconeraser
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchuizhifanzhuan"></span>
            <div class="name">
              垂直翻转
            </div>
            <div class="code-name">.iconchuizhifanzhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuipingfanzhuan"></span>
            <div class="name">
              水平翻转
            </div>
            <div class="code-name">.iconshuipingfanzhuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconrotate-anticlockwise"></span>
            <div class="name">
              旋转
            </div>
            <div class="code-name">.iconrotate-anticlockwise
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconrotate-clockwise"></span>
            <div class="name">
              旋转
            </div>
            <div class="code-name">.iconrotate-clockwise
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmidpoint"></span>
            <div class="name">
              中心点
            </div>
            <div class="code-name">.iconmidpoint
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbrush"></span>
            <div class="name">
              勾画
            </div>
            <div class="code-name">.iconbrush
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconreset"></span>
            <div class="name">
              重置
            </div>
            <div class="code-name">.iconreset
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconadjust-0"></span>
            <div class="name">
              调窗
            </div>
            <div class="code-name">.iconadjust-0
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconellipse"></span>
            <div class="name">
              椭圆
            </div>
            <div class="code-name">.iconellipse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconrotate"></span>
            <div class="name">
              旋转
            </div>
            <div class="code-name">.iconrotate
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconrectangle"></span>
            <div class="name">
              矩形
            </div>
            <div class="code-name">.iconrectangle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconarrows-1"></span>
            <div class="name">
              箭头
            </div>
            <div class="code-name">.iconarrows-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconpre-frame"></span>
            <div class="name">
              下一帧
            </div>
            <div class="code-name">.iconpre-frame
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondedent-right"></span>
            <div class="name">
              dedent
            </div>
            <div class="code-name">.icondedent-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondedent-left"></span>
            <div class="name">
              dedent
            </div>
            <div class="code-name">.icondedent-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconangle"></span>
            <div class="name">
              角度
            </div>
            <div class="code-name">.iconangle
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconturn"></span>
            <div class="name">
              翻图
            </div>
            <div class="code-name">.iconturn
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconplay-1"></span>
            <div class="name">
              播放
            </div>
            <div class="code-name">.iconplay-1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcobb"></span>
            <div class="name">
              cobb
            </div>
            <div class="code-name">.iconcobb
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlens"></span>
            <div class="name">
              透镜
            </div>
            <div class="code-name">.iconlens
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconct-value"></span>
            <div class="name">
              CT值
            </div>
            <div class="code-name">.iconct-value
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfuse"></span>
            <div class="name">
              融合
            </div>
            <div class="code-name">.iconfuse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzoom"></span>
            <div class="name">
              缩放
            </div>
            <div class="code-name">.iconzoom
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconinvert"></span>
            <div class="name">
              反片
            </div>
            <div class="code-name">.iconinvert
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconline"></span>
            <div class="name">
              直线
            </div>
            <div class="code-name">.iconline
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconnext-frame"></span>
            <div class="name">
              下一帧
            </div>
            <div class="code-name">.iconnext-frame
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconduibi_o"></use>
                </svg>
                <div class="name">对比_o</div>
                <div class="code-name">#iconduibi_o</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontop-bottom"></use>
                </svg>
                <div class="name">箭头_上下切换</div>
                <div class="code-name">#icontop-bottom</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconquxian"></use>
                </svg>
                <div class="name">曲线</div>
                <div class="code-name">#iconquxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconleft-right"></use>
                </svg>
                <div class="name">箭头_左右切换</div>
                <div class="code-name">#iconleft-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconvr"></use>
                </svg>
                <div class="name">vr播放器</div>
                <div class="code-name">#iconvr</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcutPage"></use>
                </svg>
                <div class="name">上下移动箭头</div>
                <div class="code-name">#iconcutPage</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmipRotate"></use>
                </svg>
                <div class="name">3D旋转</div>
                <div class="code-name">#iconmipRotate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbodyMeasure"></use>
                </svg>
                <div class="name">体积小</div>
                <div class="code-name">#iconbodyMeasure</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlink"></use>
                </svg>
                <div class="name">link</div>
                <div class="code-name">#iconlink</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconunlink"></use>
                </svg>
                <div class="name">unlink</div>
                <div class="code-name">#iconunlink</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcelianggongju1"></use>
                </svg>
                <div class="name">测量工具1</div>
                <div class="code-name">#iconcelianggongju1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconai69"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#iconai69</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongaibianfangxiang"></use>
                </svg>
                <div class="name">改变方向</div>
                <div class="code-name">#icongaibianfangxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfull-screen"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#iconfull-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconexit-full-screen"></use>
                </svg>
                <div class="name">退出全屏</div>
                <div class="code-name">#iconexit-full-screen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconduopingmianzhongjian1"></use>
                </svg>
                <div class="name">多平面重建</div>
                <div class="code-name">#iconduopingmianzhongjian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconportrait-film"></use>
                </svg>
                <div class="name">重建</div>
                <div class="code-name">#iconportrait-film</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyonghuxinxi"></use>
                </svg>
                <div class="name">用户信息</div>
                <div class="code-name">#iconyonghuxinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanseziduan"></use>
                </svg>
                <div class="name">颜色字段</div>
                <div class="code-name">#iconyanseziduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconloop"></use>
                </svg>
                <div class="name">转发,流转,旋转</div>
                <div class="code-name">#iconloop</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongjian"></use>
                </svg>
                <div class="name">中间</div>
                <div class="code-name">#iconzhongjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxia"></use>
                </svg>
                <div class="name">下</div>
                <div class="code-name">#iconxia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshang"></use>
                </svg>
                <div class="name">上</div>
                <div class="code-name">#iconshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjiantou"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#iconjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwenben"></use>
                </svg>
                <div class="name">文本</div>
                <div class="code-name">#iconwenben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyuanquan"></use>
                </svg>
                <div class="name">圆圈</div>
                <div class="code-name">#iconyuanquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconhou"></use>
                </svg>
                <div class="name">后</div>
                <div class="code-name">#iconhou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqian"></use>
                </svg>
                <div class="name">前</div>
                <div class="code-name">#iconqian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzuo"></use>
                </svg>
                <div class="name">左</div>
                <div class="code-name">#iconzuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyou"></use>
                </svg>
                <div class="name">右</div>
                <div class="code-name">#iconyou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyidong"></use>
                </svg>
                <div class="name">移动</div>
                <div class="code-name">#iconyidong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontiaochuang"></use>
                </svg>
                <div class="name">调窗</div>
                <div class="code-name">#icontiaochuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconsanwei"></use>
                </svg>
                <div class="name">三维</div>
                <div class="code-name">#iconsanwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshezhi1"></use>
                </svg>
                <div class="name">设 置</div>
                <div class="code-name">#iconshezhi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconban"></use>
                </svg>
                <div class="name">ban</div>
                <div class="code-name">#iconban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongxin"></use>
                </svg>
                <div class="name">中心</div>
                <div class="code-name">#iconzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconai233-01-copy"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#iconai233-01-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconai212-01-copy"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#iconai212-01-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconai212"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#iconai212</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconai233"></use>
                </svg>
                <div class="name">布局</div>
                <div class="code-name">#iconai233</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconstop-circle"></use>
                </svg>
                <div class="name">stop-circle-o</div>
                <div class="code-name">#iconstop-circle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpause-01-copy"></use>
                </svg>
                <div class="name">pause</div>
                <div class="code-name">#iconpause-01-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpause"></use>
                </svg>
                <div class="name">pause</div>
                <div class="code-name">#iconpause</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconth-large"></use>
                </svg>
                <div class="name">th-large</div>
                <div class="code-name">#iconth-large</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconreorder"></use>
                </svg>
                <div class="name">reorder</div>
                <div class="code-name">#iconreorder</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconstop1"></use>
                </svg>
                <div class="name">stop</div>
                <div class="code-name">#iconstop1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconth"></use>
                </svg>
                <div class="name">th</div>
                <div class="code-name">#iconth</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconeraser"></use>
                </svg>
                <div class="name">eraser</div>
                <div class="code-name">#iconeraser</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchuizhifanzhuan"></use>
                </svg>
                <div class="name">垂直翻转</div>
                <div class="code-name">#iconchuizhifanzhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuipingfanzhuan"></use>
                </svg>
                <div class="name">水平翻转</div>
                <div class="code-name">#iconshuipingfanzhuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrotate-anticlockwise"></use>
                </svg>
                <div class="name">旋转</div>
                <div class="code-name">#iconrotate-anticlockwise</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrotate-clockwise"></use>
                </svg>
                <div class="name">旋转</div>
                <div class="code-name">#iconrotate-clockwise</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmidpoint"></use>
                </svg>
                <div class="name">中心点</div>
                <div class="code-name">#iconmidpoint</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbrush"></use>
                </svg>
                <div class="name">勾画</div>
                <div class="code-name">#iconbrush</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconreset"></use>
                </svg>
                <div class="name">重置</div>
                <div class="code-name">#iconreset</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconadjust-0"></use>
                </svg>
                <div class="name">调窗</div>
                <div class="code-name">#iconadjust-0</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconellipse"></use>
                </svg>
                <div class="name">椭圆</div>
                <div class="code-name">#iconellipse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrotate"></use>
                </svg>
                <div class="name">旋转</div>
                <div class="code-name">#iconrotate</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconrectangle"></use>
                </svg>
                <div class="name">矩形</div>
                <div class="code-name">#iconrectangle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconarrows-1"></use>
                </svg>
                <div class="name">箭头</div>
                <div class="code-name">#iconarrows-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconpre-frame"></use>
                </svg>
                <div class="name">下一帧</div>
                <div class="code-name">#iconpre-frame</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondedent-right"></use>
                </svg>
                <div class="name">dedent</div>
                <div class="code-name">#icondedent-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondedent-left"></use>
                </svg>
                <div class="name">dedent</div>
                <div class="code-name">#icondedent-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconangle"></use>
                </svg>
                <div class="name">角度</div>
                <div class="code-name">#iconangle</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconturn"></use>
                </svg>
                <div class="name">翻图</div>
                <div class="code-name">#iconturn</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconplay-1"></use>
                </svg>
                <div class="name">播放</div>
                <div class="code-name">#iconplay-1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcobb"></use>
                </svg>
                <div class="name">cobb</div>
                <div class="code-name">#iconcobb</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlens"></use>
                </svg>
                <div class="name">透镜</div>
                <div class="code-name">#iconlens</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconct-value"></use>
                </svg>
                <div class="name">CT值</div>
                <div class="code-name">#iconct-value</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfuse"></use>
                </svg>
                <div class="name">融合</div>
                <div class="code-name">#iconfuse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzoom"></use>
                </svg>
                <div class="name">缩放</div>
                <div class="code-name">#iconzoom</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconinvert"></use>
                </svg>
                <div class="name">反片</div>
                <div class="code-name">#iconinvert</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconline"></use>
                </svg>
                <div class="name">直线</div>
                <div class="code-name">#iconline</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconnext-frame"></use>
                </svg>
                <div class="name">下一帧</div>
                <div class="code-name">#iconnext-frame</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
