<template>
    <el-dialog
        append-to-body
        title="保存病灶评估"
        :visible.sync="innerVisible"
        :close-on-click-modal="false"
        @close="closeDialog"
        @open="openDialog"
        width="721px"
        custom-class="my-dialog">
        <div class="c-main">
            <div class="left-box">
                <h5>病灶列表</h5>
                <el-scrollbar style="height: 100%" class="overflow-x-hide" ref="scrollDiv1">
                    <ul>
                        <li @click="onClickLesion(item, index)" v-for="(item, index) in list" :key="index" :class="{'i-active': active == index, 'i-not-save': !item.id}">{{ item.lesionIndex }}: {{ item.lesionPositionName }}</li>
                    </ul>
                </el-scrollbar>
            </div>
            <div class="right-box">
                <ul>
                    <li>
                        <span>病灶序号:</span>
                        <el-input-number v-model="formData.lesionIndex" :min="1" :max="999" size="small" class="input"></el-input-number>
                    </li>
                    <li>
                        <span>病灶位置:</span>
                        <el-autocomplete
                            size="small"
                            popper-class="my-autocomplete"
                            v-model="formData.lesionPositionName"
                            :fetch-suggestions="querySearch"
                            placeholder=""
                            class="input">
                            <i
                                class="el-icon-edit el-input__icon"
                                slot="suffix">
                            </i>
                            <template slot-scope="{ item }">
                                <div class="name">{{ item.value }}</div>
                            </template>
                        </el-autocomplete>
                    </li>
                    <li>
                        <span>类型:</span>
                        <el-select v-model="formData.lesionTypeId" size="small" class="input">
                            <el-option
                            v-for="item in typeOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </li>
                </ul>
                <div class="plane-box">
                    <el-scrollbar style="width: 100%" ref="scrollDiv">
                        <div v-for="(item, index) of lesionList" :key="index" class="box">
                            <h6>{{ item.checkName }} <span>{{ item.checkDate }}</span></h6>
                            <ul>
                                <li v-for="(lesion, key, index) of item.measurements" :key="index">
                                    <label>{{ key }}</label>
                                    <span :title="lesion">{{ lesion }}</span>
                                </li>
                            </ul>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        </div>
        <div slot="footer">
            <el-popover
                v-if="formData.id"
                placement="top"
                width="160"
                v-model="popoverVisible">
                <p>确定删除吗？</p>
                <div style="text-align: right; margin: 0">
                    <el-button size="mini" type="text" @click="popoverVisible = false">取消</el-button>
                    <el-button type="primary" size="mini" @click="onClickDel">确定</el-button>
                </div>
                <el-button type="danger" size="mini" slot="reference">删 除</el-button>
            </el-popover>
            <el-button type="primary" size="mini" @click="onClickSave" :loading="loading" style="margin-left: 340px;">{{ formData.id ? '编 辑' : '保 存' }}</el-button>
            <el-button size="mini" @click="innerVisible = false">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import html2canvas from "html2canvas";
import ModeDialog from "$src/mixin/ModeDialog.js";
import { dbLesionAssess } from '$library/db';
export default {
    mixins: [ ModeDialog ],
    props: {
        data: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            loading: false,
            typeOptions: [
                { value: 1, label: '原发灶' },
                { value: 2, label: '转移灶' }
            ],
            formData: {
                lesionIndex: 1,
                lesionPositionName: '',
                lesionTypeId: 1,
            },
            lesionPosition: [
                { value: "第一肋间" },
                { value: "第二肋间" },
                { value: "第三肋间" },
            ],
            lesionList: [],
            list: [],
            active: 1,
            patientKey: '',
            popoverVisible: false
        }
    },
    watch: {
        formData: {
            handler() {
                const activeItem = this.list[this.active];
                if (activeItem && !activeItem.id) {
                    activeItem.lesionIndex = this.formData.lesionIndex;
                    activeItem.lesionPositionName = this.formData.lesionPositionName;
                    activeItem.lesionTypeId = this.formData.lesionTypeId;
                }
            },
            deep: true
        }
    },
    methods: {
        openDialog() {
            this.patientKey = this.$store.state.seriesInfo.sAccessionNumber;
            this.lesionList = this.getInitLesionList();
            // 不更新，element-ui 的滚动条不出现
            this.$nextTick(() => {
                this.$refs.scrollDiv.update()
            })

            this.getData(this.patientKey);
        },
        getInitLesionList() {
            const lesionList = this.data.sort((a, b) => a.checkDate - b.checkDate );
            lesionList.forEach((item, index) => {
                item.checkName = '检查' + (index + 1);
            });

            return lesionList;
        },
        closeDialog() {
            this.formData = {
                lesionIndex: 1,
                lesionPositionName: '',
                lesionTypeId: 1,
            };
            this.list = [];
        },
        querySearch(queryString, cb) {
            cb(this.lesionPosition);
        },
        // 点击保存
        onClickSave() {
            if (!this.formData.lesionPositionName) {
                this.$message.error('病灶位置.须填写');
                return;
            }
            this.loading = true;
            setTimeout(() => {
                const result = Object.assign({}, this.formData);

                const typeName = this.typeOptions.find(item => item.value == this.formData.lesionTypeId);
                if (typeName) {
                    result.lesionTypeName = typeName.label;
                }

                if (this.lesionList[0] && this.lesionList[0].el) {
                    Promise.all(this.lesionList.map(item => {
                        return this.getImageBase64(item.el)
                    })).then(base64s => {
                        this.lesionList.forEach((item, index) => {
                            item.el = null;
                            delete item.el;
                            item.base64 = base64s[index];
                        })

                        result.lesionList = this.lesionList;
                        // TODO 这个做 id 键 会不会有相同患者不同检查，是否一样？
                        result.patientKey = this.$store.state.seriesInfo.sAccessionNumber;
                        this.loading = false;

                        this.addData(result);
                    })
                }else {
                    // 已经存在的
                    result.lesionList = this.lesionList;
                    this.loading = false;

                    this.updateData(result);
                }
                
            }, 100);
        },
        // 获取base64
        getImageBase64(el) {
            return new Promise(resolve => {
                html2canvas(el,
                {
                    // ignoreElements: (element) => {
                    //     if (element.getAttribute('saveimage')) {
                    //         return true;
                    //     };
                    // }
                }).then(canvas => {
                    // const dataURL = canvas.toDataURL("image/png", 1);

                    let extra_canvas = document.createElement("canvas");
                    extra_canvas.setAttribute('width', 300);
                    extra_canvas.setAttribute('height', 300);
                    let ctx = extra_canvas.getContext('2d');

                    ctx.strokeStyle = 'black';
                    ctx.lineJoin = 'round';
                    ctx.lineWidth = 1;
                    ctx.fillRect(0, 0, 300, 300);

                    ctx.drawImage(canvas,0,0,canvas.width, canvas.height,0, 0, 300, 300);
                    const base64 = extra_canvas.toDataURL("image/png", 1);
                    resolve(base64);
                })
            })
        },
        // 添加保存
        addData(data) {
            dbLesionAssess.then(e => {
                e.add(data).then(() => {
                    this.$message.success('保存成功！');
                    this.innerVisible = false;
                })
            });
        },
        updateData(data) {
            dbLesionAssess.then(e => {
                e.update(data).then(() => {
                    // 完成
                    this.$message.success('修改成功！');
                    this.getData(this.patientKey);
                })
            });
        },
        // 获取数据
        getData(condition) {
            dbLesionAssess.then(e => {
                e.getGroup(condition).then(e => {
                    if (e.success) {
                        let lastItem;
                        if (this.list.length) {
                            lastItem = this.$fun.deepClone(this.list[this.list.length - 1]);
                        }
                        this.list = e.data;
                        // 右键保存进入的
                        if (this.data.length) {
                            if (!lastItem) {
                                this.active = this.list.length;
                                this.formData.lesionIndex = this.active + 1;
                                let editItem = Object.assign({}, this.formData);
                                this.lesionList = this.getInitLesionList();
                                editItem.lesionList = this.lesionList;
                                this.list.push(editItem);
                            }else {
                                this.list.push(lastItem);
                            }
                        }
                        this.$nextTick(() => {
                            this.$refs.scrollDiv1.update();
                        })
                    }
                })
            })
        },
        onClickDel() {
            this.popoverVisible = false;
            dbLesionAssess.then(e => {
                e.del(this.formData.id).then(e => {
                    if (e.success) {
                        this.list.splice(this.active, 1);
                        // 删完后，还有数据，选中未保存的
                        const len = this.list.length;
                        if (this.list.length && this.list[len - 1] && !this.list[len - 1].id) {
                            this.active = len - 1;
                        }else if (this.active - 1 >= 0){
                            this.active -= 1;
                        }

                        if (this.list[this.active]) {
                            const activeItem = this.$fun.deepClone(this.list[this.active]);
                            // 病灶标注
                            this.lesionList = activeItem.lesionList;
                            
                            this.formData = activeItem;
                            this.formData.lesionList = null;
                            delete this.formData.lesionList;
                        }else {
                            this.lesionList = [];
                            this.formData = {
                                lesionIndex: 1,
                                lesionPositionName: '',
                                lesionTypeId: 1,
                            };
                        }
                    }
                })
            });

        },
        onClickLesion(item, index) {
            this.active = index;

            const lesionData = this.$fun.deepClone(item);
            // 病灶标注
            this.lesionList = lesionData.lesionList;
            
            this.formData = lesionData;
            this.formData.lesionList = null;
            delete this.formData.lesionList;
        }
    }
}
</script>

<style lang="scss" scoped>
.c-main{
    display: flex;
    .left-box{
        width: 150px;
        margin-right: 20px;
        h5{
            font-weight: bold;
            height: 34px;
            line-height: 34px;
        }
        ul {
            height: 314px;
            border: 1px solid #dcdfe6;
            li {
                position: relative;
                padding: 6px;
                height: 34px;
                line-height: 22px;
                cursor: pointer;
                &.i-active{
                    background: #afdeff;

                }
                &:hover{ 
                    text-decoration: underline;
                }
                &.i-not-save{
                    &::before{
                        content: "未存";
                        font-size: 12px;
                        position: absolute;
                        right: 2px;
                        top: 10px;
                        height: 16px;
                        line-height: 16px;
                        width: 26px;
                        background: #e6a23c;
                        border-radius: 4px;
                        color: white;
                        text-align: center;
                    }
                }
            }
        }
    }
    .right-box{
        flex: 1;
        overflow: hidden;
        > ul{
            flex: 1;
            li{
                height: 50px;
                display: flex;
                align-items: center;
                padding-bottom: 10px;
                span{
                    display: inline-block;
                    width: 80px;
                    padding-right: 10px;
                }
                .input{
                    width: 200px;
                }
            }
        }
    }
}
.plane-box{
    width: 100%;
    height: 200px;
    border: 1px solid #dcdfe6;
    // .el-scrollbar ::v-deep .el-scrollbar__wrap{
    //     margin-bottom: -21px !important;
    // }
    .el-scrollbar{
        white-space: nowrap;
    }
    > ul {
        max-width: 100%;
        height: 200px;
        white-space: nowrap;
        overflow: hidden;
        overflow-x: auto;
    }
    .box{
        display: inline-block;
        border-right: 1px solid #eee;
        width: 173px;
        height: 200px;
        h6{
            font-weight: bold;
            line-height: 24px;
            border-bottom: 1px solid #eee;
            padding-left: 20px;
            background: #f5f7fa;
            span{
                font-weight: normal;
                display: inline-block;
                padding-left: 10px;
            }
        }
        > ul {
            font-size: 13px;
            padding: 4px 4px 4px 6px;
            li {
                line-height: 28px;
                height: 28px;
                label{
                    position: relative;
                    display: inline-block;
                    width: 90px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    &::after{
                        content: ":";
                        position: absolute;
                        right: 10px;
                    }
                }
                span{
                    display: inline-block;
                    width: 75px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
}
</style>