<template>
    <div saveimage="true">
        <div class="i-right-center i-overlay"><i v-if="lock" class="el-icon-lock"></i>{{ groupName != 'undefined' ? groupName : '' }}</div>
    </div>
</template>
<script>
export default {
    props: {
        groupName: '',
        lock: false
    }
}
</script>
<style lang="scss" scoped>

.i-overlay i{
    position: absolute;
    top: 16px;
    right: -2px;
    font-size: 16px;
    font-weight: bold;
}
</style>