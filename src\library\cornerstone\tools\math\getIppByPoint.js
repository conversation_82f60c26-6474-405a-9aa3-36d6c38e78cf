import csTools from '$library/cornerstone/cornerstoneTools'
const imagePointToPatientPoint = csTools.import('util/imagePointToPatientPoint')
// const projectPatientPointToImagePlane = csTools.import('util/projectPatientPointToImagePlane')
import { vec3 } from 'gl-matrix';
const _instanceId = "mpr-crosshairs"
export default function (element) {
    const node = _getWidgetNode(element);


    // 当前图像存储的点
    if (node.dataset.x && node.dataset.y){
        let pt = { x: node.dataset.x, y: node.dataset.y }
        // 通过 dom 元素获取图像
        let targetImage;
        try {
            targetImage = csTools.external.cornerstone.getImage(element);
        } catch (ex) {
            console.warn('target image is not enabled')
            return;
        }
        if (!targetImage) {
            return;
        }
        // 获取图像平面信息
        const imagePlane =
            csTools.external.cornerstone.metaData.get(
                "imagePlaneModule",
                targetImage.imageId
            ) || {};
        const ipp = imagePointToPatientPoint(pt, imagePlane)
        const ippVec3 = vec3.fromValues(ipp.x, ipp.y, ipp.z)
        
        return ippVec3
    }
    return [];
}

export function setWidgetPoint(patientPoint, element) {
    const node = element.querySelector(`.${_instanceId}`)
    if (!node) {
        return false
    }

    let targetImage;
    try {
        targetImage = csTools.external.cornerstone.getImage(element)
    } catch (ex) {
        console.warn('target image is not enabled')
        return false
    }
    if (!targetImage) {
        return false
    }
    const imagePlane =
    csTools.external.cornerstone.metaData.get(
        "imagePlaneModule",
        targetImage.imageId
    ) || {};

    const imageIpp = vec3.fromValues(patientPoint.x, patientPoint.y, patientPoint.z)
    const point = projectPatientPointToImagePlane(imageIpp, imagePlane)

    if (point) {
        node.dataset.x = point.x
        node.dataset.y = point.y
        return true
    }
    return false
}

// Tools 内置的一个算法 
function projectPatientPointToImagePlane(patientPoint, imagePlane) {
    const rowCosines = imagePlane.rowCosines;
    const columnCosines = imagePlane.columnCosines;
    const imagePositionPatient = imagePlane.imagePositionPatient;
    const rowCosinesVec3 = vec3.fromValues(...rowCosines);
    const colCosinesVec3 = vec3.fromValues(...columnCosines);
    const ippVec3 = vec3.fromValues(...imagePositionPatient);

    const point = vec3.create();
    vec3.sub(point, patientPoint, ippVec3);

    let x = vec3.dot(rowCosinesVec3, point) / imagePlane.columnPixelSpacing;
    let y = vec3.dot(colCosinesVec3, point) / imagePlane.rowPixelSpacing;

    return { x, y };
}

/**
 * 获取当前 元素 的svg工具
 * @param {*} svgContainer 
 * @returns 
 */
function _getWidgetNode(svgContainer) {
    let node = svgContainer.querySelector(`.${_instanceId}`);
    if (!node) {
        node = document.createElement("g");
        node.setAttribute("class", _instanceId);
        node.setAttribute(
            "style",
            "position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;"
        );
        svgContainer.appendChild(node);
    }
    return node;
}