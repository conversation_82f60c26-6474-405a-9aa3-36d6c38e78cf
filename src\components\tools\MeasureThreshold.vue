<template>
    <div class="tool-measure-thres ">
        <div class=" c-item-05">
            <div class="c-item-title">
                修改体测量阈值
            </div>
            <div class="container">
                <div class="input-container">
                    肿瘤阈值
                    <template v-if="showedSUVthresType == 1">
                        <el-input-number v-model="showThresValue" :min="1" :max="100" :step="1" :readonly="disabled"
                            :controls="false" :step-strictly="true" size="small" style="width: 45px"
                            @keyup.enter.native="onInputKeyup"></el-input-number>
                        %
                    </template>
                    <template v-if="showedSUVthresType == 2">
                        <el-autocomplete v-model="showThresValue" size="mini" v-floorNumber style="width: 45px"
                            :fetch-suggestions="getOptions" class="input" popper-class="custom-popper" placeholder=""
                            :disabled="disabled" @select="onChangeThresVal"
                            @keyup.enter.native="e => onChangeThresVal(e.target.value)"></el-autocomplete>
                    </template>
                    <span>
                        MAX:{{ MeasureThresMaxVal }}
                    </span>
                </div>
                <div class="slider-container">
                    <div class="slider">
                        <el-slider v-model="thresValue" :step="MeasureThresMaxVal * 0.01"
                            :min="MeasureThresMaxVal * 0.1 || 0" :max="MeasureThresMaxVal * 0.8 || 100"
                            :format-tooltip="v => showedSUVthresType == 1 ? ((thresValue / MeasureThresMaxVal * 100).toFixed()) : Number(v).toFixed(2)"
                            :disabled="disabled"  
                            @input="onSliderInput">
                        </el-slider>
                    </div>
                    <div :class="{ btn: true, disabled: disabled }" @click="debounceChangeVal(-1)">
                        <i class="el-icon-minus"></i>
                    </div>
                    <div :class="{ btn: true, disabled: disabled }" @click="debounceChangeVal(1)">
                        <i class="el-icon-plus"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import event from '$src/event.js'
import { debounce } from 'lodash-es'

export default {
    data() {
        return {
            thresValue: 0.1,
            showThresValue: '0',
            showedSUVthresType: 1,
            MeasureThresMaxVal: 0,
            toolData: {},
            disabled: true
        }
    },
    computed: {
    },
    watch: {
        // disabled(val) {
        //     console.log(val)
        // },
    },
    mounted() {
        //   event.$emit('clearSelectLesionArea') // TODO: 标记不选中时不让点

        event.$on('selectLesionArea', this.selectLesionArea)
        event.$on('clearSelectLesionArea', this.clearSelectLesionArea)
    },
    beforeDestroy() {
        event.$off('selectLesionArea', this.selectLesionArea)
        event.$off('clearSelectLesionArea', this.clearSelectLesionArea)
    },
    methods: {
        clearSelectLesionArea() {
            this.disabled = true
        },
        selectLesionArea(toolData) {
            this.disabled = false
            if (toolData.totalSUVmax > 0) {
                this.toolData = toolData
                this.MeasureThresMaxVal = (toolData.totalSUVmax).toFixed(2) - 0
                this.showedSUVthresType = (toolData.showedSUVthresType)
                // ((this.thresValue / this.MeasureThresMaxVal).toFixed() - 0)
                this.thresValue = toolData.showedSUVthresType == 1 ?
                    ((toolData.totalSUVmax * toolData.showedSUVthresVal / 100).toFixed(2) - 0) : ((+toolData.showedSUVthresVal).toFixed(2) - 0)
                this.showThresValue = toolData.showedSUVthresType == 1 ? toolData.showedSUVthresVal : (+toolData.showedSUVthresVal).toFixed(2)

                // showedSUVthresType
            }
        },
        sliderChange(value) {
            if (this.disabled) return
            this.showThresValue = this.showedSUVthresType == 1 ? ((value / this.MeasureThresMaxVal * 100).toFixed()) : (value).toFixed(2)
            this.onChangeThresVal(value - 0)
        },
        onChangeThresVal(inputVal) {
            let val = inputVal - 0
            if (val.value) val = val.value - 0
            if (this.showedSUVthresType == 1) {
                val = (this.thresValue / this.MeasureThresMaxVal * 100).toFixed() + '%'
            }

            event.$emit('changeLesionArea', this.toolData, val)

        },
        onInputKeyup(e) {
            if (this.disabled) return
            let value = e.target.value - 0
            this.showThresValue = (value).toFixed()
            let emitval = (value).toFixed() + '%'
            event.$emit('changeLesionArea', this.toolData, emitval)
        },
        debounceChangeVal: debounce(function (percent) {
            const val = (this.thresValue + percent * this.MeasureThresMaxVal * 0.01).toFixed(2)
            // const val = (this.thresValue + this.MeasureThresMaxVal * 0.01).toFixed(2)
            this.thresValue = val - 0

            this.showThresValue = this.showedSUVthresType == 1 ? ((this.thresValue / this.MeasureThresMaxVal * 100).toFixed()) : (this.thresValue).toFixed(2)

            this.onChangeThresVal(this.thresValue)
        }, 200),
        getOptions(queryString, cb) {
            cb([
                { value: '2.50', name: '2.50' },
                { value: '3.00', name: '3.00' },
                { value: '4.00', name: '4.00' },
                { value: '5.00', name: '5.00' },
                { value: '6.00', name: '6.00' },
            ]);
        },
        onSliderInput: debounce(function (www) { 
            this.sliderChange(www)
        }, 210),
    },
}
</script>
<style lang="scss" scoped>
.tool-measure-thres {
    position: relative;
    font-size: 12px;

    .container {
        padding: 3px 10px 0;
    }

    .slider-container {
        display: flex;
        vertical-align: middle;
        margin-top: -5px;

        .slider {
            width: 60%;
            margin: 0 10px 0 10px;
        }

        .btn {
            position: relative;
            color: white;
            background: #9abdcd;
            font-size: 18px;
            border-radius: 4px;
            width: 30px;
            line-height: 30px;
            height: 30px;
            margin: 0 3px 0 1px;
            top: 6px;
            left: 3px;
            cursor: pointer;

            &.disabled {
                cursor: default;
                background: #e4e7ed;
                pointer-events: none;
            }
        }
    }
}
</style>