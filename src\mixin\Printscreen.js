import html2canvas from "html2canvas"
import broadcastManager from '$library/broadcast'
import { setWidgetPoint } from '$library/cornerstone/tools/math/getIppByPoint.js';
/**
 * 截屏保存逻辑
 */
export default {
    methods: {
        // 保存截图
        onClickSavePrint(callBack, formRemark = '') {
            let study = ''
            let hostIpPort = ''
            let modality = ''
            let groupId = ''
            let sStudyInstanceUID = ''
            if (this.$options.name === 'ViewMRLayout') {
                // 拖拽重建
                const findSeries = this.viewports.find(item => {
                    return item.studyUID
                })
                study = this.$store.state.seriesMap.get(findSeries.studyUID)

                // 接口地址
                hostIpPort = study.seriesInfo.hostIpPort

                // 获取设备类型
                modality = study.instanceList.get(findSeries.seriesUID).sModality

                // groupId 不知道要那个，就要请求接口的唯一值id
                const requestList = this.$store.state.requestList
                if (requestList.length) {
                    groupId = requestList[requestList.length - 1].key
                }

                sStudyInstanceUID = findSeries.studyUID
            } else if (this.$options.name === 'ViewVtkFuseMpr') {
                // vtk 斜切窗口
                study = this.$store.state.seriesMap.get(this.seriesId)
                // 接口地址
                hostIpPort = study.seriesInfo.hostIpPort
                // 获取设备类型
                modality = study.instanceList.get(this.series.uids.ct || this.series.uids.pet).sModality
                groupId = this.series.uids.ct + 'vtk-mpr' + this.series.uids.pet
                sStudyInstanceUID = this.seriesId
            } else if (this.$options.name === 'ReadDcm') {
                // 阅图窗口
                study = this.$store.state.seriesMap.get(this.seriesId)
                // 接口地址
                hostIpPort = study.seriesInfo.hostIpPort

                modality = this.showViewports[0] ? this.showViewports[0].sModality : 'JPG'
                groupId = this.openIds
                sStudyInstanceUID = this.seriesId

            } else {
                // 正常重建，对比
                study = this.$store.state.seriesMap.get(this.seriesId)

                // 接口地址
                hostIpPort = study.seriesInfo.hostIpPort

                // 获取设备类型
                const generalSeriesModule = cornerstone.metaData.get('generalSeriesModule', this.ctImageIds[0] || this.petImageIds[0]) || { modality: 'JPG' }
                modality = generalSeriesModule.modality

                groupId = this.groupId

                sStudyInstanceUID = this.seriesId
            }

            // 没有图，取消加载状态
            if (!study) {
                this.$message({
                    type: 'info',
                    message: '截屏区域没有图像'
                })
                callBack && callBack()
                return
            }

            const getParams = (canvas) => {
                const dataURLorigin = canvas.toDataURL("image/jpeg", 1);
                let params = {}
                if (window.configs.project.name == 'Report') {
                    params = {
                        sStudyInstanceUID,
                        base64: dataURLorigin.replace('data:image/jpeg;base64,', ''),
                        modality
                    }
                } else {
                    params = {
                        sStudyInstanceUID,
                        caseId: this.$store.state.userInfo.caseId,
                        base64: dataURLorigin.replace('data:image/jpeg;base64,', ''),
                        modality
                    }
                }
                params.info = this.getScreenData()
                params.groupId = groupId
                params.InstanceRemark = formRemark
                return params
            }

            // isMuch 是在阅图中区分，截图 = true ， 在重建中默认 true
            // 阅图中截图区域 dom 不同

            let dom = !this.isMuch ? document.getElementById(this.componentId) : this.$refs.center;
            if (!dom) {
                dom = this.$refs.center;
            }

            const cloneDom = dom.cloneNode(true)
            const cloneDomId = 'clonedom' + +new Date()
            cloneDom.id = cloneDomId
            const w = dom.clientWidth
            const h = dom.clientHeight
            const minSize = Math.min(w, h)
            const domStyle = dom.getAttribute('style') || ''
            cloneDom.style = domStyle + `width:${minSize}px; height:${minSize}px; flex: 0 0 auto; z-index: 99999; position: absolute;top:19999px;left: 19999px;`


            cloneDom.querySelectorAll('[noprint=true]').forEach(e => e.remove())
            cloneDom.querySelectorAll('.layer-tools').forEach(e => e.remove())
            cloneDom.querySelectorAll('.i-print-select').forEach(e => e.remove())
            cloneDom.querySelectorAll('.iconcelianggongju1').forEach(e => e.remove())
            cloneDom.querySelectorAll('.el-icon-lock').forEach(e => e.remove())


            dom.parentNode.appendChild(cloneDom)


            const viewportList = dom.querySelectorAll('.viewportWrapper')
            const cloneViewportList = cloneDom.querySelectorAll('.viewportWrapper')
            viewportList.forEach((viewportEle, index) => {
                const canvasEle = viewportEle.querySelector('canvas')
                const cloneWrapper = cloneViewportList[index]
                const cloneCav = cloneWrapper.querySelector('canvas')
                if(!canvasEle || !cloneCav || !cloneWrapper) return
                const cloneCavText = cloneCav.getContext('2d')
                const viewportEleClass = viewportEle.getAttribute('class')

                let widthDiff = cloneWrapper.clientWidth - canvasEle.width
                let heightDiff = cloneWrapper.clientHeight - canvasEle.height
 
                let remakeOffsetStl = `left: ${widthDiff / 2}px; top: ${heightDiff / 2}px; `
                const remakeStyle = `width:${canvasEle.clientWidth}px; 
                    height:${canvasEle.clientHeight}px; ${remakeOffsetStl}`

                if (/vtkviewport/.test(viewportEleClass)) {

                    widthDiff = cloneWrapper.clientWidth - canvasEle.clientWidth
                    heightDiff = cloneWrapper.clientHeight - canvasEle.clientHeight
                    remakeOffsetStl = `left: ${widthDiff / 2}px; top: ${heightDiff / 2}px; `

                    const vtkSvgStyle = `width:${canvasEle.clientWidth}px; 
                    height:${canvasEle.clientHeight}px; ${remakeOffsetStl}`

                    const svgDom = cloneWrapper.querySelector('svg[id^=vtkSVG]') 
                    if (svgDom) {
                        const svgStyle = svgDom.getAttribute('style') || ''
                        svgDom.style = svgStyle + vtkSvgStyle
                    }
                    cloneCav.style = vtkSvgStyle + 'position: relative; '
                } else {
                    const crossDom = cloneWrapper.querySelector('.mpr-crosshairs')
                    if (crossDom) {
                        const Style = crossDom.getAttribute('style') || ''
                        crossDom.style = Style + remakeStyle
                    }
                    cloneCav.style = remakeStyle + 'position: relative; '
                }

                cloneCavText.drawImage(canvasEle, 0, 0)
            })

            this.$message({
                type: 'success',
                message: '已截图',
                duration: 1300
            })

            setTimeout(() => {
                html2canvas(cloneDom, { scale: 1 }).then((_canvas) => {
                    let canvas = _canvas
                    let size = 1300

                    if (_canvas.width > size) {
                        const miniCav = document.createElement('canvas')
                        const miniCavCtx = miniCav.getContext('2d')
                        miniCav.width = size
                        miniCav.height = size
                        miniCavCtx.drawImage(_canvas, 0, 0, size, size)
                        canvas = miniCav
                    }

                    const params = getParams(canvas)
                    this.$Api.saveScreen(params, hostIpPort).then(res => {
                        const dom = document.querySelector('#' + cloneDomId)
                        dom && dom.remove()
                        if (res.success) {
                            setTimeout(() => {
                                const key = this.$store.state.seriesMap.get(params.sStudyInstanceUID).seriesInfo.key
                                this.$store.state.listenCapture = Object.assign({}, this.$store.state.listenCapture, {
                                    [key]: true
                                })
                                // 多屏更新广播
                                broadcastManager.postMessage(
                                    {
                                        key,
                                        fun: 'updateCapture',
                                    }
                                );
                                // 截图成功，回调
                                callBack && callBack()
                            }, 1000);
                            // this.$message({
                            //     type: 'success',
                            //     message: '保存成功！'
                            // })
                            return;
                        }else {
                            callBack && callBack()
                        }
                        this.$message({
                            type: 'error',
                            message: res.msg
                        })
                    }).catch(() => {
                        callBack && callBack()
                        const dom = document.querySelector('#' + cloneDomId)
                        dom && dom.remove()
                    })
                })
            }, 100);

        },
        // 重新渲染视图
        redoRender(callback) {
            this.$nextTick(() => {
                const myEvent = new Event('resize')
                window.dispatchEvent(myEvent)
            })
            setTimeout(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent);

                if (this.apis) {
                    this.apis.forEach(api => {
                        if (api.svgWidgets.crosshairsWidget) {
                            api.svgWidgets.crosshairsWidget.updateCrosshairForApi(api);
                        }
                    })
                }

                setTimeout(() => {
                    callback && callback()
                }, 160)
            }, 0);
        },
        /**
         * 截图时候图像数据
         * @returns 
         */
        getScreenData() {

            // TODO 拖拽布局逻辑应该不一样
            if (this.$options.name === 'ViewMRLayout' || this.$options.name === 'ViewVtkFuseMpr' || this.$options.name === 'ReadDcm') {
                return JSON.stringify({ data: [] })
            }

            let item = {};

            item.thickness = this.thickness;
            item.layout = { column: this.layoutMatrix.curColumn, row: this.layoutMatrix.curRow };
            item.selectValue = []
            item.data = [];

            if (this.gridLayoutStyle && Object.keys(this.gridLayoutStyle.containerStyle).length) {
                item.containerStyle = this.gridLayoutStyle.containerStyle;
            }

            if (this.gridLayoutStyle && this.gridLayoutStyle.itemStyle.length) {
                item.itemStyle = this.gridLayoutStyle.itemStyle;
            }

            const len = this.viewportElements.length;
            for (let index = 0; index < len; index++) {
                const el = this.getEnabledElement(index);
                const viewport = this.viewports[index]

                // 当前布局相应位置id
                item.selectValue.push(viewport.layoutId)

                if (el) {
                    const viewport = cornerstone.getViewport(el);
                    if (viewport) {
                        const toolData = cornerstoneTools.getToolState(el, 'stack');
                        let stackData;
                        if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0) {
                            stackData = {
                                currentImageIdIndex: 0,
                            };
                        } else {
                            stackData = toolData.data[0];
                        }
                        const toolCrosshairs = cornerstoneTools.getToolState(el, 'NuclearCrosshairs')
                        let patientPoint = ''

                        if (toolCrosshairs && toolCrosshairs.data[0] && toolCrosshairs.data[0].synchronizationContext) {
                            patientPoint = toolCrosshairs.data[0].synchronizationContext.patientPoint
                        }

                        let colormap = viewport.colormap
                        let strColorMap = ''
                        if (colormap) {
                            let value = 'gray';
                            value = typeof colormap == 'string' ? colormap : colormap.getId();

                            strColorMap = value;
                        }

                        item.data.push(
                            {
                                patientPoint,
                                currentImageIdIndex: stackData.currentImageIdIndex,
                                viewport: {
                                    voi: viewport.voi,
                                    colormap: strColorMap,
                                    invert: viewport.invert,
                                    scale: viewport.scale,
                                    translation: viewport.translation
                                }
                            }
                        );
                    } else {
                        item.data.push({})
                    }

                } else {
                    const statusData = {}
                    const api = this.apis[index]
                    if (api) {
                        statusData.viewUp = api.getViewUp()
                        statusData.sliceNormal = api.getSliceNormal()
                        statusData.parallelScale = api.genericRenderWindow.getRenderer().getActiveCamera().getParallelScale()
                    }

                    item.data.push(statusData);
                }
            }
            return JSON.stringify(item)
        },
        /**
         * 选中还原布局
         * @param {*} info 
         */
        async screenToRebuild(info) {
            if (!info.layout || !info.selectValue) {
                return
            }
            // 改变层间隔
            // if (info.thickness != this.thickness) {
            //     await this.onChangeImg(info.thickness, 'thickness');
            // }

            const layoutId = this.viewports.map(item => {
                if (item) {
                    return item.layoutId
                }
            })

            if (info.selectValue.join() != layoutId.join()) {

                const params = {
                    layout: info.layout,
                    selectValue: info.selectValue,
                };

                if (info.containerStyle) {
                    params.containerStyle = info.containerStyle;
                }
                if (info.itemStyle) {
                    params.itemStyle = info.itemStyle;
                }

                this.onClickChangeLayout(params, -1, true);
            }

            // 设置切换，设置 viewport
            setTimeout(() => {

                const len = this.viewportElements.length;
                for (let index = 0; index < len; index++) {
                    const el = this.getEnabledElement(index)

                    const item = info.data[index];
                    if (el) {
                        // cornerstone 显示的
                        if (item.currentImageIdIndex !== undefined) {
                            // 切换滚动
                            cornerstoneTools.scrollToIndex(el, item.currentImageIdIndex);

                            // 设置 viewport
                            const viewport = cornerstone.getViewport(el);
                            if (item.viewport.colormap) {
                                viewport.colormap = item.viewport.colormap;
                            }
                            viewport.invert = item.viewport.invert;
                            viewport.scale = item.viewport.scale; // TODO 浏览器缩放后 canvas 大小，缩放比例也有所变化
                            viewport.translation.x = item.viewport.translation.x;
                            viewport.translation.y = item.viewport.translation.y;
                            viewport.voi.windowCenter = item.viewport.voi.windowCenter;
                            viewport.voi.windowWidth = item.viewport.voi.windowWidth;
                            cornerstone.setViewport(el, viewport);
                        }

                        if (item.patientPoint) {
                            const toolCrosshairs = cornerstoneTools.getToolState(el, 'NuclearCrosshairs')
                            if (toolCrosshairs && toolCrosshairs.data[0] && toolCrosshairs.data[0].synchronizationContext) {
                                toolCrosshairs.data[0].synchronizationContext.patientPoint = item.patientPoint
                            }
                            setWidgetPoint(item.patientPoint, el)
                        }
                    } else {
                        // 其它， 空的，或者 vtk 显示的
                        const api = this.apis[index]
                        if (api) {
                            if (item.viewUp && item.sliceNormal) {
                                api.setOrientation(item.sliceNormal, item.viewUp)

                            }
                            if (item.parallelScale) {
                                api.genericRenderWindow.getRenderer().getActiveCamera().setParallelScale(item.parallelScale)
                            }

                            api.genericRenderWindow.resize();
                        }
                    }
                }
            }, 400); // 不够长，就会被重置了
        },

        async onSaveCropImage(detail) {
            const loading = this.$loading.service({
                target: '#onSaveCropImage',
                text: '处理中...',
                background: 'rgba(60, 62, 66, 0.3)'
            })

            let groupId = ''
            let hostIpPort = ''
            const requestList = this.$store.state.requestList
            if (requestList.length) {
                groupId = requestList[requestList.length - 1].key
            }

            let params = {
                sStudyInstanceUID: this.$store.state.seriesInfo.sStudyInstanceUID,
                base64: detail.dataURLorigin.replace('data:image/jpeg;base64,', ''),
                modality: 'JPG'
            }
            params.info = '{}'
            params.groupId = groupId
            params.InstanceRemark = ''

            hostIpPort = this.$store.state.seriesInfo.hostIpPort || ''

            // console.log(params)

            this.$Api.saveScreen(params, hostIpPort).then(res => {
                // callBack && callBack()
                loading.close()

                if (res.success) {

                    if (this.$store.state.seriesInfo.key && this.$parent.openTabByOpenIds) {
                        setTimeout(() => {
                            this.$parent.openTabByOpenIds(this.$store.state.seriesInfo.key, 1)
                        }, 1000);
                    }

                    this.$message({
                        type: 'success',
                        message: '裁剪图像保存成功！'
                    })

                } else {

                    this.$message({
                        type: 'error',
                        message: res.msg
                    })
                }
            })

            // const dataCanvas = detail.dataCanvas
            // if (!dataCanvas) return

            // // console.log(this.$store.state)

            // const sPatientName = this.$store.state.seriesInfo.sPatientName || ''


            // dataCanvas.toBlob(async blob => {
            //     if ("showSaveFilePicker" in window) {
            //         try {
            //             const handle = await window.showSaveFilePicker({
            //                 suggestedName: sPatientName + new Date().toLocaleString(),
            //                 types: [
            //                     {
            //                         description: "A image/jpeg File",
            //                         accept: { "image/jpeg": [".jpg"] }
            //                     }
            //                 ]
            //             })

            //             const writable = await handle.createWritable()
            //             await writable.write(blob)
            //             await writable.close()
            //         } catch (e) {
            //             console.log(e)
            //         }
            //     } else {

            //         const dataURLorigin = dataCanvas.toDataURL("image/jpeg", 1);

            //         const linkEl = document.createElement('a')
            //         linkEl.href = dataURLorigin;
            //         linkEl.download = sPatientName + new Date().toLocaleString() + '.jpg';
            //         linkEl.click();

            //     }

            // }, 'image/jpeg', 1)


        }
    }
}
