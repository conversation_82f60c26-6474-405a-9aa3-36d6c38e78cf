<template>
  <div class="vtkviewport viewportWrapper" ref="containerParent">
    <div ref="container" class="vtkviewport" />
    <ViewportGroup v-if="contrast" :groupName="groupName" :lock="lock"></ViewportGroup>
    <slot></slot>

    <ViewportOverlayCustom v-if="isOverlayVisible" :imageId="imageId" :modality="modality" :volumes="volumes"
      :renderOverlay="voi" :seriesUID="seriesUID"></ViewportOverlayCustom>
    <!-- <div noprint="true" class="c-select-shade c-select-shade-top"></div>
    <div noprint="true" class="c-select-shade c-select-shade-right"></div>
    <div noprint="true" class="c-select-shade c-select-shade-bottom"></div>
    <div noprint="true" class="c-select-shade c-select-shade-left"></div> -->
  </div>
</template>

<script>
import { isNumber } from "lodash-es";
import vtkGenericRenderWindow from "./vtkGenericRenderWindow";
import vtkRenderer from "vtk.js/Sources/Rendering/Core/Renderer";
import vtkWidgetManager from "vtk.js/Sources/Widgets/Core/WidgetManager";
import vtkInteractorStyleMPRSlice from "./vtkInteractorStyleMPRSlice";
import vtkPaintFilter from "vtk.js/Sources/Filters/General/PaintFilter";
import vtkPaintWidget from "vtk.js/Sources/Widgets/Widgets3D/PaintWidget";
import vtkSVGWidgetManager from "./vtkSVGWidgetManager";

import ViewportOverlayCustom from '$src/components/ViewportOverlayCustom'
import ViewportGroup from "$src/components/ViewportGroup.vue";
import realsApproximatelyEqual from "../lib/math/realsApproximatelyEqual";
import { uuidv4 } from "./../helpers";
import setGlobalOpacity from "./setGlobalOpacity";
import "./viewport.css";
import { globalQueue } from "$library/utils/queue.js";
import getConfigByStorageKey from "$library/utils/configStorage.js";
import VoiMapping from "$library/cornerstone/function/VoiMapping.js";

import "vtk.js/Sources/Rendering/OpenGL/Profiles/All";
import "vtk.js/Sources/Rendering/WebGPU/Profiles/All";
import vtkPlane from 'vtk.js/Sources/Common/DataModel/Plane';
import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';
import vtkColorTransferFunction from '$library/vtk/lib/colorTransferFunction.js';

import customLocalMeta from '$library/cornerstone/mpr/store/customLocalMeta.js';

const throttle = cornerstoneTools.importInternal("util/throttle");

const minSlabThickness = 0.1; // TODO -> Should this be configurable or not?

const radiansToDegrees = 360 / (2.0 * Math.PI);

export default {
  name: "view-2d",
  components: { ViewportGroup, ViewportOverlayCustom },
  props: {
    volumes: { type: Array, required: true },
    actors: { type: Array },
    layoutData: { type: Object },
    onCreated: { type: Function },
    onDestroyed: { type: Function },
    orientation: { type: Object },
    labelmapRenderingOptions: {
      type: Object,
      default() {
        return {
          visible: true,
          renderOutline: true,
          segmentsDefaultProperties: [],
          onNewSegmentationRequested: () => { },
        };
      },
    },
    showRotation: { type: Boolean, default: false },
    isOverlay: {
      type: Boolean,
      default: true,
    },
    scrollRotate: { type: Boolean, default: false },
    lock: {
      type: Boolean,
      default: false,
    },
    contrast: {
      type: Boolean,
      default: false,
    },
    layoutId: {
      type: [String, Number],
      default: "",
    },
    group: {
      type: String,
      default: "",
    },
    imageId: {
      type: String,
      default: "",
    },
    modality: {
      type: String,
      default: "",
    },
    clipZ: {
      default: null
    },
    isOverlayVisible: {     // 是否显示覆盖层（患者信息）
      type: Boolean,
      default: true
    },
    windowIsActive: {
      default: true,
    },
    seriesUID: {
      type: String,
      default: "",
    },
    isFun: {
        type: Boolean,
        default: true
    },
    defaultViewport: {
        type: Object,
        default() {
            return {
            }
        }
    }
  },

  data() {
    return {
      container: {},
      voi: {
        windowCenter: 0,
        windowWidth: 0,
      },
      rotation: { theta: 0, phi: 0 },
      colormapId: 'gray',
      invert: false,
      subs: {},
      groupName: "",
      _unwatchVOI: null,
      genericRenderWindow: null,
      readyToEmitVoiChange: false
    };
  },
  computed: { 
    showRebuild() {
        return this.$store.state.showRebuild
    }
  },

  beforeCreate() { },

  created() {
    this.genericRenderWindow = null;
    this.widgetManager = vtkWidgetManager.newInstance();


    this.interactorStyleSubs = [];

    this.apiProperties = {};


  },

  mounted() {
    // 推入队列，进行节流
    const mountedThrottle = (func) => {
      this.getDefaultVoi().finally(() => {

        globalQueue.push(func);
      })
    };


    mountedThrottle(() => {
      if (this.layoutId) {
        let [group] = String(this.layoutId).split(",");
        let newGroup = parseInt(group);
        if (!isNaN(newGroup)) {
          newGroup += 1;
          this.groupName = newGroup;
        } else {
          this.groupName = group;
        }
      } else {
        this.groupName = "";
      }
      // Tracking ID to tie emitted events to this component
      const uid = uuidv4();

      // this.genericRenderWindow = vtkGenericRenderWindow.newInstance({
      //   background: [0, 0, 0],
      // });
      this.genericRenderWindow = vtkGenericRenderWindow.newInstance({
        background: [0, 0, 0],
      });

      this.container = this.$refs.container;
      this.genericRenderWindow.setContainer(this.container);

      let widgets = [];
      let filters = [];
      let actors = [];
      let volumes = [];

      const radius = 5;
      const label = 1;

      this.renderer = this.genericRenderWindow.getRenderer();
      this.renderWindow = this.genericRenderWindow.getRenderWindow();
      const oglrw = this.genericRenderWindow.getOpenGLRenderWindow();

      // add paint renderer
      // this.paintRenderer = vtkRenderer.newInstance();
      // this.renderWindow.addRenderer(this.paintRenderer);
      // this.renderWindow.setNumberOfLayers(2);
      // this.paintRenderer.setLayer(1);
      // this.paintRenderer.setInteractive(false);

      // update view node tree so that vtkOpenGLHardwareSelector can access
      // the vtkOpenGLRenderer instance.
      oglrw.buildPass(true);

      const istyle = vtkInteractorStyleMPRSlice.newInstance({
        scrollRotate: this.scrollRotate,
      });
      // console.log(istyle)
      this.renderWindow.getInteractor().setInteractorStyle(istyle);

      // const inter = this.renderWindow.getInteractor();
      // const updateCameras = () => {
      //   const baseCamera = this.renderer.getActiveCamera();
      //   const paintCamera = this.paintRenderer.getActiveCamera();

      //   const position = baseCamera.getReferenceByName("position");
      //   const focalPoint = baseCamera.getReferenceByName("focalPoint");
      //   const viewUp = baseCamera.getReferenceByName("viewUp");
      //   const viewAngle = baseCamera.getReferenceByName("viewAngle");

      //   paintCamera.set({
      //     position,
      //     focalPoint,
      //     viewUp,
      //     viewAngle,
      //   });
      // };
      // // TODO unsubscribe from this before component unmounts.
      // inter.onAnimation(updateCameras);
      // updateCameras();

      this.widgetManager.disablePicking();
      // this.widgetManager.setRenderer(this.paintRenderer);
      // this.paintWidget = vtkPaintWidget.newInstance();
      // this.paintWidget.setRadius(radius);
      // this.paintFilter = vtkPaintFilter.newInstance();
      // this.paintFilter.setLabel(label);
      // this.paintFilter.setRadius(radius);

      // trigger pipeline update
      // this.componentDidUpdate({});
      if (this.volumes && this.volumes.length > 0) {
        if (this.volumes.length > 1) {

            let colormap = this.showRebuild.colormap || 'hot'
            if (this.defaultViewport.colormap) {
                if (typeof this.defaultViewport.colormap === 'string') {
                    colormap = this.defaultViewport.colormap;
                } else if (this.defaultViewport.colormap && this.defaultViewport.colormap.getId()) {
                    colormap = this.defaultViewport.colormap.getId();
                }
            }

            this.colormapId = colormap
        }
        this.updateVolumesForRendering(this.volumes);
        this.volumes[0].getMapper().setAutoAdjustSampleDistances(false)

        const rgbTransferFunction = this.volumes[0]
          .getProperty()
          .getRGBTransferFunction(0);

        const range = this.volumes[0].getMapper().getInputData().getPointData().getScalars().getRange()

        rgbTransferFunction.setRange(range[0], range[1]);


        if (this.layoutData && this.layoutData[`mip_${this.layoutId}_data`]) {
          const data = this.layoutData[`mip_${this.layoutId}_data`];
          const { windowCenter, windowWidth } = data;
          if (isNumber(windowWidth) && isNumber(windowCenter)) {
            this.voi = {
              windowCenter,
              windowWidth,
            };

            const { lower, upper } = VoiMapping.toLowHighRange(
              windowWidth,
              windowCenter
            );
            rgbTransferFunction.setMappingRange(lower, upper);
          }
        } else {
          
          // 获取原图
          cornerstone.loadAndCacheImage(this.imageId).then(() => {

            let imageId = this.imageId
            // 是否有修改 suv 参数，有就从设置中读取
            if (customLocalMeta.data[this.seriesUID]) {
                imageId = `mpr:${this.seriesUID}`
                customLocalMeta.setImageIdMetaData(imageId)
            }

            const { windowWidth, windowCenter } = VoiMapping.getVoi(imageId, this.seriesUID);
            const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter)
            rgbTransferFunction.setMappingRange(lower, upper);

            this.voi = { windowWidth, windowCenter }
          })
        }

      }

      // must be added AFTER the data volume is added so that this can be rendered in front
      if (this.labelmap && this.labelmap.actor) {
        this.renderer.addVolume(this.labelmap.actor);
      }

      if (this.actors) {
        actors = actors.concat(this.actors);
      }

      if (this.labelmap && this.labelmap.actor) {
        actors = actors.concat(this.labelmap.actor);
      }

      if (this.volumes) {
        volumes = volumes.concat(this.volumes);
      }

      filters = [];
      widgets = [];

      // Make throttled function for rotation update.
      this.throttledUpdateRotationOverlay = throttle(
        this.updateRotationOverlay,
        1 / 30,
        { trailing: true }
      ); // ~ 30 fps

      // Set orientation based on props
      if (this.orientation) {
        const orientation = this.orientation;
        this.setOrientation(orientation.sliceNormal, orientation.viewUp);
      } else {
        istyle.setSliceNormal(0, 0, 1);
      }

      const camera = this.renderer.getActiveCamera();
      camera.setParallelProjection(true);

      this.renderer.resetCamera();

      istyle.setVolumeActor(this.volumes[0]);
      const range = istyle.getSliceRange();
      istyle.setSlice((range[0] + range[1]) / 2);

      // istyle.onModified(() => {
      //   this.updatePaintbrush();
      // });
      // this.updatePaintbrush();

      const svgWidgetManager = vtkSVGWidgetManager.newInstance();

      svgWidgetManager.setRenderer(this.renderer);
      svgWidgetManager.setScale(1);

      this.svgWidgetManager = svgWidgetManager;

      // TODO: Not sure why this is necessary to force the initial draw
      this.genericRenderWindow.resize();

      this.svgWidgets = {};

      if (this.onCreated) {
        const boundUpdateVOI = this.updateVOI.bind(this);
        const boundGetMaxLabThickness = this.getMaxLabThickness.bind(this);
        const boundGetOrienation = this.getOrientation.bind(this);
        const boundSetOrientation = this.setOrientation.bind(this);
        const boundResetOrientation = this.resetOrientation.bind(this);
        const boundGetViewUp = this.getViewUp.bind(this);
        const boundGetSliceNormal = this.getSliceNormal.bind(this);
        const boundSetInteractorStyle = this.setInteractorStyle.bind(this);
        const boundGetSlabThickness = this.getSlabThickness.bind(this);
        const boundSetSlabThickness = this.setSlabThickness.bind(this);
        const boundAddSVGWidget = this.addSVGWidget.bind(this);
        const boundGetApiProperty = this.getApiProperty.bind(this);
        const boundSetApiProperty = this.setApiProperty.bind(this);
        const boundSetSegmentRGB = this.setSegmentRGB.bind(this);
        const boundSetSegmentRGBA = this.setSegmentRGBA.bind(this);
        const boundSetSegmentAlpha = this.setSegmentAlpha.bind(this);
        const boundUpdateImage = this.updateImage.bind(this);
        const boundSetSegmentVisibility = this.setSegmentVisibility.bind(this);
        const boundSetGlobalOpacity = this.setGlobalOpacity.bind(this);
        const boundSetVisibility = this.setVisibility.bind(this);
        const boundSetOutlineThickness = this.setOutlineThickness.bind(this);
        const boundOutlineRendering = this.setOutlineRendering.bind(this);
        const boundRequestNewSegmentation = this.requestNewSegmentation.bind(
          this
        );

        /**
         * Note: The contents of this Object are
         * considered part of the API contract
         * we make with consumers of this component.
         */
        const api = {
          seriesUID: this.seriesUID,
          getColormapId: () => this.colormapId,
          getInvert: () => this.invert,
          changeMipColormap: this.changeMipColormap,
          changeInvert: this.changeInvert,
          uid, // Tracking id available on `api`
          genericRenderWindow: this.genericRenderWindow,
          widgetManager: this.widgetManager,
          svgWidgetManager: this.svgWidgetManager,
          addSVGWidget: boundAddSVGWidget,
          container: this.container.current,
          widgets,
          svgWidgets: this.svgWidgets,
          filters,
          actors,
          volumes,
          group: this.group,
          imageId: this.imageId,
          modality: this.modality,
          _component: this,
          updateImage: boundUpdateImage,
          updateVOI: boundUpdateVOI,
          getVoi: this.getVoi,
          watchVOI: this.watchVOI,
          unwatchVOI: this.unwatchVOI,
          updateVOIwithoutCB: this.updateVOIwithoutCB,
          getMaxLabThickness: boundGetMaxLabThickness,
          getOrientation: boundGetOrienation,
          setOrientation: boundSetOrientation,
          resetOrientation: boundResetOrientation,
          getViewUp: boundGetViewUp,
          getSliceNormal: boundGetSliceNormal,
          setInteractorStyle: boundSetInteractorStyle,
          getSlabThickness: boundGetSlabThickness,
          setSlabThickness: boundSetSlabThickness,
          setSegmentRGB: boundSetSegmentRGB,
          setSegmentRGBA: boundSetSegmentRGBA,
          setSegmentAlpha: boundSetSegmentAlpha,
          setSegmentVisibility: boundSetSegmentVisibility,
          setGlobalOpacity: boundSetGlobalOpacity,
          setVisibility: boundSetVisibility,
          setOutlineThickness: boundSetOutlineThickness,
          setOutlineRendering: boundOutlineRendering,
          requestNewSegmentation: boundRequestNewSegmentation,
          get: boundGetApiProperty,
          set: boundSetApiProperty,
          type: "VIEW2D",
        };
        // 绑定元素事件
        this.bindInternalElementEventListeners();
        this.onCreated(api);
        this.readyToEmitVoiChange = true
      }
    });

    // 启动队列
    const sampleDistanceConfig = getConfigByStorageKey(
      "configs-imageSampleDistance"
    ); // 1, 3, 5
    const minInterval = 20
    globalQueue.timeInterval = sampleDistanceConfig < 2 ? minInterval : sampleDistanceConfig * 100;
    if (!globalQueue.isRunning) {
      globalQueue.start();
    }
  },
  beforeDestroy() {
    globalQueue.end()
    if (this.onDestroyed) {
      this.onDestroyed();
    }
    this.renderWindow.getInteractor().setInteractorStyle(null);
    this.genericRenderWindow && this.genericRenderWindow.delete();
    this.genericRenderWindow = null;

    // 销毁元素事件
    this.bindInternalElementEventListeners(true);
  },

  methods: {
    async getDefaultVoi() {
      const modality = this.modality // 非必填
      if (modality) {
        const totalData = this.layoutData || {}
        const layoutId = modality === 'CT' ? 6 : 7

        const defaultViewportData = totalData[`${modality}mipViewport`]
        // console.log(defaultViewportData)
        if (defaultViewportData && defaultViewportData.windowWidth && defaultViewportData.windowCenter) {

          let defWW = +defaultViewportData.windowWidth
          let defWC = +defaultViewportData.windowCenter

          if (layoutId == 7) {
            // layoutId == 7 : PT NM  需要从U L 转成WW WC
            await cornerstone.loadAndCacheImage(this.imageId)
            const { w, l } = this.$fun.transformULtoVoiWL(defaultViewportData.windowWidth || 0, defaultViewportData.windowCenter || 0, this.imageId)
            defWW = +w
            defWC = +l
          }

          if (this.layoutData[`mip_${layoutId}_data`]) {
            // 已存在窗宽数据，不能覆盖
          } else {
            this.layoutData[`mip_${layoutId}_data`] = {}
            const data = this.layoutData[`mip_${layoutId}_data`];
            data.windowWidth = defWW
            data.windowCenter = defWC
          }
        }
      }
    },
    bindInternalElementEventListeners(clear = false) {
      const addOrRemoveEventListener = clear
        ? "removeEventListener"
        : "addEventListener";
      const containerParent = this.$refs.containerParent;
      containerParent[addOrRemoveEventListener](
        "mousedown",
        this.setViewportActive,
        true
      );
    },
    // 设置当前选中窗口
    setViewportActive() {
      this.$emit("setViewportActive");
    },
    updatePaintbrush() {
      // const manip = this.paintWidget.getManipulator();
      const handle = this.paintWidget.getWidgetState().getHandle();
      // const camera = this.paintRenderer.getActiveCamera();
      // const normal = camera.getDirectionOfProjection();
      // manip.setNormal(...normal);
      // manip.setOrigin(...camera.getFocalPoint());
      // handle.rotateFromDirections(handle.getDirection(), normal);
      if (typeof handle.rotateFromDirections === "function") {
        const manip = this.paintWidget.getManipulator();
        const camera = this.paintRenderer.getActiveCamera();
        const normal = camera.getDirectionOfProjection();
        manip.setNormal(...normal);
        manip.setOrigin(...camera.getFocalPoint());
        handle.rotateFromDirections(handle.getDirection(), normal);
      }
    },

    getViewUp() {
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();

      return currentIStyle.getViewUp();
    },

    getSliceNormal() {
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();

      return currentIStyle.getSliceNormal();
    },

    setOrientation(sliceNormal, viewUp) {
      // console.log(sliceNormal, viewUp) // 设置选择角度？
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();

      // this.updateRotationRelativeToOrientation(sliceNormal);

      currentIStyle.setSliceOrientation(sliceNormal, viewUp);
    },

    // 重置方向
    resetOrientation() {
      const orientation = this.orientation || {
        sliceNormal: [0, 0, 1],
        viewUp: [0, -1, 0],
      };

      // Reset orientation.
      this.setOrientation(orientation.sliceNormal, orientation.viewUp);

      // Reset slice.
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();
      const range = currentIStyle.getSliceRange();

      currentIStyle.setSlice((range[0] + range[1]) / 2);
    },

    getApiProperty(propertyName) {
      return this.apiProperties[propertyName];
    },

    setApiProperty(propertyName, value) {
      this.apiProperties[propertyName] = value;
    },

    addSVGWidget(widget, name) {
      const { svgWidgetManager } = this;

      svgWidgetManager.addWidget(widget);
      svgWidgetManager.render();

      this.svgWidgets[name] = widget;
    },

    getSlabThickness() {
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();

      if (currentIStyle.getSlabThickness) {
        return currentIStyle.getSlabThickness();
      }
    },

    setSlabThickness(slabThickness) {
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const istyle = renderWindow.getInteractor().getInteractorStyle();

      if (istyle.setSlabThickness) {
        istyle.setSlabThickness(slabThickness);

        if (this.paintFilterLabelMapImageData) {
          const labelmapActor = this.labelmap.actor;

          if (realsApproximatelyEqual(slabThickness, minSlabThickness)) {
            if (
              labelmapActor.getVisibility() !==
              this.labelmapRenderingOptions.visible
            ) {
              labelmapActor.setVisibility(
                this.labelmapRenderingOptions.visible
              );
            }
          } else {
            labelmapActor.setVisibility(false);
          }
        }
      }

      renderWindow.render();
    },

    updateImage() {
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      renderWindow.render();
    },

    setInteractorStyle({ istyle, callbacks = {}, configuration = {} }) {
      const volumes = this.volumes;
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();
      // unsubscribe from previous iStyle's callbacks.
      while (this.interactorStyleSubs.length) {
        this.interactorStyleSubs.pop().unsubscribe();
      }

      let currentViewport;
      if (currentIStyle.getViewport && istyle.getViewport) {
        currentViewport = currentIStyle.getViewport();
      }

      const slabThickness = this.getSlabThickness();
      const interactor = renderWindow.getInteractor();

      interactor.setInteractorStyle(istyle);

      // TODO: Not sure why this is required the second time this function is called
      istyle.setInteractor(interactor);

      if (currentViewport) {
        istyle.setViewport(currentViewport);
      }

      if (istyle.getVolumeActor() !== volumes[0]) {
        if (slabThickness && istyle.setSlabThickness) {
          istyle.setSlabThickness(slabThickness);
        }

        istyle.setVolumeActor(volumes[0]);
      }
      // 可以用来做覆盖层的信息更新用
      // Add appropriate callbacks
      Object.keys(callbacks).forEach((key) => {
        if (typeof istyle[key] === "function") {
          const subscription = istyle[key](callbacks[key]);

          if (subscription && typeof subscription.unsubscribe === "function") {
            this.interactorStyleSubs.push(subscription);
          }
        }
      });

      // Set Configuration
      if (configuration) {
        istyle.set(configuration);
      }

      renderWindow.render();
    },

    getVoi() {
      return this.voi;
    },

    updateVOI(windowWidth, windowCenter) {
      this.voi = { windowWidth, windowCenter };
    },

    watchVOI(callback) {
      this._unwatchVOI = this.$watch("voi", () => callback(this.voi), { deep: true });
    },

    unwatchVOI() {
      this._unwatchVOI && this._unwatchVOI();
    },

    updateRotationOverlay(theta, phi) {
      this.rotation = { theta, phi };
    },

    updateRotationRelativeToOrientation(newNormal) {
      const orientation = this.orientation;
      const { sliceNormal: originalSliceNormal } = orientation;

      // convert to spherical coords;

      // All unit vectors so no reason to calculate r for the speherical coords.

      // Get original offset of normal relative to Z axis.
      let [thetaOriginal, phiOriginal] = [
        Math.acos(originalSliceNormal[2]), // r === 1
        Math.atan2(originalSliceNormal[1], originalSliceNormal[0]),
      ];

      // Get new offset of normal relative to Z axis.
      let [thetaNew, phiNew] = [
        Math.acos(newNormal[2]), // r === 1
        Math.atan2(newNormal[1], newNormal[0]),
      ];

      // Convert to degrees for the UI.
      thetaOriginal *= radiansToDegrees;
      phiOriginal *= radiansToDegrees;
      thetaNew *= radiansToDegrees;
      phiNew *= radiansToDegrees;

      // Get the relative angle to the original orientation.
      let thetaRelative = thetaNew - thetaOriginal;
      let phiRelative = phiNew - phiOriginal;

      // Rescale to the right ranges (0 <= theta <= pi, 0 <= phi < 2*pi)
      if (thetaRelative > 180) {
        thetaRelative -= 180;
      } else if (thetaRelative < 0) {
        thetaRelative = 180 - Math.abs(thetaRelative);
      }

      if (phiRelative >= 360) {
        phiRelative -= 360;
      } else if (phiRelative < 0) {
        phiRelative = 360 - Math.abs(phiRelative);
      }

      this.throttledUpdateRotationOverlay(thetaRelative, phiRelative);
    },

    getOrientation() {
      return this.orientation;
    },

    getMaxLabThickness(index = 0) {
      const dimensions = this.volumes[index]
        .getMapper()
        .getInputData()
        .getDimensions();

      if (dimensions && dimensions.length) {
        return Math.sqrt(
          dimensions[0] * dimensions[0] +
          dimensions[1] * dimensions[1] +
          dimensions[2] * dimensions[2]
        ) * 1.15;
        // return Math.sqrt(
        //   dimensions[0] * dimensions[0] +
        //   dimensions[1] * dimensions[1] +
        //   dimensions[2] * dimensions[2]
        // );
      } else {
        return 360;
      }
    },

    setSegmentRGBA(segmentIndex, [red, green, blue, alpha]) {
      this.setSegmentRGB(segmentIndex, [red, green, blue]);
      this.setSegmentAlpha(segmentIndex, alpha);
    },

    setGlobalOpacity(globalOpacity) {
      const { labelmap } = this;
      const colorLUT = this.labelmapRenderingOptions.colorLUT;
      setGlobalOpacity(labelmap, colorLUT, globalOpacity);
    },

    setVisibility(visible) {
      const { labelmap } = this;
      labelmap.actor.setVisibility(visible);
    },

    setOutlineThickness(outlineThickness) {
      const { labelmap } = this;
      labelmap.actor.getProperty().setLabelOutlineThickness(outlineThickness);
    },

    setOutlineRendering(renderOutline) {
      const { labelmap } = this;
      labelmap.actor.getProperty().setUseLabelOutline(renderOutline);
    },

    requestNewSegmentation() {
      this.labelmapRenderingOptions.onNewSegmentationRequested();
    },

    setSegmentRGB(segmentIndex, [red, green, blue]) {
      const { labelmap } = this;

      labelmap.cfun.addRGBPoint(
        segmentIndex,
        red / 255,
        green / 255,
        blue / 255
      );
    },

    setSegmentVisibility(segmentIndex, isVisible) {
      this.setSegmentAlpha(segmentIndex, isVisible ? 255 : 0);
    },

    setSegmentAlpha(segmentIndex, alpha) {
      const { labelmap } = this;
      let { globalOpacity } = this.labelmapRenderingOptions;

      if (globalOpacity === undefined) {
        globalOpacity = 1.0;
      }

      const segmentOpacity = (alpha / 255) * globalOpacity;

      labelmap.ofun.addPointLong(segmentIndex, segmentOpacity, 0.5, 1.0);
    },

    // getVOI(actor) {
    //   // Note: This controls window/level

    //   // TODO: Make this work reactively with onModified...
    //   const rgbTransferFunction = actor.getProperty().getRGBTransferFunction(0);
    //   const range = rgbTransferFunction.getMappingRange();
    //   const windowWidth = Math.abs(range[1] - range[0]);
    //   const windowCenter = range[0] + windowWidth / 2;

    //   return {
    //     windowCenter,
    //     windowWidth,
    //   };
    // },

    updateVolumesForRendering(volumes) {
      volumes &&
        volumes.forEach((volume, index) => {
          if (!volume.isA("vtkVolume")) {
            console.warn("Data to <Vtk2D> is not vtkVolume data");
          }
          if (index < 1) this.setClip(volume)
          this.renderer.addVolume(volume);
        });
      this.renderWindow.render();
    },

    setClip(volume) {
      // z轴裁剪
      const modality = this.modality // 非必填
      if (modality !== 'PT') return

      if (this.clipZ) {
        const volData = volume.getMapper().getInputData()
        const direction = volData.getDirection() // PT
        const origin = volData.getOrigin()  // PT
        const extentPT = volData.getExtent()  // PT
        const spacingPT = volData.getSpacing()  // PT
        const rotationNormal = [0, 1, 0];


        const clipPlane1 = vtkPlane.newInstance();
        const clipPlane2 = vtkPlane.newInstance();
        const { extent, diff } = this.clipZ
        let clipPlane1Position = origin[2] - diff + extent  // 裁剪位置 = 体的起点 + 偏差值
        let clipPlane2Position = origin[2] - diff
        // console.log(clipPlane1Position, clipPlane2Position)

        const clipPlane1Normal = [direction[6], direction[7], direction[8]];
        const clipPlane2Normal = [direction[6], direction[7], direction[8]];
        const clipPlane1Origin = [
          clipPlane1Position * clipPlane1Normal[0],
          clipPlane1Position * clipPlane1Normal[1],
          clipPlane1Position * clipPlane1Normal[2],
        ];
        const clipPlane2Origin = [
          clipPlane2Position * clipPlane2Normal[0],
          clipPlane2Position * clipPlane2Normal[1],
          clipPlane2Position * clipPlane2Normal[2],
        ];

        vtkMatrixBuilder
          .buildFromDegree()
          .rotate(180, rotationNormal)
          .apply(clipPlane1Normal);

        clipPlane1.setNormal(clipPlane1Normal);
        clipPlane1.setOrigin(clipPlane1Origin);
        clipPlane2.setNormal(clipPlane2Normal);
        clipPlane2.setOrigin(clipPlane2Origin);

        const mapper = volume.getMapper()
        mapper.addClippingPlane(clipPlane1);
        mapper.addClippingPlane(clipPlane2);

      }
    },
    updateVOIwithoutCB(windowWidth, windowCenter) {
      this.readyToEmitVoiChange = false
      this.voi = { windowWidth, windowCenter };
    },
    changeMipColormap(colormapId) {
      // const api = this.vtkApi
      this.colormapId = colormapId

      const colormap = cornerstone.colors.getColormap(colormapId);

      const _colormap = transferOldColorMAP(colormap.getObj ? colormap.getObj() : colormap, this.invert)

      const cfun = vtkColorTransferFunction.newInstance();
      if (_colormap) cfun.applyColorMap(_colormap);

      const { windowCenter,
        windowWidth, } = this.voi
      const { lower, upper } = VoiMapping.toLowHighRange(
        windowWidth,
        windowCenter
      );

      if (windowWidth >= 0 && windowCenter >= 0) {
        cfun.setMappingRange(lower, upper);
      } else {
        // cfun.setMappingRange(0, 99999);
      } 

      this.volumes[0]
        .getProperty()
        .setRGBTransferFunction(0, cfun);

      this.genericRenderWindow.getRenderWindow().render();
    },
    changeInvert(invert) {
      this.invert = invert
      this.genericRenderWindow.setBackground(invert ? [255, 255, 255] : [0, 0, 0])
      this.changeMipColormap(this.colormapId)
    }
  },

  watch: {
    volumes(newVolumes, prevVolumes) {
      if (prevVolumes !== newVolumes) {
        this.updateVolumesForRendering(newVolumes);
      }
    },
    voi: {
      handler({ windowCenter, windowWidth }) {
        if (this.readyToEmitVoiChange) {
          this.$emit('vtkWwwcChange', { windowCenter, windowWidth })
        } else {
          this.readyToEmitVoiChange = true
        }
      },
      deep: true,
      immediate: false
    },
    windowIsActive(val, old) {
      // console.log(old, '->' , val)
      // const renderWindow = this.genericRenderWindow.getRenderWindow(); 
      // const interactor = renderWindow.getInteractor();
        if (val && !old) {
          // interactor.setCurrentRenderer(interactor.getCurrentRenderer());
          // this.genericRenderWindow.setContainer(this.container);
          // this.$destroy();
          const oglrw = this.genericRenderWindow.getOpenGLRenderWindow()
          if (oglrw.getIslostcontext()) {
            this.$emit('refresh');
          }

          // this.updateImage()
          // this.renderer.getRenderWindow().render()
        } else {
          // interactor.setCurrentRenderer(null);
        }
    }
  },

  
};

const _innerColorMap = {}
function transferOldColorMAP(colormapItem, invert = false) {
  const result = {
    ColorSpace: 'RGB',
    Name: 'name',
    RGBPoints: [] // [[0,0,0]]
  };

  if (colormapItem.colors) {
    const _innerCid = colormapItem.name + (invert ? 1 : 0)
    let arr = []

    if (colormapItem.name && _innerColorMap[_innerCid]) {
      arr = _innerColorMap[_innerCid]
    } else {
      const invertColors = invert ? colormapItem.colors.slice(0).reverse() : colormapItem.colors
      invertColors.forEach((entry, index) => {
        arr.push(index / 255)
        arr.push(entry[0] / 255)
        arr.push(entry[1] / 255)
        arr.push(entry[2] / 255)
      })
      _innerColorMap[_innerCid] = arr
    }

    result.RGBPoints = arr
    result.Name = colormapItem.name
    return result
  } else if (colormapItem.segmentedData) {
    return vtkColorMaps.getPresetByName(colormapItem.name);
  } else {
    return null
  }

}
</script>
