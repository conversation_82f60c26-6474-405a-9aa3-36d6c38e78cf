<template>
    <div class="content">
        <div>
             <el-checkbox class="checkbox" v-model="isRemark">备注说明</el-checkbox>
            <div class="box-item box-item-title">
                <div class="item-text">位置</div>
                <div class="item-text">备注说明</div>
            </div>
        </div>
        <el-scrollbar class="screen-file-scrollbar">
            <ul style="overflow: hidden;height: auto;">
                <transition-group name="list-complete">
                    <li class="box-item j-shoping-list" @click="onClickActive(item)" v-for="(item, index) in list" :key="item.sId">
                        <div class="item-text" :title="item.sSopInstanceUid">{{ index + 1 }}</div>
                        <div class="item-text" :title="item.instanceRemark">{{ item.instanceRemark }}</div>
                        <div class="item-action">
                            <span class="i-commit" @click.stop="onClickEdit(item, index)">编辑</span>
                            <span class="i-commit i-delete" @click.stop="onClickDel(item, index)">删除</span>
                        </div>
                    </li>
                </transition-group>

                <div class="edit-popover" v-show="visible">
                    <h6>编辑备注</h6>
                    <el-input size="mini" type="textarea" style="border-radius: 0px;" v-model="form.InstanceRemark" :rows="2" resize="none" ref="editInput"></el-input>
                    <div class="edit-action">
                        <el-button size="mini" @click="visible = false">取消</el-button>
                        <el-button size="mini" type="primary" :loading="loading" @click="onClickSave">保存</el-button>
                    </div>
                </div>
            </ul>
        </el-scrollbar>

        <span v-if="!list.length" class="c-tip">无</span>
    </div>
</template>
<script>
export default {
    props: {
        groupId: {
            type: String,
            default: '',
        }
    },
    data() {
        return {
            loading: false,
            list: [],
            visible: false,
            form: {
                sopInstanceUid: '',
                InstanceRemark: '',
                InstanceRemarkEn: ''
            },
        }
    },
    watch: {
        groupId: {
            handler() {
                this.getData()
            },
            immediate: true
        }
    },
    computed: {
        isRemark: {
            get() {
                return this.$store.state.isRemark
            },
            set(value) {
                this.$store.commit('setRemark', value)

                localStorage.setItem('setting-remark', value);
            }
        }
    },
    methods: {
        // 获取列表
        getData() {
            if (this.groupId && !this.groupId.includes('null')){
                
                this.$Api.findScreenByGroupId({sGroupId: this.groupId}).then(res => {
                    if (res.success) {
                        this.list = res.data
                    }
                })
            }
        },
        // 点击恢复
        onClickActive(item) {
            const info = JSON.parse(item.sInfo)

            const params = {
                thickness: info.thickness,
                layout: info.layout,
                selectValue: info.selectValue,
                data: info.data
            };
            if (info.containerStyle) {
                params.containerStyle = info.containerStyle;
            }
            if (info.itemStyle) {
                params.itemStyle = info.itemStyle;
            }
            this.$emit('screenToRebuild', params);
        },
        // 点击修改备注
        onClickEdit(item, index) {
            this.form.sopInstanceUid = item.sSopInstanceUid
            this.form.InstanceRemark = item.instanceRemark
            this.form.idx = index
            this.visible = true
            this.$nextTick(() => {
                this.$refs.editInput.focus();
            });
        },
        // 删除
        onClickDel(item, index) {
            // 接口应该是用 sSOPInstanceUID、dateTime 进行删除；
            this.$Api.delScreen(
                {
                    iStudyDate: item.iStudyDate,
                    list: [item.sSopInstanceUid]
                }
            ).then(res => {
                if (res.success){
                    this.$message({
                        type: 'success',
                        message: '删除成功!'
                    })
                    this.list.splice(index, 1)
                    // TODO 要告诉阅图更新？
                    return
                }
                this.$message({
                    type: 'error',
                    message: res.msg
                })
            })
        },
        onClickSave() {
            this.loading = true
            this.$Api.editInstanceRemark(this.form).then(res => {
                this.loading = false
                if (res.success) {
                    this.$message({
                        type: 'success',
                        message: res.msg
                    })
                    this.list[this.form.idx].instanceRemark = this.form.InstanceRemark
                    this.visible = false
                    return
                }
                this.$message({
					type: 'error',
					message: res.msg
				})
            }).catch(() => {
				this.$message({
					type: 'error',
					message: '备注失败！'
				})
				this.loading = false
			})
        }
    },
}
</script>
<style lang="scss" scoped>
.content{
    height: 212px;
    width: 100%;
    text-align: left;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}
.screen-file-scrollbar{
    height: 100%;
    width: 100%;
    padding-bottom: 2px;
    ::v-deep .el-scrollbar__wrap{
        overflow-x: hidden;
    }
}
.c-tip{
    position: absolute;
    left: calc(50% - 7px);
    top: 50%;
    color: #c0c0c0;
}
.edit-popover{
    position: absolute;
    top: -1px;
    left: 4px;
    width: 232px;
    background: #f5f9fc;
    padding: 6px 10px;
    box-shadow: 1px 1px 1px #eee;
    border: 1px solid #eee;
    h6{
        font-size: 14px;
        padding-bottom: 5px;
    }
    .edit-action{
        padding-top: 6px;
        text-align: right;
        span:last-child{
            margin-left: 4px;
        }
    }
}
.checkbox{
    height: 36px;
    line-height: 36px;
    padding-left: 4px;
}
.box-item{
    position: relative;
    display: flex;
    height: 30px;
    margin: 0px 8px 0px 4px;
    background: #fff;
    padding: 0px;
    cursor: pointer;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    border-top: none;
    // box-shadow: 1px 1px 1px #eee;
    &:hover{
        background: #f5f9fc;
        .item-action{
            right: 0px;
            transition: all 0.3s;
        }
    }
    &.box-item-title{
        cursor: initial;
        border-top: 1px solid #dcdfe6;
        margin-right: 8px;
    }
    .item-text{
        width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 4px;
        line-height: 22px;
        &:first-child{
            text-align: center;
            width: 60px;
            position: relative;
            &::before{
                content: "";
                position: absolute;
                right: 0px;
                top: 0px;
                height: 100%;
                width: 1px;
                background: #dcdfe6;
            }
        }
        .el-image{
            border-radius: 2px;
        }
        span {
            font-size: 13px;
        }
    }
    .item-action{
        display: flex;
        flex-direction: column;
        flex-direction: row;
        position: absolute;
        right: -96px;
        top: -1px;
        span {
            display: block;
            font-size: 13px;
            line-height: 30px;
            background: white;
            &:hover{
                background: #f5f7fa;
            }
        }
        .i-delete{
            border-top: none;
            padding-top: 1px;
        }
    }
}
.j-shoping-list{
    transition: all 0.4s;
}
.list-complete-leave-to{
    opacity: 0;
    transform: translateX(30px);
}
.list-complete-leave-active {
    position: absolute;
}
</style>