<template>
    <div style="display: inline-block;">
        <div class="c-restore">
            <div class="c-set">视图 <i class="fa el-icon-caret-bottom"></i>
                <ul style="width: 120px">
                    <li @click="onClickFullscreen"><i :class="['el-icon-full-screen']"></i> <span>全屏显示</span></li>
                    <li @click="onClickLeft"><i :class="[hideRragTools ? 'el-icon-close' : 'el-icon-check']"></i> <span>拖拽序列</span></li>
                    <li @click="onClickRight"><i :class="[hideTools ? 'el-icon-close' : 'el-icon-check']"></i> <span>常用工具</span></li>
                    <li @click="onClickLayoutFill"><i :class="[layoutFill ? 'el-icon-close' : 'el-icon-check']"></i> <span>布局为正方形</span></li>
                </ul>
            </div>
            <div class="c-tool">工具 <i class="fa el-icon-caret-bottom"></i>
                <ul>
                    <li @click="onClick2DMPR"><i class="iconfont iconvr"></i> <span>重建 MPR</span></li>
                    <li @click="onClick3DVR"><i class="iconfont iconsanwei"></i> <span>重建 VR</span></li>
                    <li @click="onClickMriLayout"><i class="el-icon-monitor"></i> <span>拖拽重建</span></li>
                    <li @click="onClickContrast"><i class="iconfont iconduibi_o"></i> <span>重建对比</span></li>
                </ul>
            </div>
            <div class="c-set">配置 <i class="fa el-icon-caret-bottom"></i>
                <ul style="width: 120px">
                    <li @click="onClickLayoutSet"><i class="iconfont iconth-large"></i> <span>布局设置</span></li>
                    <li @click="onClickConfigs"><i class="el-icon-setting"></i> <span>系统设置</span></li>
                    <li @click="onClickSetNoCache"><i class="el-icon-refresh"></i> <span>清除图像缓存</span></li>
                </ul>
            </div>
        </div>

        <!-- <ConfigsSet :dialogVisible.sync="configsSetVisible"></ConfigsSet> -->
        <ImageSeries :dialogVisible.sync="openSlectSeries.dialogVisible" :title="openSlectSeries.title" :limitType="openSlectSeries.limitType"/>
        <!-- <SliceContrast :dialogVisible.sync="sliceContrast.dialogVisible"></SliceContrast> -->
        <component :is="lazy.ConfigsSet" :dialogVisible.sync="configsSetVisible"></component>
        <component :is="lazy.SliceContrast" :dialogVisible.sync="rebuildContrastShow"></component>

        <component :is="lazy.ImageLayoutSet" v-model="imageLayoutSet.dialogVisible"></component>
        <component :is="lazy.Shortcut" :visible="shortcutVisible"></component>

        <component :is="lazy.DicomTags" :visible="dicomTagsVisible"></component>
        <component :is="lazy.SuvParams" :visible="suvParamsVisible"></component>

    </div>
</template>
<script>
// import ConfigsSet from '$components/layer/ConfigsSet'
// import SliceContrast from '$components/layer/SliceContrast'
import ImageSeries from '$components/layer/ImageSeries'
const ConfigsSet = () => import('$components/layer/ConfigsSet')
const SliceContrast = () => import('$components/layer/SliceContrast')
const ImageLayoutSet = () => import('$components/layer/ImageLayoutSet')
const Shortcut = () => import('$components/layer/Shortcut')
const DicomTags = () => import('$components/layer/DicomTags')
const SuvParams = () => import('$components/layer/SuvParams')

import { mapState } from 'vuex'
import event from '$src/event.js'
export default {
    data() {
        return {
            configsSetVisible: false,
            sliceContrast: {
                dialogVisible: false
            },
            imageLayoutSet: {
                dialogVisible: false
            },
            lazy: { 
                SliceContrast
             },
            LesionChartDialogVisible: false,
        }
    },
    components: {
        ImageSeries
    },
    watch: {
        triggerLayoutSet(later) {
            if (later) {
                this.onClickLayoutSet();
            }
        },
        titleInfo(later) {
            if (later) {
                let oTitle = document.getElementsByTagName('title')[0];
                oTitle.innerText = oTitle.innerText + '_' + later;
            }
        }
    },
    computed: {
        ...mapState({
            hideRragTools: 'hideRragTools',
            hideTools: 'hideTools',
            openSlectSeries: 'openSlectSeries',
            triggerLayoutSet: 'triggerLayoutSet',
            layoutFill: 'layoutFill',
        }),
        seriesInfo() {
            return this.$store.state.seriesInfo
        },
        titleInfo() {
           return this.seriesInfo.sPatientName
        },
        shortcutVisible() {
            return this.$store.state.shortcutVisible
        },
        dicomTagsVisible() {
            return this.$store.state.dicomTagsVisible
        },
        suvParamsVisible() {
            return this.$store.state.suvParamsVisible
        },
        rebuildContrastShow: {
            get() {
                return this.$store.state.rebuildContrastShow
            },
            set(later) {
                this.$store.state.rebuildContrastShow = later
            }
        }
    },
    watch: {
        shortcutVisible(val) {
            if (val && !this.lazy.Shortcut) {
                this.lazy.Shortcut = Shortcut;
            } 
        },
        dicomTagsVisible(val) {
            if (val && !this.lazy.DicomTags) {
                this.lazy.DicomTags = DicomTags;
            } 
        },
        suvParamsVisible(val) {
            if (val && !this.lazy.SuvParams) {
                this.lazy.SuvParams = SuvParams;
            } 
        },
        rebuildContrastShow: {
            handler(later) {
                if (later) {
                    this.onClickContrast()
                }
            },
            immediate: true
        }
    },
    methods: {
        // 重新渲染视图
        redoRender() {
            this.$nextTick(() => {
                const myEvent = new Event('resize')
                window.dispatchEvent(myEvent)
            })
            setTimeout(() => {
                const myEvent = new Event('resize')
                window.dispatchEvent(myEvent)
            }, 0);
        },
        onClickRight(){
            this.$store.commit('SET_HIDETOOLS', !this.hideTools)
            this.redoRender()
        },
        onClickLeft(){
            this.$store.commit('SET_HIDERAGTOOLS', !this.hideRragTools)
            this.redoRender()
        },
        onClickLayoutFill() {
            this.$store.commit('setLayoutFill', !this.layoutFill)
            localStorage.setItem('configs-layoutFill', this.layoutFill)
            this.redoRender()
        },
        onClickFullscreen(){
            this.$fun.triggerLayoutFullscreen.call(this)
        },
        // 点击配置设置
        onClickConfigs(){
            if (!this.lazy.ConfigsSet) {
                this.lazy.ConfigsSet = ConfigsSet
            }
            this.configsSetVisible = true
        },
        onClickSetNoCache(){
            sessionStorage.setItem('noCacheHash', location.hash)
            this.$message.success('操作成功，请刷新页面')
        },
        onClick3DVR() {
            this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: true, limitType: 6})
        },
        onClick2DMPR() {
            this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: true, limitType: 2})
        },
        // 点击重建对比
        onClickContrast() {
            this.rebuildContrastShow = true
        },
        onClickMriLayout() {
            event.$emit('onSelectUid', '', 'ViewMRLayout')
        },
        onClickLayoutSet() {
            if (!this.lazy.ImageLayoutSet) {
                this.lazy.ImageLayoutSet = ImageLayoutSet;
            }
            this.imageLayoutSet.dialogVisible = true;
        }
    }
}
</script>
<style lang="scss" scoped>
.c-restore{
    // position: absolute;
    // right: 0;
    // top: 0;
    box-sizing: border-box;
    background-color: #547495;
    color: #ebeef5;
    cursor: pointer;
    display: flex;
    > div{
        position: relative;
        width: 68px;
        padding: 0px 10px;
        border-left: 1px solid #7d92b9;
        &:hover{
            // background-color: #a2c2df;
            text-decoration: underline;
            ul {
                display: block;
            }
        }
        &.lesion-btn {
            width: 78px;
        }
    }
    ul{
        display: none;
        padding: 0;
        list-style-type: none;
        z-index: 2;
        background-color: #6087af;
        position: absolute;
        right: 0px;
        border: 1px solid #d7dae1;
        border-right: none;
        width: 100px;
        z-index: 1003;
        li{
            border-bottom: 1px solid #d7dae1;
            padding: 0px 5px;
            &:last-child{
                border-bottom: 1px dashed #d7dae1;
            }
            > i {
                padding-right: 4px;
            }
            &:hover > span{
                // background-color: #D3E6F5;
                text-decoration: underline;
            }
        }
    }
}
</style>