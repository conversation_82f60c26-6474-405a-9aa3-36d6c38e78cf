/* http://meyerweb.com/eric/tools/css/reset/ 
   v2.0 | 20110126
   License: none (public domain)
*/

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
    box-sizing: border-box;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
	box-sizing: content-box;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
html, body, #app{
    height: 100%;
    width: 100%;
	overflow: hidden;
	user-select: none;
	color: #606266;
}

// 100谷歌浏览器按下滚轮出现圆圈
body {
	position: relative;
}




// 布局样式
.iconfont{
	font-size: 14px;
}
.inner-pc-plan{
	height: 100%;
	font-size: 14px;
	display: flex;
	flex-direction: column;
	background-color: #EAEAEA;
	.c-header{
		position: relative;
		width: 100%;
		height: 34px;
		line-height: 34px;
		text-align: left;
		background-color: #b0c8de;
		box-shadow: 0 0px 2px 0px #333;
		padding-left: 20px;
		> span{
			font-size: 16px;
			padding-right: 30px;
		}
		.i-text{
			color: #2283CF;
			font-weight: bold;
		}
	}
	.overlay-10 .i-overlay{
		transform: scale(1);
	}
	.overlay-08 .i-overlay{
		transform: scale(0.8);
	}
	.overlay-07 .i-overlay{
		transform: scale(0.7);
	}
	.overlay-06 .i-overlay{
		transform: scale(0.6);
	}
	.overlay-05 .i-overlay{
		transform: scale(0.5);
	}
	.overlay-04 .i-overlay{
		transform: scale(0.4);
	}
	.overlay-03 .i-overlay{
		transform: scale(0.3);
	}
	.overlay-02 .i-overlay{
		transform: scale(0.2);
	}
	.overlay-01 .i-overlay{
		transform: scale(0.1);
	}
	.c-content{
		flex: 1;
		width: 0px;
		position: relative;
		background-color: black;
		overflow: hidden;
		> div{
			height: 100%;
		}
		.viewportWrapper, .vtk-viewport{
			box-sizing: border-box;
			border: 1px solid #444;
			.c-select-shade{
				position: absolute;
				background: #444;
				z-index: 1;
				&.c-select-shade-top{
					width: 100%;
					height: 0px;
					top: 0;
				}
				&.c-select-shade-right{
					width: 0px;
					height: 100%;
					right: 0px;
					top: 0px;
				}
				&.c-select-shade-bottom{
					width: 100%;
					height: 0px;
					bottom: -1px;
				}
				&.c-select-shade-left{
					width: 0px;
					height: 100%;
					left: 0px;
					top: 0px;
				}
			}

			&.i-active{
				.c-select-shade {
					background: #409eff;
					&.c-select-shade-top{
						height: 3px;
					}
					&.c-select-shade-right{
						width: 3px;
					}
					&.c-select-shade-bottom{
						height: 3px;
					}
					&.c-select-shade-left{
						width: 3px;
					}
				}
			}

			.overlay {
				position: absolute;
				color: #C0C0C0;
			}
			.viewport {
				width: 100%;
				height: 100%;
				top: 0px;
				left: 0px;
				position: absolute;
				overflow: hidden;
				background-color: black;
			}
			.i-overlay{
				position: absolute;
				font-size: 12px;
				color: #afafaf;
				font-weight: bold;
				p{
					padding-bottom: 2px;
					// margin-bottom: 1px;
					&:empty{
						display: none;
					}
				}
				&.black {
					color: #666666;
				}
				&.white {
					color: #d2d2d2;
				}
			}
			.c-slider{
				width: 30%;
				position: absolute;
				bottom: 20px;
				left: 35%;
			}
			.i-top-left{
				text-align: left;
				top: 4px;
				left: 4px;
				transform-origin: top left;
			}
			.i-top-right{
				text-align: right;
				top: 4px;
				right: 4px;
				transform-origin: top right;
			}
			.i-bottom-right{
				text-align: right;
				right: 4px;
				bottom: 0px;
				transform-origin: bottom right;
			}
			.i-bottom-left{
				text-align: left;
				left: 4px;
				bottom: 0px;
				transform-origin: bottom left;
			}
			.i-left-center{
				font-size: 12px;
				color: #C0C0C0;
				top: 50%;
				left: 18px;
				height: 20px;
				margin-top: -10px;
				ul{
					// margin-left: 33px;
					> li{
						float: left;
						height: 4px;
						width: 10px;
						border-bottom: 1px solid #d4ce2f;
						border-left: 1px solid #d9d565;
						box-sizing: border-box;
						&:first-child{
							border-top: 1px solid #d9d565;
						}
					}
				}
			}
			.i-right-center{
				font-size: 14px;
				color: #008000;
				top: 50%;
				right: 4px;
				height: 20px;
				margin-top: -10px;
			}
			.i-top-center{
				font-size: 12px;
				// color: #C0C0C0;
				top: 4px;
				left: 50%;
				width: 20px;
				margin-left: -10px;
			}
			.i-bottom-center{
				font-size: 12px;
				color: #C0C0C0;
				position: relative;
    			bottom: 18px;
				left: 0;
				right: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				height: 18px;
				ul{
					// margin-left: 33px;
					> li{
						float: left;
						height: 4px;
						width: 10px;
						border-bottom: 1px solid #d4ce2f;
						border-left: 1px solid #d9d565;
						box-sizing: border-box;
						&:last-child{
							border-right: 1px solid #d9d565;
						}
					}
				}
				span{
					display: inline-block;
					width: 33px;
					font-size: 12px;
					padding-left: 3px;
					color: #c8c8c8;
					box-sizing: border-box;
					position: absolute;
					bottom: 13px;
				}
				&.black {
					span {
						color: #666666;
					}
				}
				&.white {
					span {
						color: #d2d2d2;
					}
				}
			}
			// .c-progress{
			// 	position: absolute;
			// 	bottom: 0;
			// 	width: 100%;
			// 	background-color: #333;
			// 	height: 8px;
			// 	.el-slider__runway{
			// 		position: absolute;
			// 		bottom: 0px; 
			// 		margin: 0;
			// 		height: 8px;
			// 		background-color: #333;
			// 		width: calc(100% - 16px);
			// 		margin-left: 8px;
			// 		.el-slider__bar{
			// 			background-color: #333;
			// 		}
			// 		.el-slider__button-wrapper{
			// 			height: 8px;
			// 			z-index: 1;
			// 			height: 6px;
			// 			top: -5px;
			// 		}
			// 		.el-slider__button{
			// 			border: none;
			// 			background-color: #606266;
			// 			border-radius: 0px;
			// 			height: 8px;
			// 		}
			// 	}
			// }
		}
	}
	.c-body{
		flex: 1;
		display: flex;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
}

.inner-pc-plan, .c-mpr-content{
	font-size: 14px;
	.c-right{
		display: flex;
    	flex-direction: column;
		width: 308px;
		height: 100%;
		border-left: 1px solid #c1c2c3;
		background: white;
		.inner-container{
			height: 100%;
			// overflow: scroll;
			// overflow-x: hidden;
			// position: absolute;
			// left: 0;
			// top: 28px;
			// right: -17px;
			// bottom: 0;
		}
		.c-item-01{
			position: relative;
			color: #606266;
			height: 28px;
			line-height: 28px;
			background-color: #cddee6;
			border-bottom: 1px solid #c8d0db;
			i{
				position: absolute;
				left: 0px;
				cursor: pointer;
				width: 62px;
				border-right: 1px solid #c8d1dc;
				&:hover{
					color: black;
				}
			}
			span{
				display: block;
				text-align: center;
				margin-left: 62px;
			}
		}
		.c-item-02{
			display: flex;
			flex-wrap: wrap;
			background-color: white;
			border-bottom: 1px solid #D2D2D2;
			padding-top: 8px;
		}
		.c-item-03{
			padding-top: 4px;
			overflow: hidden;
			background-color: white;
			&.i-bottom-line{
				border-bottom: 1px solid #D2D2D2;
			}
			.i-button{
				position: relative;
				box-sizing: border-box;
				float: left;
				// width: 43px;
				min-width: 55px;
				height: 45px;
				color: #142B4B;
				font-size: 12px;
				background-color: #F5F9FC;
				border: 1px solid #E4E7ED;
				border-radius: 3px;
				margin: 0px 0px 4px 4px;
				cursor: pointer;
				overflow: hidden;
				.i-tip{
					position: absolute;
					left: 0px;
					top: 0px;
					font-size: 12px;
				}
				.iconfont{
					font-size: 18px;
					display: block;
					height: 28px;
					line-height: 28px;
					// padding-bottom: 2px;
					// margin-top: 2px;
				}
				&:hover{
					background-color: #D1DBE7;
				}
				&.i-more{
					display: flex;
					align-items: center;
					> div:first-child{
						flex: 1;
					}
					> div:last-child{
						width: 16px;
						text-align: left;
					}
				}
				&.i-active{
					background-color: #9abdcd;
					color: white;
				}
				.i-loading{
					padding: 5px;
					width: 100%;
					height: 100%;
					background-color: #F5F9FC;
					box-sizing: border-box;
					border-radius: 2px;
				}
				.i-mode{
					width: 100%;
					height: 100%;
					background-size: cover;
					box-sizing: border-box;
					height: calc(100% - 4px);
					width: calc(100% - 4px);
					margin: 2px;
				}
			}
			.c-layout-mode{
				display: block;
				width: 100%;
				height: auto;
				overflow: auto;
			}
		}
		.c-item-04{
			display: flex;
			align-items: center;
			box-sizing: border-box;
			height: 38px;
			background-color: #F5F9FC;
			border: 1px solid #E4E7ED;
			border-right: none;
			border-left: none;
			.c-button{
				display: flex;
				align-items: center;
				padding-left: 15px;
				padding-right: 30px;
				.iconstop-circle{
					font-size: 26px;
					width: 32px;
				}
				i{
					font-size: 32px;
					cursor: pointer;
					&:hover{
						color: #546371;
					}
				}
			}
			.c-select{
				width: 100px;
				margin-right: 10px;
			}
		}
		.c-item-05{
			overflow: hidden;
			background-color: white;
			// padding-bottom: 2px;
			padding-right: 0px;
			.c-item-title{
				box-sizing: border-box;
				height: 26px;
				line-height: 24px;
				font-size: 13px;
				text-align: left;
				color: #666;
				padding-left: 10px;
				background-color: #f5f9fc;
				border: 1px solid #E4E7ED;
				border-left: none;
				border-right: none;
			}
			.i-button{
				box-sizing: border-box;
				float: left;
				width: 110px;
				height: 28px;
				line-height: 26px;
				font-size: 13px;
				border: 1px solid #E4E7ED;
				border-radius: 2px;
				margin: 8px 0px 0px 8px;
				cursor: pointer;
				&:hover{
					background-color: #D1DBE7;
				}
				&.i-yes{
					background-color: #6294B7;
					color: white;
					border: 1px solid #6294B7;
					&:hover{
						background-color: #41769b;
						border: 1px solid #6294B7;
					}
				}
			}
		}
	}
}

.i-dropdown{
    box-sizing: border-box;
    width: 110px;
    line-height: 26px;
    border: 1px solid #D2D2D2;
    border-radius: 2px;
    background-color: white;
    margin: 0px 0px 2px 8px;
	text-align: center;
	&.i-gray{
		background: #F5F7FA;
	}
    .el-dropdown-link{
        display: block;
        width: 100%;
        height: 100%;
    }
}

.xx-full-screen {
	position: absolute !important;
	width: 100% !important;
	height: 100% !important;
	z-index: 1002 !important;
}

.xx-full-screen-preload {
	position: absolute !important;
	width: 100% !important;
	height: 100% !important;
	z-index: 1 !important;
	visibility: hidden;
}

.cvp-is-hidden {
	position: absolute !important;
	top: -99999px;
	visibility: hidden;
}

// 覆盖 element-ui
.c-slider .el-slider__button{
    border-radius: 2px;
    border: none;
    background-color: #606266;
    width: 14px;
    height: 14px;
}
.c-slider .el-slider__bar{
    background-color: #909399;
}
.g-window-value.el-dropdown-menu{
    width: auto;
    padding: 5px 0px;
}
.g-window-value .el-dropdown-menu__item{
    display: flex;
    justify-content: space-between;
    padding: 0px 10px;
    align-items: center;
    canvas{
        margin-right: 10px;
    }
}
.g-window-value .el-dropdown-menu__item span:first-child{
    padding-right: 10px;
}
.g-window-value.c-layout .el-dropdown-menu__item span{
    padding-right: 40px;
}
.g-window-value.c-layout .el-dropdown-menu__item i{
    margin-right: 0px;
}
.g-window-value .i-set-p{
    font-size: 14px;
    color: #606266;
    padding-left: 10px;
    height: 36px;
    line-height: 36px;
    position: relative;
    i{
        position: absolute;
        right: 10px;
        height: 36px;
        line-height: 36px;
    }
    cursor: pointer;
    &:hover{
        background-color: #ecf5ff;
        color: #66b1ff;
    }
}


.c-select .el-input--suffix .el-input__inner{
    padding: 0px 15px 0px 5px;
    border-radius: 0px;
}
.c-select .el-input__suffix{
    right: 2px;
    width: 20px;
}
.c-select .el-icon-arrow-up:before{
    content: "\e78f";
}
.c-select .el-input{
    position: relative;
}
.c-select .el-input::before{
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    right: 0;
}
.c-select .el-select .el-input.is-focus .el-input__inner, .c-select .el-select .el-input__inner:focus{
    border-color: #eee;
}

.el-dialog{
    .el-dialog__header{
        font-size: 16px;
        height: 36px;
        line-height: 36px;
        padding: 0;
        border-bottom: 1px solid #c4cfd5;
        background-color: #f5f9fc;
        text-align: left;
        .el-dialog__title{
            font-size: 14px;
            font-weight: 700;
            padding-left: 20px;
            color: #2384d3;
        }
        .el-dialog__headerbtn{
            top: 10px;
        }
    }
    .el-dialog__footer{
        padding: 10px;
        border-top: 1px solid #dcdcdc;
    }
    .el-dialog__body{
		padding: 15px !important;
		background: #fbfbfb;
	}

}

.i-commit{
    display: inline-block;
    box-sizing: border-box;
    line-height: 26px;
    border: 1px solid #D2D2D2;
    border-radius: 2px;
    cursor: pointer;
    padding: 0px 10px;
    font-size: 14px;
	&:hover{
		background-color: #D1DBE7;
	}
}


.el-progress-bar__inner{
	transition: width 0s ease !important;
}
.el-progress-bar{
	padding-right: 0px !important;
	margin-right: 0px !important;
}
.input, .input .el-input{
	font-size: 14px !important;
}
.el-input__inner{
	border-radius: 0px !important;
	padding: 0px 4px !important;
}
.custom-popper{
    width: 80px !important;
}


// Element
html .el-table--mini td, html .el-table--mini th{
	padding: 2px 0px;
	font-weight: bold;
}


.magnifyTool {
	border: 1px #eee solid;
	box-shadow: 2px 2px 10px #1e1e1e;
	display: none;
	cursor: none;
	z-index: 100;
	border-radius: 50%;
}


html .el-table th {
    padding: 0px;
    font-weight: 700;
    background-color: #fafafa;
    font-size: 14px;
    color: #97999b;
    border-bottom: 1px solid #eceff0!important;
}


.xx-popover{
	padding: 6px !important;
	border-radius: 0px !important;
	margin-bottom: 4px !important;
	border: none !important;
}

.overflow-x-hide > .el-scrollbar__wrap{
	overflow-x: hidden !important;
}

.el-scrollbar__view{
	height: 100% !important;
}

.el-input--mini{
	font-size: 14px;
}


.popover-lis--item{
    position: relative;
    height: 26px;
    line-height: 26px;
    background: #f5f7fa;
    padding-left: 24px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: all 0.2s;
    &:hover{
        background: #eee;
    }
    &:last-child{
        margin-bottom: 0px;
    }
    > i {
        position: absolute;
        right: 6px;
        top: 6px;
    }
    &.is-dot{
        &::before{
            content: "";
            display: block;
            width: 6px;
            height: 6px;
            background: black;
            position: absolute;
            left: 10px;
            top: 10px;
            border-radius: 50%;
        }
    }
	&.custom-icon{
		margin-bottom: 0px;
		padding-left: 6px;
		width: 100%;
		&::before{	
			width: 18px;
			display: inline-block;
		}
	}
}


body .v-contextmenu, body .v-contextmenu .v-contextmenu {
    border-color: #3d434d;
    background-color: #3d434d;
	box-shadow: #777 0px 0px 2px 0px;
    border-radius: 0px;
    &-item {
		color: #f0f0f0;
		&:not(.v-contextmenu-submenu){
			margin: 5px 0px;
		}
		&--hover {
			color: #d7dae0 !important;
			background-color: #1e2127;
		}
		
		&--disabled {
		color: rgba(#888, 0.9);
		}
    }
    
    &-divider {
		border-bottom-color: rgba(#142B4B, 0.6);
    }
    
    &-group__title {
		color: rgba(#c8c8c8, 0.7);
    }
}
.el-tabs--card.tabs-base-tools{
	.el-tabs__header {
		background: #f5f9fc;
		margin-bottom: 0px;
		.el-tabs__nav{
			border-radius: 0px;
			border-top: none;
		}
		.el-tabs__item{
			font-size: 13px;
			height: 32px;
			line-height: 32px;
			color: #666;
			padding: 0px 10px;
			&.is-active{
				background: #fff;
				color: #303133;
			}
			&:hover{
				text-decoration: underline;
			}
		}
		.el-tabs__nav-next, .el-tabs__nav-prev{
			line-height: 32px;
			width: 22px;
		}

		.el-tabs__item:nth-child(2){
			padding-left: 10px !important;
		}
		.el-tabs__item:last-child{
			padding-right: 10px !important;
		}
	}
}

.el-popover__title{
	font-size: 14px;
}
.el-textarea__inner{
	font-size: 16px;
}

.coordinate2d-drag-tip {
    background: #cddee6;
    color: #343e4c;
    position: absolute;
    width: 100px;
    height: 20px;
    line-height: 23px;
    font-size: 13px;
    left: 50%;
    margin-left: -50px;
    cursor: pointer;
    border-radius: 0 0 2px 2px;
	&:hover {
		background: #d7f2ff;
	}
}
.dropdown-item {
	display: flex;
	align-items: center;
	&.divided {
		.el-checkbox {
			padding-top: 14px;
		}
	}
	.el-checkbox {
		padding: 0 10px;
	}
	.el-dropdown-menu__item {
		padding: 0 20px 0 10px;
	}
}