/**
 * @param {vtkVolume} volume A vtkVolume object to invert
 * @param {()=>void|{render:()=>void}} [rendering] - A function to render the volume after
 * being inverted or a RenderWindow object. Can be null.
 * @returns void
 */
export default function invertVolume(volume, rendering = null) {
  if (!volume) {
    return;
  }

  const rgbTransferFunction = volume.getProperty().getRGBTransferFunction(0);
  const size = rgbTransferFunction.getSize();
  // size = 2; nodeValue1 0下标 数组 1,2,3下标 为 000 不反片？ 
  for (let index = 0; index < size; index++) {
    const nodeValue1 = [];

    rgbTransferFunction.getNodeValue(index, nodeValue1);

    nodeValue1[1] = 1 - nodeValue1[1];
    nodeValue1[2] = 1 - nodeValue1[2];
    nodeValue1[3] = 1 - nodeValue1[3];
    rgbTransferFunction.setNodeValue(index, nodeValue1);
  }
  if (rendering instanceof Function) {
    let p = []
    rgbTransferFunction.getNodeValue(0, p)
    rendering(_isInvert(p));
  } else if (rendering && rendering.render) {
    rendering.render();
  }
};

function _isInvert(nodeValue){
  if (
    nodeValue[1] === 1 && 
    nodeValue[2] === 1 && 
    nodeValue[3] === 1 
  ) {
    return true;
  }
  return false;
}