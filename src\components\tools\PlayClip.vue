<template>
    <div class="c-item-04">
        <div class="c-button">
            <i class="iconfont iconpre-frame" @click="onClickToggle('prev')" :style="{'color': isMuch ? '' : '#c7c7c7'}"></i>

            <i v-if="player.status === 1" class="iconfont iconplay-1" :style="{'color': isMuch ? '' : '#c7c7c7'}" @click="onClickPlayClip(2)"></i>
            <i v-else-if="player.status === 2" class="iconfont iconstop-circle" @click="onClickPlayClip(1)"></i>
            <i v-else class="iconfont iconplay-1" style="color: #c7c7c7;"></i>
            
            <i class="iconfont iconnext-frame" @click="onClickToggle('next')" :style="{'color': isMuch ? '' : '#c7c7c7'}"></i>
        </div>
        <div class="c-select">
            <el-select v-model="player.speed" size="mini" :disabled="!isMuch">
                <el-option label="默认速度[10]" :value="10"></el-option>
                <el-option label="全速播放[60]" :value="60"></el-option>
                <el-option label="匀速播放[30]" :value="30"></el-option>
                <el-option label="慢速播放[15]" :value="15"></el-option>
                <el-option label="缓速播放[5]" :value="5"></el-option>
                <el-option label="幻灯模式[1]" :value="1"></el-option>
            </el-select>
        </div>
        <!-- <div class="c-select">
            <el-select v-model="player.type" size="mini">
                <el-option label="播放当前序列" value="active"></el-option>
                <el-option label="播放所有影像" value="all"></el-option>
            </el-select>
        </div> -->
    </div>
</template>
<script>

export default {
    props: {
        activeViewportIndex: {
            type: Number,
            default: 0
        },
        viewportElements: {
            type: [Array, HTMLCollection],
            default: () => { [] }
        },
    },
    data() {
        return {
            player: {                   // 播放
                status: 1,              // 1 未播放， 2 播放，3 不能播放
                speed: 10,
                type: 'active'
            },
        }
    },
    computed: {
        isMuch() {
            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) {
                // 没有enable，尝试查找是否是vtk视图
                const dom = this.viewportElements[this.activeViewportIndex];
                if (dom && dom.getElementsByClassName('vtk-viewport')) {
                    return true;
                }
                return false;
            };
            let toolData = cornerstoneTools.getToolState(el, 'stack')  // 获取堆的信息
            if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0){
                return false;
            }
            if (toolData.data[0].imageIds.length === 1) {
                return false;
            }
            return true;
        }
    },
    watch: {
        activeViewportIndex(){
            // 获取当前点击元素的 播放信息
            this.$nextTick(() => {
                const el = this.getEnabledElement(this.activeViewportIndex);
                if (!el) {
                    // 获取不到，当做vtk视窗处理
                    this.$emit('playClip', 'status', 0, (e) => {
                        this.setPlayerStatus(e)
                    });
                    return;
                };
                let toolData = cornerstoneTools.getToolState(el, 'playClip')
                
                // 当前不是播放状态
                if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0 || toolData.data[0].intervalId === undefined){
                    // 当前没有影像
                    // if (isNaN($(element).data('useStack'))) {
                    //     this.player.status =  3 // 不能播放
                    //     return;
                    // }
                    // 存在同步操作
                    if (this.isSync){
                        this.viewportMap((el) => {
                            let toolData = cornerstoneTools.getToolState(el, 'playClip')
                            // 同步操作中有播放中的
                            if (toolData !== undefined && toolData.data !== undefined && toolData.data[0] !== undefined && toolData.data[0].intervalId !== undefined){
                                this.player.status = 2 // 2 播放中
                            }
                        })
                        if (this.player.status === 2){
                            return;
                        }
                    }
                    this.player.status = 1 // 未播放
                    return;
                }
                this.player.status = 2 // 2 播放中
            })
        }
    },
    methods: {
        // 设置播放状态
        setPlayerStatus(e){
            this.player.status = e
        },
		// 从视窗中通过下标获取 enabled 元素
		getEnabledElement(index){
            const dom = this.viewportElements[index];
            if (dom){
                const el = dom.getElementsByClassName('viewport-element')[0];
                return el;
            }
            return null;

		},
		// 元素视图遍历
		viewportMap(callBack){
			for (let index = 0; index < this.viewportElements.length; index++) {
				const dom = this.getEnabledElement(index);
                if (dom) callBack && callBack(dom, index);
			}
		},
        onClickToggle(status){
            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) {
                this.$emit('playClip', status);
                return;
            };

            let toolData = cornerstoneTools.getToolState(el, 'stack')  // 获取堆的信息
            if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0){
                return;
            }
            let index = toolData.data[0].currentImageIdIndex // 下标
            let total = toolData.data[0].imageIds.length     // 总数
            if (status === 'next'){
                index += 1
                if (index >= total){
                    index = 0
                }
            }else {
                if (index === 0){
                    index = total -= 1
                }else {
                    index -= 1
                }
            }
            cornerstoneTools.scrollToIndex(el, index)   // 滚动到对应位置
        },
        // 点击播放
        onClickPlayClip(status){

            const el = this.getEnabledElement(this.activeViewportIndex);
			if (!el) {
                this.$emit('playClip', status, this.player.speed);
                // 改变图标状态
                this.player.status = status;
                return;
            };
            let toolData = cornerstoneTools.getToolState(el, 'stack')  // 获取堆的信息
            if (toolData === undefined || toolData.data === undefined || toolData.data.length === 0){
                return;
            }
            if (toolData.data[0].imageIds.length === 1) {
                return;
            }

            this.player.status = status

            if (status === 2){
                // 需要播放
                if (this.player.type == 'active'){
                    cornerstoneTools.playClip(el, this.player.speed)
                }else {
                    // 全部播放
                    this.viewportMap((el) => {
                        if (el){
                            cornerstoneTools.stopClip(el)
                            cornerstoneTools.playClip(el, this.player.speed)  
                        }

                    })
                }
            }else {
                // 需要暂停
                if (this.player.type == 'active'){
                    // 存在同步操作
                    if (this.isSync){
                        this.viewportMap((el) => {
                            let toolData = cornerstoneTools.getToolState(el, 'playClip')
                            // 同步操作中有播放中的
                            if (toolData !== undefined && toolData.data !== undefined && toolData.data[0] !== undefined && toolData.data[0].intervalId !== undefined){
                                cornerstoneTools.stopClip(el)
                            }
                        })
                        return;
                    }
                    cornerstoneTools.stopClip(el)
                }else {
                    // 全部暂停
                    this.viewportMap((el) => {
                        if (el){
                            cornerstoneTools.stopClip(el)
                        }
                    })
                }
            }
        },
        /**
         * 获取播放状态
         * @param {*} element dom元素
         */
        playerGetStatus(element){
            let playInfo = cornerstoneTools.getToolState(element, 'playClip')
            if (playInfo !== undefined && playInfo.data !== undefined && playInfo.data.length > 0 && playInfo.data[0].intervalId !== undefined){
                return true;
            }
            return false;
        }
    },
}
</script>
<style lang="scss" scoped>
.c-select{
    ::v-deep .el-input{
        font-size: 12px;
    }
}
</style>