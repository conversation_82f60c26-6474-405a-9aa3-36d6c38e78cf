window.configs = {
    imageServe: window.location.host + '/image',   // '*************:19002/image',  // 127.0.0.1:3000/image   *************:29002
    reportServe: window.location.host + '/report', // *************:19002/report
    apricotAssist: 'http://localhost:18153',       // 报告通过接口方式调用本地服务
    systemName: '图像',
    startLimitDownload: false,                     // true 以浏览器最大并发发送请求(浏览器限制向单个服务器最多同时请求[谷歌浏览器 6 个])
    webSort: false,                                // 前端排序，按照 sSOPInstanceUID 排序以 最后一个点 . 的值做排序

    // 成都云
    // project: {
    //     name: 'CDCloud',     // 系统名称
    //     token_expired: [401] // 错误权限码
    // }
    protocol: window.location.protocol,
    useApiDownloadIp: false,   // true = 接口返回ip端口下载图像
    // 报告系统
    project: {
        name: 'Report',
        token_expired: [1008001, 1000002, 1000003]
    }
}