import request from '../request'
import qs from 'qs'
const baseUrl = window.configs.protocol + '//' + window.configs.imageServe
const reportUrl = window.configs.protocol + '//' + window.configs.reportServe

export default {
    findList(data){
        return request({
            url: baseUrl + '/webReadSet/findList',
            method: 'POST',
            data
        })
    },
    addOne(data){
        return request({
            url: baseUrl + '/webReadSet/addOne',
            method: 'POST',
            data
        })
    },
    delOne(params) {
        return request({
            url: baseUrl + '/webReadSet/delOne',
            method: 'POST',
            params
        })
    },
    editOne(data){
        return request({
            url: baseUrl + '/webReadSet/editOne',
            method: 'POST',
            data
        })
    },
}

