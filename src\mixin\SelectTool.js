import event from '$src/event.js'
export default {
    mounted() {
        event.$on('onKeyDel', this._onKeyDel)
    },
    beforeDestroy() {
        event.$off('onKeyDel', this._onKeyDel)
    },
    methods: {
        // 清除当前 viewport 工具选中状态
        clearSelectTool() {
            
            const elements = this.$refs['center'].getElementsByClassName('viewport-element')
            if (!elements) {
                return
            }
            for (let index = 0; index < elements.length; index++) {
                const el = elements[index];
                
                if (!el){
                    continue;
                }

                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const data = allStack.data[0];
                cornerstoneTools.imageIdStateManager.clearSelect(data.state.seriesId, data.imageIds[data.currentImageIdIndex])
                
                const enabledElement = cornerstone.getEnabledElement(el);
                if (enabledElement && enabledElement.image) {
                    cornerstone.updateImage(el);
                }
            }

            event.$emit('clearSelectTool')
        },
        selectTool(toolData) {
            if (toolData.toolName === 'LesionArea') {
                event.$emit('selectLesionArea', toolData)
            }
        },
        /**
         * 
         * @param {*} event     键盘监听返回的 event 事件
         * @param {*} tabId     当前操作的 tab id值
         */
        _onKeyDel(tabId) {
            if (this.tabId === tabId){
                const elements = this.$refs['center'].getElementsByClassName('viewport-element')
                if (!elements) {
                    return
                }

                // 遍历视图
                let isRemove = false; // 做个截断
                for (let index = 0; index < elements.length; index++) {
                    const el = elements[index];
                    if (!el){
                        continue;
                    }
                    
                    // 没有 enable 或者 没有图像
                    const enabledElement = cornerstone.getEnabledElement(el);
                    if (!enabledElement || !enabledElement.image) {
                        continue;
                    }

                    // 已删除，不在处理删除，做更新处理。
                    if (isRemove) {
                        cornerstone.updateImage(el);
                        continue;
                    }

                    let allStack = cornerstoneTools.getToolState(el, 'stack');
                    if (!allStack || !allStack.data.length) { 
                        break;
                    }
                    const data = allStack.data[0];
                    // 获取 element 中所有工具
                    const list = cornerstoneTools.imageIdStateManager.getElementAllTools(el, data.state.seriesId);
                    // console.log(this.viewportElements.length);
                    if (!list || !list.length) {
                        continue;
                    }
                    // 查找是否有选中的工具
                    isRemove = list.find(activeTool => {
                        if (activeTool.toolData.select) {
                            // 移除原生工具状态
                            cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(activeTool.imageId, activeTool.toolName, activeTool.toolData);
                            // 移除在原生基础工具状态
                            cornerstoneTools.imageIdStateManager.delToolState(data.state.seriesId, activeTool.imageId, activeTool.toolName, activeTool.toolData.uuid);
                            // 人为触发工具移除事件（在 cornerstoneViewport.vue 中有监听事件，那里会触发）
                            this._triggerEventRender(el, activeTool);
                            cornerstone.updateImage(el);
                            return true;
                        }
                        return false;
                    })
                }
            }
        },

        // 触发事件
        _triggerEventRender(element, activeTool) {
            const eventType = cornerstoneTools.EVENTS.MEASUREMENT_REMOVED;
            const eventData = {
                element,
                measurementData: activeTool.toolData,
                toolName: activeTool.toolName
            };
            cornerstoneTools.triggerEvent(element, eventType, eventData);
        }
    },
}