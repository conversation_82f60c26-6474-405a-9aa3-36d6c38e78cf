<template>
    <div class="progress">
        <i class="el-icon-loading"></i>
        <!-- <h2>{{imageProgress < 100 ? '加载...' : '加载 -'}} {{ imageProgress }}%</h2> -->
    </div>
</template>

<script>
export default {
    props: {
        imageProgress: {
            type: Number,
            default: 0
        }
    }
}
</script>
<style lang="scss" scoped>
.progress{
    cursor: progress;
    position: relative;
    font-size: 18px;
    height: 0px;
    width: 0px;
    background: rgba(0, 0, 0, 0.75);
    z-index: 1;
    top: 5px;
    left: 5px;
    i {
        color: #409eff;
        font-weight: bold;
    }
    h2 {
        font-size: 12px;
        font-weight: 300;
        position: absolute;
        text-align: center;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        color: rgb(145, 185, 205);
    }
}
</style>