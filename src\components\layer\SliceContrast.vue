<template>
    <el-dialog
        append-to-body
        :title="title"
        :visible.sync="visible"
        width="720px"
        @close="closeDialog"
        @open="openDialog"
        custom-class="my-dialog">
        <div class="c-main layer-contrast" v-if="visible">
            <el-scrollbar class="layer-contrast-scrollbar">
                <div class="user-item" v-for="patient in datalist" :key="patient.patientID">
                    <div class="user-item-left">
                        <p>{{ patient.patientName || '__' }}</p>
                        <p>{{ patient.patientID || '__' }}</p>
                    </div>
                    <div class="user-item-right">
                        <div v-for="series in patient.seriesList" :key="series.studyInstanceUID">
                            <p class="right-header">
                                <span>{{ series.studyDate }}</span>
                                <span>{{ series.studyDescription }}</span>
                            </p>
                            <ul class="right-item">
                                <li v-for="(instance, index) in series.instanceList" :key="index" @click="onClickRowCheck(instance.id)">
                                    <div style="width: 130px;height: 100px">
                                        <CornerstoneViewport style="width: 100%;height: 100%" :isLook="true"
                                        :state="{uid: instance.id, sModality: instance.twoModality, orientation: instance.orientation}"
                                        :isOverlayVisible="false"
                                        :imageIds.sync="instance.twoImageIds"
                                        :layerIds="instance.oneImageIds"
                                        ></CornerstoneViewport>
                                    </div>
                                    <div @click.stop="onClickStop">
                                        <el-checkbox v-model="checked" :label="instance.id">{{ index + 1 }}</el-checkbox>
                                    </div>
                                    <div>
                                        <p>{{ instance.twoModality }}：{{ instance.twoLength }}</p>
                                        <p>{{ instance.oneModality }}：{{ instance.oneLength }}</p>
                                    </div>
                                    <div>
                                        <p>{{ instance.twoDesc }}</p>
                                        <p>{{ instance.oneDesc }}</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <span v-if="!datalist.length" class="vue-tip">无</span>
            </el-scrollbar>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="small" icon="el-icon-picture-outline" @click="onClickSure">确定</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { storePatientPosition } from '$library/cornerstone/function/getImageIds.js';
import CornerstoneViewport from '$components/CornerstoneViewport.vue'
import appState from '$library/cornerstone/mpr/store/appState.js';
import event from '$src/event.js'
export default {
    components: {
        CornerstoneViewport
    },
    props: {
        title: {
            type: String,
            default: '重建对比'
        },
        dialogVisible: {
			type: Boolean,
			default: false
		},
    },
    data() {
        return {
            visible: false,
            checked: [],
            datalist: []
        }
    },
    computed: {
        contrastUId() {
            return this.$store.state.contrastUId
        }
    },
    watch: {
		dialogVisible: {
            handler() {
                this.visible = this.dialogVisible
            },
            immediate: true
        }
    },
    mounted() {
        // setTimeout(() => {
        //     this.transformData()
        // }, 0);
    },
    methods: {
        closeDialog() {
            this.$emit('update:dialogVisible', false);
            this.datalist = []
        },
        openDialog() {
            this.checked = []
            this.transformData()
        },
        async transformData() {
            let allInfo = []
			const loading = this.$loading.service({
				target: '.layer-contrast'
			})
            for (const idStr of this.contrastUId){
                const ids = idStr.split(',')
                await this.loadClearImage(ids[0]); 
                this.$store.state.seriesMap.forEach((series, key) => {
                    const patientItem = {}
                    patientItem.patientName = series.seriesInfo.sPatientName
                    patientItem.patientID = series.seriesInfo.sPatientID
                    
                    patientItem.seriesList = []

                    const instance = {}
                    instance.id = idStr

                    if (series.instanceList.has(ids[0])) {

                        const oneValue = series.instanceList.get(ids[0])
                        const twoValue = series.instanceList.get(ids[1]) || {
                            sModality: '',
                            imageIds: [],
                            sSeriesDescription: ''
                        }
                        const [oneId, twoId, orientation] = this.getImageIds(ids[0], ids[1])

                        if (ids[1]) {
                            // 有两组图，融合的设置
                            instance.oneModality = oneValue.sModality
                            instance.oneLength = oneValue.imageIds.length
                            instance.oneDesc = oneValue.sSeriesDescription
                            instance.oneImageIds = [oneId]

                            instance.twoModality = twoValue.sModality
                            instance.twoLength = twoValue.imageIds.length
                            instance.twoDesc = twoValue.sSeriesDescription
                            instance.twoImageIds = [twoId]
                        }else {
                            // 只有一组图
                            instance.twoModality = oneValue.sModality
                            instance.twoLength = oneValue.imageIds.length
                            instance.twoDesc = oneValue.sSeriesDescription
                            instance.twoImageIds = [oneId]

                            instance.oneImageIds = []
                        }


                        instance.orientation = orientation
                    }

                    if (Object.keys(instance).length <= 1) {
                        return
                    }

                    const seriesData = {
                        studyDate: series.seriesInfo.iStudyDate,
                        studyDescription: series.seriesInfo.sStudyDescription,
                        studyInstanceUID: series.seriesInfo.sStudyInstanceUID,
                        instanceList: [instance]
                    }
                    patientItem.seriesList[0] = seriesData

                    // 是否有这个病人了
                    const patientIdx = allInfo.findIndex(item => {
                        return item.patientID === patientItem.patientID
                    })
                    if (patientIdx === -1){
                        allInfo.push(patientItem)
                    }else {
                        // 有病人
                        const seriesIdx = allInfo[patientIdx].seriesList.findIndex(item => {
                            return item.studyInstanceUID === series.seriesInfo.sStudyInstanceUID
                        })
                        // 没有检查
                        if (seriesIdx === -1) {
                            allInfo[patientIdx].seriesList.push(seriesData)
                        }else {
                            // 有检查了放在序列项中
                            allInfo[patientIdx].seriesList[seriesIdx].instanceList.push(instance)
                        }
                    }
                });

            };
            this.datalist = allInfo.sort((a, b) => {
                const aStudyDate = a.seriesList[0].studyDate + '';
                const bStudyDate = b.seriesList[0].studyDate + '';

                let yyyyB = parseInt(aStudyDate.substring(0, 4), 10);
                let mmB = parseInt(aStudyDate.substring(4, 6), 10);
                let ddB = parseInt(aStudyDate.substring(6, 8), 10);

                let yyyyA = parseInt(bStudyDate.substring(0, 4), 10);
                let mmA = parseInt(bStudyDate.substring(4, 6), 10);
                let ddA = parseInt(bStudyDate.substring(6, 8), 10);

                if (this.$fun.isValidDate(ddA, mmA, yyyyA) && this.$fun.isValidDate(ddB, mmB, yyyyB)) {
                    const tA = new Date(yyyyA,mmA,ddA).getTime();
                    const tB = new Date(yyyyB,mmB,ddB).getTime();
                    
                    return tA > tB ? -1 : tA < tB ? 1 : 0
                }else {
                    return 0
                }
            });
            loading.close()
        },
        async loadClearImage(id) {
            const series  = appState[id].series;
            const noPet = series[Math.ceil(series.length / 2)];

            await cornerstone.loadImage(series[0]);
            await cornerstone.loadImage(series[1]);
            await cornerstone.loadImage(noPet);

            return true
        },
        getImageIds(ctUid, ptUid) {
            // 读取图像截面信息
           const componentId = 'sliceContrast'
           const series  = appState[ctUid].series
           const startMeta = cornerstone.metaData.get('imagePlaneModule', series[0]);
           const endMeta   = cornerstone.metaData.get('imagePlaneModule', series[1]);
           const thickness = Math.abs( startMeta.sliceLocation - endMeta.sliceLocation ) || 2.5
           const sliceType = this.$fun.getNormal(startMeta.imageOrientationPatient, true)

           const patientPosition = cornerstone.metaData.get('patientPosition', series[0]) || {};
           storePatientPosition(patientPosition, ptUid, ctUid, 'axial');

           if (sliceType !== 'axial' || this.$fun.isItalic(startMeta.imageOrientationPatient)) {
                //  非横截面
                const noPet = series[Math.ceil(series.length / 2)]
                const metaData = cornerstone.metaData.get('imagePlaneModule', noPet);

                const idx = ['sagittal', 'coronal', 'axial'].indexOf(sliceType)
                const angle = ['x','y','z'][idx]
                return [
                    noPet,
                    'mpr:'+ ptUid +':' + metaData.imageOrientationPatient.join(',') + ':' + metaData.imagePositionPatient.join(',') + ':clip-special:' + ctUid + '::' + componentId,
                    angle
                ];
           }else {
                //  横截面
                const vtkImageData = appState[ctUid].vtkVolumes.vtkImageData
                const [x0, y0, z0] = vtkImageData.getOrigin();  // x,y,z 原点
                const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
                const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

                let zStart = z0 + zSpacing * (zMax - zMin);  // z最大值

                // 计算中间位置点
                const zNmax = zSpacing * (zMin + zMax)
                const midCount = Math.round((zNmax / thickness) / 2)
                const midPosition = midCount * thickness
                const start = zStart - midPosition

                return [
                    'mpr:'+ ctUid +':1,0,0,0,1,0:0,0,' + start + ': : ::' + componentId,
                    'mpr:'+ ptUid +':1,0,0,0,1,0:0,0,' + start + ':clip:' + ctUid + '::::' + componentId,
                    'z'
                ];
           }
        },
        onClickSure() {
            if (!this.checked.length) {
                this.$message({
                    type: 'warning',
                    message: '请选择'
                })
                return;
            }
            // if (this.checked.length <= 1) {
            //     this.$message({
            //         type: 'warning',
            //         message: '请选中二组'
            //     })
            // }
            this.visible = false
            event.$emit('onSelectUid', this.checked, 'ViewContrast', this.$store.state.seriesInfo.sStudyInstanceUID)
        },
        // 点击其它div行就赋值选中
        onClickRowCheck(id) {
            const idx = this.checked.findIndex(item => {
                return item === id
            })
            if (idx === -1) {
                this.checked.push(id)
            }else {
                this.checked.splice(idx, 1)
            }
        },
        // 停止事件传播
        onClickStop() {}
    },
    beforeDestroy() {
        this.$emit('update:dialogVisible', false);
    }
}
</script>
<style lang="scss" scoped>
.c-main{
    height: 468px;
    border: 1px solid #eee;
}
.layer-contrast-scrollbar{
    height: 100%;
    ::v-deep .el-scrollbar__wrap{
        overflow-x: hidden;
    }
}
.user-item{
    display: flex;
    border-bottom: 1px solid #dcdfe6;
    .user-item-left{
        width: 150px;
        p {
            font-weight: bold;
            color: #388de4;
            padding-left: 10px;
            line-height: 32px;
        }
    }
    .user-item-right{
        flex: 1;
        .right-header {
            height: 32px;
            line-height: 32px;
            background: #727f8e;
            color: white;
            padding-left: 10px;
            span {
                padding-right: 40px;
            }
        }
        .right-item{
            li{
                display: flex;
                align-items: center;
                height: 100px;
                cursor: pointer;
                margin: 1px 0px;
                &:hover{
                    background: #f5f7fa;
                }
                > div{
                    padding-right: 30px;
                }
                p {
                    line-height: 24px;
                }
            }
        }
    }
}
.viewportWrapper, .vtk-viewport{
    position: relative;
    ::v-deep .viewport{
        width: 100%;
        height: 100%;
        top: 0px;
        left: 0px;
        position: absolute;
        overflow: hidden;
        background-color: black;
    }
}
.vue-tip{
    display: block;
    text-align: center;
    height: 100%;
    line-height: 200px;
    font-size: 18px;
    color: #d2d2d2;
}
</style>