<template>
    <div class="print-thumb">
        <header class="header">
            选中：{{ selectLen }} / {{viewportsFilter.length}}
        </header>
        <div class="body">
            <el-scrollbar style="height: 100%" class="overflow-x-hide" ref="scrollDiv">
                <div style="height: 100%">
                    <div v-for="(item, index) in viewportsFilter" ref="imageBox" class="image-box" :class="{'i-select': item.sSOPInstanceUID == selectHighlight, 'i-check': item.checkbox}" @click="onClickSelect(item)">
                        <div class="item-01">
                            <BaseViewport 
                                :key="item.uid"
                                :imageIds.sync="item.imageIds"
                                :iIsGraphic="item.iIsGraphic"
                                :iIsPrinted="item.iIsPrinted"
                                :isShowLen="false"
                                :styleObj="{
                                    width: '116px',
                                    height: '116px'
                                }">
                            </BaseViewport>
                        </div>
                        <div class="item-02">
                            <span style="padding: 8px;">{{ index + 1 }}</span>
                            <div @click="onClickCheckBox(item)">
                                <div class="i-checkbox"></div>
                                <span>选中</span>
                            </div>
                        </div>
                    </div>
                </div>
            </el-scrollbar>
        </div>
        <div class="footer">
            <div class="buttons">
                <span @click="onClickAll(true)">全选</span>
                <span @click="onClickAll(false)">清空</span>
            </div>
            <el-popover
            v-model="visiblePrint"
            placement="top"
            width="565"
            trigger="click">
                <ImagePrintTemplate 
                :patientId="openIds"
                v-model="visiblePrint" 
                @reloadScreen="reloadScreen" 
                :printSelect="printSelect"></ImagePrintTemplate>
                <div class="print" slot="reference"><i class="el-icon-printer"></i></div>
            </el-popover>

        </div>
    </div>
</template>
<script>
// import CornerstoneViewport from '$components/CornerstoneViewport.vue'
import BaseViewport from '$components/tools/components/BaseViewport'
import ImagePrintTemplate from '$components/layer/ImagePrintTemplate'
export default {
    name: 'PrintThumb',
    components: {
        // CornerstoneViewport
        BaseViewport,
        ImagePrintTemplate
    },
    props: {
        viewports: {
            type: Array,
            default: () => []
        },
        openIds: {
			default: ''
		},
        fullScreenUID: {
            type: [Number, String],
            default: ''
        }
    },
    data() {
        return {
            printSelect: [],
            visiblePrint: false,
            selectHighlight: '',
            stopRender: false,
        }
    },
    watch: {
        fullScreenUID(later) {
            if (later) {
                if (this.stopRender) {
                    return
                }
                this.selectHighlight = later

                const idx = this.viewportsFilter.findIndex(item => item.sSOPInstanceUID == later)
                if (idx != -1) {
                    const domWrap = this.$refs.scrollDiv.$refs['wrap']
                    const domCurrent = this.$refs['imageBox'][idx]
                    this.$nextTick(() => {
                        // domWrap.scrollTop = domCurrent.offsetTop

                        domWrap.scrollTo({ top: domCurrent.offsetTop, behavior: 'smooth' });
                    })
                }
            }
        }
    },
    computed: {
        selectLen() {
            const items = this.viewports.filter(item => item.checkbox) || []
            return items.length
        },
        viewportsFilter() {
            const items = this.viewports.filter(item => item.isCaptrue) || []
            return items
        }
    },
    methods: {
        reloadScreen() {
            // this.printSelect = []
			// 确认有截图的
			// this.$emit('reloadScreen')
            this.printSelect.forEach(item => {
                const obj = this.viewports.find(viewport => item.sopInstanceUID == viewport.sSOPInstanceUID)
                if (obj) {
                    obj.iIsGraphic = 1
                    obj.iIsPrinted = 1
                }
            })
		},
        onClickSelect(item) {
            this.selectHighlight = item.sSOPInstanceUID
            this.$emit('selectThumb', this.selectHighlight)
            this.stopRender = true
            setTimeout(() => {
                this.stopRender = false
            }, 200);
        },
        // 点击选择
        onClickCheckBox(item) {
            item.checkbox = !item.checkbox
            if (item.checkbox) {
                // 不是截图，不要
                this.printSelect.push({
                    studyDate: item.iStudyDate + '',
                    sopInstanceUID: item.sSOPInstanceUID,
                    sRemarkCn: item.sRemarkCn
                })
            }else {
                const idx = this.printSelect.findIndex(select => select.sopInstanceUID == item.sSOPInstanceUID)
                if (idx != -1) {
                    this.printSelect.splice(idx, 1)
                }
            }
        },
        onClickAll(status) {
            this.printSelect = []
            this.viewportsFilter.forEach(item => {
                item.checkbox = status
                if (status) {
                    this.printSelect.push({
                        studyDate: item.iStudyDate + '',
                        sopInstanceUID: item.sSOPInstanceUID,
                        sRemarkCn: item.sRemarkCn
                    })
                }
            })
        }
    },
    mounted() {
        this.viewportsFilter.forEach(item => {
            item.checkbox = false
        })

        setTimeout(() => {
            if (this.$refs.scrollDiv) {
                this.$refs.scrollDiv.update()
            }
        }, 2000);
    }
}
</script>
<style lang="scss" scoped>
.print-thumb{
    display: flex;
    flex-direction: column;
    width: 140px;
    ::v-deep .el-scrollbar__thumb {
        background-color: rgba(144,147,153,1);
    }
    .header {
        height: 34px;
        line-height: 34px;
        // width: calc(100% + 16px);
        // position: relative;
        // left: -8px;
        background: #547495;
        color: white;
    }
    .image-box {
        padding: 4px 4px 0;
        background: #f5f7fa;
        margin: 0 8px 10px;
        cursor: pointer;
        .item-01 {
            position: relative;
            background: beige;
            width: 116px;
            height: 116px;
            ::v-deep .viewport{
                width: 100%;
                height: 100%;
                top: 0px;
                left: 0px;
                position: absolute;
                overflow: hidden;
                background-color: black;
            }
        }
        .item-02 {
            display: flex;
            justify-content: space-between;
            > div {
                display: flex;
                align-items: center;
                padding: 4px 6px;
                margin: 2px 0;
            }
        }
        &.i-select {
            background: #409eff;
            color: white;
        }
        &.i-check{
            .item-02 {
                > div {
                    color: white;
                    background: #55d07c;
                }
            }
            .i-checkbox {
                background-color: #55d07c;
                &::after {
                    transform: rotate(45deg) scaleY(1);
                }
            }
        }

    }
    .body {
        width: 100%;
        height: calc(100% - 136px);
        // overflow: hidden;
        margin: 8px 0;
    }
    .footer {
        display: flex;
        height: 86px;
        background: #547495;
        color: white;
        padding-top: 12px;
        .buttons {
            > span {
                display: inline-block;
                width: 50px;
                height: 24px;
                line-height: 24px;
                background: #919191;
                // border: 1px solid #dcdfe6;
                margin: 4px 6px;
                border-radius: 4px;
                cursor: pointer;
                &:hover {
                    opacity: 0.85;
                }
            }
        }
        .print {
            border: 1px solid white;
            width: 60px;
            height: 42px;
            line-height: 48px;
            margin: 10px 15px 10px 5px;
            border-radius: 4px;
            cursor: pointer;
            > i {
                font-size: 24px;
            }
            &:hover {
                opacity: 0.85;
            }
        }
    }

    .i-checkbox{
        position: relative;
        width: 15px;
        height: 15px;
        line-height: 20px;
        border: 1px solid #DCDFE6;
        border-radius: 2px;
        margin-top: -2px;
        margin-right: 4px;
        background: #fff;

        &::after{
            box-sizing: content-box;
            content: "";
            border: 1px solid #FFF;
            border-left: 0;
            border-top: 0;
            width: 4px;
            height: 8px;
            left: 4px;
            position: absolute;
            top: 1px;
            transform: rotate(45deg) scaleY(0);
            transition: transform .15s ease-in .05s;
            transform-origin: center;
        }
    }
}
</style>