<template>
    <div class="remark-parent i-overlay" v-show="sRemarkCn">
        <div :title="sRemarkCn">{{sRemarkCn}}</div>
    </div>
</template>
<script>
export default {
    props: {
        sRemarkCn: {
            type: String,
            default: ''
        }
    }
}
</script>
<style lang="scss" scoped>
.remark-parent{
    position: relative;
    bottom: 0px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    > div{
        font-size: 13px;
        text-align: left;
        color: #142b4b;
        background: #ffffffb3;
        padding: 2px;
        border-radius: 2px 2px 0px 0px;
        line-height: 16px;
        p {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            padding-bottom: 0px !important;
            margin-bottom: 3px !important;
        }
    }
}
</style>