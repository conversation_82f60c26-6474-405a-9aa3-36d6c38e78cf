<template>
    <div class="layout">
        <router-view v-if="visible"></router-view>
        <div v-else class="hint">
            <h2>Oops!</h2>
            <h4>{{ title }}</h4>
        </div>
    </div>
</template>
<script>
import ProjectClass from './js'
export default {
    name: 'Index',
    data() {
        return {
            Auth: Object
        }
    },
    computed: {
        title() {
            return this.Auth.title
        },
        visible() {
            return this.Auth.visible
        }
    },
    mounted() {
        this.Auth = ProjectClass.getInstance()
        this.Auth.authorization()
    }
}
</script>
<style lang="scss" scoped>
.xx-view{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}
.layout{
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.hint{
    width: 200px;
    height: 200px;
    position: absolute;
    left: 50%;
    top: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f9fc;
    border-radius: 20px;
    margin: -100px 0px 0px -100px;
    box-shadow: #3c40434d 0px 1px 2px 0px, #3c404326 0px 2px 6px 2px;
    h2 {
        font-size: 32px;
        padding-bottom: 20px;
    }
    h4 {
        font-size: 24px;
        margin-bottom: 20px;
    }
}
</style>