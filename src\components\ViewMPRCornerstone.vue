<template>
    <CornerstoneViewport style="position: relative;width: 100%;height: 100%;"
        toolType="slice"
        ref="csViewport"
        :isCenterIndex="true"
        :isviewmprcornerstone="true"
        :activeSelect="activeSelect"
        :state="viewportState"
        :imageIds="layer1ImageIds"
        :layerIds="layer2ImageIds"
        :showAction="showAction"
        :activeTool.sync="activeTool"
        :crosshairsTool.sync="crosshairsTool"
        :isOverlayVisible.sync="isOverlayVisible"
        :showCoordinateTools="provideShowToolObj.showCoordinateTools && activeSelect"
        :showRotationTools="provideShowToolObj.showRotationTools && activeSelect"
        @onOffsetChange="onOffsetChange"
        @setCrosshairs="setCrosshairs"
        @clearSelectTool="clearSelectTool"
        @selectTool="selectTool"
        @onToolRenderCompleted="onToolRenderCompleted"
        @setViewportActive="setViewportActive"
        @onNewImage="onNewImage"
        @renderCallBack="renderCallBack">
        </CornerstoneViewport>
</template>
<script>
import setClipPlaneXY from '$library/cornerstone/mpr/getClipPlaneXY.js'
import { 
    getNewImageIds, 
    getVolumeSliceNumber, storePatientPosition } from '$library/cornerstone/function/getImageIds.js'

import CornerstoneViewport from '$components/CornerstoneViewport.vue'

import appState from '$library/cornerstone/mpr/store/appState.js';
export default {
    components: {
        CornerstoneViewport,
    },
    props: {
        layer1series: {
            type: String,
            default: ''
        },
        layer2series: {
            type: String,
            default: ''
        },
        sliceFace: {
            type: String,
            default: 'z'  // z = 横截面 y 冠状位, x 矢状位
        },
        curSeriesChange: {
            type: Boolean,
            default: false   // 融合，退出融合，改变正切 ture
        },
        showAction: {
            type: Boolean,
            default: false
        },
        propActiveTool: {    // 当前工具
            type: String,
            default: 'Wwwc'
        },
        propCrosshairsTool: {
            type: Boolean,
            default: false
        },
        propIsOverlayVisible: {
            type: Boolean,
            default: true
        },
        modalityTypeFake: {
            type: String,
            default: ''
        },
        originalImage: {
            type: Number,
            default: 1  // 1 重建图，2可融合不重建原始图，3 不重建原始图
        },
        patientId: {
            type: String,
            default: ''
        },
        activeSelect: {         // 选中当前组件
            type: Boolean,
            default: false
        },
    },
    inject: ['provideShowToolObj'],
    watch: {
        allSeries: {
            handler() {
                // 不是组件创建时，保存当前切面位置
                if (!this.lifecycle.create) {
                    this.setSliceFaseIndex()
                }
                this.lifecycle.change = true // 图像切面序列改变
                // 监听序列是否有变化，变化就获取新切片
                this.getSliceImageIds()
            },
            immediate: true
        },
        sliceFace: {
            handler() {
                // 改变-显示其它切面

                // 改变切面，保存当前前面位置
                this.setSliceFaseIndex()

                this.lifecycle.change = true // 图像切面序列改变
                this.setShowViewport()
            }
        },
        crosshairsTool: {
            handler() {
                // 最近工具是定位线，就更新图像
                const refCs = this.$refs.csViewport
                if (refCs) {
                    cornerstone.updateImage(refCs.$refs.element)
                }
            }
        }
    },
    computed: {
        allSeries() {
            // 把二组序列号组装一起
            return this.layer1series + ',' + this.layer2series
        },
        activeTool: {
            get: function() {
                return this.propActiveTool
            },
            set: function(val) {
                this.$emit('update:propActiveTool', val)
            }
        },
        crosshairsTool: {
            get: function() {
                return this.propCrosshairsTool
            },
            set: function(val) {
                this.$emit('update:propCrosshairsTool', val)
            }
        },
        isOverlayVisible: {
            get: function() {
                return this.propIsOverlayVisible
            },
            set: function(val) {
                this.$emit('update:propIsOverlayVisible', val)
            }
        }
    },
    data() {
        return {
            originSeries: [ [], [], [], [], [], []
                // 存储的显示数据位置是固定的
                // 0 , 1 y 冠. 2,3 x 矢. 4,5 z 横. 
                // 0,2,4 ct、mr
                // 1,3,5 pt、nm
            ],
            layer1ImageIds: [],
            layer2ImageIds: [],
            viewportState: {},  // 显示图像状态信息
            spacingThick: 2.5,
            sliceLocDistance: 0,// 两张图的切位位置
            layer1Meta: {},     // 存储第一层的一张 meta 信息
            lifecycle: {        // 当前显示生命周期
                change: true,   // 显示改变更新（冠、矢、横、融合变化）
                create: true,   // 初始创建时
                data: {
                    x: -1,
                    y: -1,
                    z: -1
                }
            },
            layerOffset: '0,0,0',
            patientPosition: {}
        }
    },
    methods: {
        // 生成显示的图像
        async getSliceImageIds() {
            this.$emit('update:originalImage', 1) 
            // 参照序列（要第二层，第二层放的是 MR CT，以 MR CT 做参考序列）
            const refeSerie = appState[this.layer2series || this.layer1series].series

            const image = await cornerstone.loadAndCacheImage(refeSerie[0])

            const imageType = image.data.string('x00080008') || ''
            const seriesDesc = image.data.string('x0008103e') || ''

            // 有这个类型，不在重建
            const types = ['WHOLE', 'LOCALIZER']

            // 描述包含 scout 不在重建
            if (types.find(type => imageType.includes(type)) || seriesDesc.includes('scout')) {
                // 不重建、不融合、不改变切面
                // 清空
                for (let index = 0; index < this.originSeries.length; index++) {
                    this.originSeries[index] = []
                }
                
                let meta1 = cornerstone.metaData.get('imagePlaneModule', refeSerie[0])
                const sliceFace = this.getSliceFase(meta1)

                this.$emit('update:sliceFace', sliceFace)
                
                // 按照不同切面放在数组不同位置
                this.originSeries[sliceFace == 'y' ? 0 : sliceFace == 'x' ? 2 : 4] = refeSerie

                this.$emit('update:originalImage', 3) 
                // 设置显示
                this.setShowViewport()

                return
            }

            // 加载
            await cornerstone.loadAndCacheImage(refeSerie[1])
            
            // 获取 tag
            let meta1 = cornerstone.metaData.get('imagePlaneModule', refeSerie[0])
            let meta2 = cornerstone.metaData.get('imagePlaneModule', refeSerie[1])
            let metaGeneralSeriesModule = cornerstone.metaData.get('generalSeriesModule', refeSerie[0])

            this.patientPosition = cornerstone.metaData.get('patientPosition', refeSerie[0]) || {};
            
            // 保存当前参照的 tag 信息
            this.layer1Meta.imagePlaneModule = meta1
            this.layer1Meta.generalSeriesModule = metaGeneralSeriesModule

            // 设置当前切面（x||y||z）
            const sliceFace = this.getSliceFase(meta1)

            // 层间隔
            this.spacingThick = Number((Math.abs( meta1.sliceLocation - meta2.sliceLocation ) || 2.5).toFixed(2))

            this.sliceLocDistance = Math.abs(meta1.sliceLocation - meta2.sliceLocation)
            
            // 创建切片图像id
            await this.createImageIds(this.spacingThick, sliceFace, '0,0,0,0,0,0', meta1.imageOrientationPatient)

            // 向外提交告知当前显示切面
            this.$emit('update:sliceFace', !this.curSeriesChange ? sliceFace : this.sliceFace) 
            
            // 设置显示
            this.setShowViewport()
        },
        // 不重建方式显示
        async notRebuild(sliceFace) {
            this.$emit('update:originalImage', 2) 
            // 不是横截面(MR 才有这种情况)，只需要重建 PT 即可
            let layer1ImageIds = appState[this.layer1series].series
            let layer2ImageIds = []
            // 有第二层，融合才生成图像 id
            if (this.layer2series) {
                // 有第二层，代表是融合的
                layer1ImageIds = appState[this.layer2series].series // MR
                layer2ImageIds = [] // PT
                // 遍历 MR/CT
                layer1ImageIds.forEach((imageId, index) => {
                    const metaData = cornerstone.metaData.get('imagePlaneModule', imageId)
                    // 一个一模一样切面的 PT imageId
                    layer2ImageIds.push('mpr:'+ this.layer1series +':' + metaData.imageOrientationPatient.join(',') + ':' + metaData.imagePositionPatient.join(',') + ':clip-special:' + this.layer2series + (this.sliceLocDistance == 0 ? ':0,0,0:' + index : ''))
                })

                const ptId = layer2ImageIds[0].replace('clip', 'reference')

                await cornerstone.loadAndCacheImage(layer1ImageIds[0])
                await cornerstone.loadAndCacheImage(ptId)

                setClipPlaneXY(layer1ImageIds[0], ptId, this.layer1series + '&' + this.layer2series)
            }
            // 冠状位 y 0 2, x 2,3
            const mapIndex = {x: [2, 3], y: [0, 1], z: [4,5]}

            this.originSeries[mapIndex[sliceFace][0]] = layer1ImageIds // MR/CT
            this.originSeries[mapIndex[sliceFace][1]] = layer2ImageIds // PT/NM
        },
        // 创建切片图像id
        async createImageIds(spacingThick, sliceFace, offset = '0,0,0,0,0,0', iop) {
            // 清空
            for (let index = 0; index < this.originSeries.length; index++) {
                this.originSeries[index] = []
            }
            // 横截面，需要生成三个正切
            if (sliceFace !== 'z' || this.$fun.isItalic(iop)) {
                await this.notRebuild(sliceFace)
            }else {
                // 获取的位置以(CT\MR)为主， (CT\MR)融合的时候在第二层。
                const ctData = await getVolumeSliceNumber(this.layer2series || this.layer1series, spacingThick)
                if (ctData.zCount === 0){
                    await this.notRebuild(sliceFace)
                    return
                }
                // 三个方向类型（冠、矢、横）
                const [ imageIds0, imageIds1 ] = getNewImageIds('coronal', ctData, spacingThick, 
                    this.layer2series || this.layer1series,     //  放 CT、MR
                    this.layer2series ? this.layer1series : '', //  放 PT NM
                    undefined, offset
                    )
                const [ imageIds2, imageIds3 ] = getNewImageIds('sagittal', ctData, spacingThick, 
                    this.layer2series || this.layer1series,     //  放 CT、MR
                    this.layer2series ? this.layer1series : '', //  放 PT NM
                    undefined, offset
                    )
                const [ imageIds4, imageIds5 ] = getNewImageIds('axial', ctData, spacingThick, 
                    this.layer2series || this.layer1series,     //  放 CT、MR
                    this.layer2series ? this.layer1series : '', //  放 PT NM
                    undefined, offset
                    )

                storePatientPosition(this.patientPosition.name, this.layer2series || this.layer1series, this.layer2series ? this.layer1series : '', 'coronal')
                storePatientPosition(this.patientPosition.name, this.layer2series || this.layer1series, this.layer2series ? this.layer1series : '', 'sagittal')
                storePatientPosition(this.patientPosition.name, this.layer2series || this.layer1series, this.layer2series ? this.layer1series : '', 'axial')


                // 融合时，裁剪
                if (this.layer1series && this.layer2series) {
                    // 参照图 id
                    const imageId0 = imageIds0[0] + ':reference'
                    const imageId1 = imageIds1[0].replace('clip', 'reference')

                    const imageId2 = imageIds2[0] + ':reference'
                    const imageId3 = imageIds3[0].replace('clip', 'reference')

                    const imageId4 = imageIds4[0] + ':reference'
                    const imageId5 = imageIds5[0].replace('clip', 'reference')

                    // 加载参照图
                    await cornerstone.loadAndCacheImage(imageId0)
                    await cornerstone.loadAndCacheImage(imageId1)
                    await cornerstone.loadAndCacheImage(imageId2)
                    await cornerstone.loadAndCacheImage(imageId3)
                    await cornerstone.loadAndCacheImage(imageId4)
                    await cornerstone.loadAndCacheImage(imageId5)

                    setClipPlaneXY(imageId0, imageId1, this.layer1series + '&' + this.layer2series);
                    setClipPlaneXY(imageId2, imageId3, this.layer1series + '&' + this.layer2series);
                    setClipPlaneXY(imageId4, imageId5, this.layer1series + '&' + this.layer2series);
                }

                // 存入显示
                this.originSeries[0] = imageIds0
                this.originSeries[1] = imageIds1
                this.originSeries[2] = imageIds2
                this.originSeries[3] = imageIds3
                this.originSeries[4] = imageIds4
                this.originSeries[5] = imageIds5
            }
        },
        // 设置显示图像
        setShowViewport() {
            // TODO 切换切面，是否需要赋原先的窗宽窗位?
            // 设置 viewpoint 状态
            this.setViewportState()

            // 显示 cornerstone 组件
            // layer1ImageIds、layer2ImageIds 为显示的图像id
            // 有第二层，有融合
            if (this.layer2series) {
                switch (this.sliceFace) {
                    case 'z':
                        // 横截面 4,5
                        this.layer1ImageIds = this.originSeries[5]
                        this.layer2ImageIds = this.originSeries[4]
                        break;
                    case 'y':
                        // 冠状位 0,1
                        this.layer1ImageIds = this.originSeries[1]
                        this.layer2ImageIds = this.originSeries[0]
                        break;
                    default:
                        // 矢状位 2,3
                        this.layer1ImageIds = this.originSeries[3]
                        this.layer2ImageIds = this.originSeries[2]
                        break;
                }
            }else {
                switch (this.sliceFace) {
                    case 'z':
                        // 横截面 4,5
                        this.layer1ImageIds = this.originSeries[4]
                        this.layer2ImageIds = []
                        break;
                    case 'y':
                        // 冠状位 0,1
                        this.layer1ImageIds = this.originSeries[0]
                        this.layer2ImageIds = []
                        break;
                    default:
                        // 矢状位 2,3
                        this.layer1ImageIds = this.originSeries[2]
                        this.layer2ImageIds = []
                        break;
                }
            }
        },
        async onOffsetChange(isCT, offset, element) {
            this.lifecycle.change = true;
            this.setSliceFaseIndex();
            this.layerOffset = offset;

            let offsetVal = '';
            if (isCT && this.layer2series) {
                offsetVal = this.layerOffset + ',0,0,0'
            }else {
                offsetVal = '0,0,0,' + this.layerOffset
            }

            await this.createImageIds(this.spacingThick, this.sliceFace, offsetVal);
            // 设置显示
            this.setShowViewport()
        },
        // 设置 viewport 的状态信息
        setViewportState() {
            const metaSeriesModule = this.layer1Meta.generalSeriesModule
            const sModality = metaSeriesModule && ['PT', 'NM'].includes(metaSeriesModule.modality) ? 'PT' : 'CT'

            this.viewportState = {
                uid: this.$fun.onlyValue(),
                sModality,
                orientation: this.sliceFace,
                seriesId: 'mpr:' + this.layer2series + '&&' + this.layer1series + this.spacingThick + this.sliceFace,
                ctUid: this.layer1series,
                petUid: this.layer2series,
                thickness: this.spacingThick,
                patientId: this.patientId,
                imgIndex: this.lifecycle.data[this.$refs.csViewport.state.orientation]
            }
            this.$emit('update:modalityTypeFake', sModality)
        },
        // 获取当前切面 x,y,z
        getSliceFase(imagePlaneModule) {
            if (!imagePlaneModule.imageOrientationPatient) {
                return 'y'
            }
            // 原始序列类型（横、冠、矢）
            const originSerieType = this.$fun.getNormal(imagePlaneModule.imageOrientationPatient, true)
            // 设置当前切面
            let orientation = 'z'

            if (originSerieType === 'axial') {
                orientation = 'z'
            }else if (originSerieType === 'coronal') {
                orientation = 'y'
            }else {
                orientation = 'x'
            }
            return orientation
        },
        setSliceFaseIndex() {
            this.lifecycle.data[this.$refs.csViewport.state.orientation] = this.$refs.csViewport.imageIdIndex
        },
        // cornerstoneViewport 组件的返回方法，在向上传递
        renderCallBack(sourceViewport, clayData, type, isSend = false) {
            this.$emit('renderCallBack', sourceViewport, clayData, type, isSend)
        },
        /**
         * 只要序列图像发生改变时，都会进入该方法
         */
        onNewImage(obj) {
            this.$emit('onNewImage', obj, this.lifecycle)

            this.lifecycle.change = false // 结束
            this.lifecycle.create = false // 结束
        },
        setViewportActive(val) {
            this.$emit('setViewportActive', val)
        },
        clearSelectTool() {
            this.$emit('clearSelectTool')
        },
        selectTool(toolData) {
            this.$emit('selectTool', toolData)
        },
        onToolRenderCompleted(sourceElement, sourceOrientation, status) {
            this.$emit('onToolRenderCompleted', sourceElement, sourceOrientation, status)
        },
        setCrosshairs() {
            this.$emit('setCrosshairs')
        }
    }
}
</script>