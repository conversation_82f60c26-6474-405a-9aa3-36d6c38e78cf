<template>
    <div class="coordinate2dTols-tools">
        <template v-if="!showInput">
            <div class="item">
                <div class="item-btn btn-none btn-drag" :class="{active: dragSwitch, 'no-drag': !fuse}" @click="onClickDrag" title="在融合序列中开启。开启后可拖拽PT/NM调整融合位置">拖拽调整</div>
                <!-- <div class="item-btn btn-none"></div> -->
                <div class="item-btn btn-02" title="上平移" @click="onClickAction('up')"><i class="el-icon-caret-top"></i></div>
            </div>
            <div class="item">
                <div class="item-btn btn-01" title="左平移" @click="onClickAction('left')"><i class="el-icon-caret-left"></i></div>
                <div class="item-btn" title="重置" @click="onClickAction('reset')"><i class="reset-icon"></i></div>
                <div class="item-btn btn-01" title="右平移" @click="onClickAction('right')"><i class="el-icon-caret-right"></i></div>
            </div>
            <div class="item">
                <div class="item-btn btn-none"></div>
                <div class="item-btn btn-02" title="下平移" @click="onClickAction('bottom')"><i class="el-icon-caret-bottom"></i></div>
            </div>
        </template>
        <template v-else>
            <div class="input-container">
                <div class="input-box">
                    <span>x</span><el-input-number
                        class="input" 
                        size="mini" 
                        controls-position="right"
                        @change="onClickAction('inputLeft')" 
                        v-model="formData.x"></el-input-number>
                </div>
                <div class="input-box">
                    <span>y</span><el-input-number
                        class="input" 
                        size="mini" 
                        controls-position="right"
                        @change="onClickAction('inputRight')" 
                        v-model="formData.y"></el-input-number>
                </div>
            </div>
        </template>
        
    </div>
</template>
<script>
export default {
    props: {
        element: {
            type: [Array, HTMLCollection, HTMLDivElement],
            default: () => { [] }
        },
        imageId: {
            default: null
        },
        offsetKey: '',
        showInput: false,
        showState: false
    },
    data() {
        return {
            formData: {
                x: 0,
                y: 0
            },
            step: 0.4,
            dragSwitch: false,
            fuse: false,
            baseOffsetValue: { x: 0, y: 0 },
            baseScale: 1,
            isDrag: false,
        }
    },
    watch: {
        showInput() {
            this.formData = cornerstone.translationMap[this.offsetKey] || {x: 0, y: 0};
        },
        showState() {
            const layers = cornerstone.getLayers(this.element);
            this.fuse = !!layers.length;

            // 显示后，还显示 input 。获取一次数据
            if (this.showState && this.showInput) {
                this.formData = cornerstone.translationMap[this.offsetKey] || {x: 0, y: 0};
            }

            // 关闭的时候，如果开启拖拽就调用点击拖拽事件方法
            if (!this.showState && this.dragSwitch) {
                this.onClickDrag()
            }
        }
    },
    mounted() {
        // 切换其它布局，在回去后，showstate 一直 true
        if (this.showState) {
            setTimeout(() => {
                if (this.element) {
                    const layers = cornerstone.getLayers(this.element)
                    this.fuse = !!layers.length
                }
            }, 1500)
        }
    },
    methods: {
        syncSetTranslation(translation, reset) {
            const [scheme, seriesNumber, imageOrientationPatient, fuseSeriesNumber] = this.offsetKey.split('+');

            // 冠、矢、横
            const iops = ['1,0,0,0,0,-1', '0,1,0,0,0,-1', '1,0,0,0,1,0']

            const yId = `${scheme}+${seriesNumber}+${iops[0]}+${fuseSeriesNumber}`
            const xId = `${scheme}+${seriesNumber}+${iops[1]}+${fuseSeriesNumber}`
            const zId = `${scheme}+${seriesNumber}+${iops[2]}+${fuseSeriesNumber}`

            if (imageOrientationPatient === iops[2]) {
                // 横截面

                const yTranslation = cornerstone.translationMap[yId] || {x: 0, y: 0};
                yTranslation.x = translation.x

                const xTranslation = cornerstone.translationMap[xId] || {x: 0, y: 0};
                xTranslation.x = translation.y

                cornerstone.translationMap[yId] = yTranslation
                cornerstone.translationMap[xId] = xTranslation

            }else if (imageOrientationPatient === iops[1]) {
                const zTranslation = cornerstone.translationMap[zId] || {x: 0, y: 0};
                zTranslation.y = translation.x
                cornerstone.translationMap[zId] = zTranslation
            }else if (imageOrientationPatient === iops[0]) {
                const zTranslation = cornerstone.translationMap[zId] || {x: 0, y: 0};
                zTranslation.x = translation.x
                cornerstone.translationMap[zId] = zTranslation
            }
            
            if (reset === 'reset') {
                cornerstone.translationMap[yId] = {x: 0, y: 0}
                cornerstone.translationMap[xId] = {x: 0, y: 0}
                cornerstone.translationMap[zId] = {x: 0, y: 0}
            }
            cornerstone.translationMap[this.offsetKey] = translation

            // 触发渲染
            this.triggerRender()
        },
        // 给父组件调用
        renderCallBack() {
            if (this.dragSwitch) {
                this.isDrag = true
                // 
                const viewport = cornerstone.getViewport(this.element);

                const offsetTranslation = viewport.offsetTranslation || { x: 0, y: 0 };
                const translation = viewport.translation;

                let newTransition = {
                    x: offsetTranslation.x + translation.x,
                    y: offsetTranslation.y + translation.y
                }

                viewport.translation = {x: 0, y: 0}

                cornerstone.setViewport(this.element, viewport);
                // 更新保存的偏移值
                this.syncSetTranslation(newTransition);
            }

        },
        onClickDrag() {
            if (!this.fuse) {
                return;
            }
            this.dragSwitch = !this.dragSwitch;

            const parentDom = this.$parent.$refs.curDom.parentNode;
            const enabledElement = cornerstone.getEnabledElement(this.element);

            if (this.dragSwitch) {
                this.isDrag = false
                enabledElement.syncViewports = false;
                this.$parent.$parent.$emit('update:activeTool', 'Pan');

                const div  = document.createElement('div');
                const text = document.createTextNode('退出融合调整');
                const attr = document.createAttribute('class');
                attr.value = 'coordinate2d-drag-tip';

                div.setAttributeNode(attr);
                div.appendChild(text);
                div.addEventListener('click', this.onClickDrag);

                parentDom.appendChild(div);

                // 记录下一开始就移动的位置
                this.baseOffsetValue.x = enabledElement.viewport.translation.x
                this.baseOffsetValue.y = enabledElement.viewport.translation.y

                this.baseScale = enabledElement.viewport.scale
            }else {
                enabledElement.syncViewports = true;
                const dom = parentDom.querySelector('.coordinate2d-drag-tip');
                if (dom) {
                    dom.removeEventListener('click', this.onClickDrag);
                    dom.remove();

                    if (this.isDrag) {
                        // 还原一开始就移动的位置
                        const viewport = cornerstone.getViewport(this.element)
                        const offsetTranslation = viewport.offsetTranslation || { x: 0, y: 0 }
                        viewport.translation.x = this.baseOffsetValue.x
                        viewport.translation.y = this.baseOffsetValue.y
                        let newTransition = {
                            x: offsetTranslation.x - this.baseOffsetValue.x,
                            y: offsetTranslation.y - this.baseOffsetValue.y
                        }
                        if (viewport.scale != this.baseScale) {
                            viewport.scale = this.baseScale
                        }
                        cornerstone.setViewport(this.element, viewport)
                        // 更新保存的偏移值
                        this.syncSetTranslation(newTransition);

                        // // 清除原始移动值
                        // this.baseOffsetValue.x = 0
                        // this.baseOffsetValue.y = 0

                        this.$parent.storeData()

                        // 更新
                        setTimeout(() => {
                            this.$parent.$parent.syncCallBack('relative')
                        }, 100)
                    }
                }
            }
        },
        onClickAction(status) {
            let translation = cornerstone.translationMap[this.offsetKey] || {x: 0, y: 0};
            if (status === 'up') {
                translation.y -= this.step;
            }else if (status === 'right') {
                translation.x += this.step;
            }else if (status === 'bottom') {
                translation.y += this.step;
            }else if (status === 'left'){
                translation.x -= this.step;
            }else if (status === 'inputLeft') {
                translation.x = this.formData.x
            }else if (status === 'inputRight'){
                translation.y = this.formData.y
            }else {
                translation.x = 0;
                translation.y = 0;
                this.$emit('onClickAction', 'reset');
            }

            this.syncSetTranslation(translation, status);

            this.$parent.storeData()
        },
        triggerRender() {
            // 更新其它同屏中相同 id 的旋转
            const tabDom = this.$parent.getContentDom();
            const elements = tabDom.getElementsByClassName('viewport-element');
            if (!elements) {
                return;
            }

            for (let index = 0; index < elements.length; index++) {
                const el = elements[index];
                
                if (!el){
                    continue;
                }

                const layers = cornerstone.getLayers(el);
                if (layers.length) {
                    layers.forEach(_ => {
                        const layer = cornerstone.getLayer(el, _.layerId);
                        const viewport = layer.viewport;

                        const image = layer.image;
                        const renderOffsetKey = cornerstone.getOffsetRotationKey(image.imageId);
                        const offsetTranslation = cornerstone.translationMap[renderOffsetKey] || {x: 0, y: 0}
                        viewport.offsetTranslation = offsetTranslation;
                        cornerstone.updateImage(el);
                    })
                }else {
                    const image = cornerstone.getImage(el);
                    if (!image) continue

                    const renderOffsetKey = cornerstone.getOffsetRotationKey(image.imageId);
                    const offsetTranslation = cornerstone.translationMap[renderOffsetKey] || {x: 0, y: 0}

                    const viewport = cornerstone.getViewport(el);
                    viewport.offsetTranslation = offsetTranslation;
                    cornerstone.setViewport(el, viewport);
                    cornerstone.updateImage(el);
                }
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.coordinate2dTols-tools {
    position: relative;
    right: -28px;
    bottom: -28px;
    display: flex;
    flex-direction: column;
    width: 84px;
    background: #efefef;
    .item {
        display: flex;
    }
}
.item-btn {
    width: 28px;
    height: 28px;
    background: #f5f9fc;
    border: 1px solid #d0d2d1;
    color: #142b4b;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1;
    &:hover {
        background-color: #d1dbe7;
    }
    // &.btn-01 {
    //     width: 52px;
    // }
    // &.btn-02 {
    //     height: 52px;
    // }
    .el-checkbox{
        width: 100%;
        height: 100%;
        padding-top: 6px;
    }
    > i {
        display: inline-block;
        width: 100%;
        height: 100%;
        font-size: 22px;
        line-height: 28px;
        &:active {
            filter: invert(50%);
        }
    }
    &.btn-none{
        background: none;
        border: none;
        cursor: default;
    }
    &.btn-drag {
        font-size: 12px;
        text-align: center;
        line-height: 12px;
        padding-top: 2px;
        color: #444;
        cursor: pointer;
        &:hover {
            background-color: #fff;
        }
        &.active {
            background: #9abdcd;
            color: white;
        }
        &.no-drag {
            cursor: no-drop;
        }
    }
    .reset-icon {
        position: relative;
        width: 15px;
        height: 15px;
        font-size: 26px;
        margin: 6px auto;
        background: #111;
        border-radius: 50%;
    }
}
.input-container {
    padding-bottom: 28px;
}
.input-box {
    width: 84px; 
    display: flex;
    align-items: center;
    > span {
        display: inline-block;
        width: 18px;
    }
}
.input{
    width: 66px;
    ::v-deep input{
        text-align:left;
        font-size: 16px;
    }
}

</style>