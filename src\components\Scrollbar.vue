<template>
	<div class="scroll">
		<div class="scroll-holder" ref="scroll">
			<input
			@mousewheel="mousewheel"
			style="width: calc(100vh - 32px);"
			class="imageSlider"
			type="range"
			min="0"
			:step="step"
			:max="max"
			:value="curVal"
			@input="onChange"
			/>
		</div>
	</div>
</template>
<script>
export default {
	props: {
		curVal: {
			type: [Number, String],
			default: 0,
		},
		max: {
			type: [Number, String],
			default: 0,
		},
		height: {
			type: String,
			default: '100px',
		},
		step: {
			type: [Number, String],
			default: 1
		}
	},
	// computed: {
	// 	curValue() {
	// 		if (this.max - this.curVal < this.step) {
	// 			return this.max;
	// 		}
	// 		return this.curVal;
	// 	}
	// },
	data() {
		return {
			width: '100px',
		}
	},
	mounted() {
		this.width = this.$refs.scroll.clientHeight + 'px';
	},
	methods: {
		// 改变滚动位置
		onChange(event) {
			const intValue = parseInt(event.target.value, 10);
			this.$emit('onInputCallback', intValue)
		},
		mousewheel(e){
			// 滚轮滚动
			const direction = e.deltaY > 0 ? 'down' : 'up';
			if (direction === 'down' && this.curVal < this.max){

				if ((this.curVal + this.step) > this.max) {
					return;
				}
				// 向下滚动
				this.$emit('onInputCallback', this.curVal + this.step)
				return;
			}else if (direction === 'up' && this.curVal > 0){

				let curVal = this.curVal - this.step;
				// 向上滚动
				this.$emit('onInputCallback', curVal < 0 ? 0 : curVal)
			}
		}
	},
}
</script>
<style lang="scss" scoped>
.scroll {
		height: 100%;
		position: absolute;
		right: 0;
		top: 0;
}
.scroll .scroll-holder {
		height: 100%;
		// margin-top: 5px;
		position: relative;
		width: 20px;
		padding: 4px;
		background: #ebeef5;
		// border-left: 1px solid #ccc;
}
.scroll .scroll-holder .imageSlider {
	height: 20px;
	left: 18px;
	padding: 0;
	position: absolute;
	top: 0;
	transform: rotate(90deg);
	transform-origin: top left;
	-webkit-appearance: none;
	background-color: #f0f0f0;
	cursor: pointer;
	margin: 0px;
}
.scroll .scroll-holder .imageSlider:focus {
	outline: none;
}
.scroll .scroll-holder .imageSlider::-moz-focus-outer {
	border: none;
}
.scroll .scroll-holder .imageSlider::-webkit-slider-runnable-track {
	background-color: rgba(0, 0, 0, 0);
	border: none;
	cursor: pointer;
	height: 5px;
	z-index: 6;
}
.scroll .scroll-holder .imageSlider::-moz-range-track {
	background-color: rgba(0, 0, 0, 0);
	border: none;
	cursor: pointer;
	height: 2px;
	z-index: 6;
}
.scroll .scroll-holder .imageSlider::-ms-track {
	animation: 0.2s;
	background: transparent;
	border: none;
	border-width: 15px 0;
	color: rgba(0, 0, 0, 0);
	cursor: pointer;
	height: 12px;
	width: 100%;
}
.scroll .scroll-holder .imageSlider::-ms-fill-lower {
	background: rgba(0, 0, 0, 0);
}
.scroll .scroll-holder .imageSlider::-ms-fill-upper {
	background: rgba(0, 0, 0, 0);
}
.scroll .scroll-holder .imageSlider::-webkit-slider-thumb {
	-webkit-appearance: none !important;
	background-color: #cdcdcd;
	border: none;
	border-radius: 57px;
	cursor: -webkit-grab;
	height: 12px;
	margin-top: -4px;
	width: 39px;
}
.scroll .scroll-holder .imageSlider::-webkit-slider-thumb:active {
	background-color: #a6a6a6;
	cursor: -webkit-grabbing;
}
.scroll .scroll-holder .imageSlider::-moz-range-thumb {
	background-color: #163239;
	border: none;
	border-radius: 57px;
	cursor: -moz-grab;
	height: 12px;
	width: 39px;
	z-index: 7;
}
.scroll .scroll-holder .imageSlider::-moz-range-thumb:active {
	background-color: #20a5d6;
	cursor: -moz-grabbing;
}
.scroll .scroll-holder .imageSlider::-ms-thumb {
	background-color: #163239;
	border: none;
	border-radius: 57px;
	cursor: ns-resize;
	height: 12px;
	width: 39px;
}
.scroll .scroll-holder .imageSlider::-ms-thumb:active {
	background-color: #20a5d6;
}
.scroll .scroll-holder .imageSlider::-ms-tooltip {
	display: none;
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
	.imageSlider {
	left: 50px;
	}
}

</style>