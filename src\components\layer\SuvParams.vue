<template>
    <el-dialog
    append-to-body
    :title="title"
    :visible="visible"
    @close="closeDialog"
    @open="openDialog"
    :close-on-click-modal="false"
    width="820px"
    custom-class="my-dialog">
        <div class="c-content">
            <h4>患者信息</h4>
            <ul>
                <li>
                    <span>身高:</span>
                    <el-input v-model="formData.patientStudyModule.patientSize" v-floorNumber placeholder="" size="small">
                        <template slot="append">{{ sizeUnit }}</template>
                    </el-input>
                </li>
                <li>
                    <span>体重:</span>
                    <el-input v-model="formData.patientStudyModule.patientWeight" v-floorNumber placeholder="" size="small">
                        <template slot="append">KG</template>
                    </el-input>
                </li>
                <li>
                    <span>性别:</span>
                    <el-radio-group v-model="formData.patientStudyModule.patientSex" size="small">
                    <el-radio label="M">男</el-radio>
                    <el-radio label="F">女</el-radio>
                    </el-radio-group>
                </li>
            </ul>
            <h4 style="border-top: 1px solid #d9d9d9ee;padding-top: 20px;">药物信息</h4>
            <ul v-if="!isTotalDose && formData.geModule.manufacturer.toUpperCase() === 'GE MEDICAL SYSTEMS'">
                <!-- GE 信息设置 -->
                <li class="date-picker">
                    <span>measuredTracerActivityDate (示踪剂活动时间):</span>
                    <el-date-picker
                    v-model="formData.geModule.measuredTracerActivityDate"
                    type="datetime"
                    prefix-icon="el-icon-dat"
                    placeholder=""
                    size="small">
                    </el-date-picker>
                </li>
                <li class="date-picker">
                    <span>measuredTracerActivityDose (示踪剂剂量):</span>
                    <el-input v-model="formData.measuredTracerActivityDoseMci" v-floorNumber placeholder="" size="small">
                        <template slot="append">mCi</template>
                    </el-input> <label style="padding-left: 10px;">{{ formData.geModule.measuredTracerActivityDose }} MBq</label>
                </li>
                <li class="date-picker">
                    <!-- 注射时间 -->
                    <span>administreredDateTime (注射时间):</span>
                    <el-date-picker
                    v-model="formData.geModule.administreredDateTime"
                    prefix-icon="el-icon-dat"
                    type="datetime"
                    placeholder=""
                    size="small">
                    </el-date-picker>
                </li>
                <li class="date-picker">
                    <span>postInjectionMeasuredDose (注射残余剂量):</span>
                    <el-input v-model="formData.postInjectionMeasuredDoseMci" v-floorNumber placeholder="" size="small">
                        <template slot="append">mCi</template>
                    </el-input> <label style="padding-left: 10px;">{{ formData.geModule.postInjectionMeasuredDose }} MBq</label>
                </li>
                <li class="date-picker">
                    <!-- 采集时间 -->
                    <span>acquisitionDateTime (采集时间):</span>
                    <el-date-picker
                    v-model="formData.geModule.acquisitionDateTime"
                    prefix-icon="el-icon-dat"
                    type="datetime"
                    placeholder=""
                    size="small">
                    </el-date-picker>
                </li>
                <li class="date-picker">
                    <span>decayFactor (衰变因子):</span>
                    <el-input v-model="formData.geModule.decayFactor" v-floorNumber:20 placeholder="" size="small"></el-input>
                </li>
            </ul>
            <ul v-else>
                <li class="text-item">{{ radiopharmaceutical }}</li>
                <li class="text-item">衰变周期：{{ halfLife }}</li>
                <li class="text-item">{{ formData.generalSeriesModule.seriesDate.year }} 年 {{ formData.generalSeriesModule.seriesDate.month }} 月 {{ formData.generalSeriesModule.seriesDate.day }} 日</li>
                <li style="width: 480px;">
                    <span>药物总量:</span>
                    <el-input v-model="formData.radionuclideMci" placeholder="" v-floorNumber size="small" style="width: 140px;flex: initial;">
                        <template slot="append">mCi</template>
                    </el-input>
                    <!-- 有损不在显示 -->
                    <!-- <label style="padding-left: 10px;">{{ formData.radiopharmaceuticalInfo.radionuclideTotalDose }} Bq</label> -->
                </li>
                <li class="date-li">
                    <span>注射时间:</span>
                    <div class="date-li-list">
                        <div>
                            <el-input v-model="formData.radiopharmaceuticalInfo.radiopharmaceuticalStartTime.hours" placeholder="" v-number size="small"></el-input>
                            <span>时</span>
                        </div>
                        <div>
                            <el-input v-model="formData.radiopharmaceuticalInfo.radiopharmaceuticalStartTime.minutes" placeholder="" v-number size="small"></el-input>
                            <span>分</span>
                        </div>
                        <div>
                            <el-input v-model="formData.radiopharmaceuticalInfo.radiopharmaceuticalStartTime.seconds" placeholder="" v-number size="small"></el-input>
                            <span>秒</span>
                        </div>
                        <div>
                            <el-input v-model="formData.radiopharmaceuticalInfo.radiopharmaceuticalStartTime.fractionalSeconds" placeholder="" v-number size="small"></el-input>
                            <span>毫秒</span>
                        </div>
                    </div>
                </li>
                <li class="date-li">
                    <span>采集时间:</span>
                    <div class="date-li-list">
                        <div>
                            <el-input v-model.number="formData.generalSeriesModule.seriesTime.hours" v-number placeholder="" size="small"></el-input>
                            <span>时</span>
                        </div>
                        <div>
                            <el-input v-model.number="formData.generalSeriesModule.seriesTime.minutes" v-number placeholder="" size="small"></el-input>
                            <span>分</span>
                        </div>
                        <div>
                            <el-input v-model.number="formData.generalSeriesModule.seriesTime.seconds" v-number placeholder="" size="small"></el-input>
                            <span>秒</span>
                        </div>
                        <div>
                            <el-input v-model.number="formData.generalSeriesModule.seriesTime.fractionalSeconds" v-number placeholder="" size="small"></el-input>
                            <span>毫秒</span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div slot="footer" class="dialog-footer">
            <span class="tips">设置保存在本地</span>
            <el-button type="text" icon="el-icon-refresh" style="margin-right: 30px" @click="onClickSave(true)">还原</el-button>
            <el-button type="small" icon="el-icon-folder-checked" @click="onClickSave(false)">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { cloneDeep, isNumber } from 'lodash-es';
import customLocalMeta from '$library/cornerstone/mpr/store/customLocalMeta.js';
import VoiMapping from '$library/cornerstone/function/VoiMapping.js';
import { format } from 'date-fns';
export default {
    props: {
        title: {
            type: String,
            default: "重建 SUV 参数设置",
        },
        visible: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            storageKey: 'setting-suv-params',
            radiopharmaceutical: '',
            formData: {
                geModule: {
                    manufacturer: ''
                },
                patientStudyModule: {},
                generalSeriesModule: {
                    seriesTime: {},
                    seriesDate: {}
                },
                radiopharmaceuticalInfo: {
                    radiopharmaceuticalStartTime: {}
                }
            },
            localStoreItem: {},
            petUid: '',
            element: {},
            imageId: '',
            seriesId: '',
            sizeUnit: '',
            voi: {
                u: 6,
                l: 0
            },
            apis: [],
            isTotalDose: true
        }
    },
    computed: {
        halfLife() {
            let value = this.formData.radiopharmaceuticalInfo.radionuclideHalfLife
            if (!value) {
                return ''
            }
            value = Number(value)
            
            if (isNaN(value)) {
                return ''
            }

            return `${(value / 60).toFixed(2)} 分钟`
        },
    },
    watch: {
        'formData.radionuclideMci': {
            handler(later) {
                let num = Number(later)
                if (isNaN(num)) {
                    this.formData.radiopharmaceuticalInfo.radionuclideTotalDose = 0 
                }
                const value = num.toFixed(2) * 1000000 / 0.027027027

                this.formData.radiopharmaceuticalInfo.radionuclideTotalDose = value
            }
        },
        'formData.measuredTracerActivityDoseMci': {
            handler(later) {
                let num = Number(later)
                if (isNaN(num)) {
                    this.formData.geModule.measuredTracerActivityDose = 0 
                }
                const value = num.toFixed(2) / 0.027027027

                this.formData.geModule.measuredTracerActivityDose = value
            }
        },
        'formData.postInjectionMeasuredDoseMci': {
            handler(later) {
                let num = Number(later)
                if (isNaN(num)) {
                    this.formData.geModule.postInjectionMeasuredDose = 0 
                }
                const value = num.toFixed(2) / 0.027027027

                this.formData.geModule.postInjectionMeasuredDose = value
            }
        }
        
    },
    methods: {
        closeDialog() {
            this.$store.state.suvParamsVisible = false;
        },
        openDialog() {
            this.initFormData()
            const viewport = cornerstone.getViewport(this.element)
            const { u, l } = this.$fun.transformVoiWLToUL(viewport.voi.windowWidth, viewport.voi.windowCenter, this.imageId)
            this.voi.u = u
            this.voi.l = l
        },
        getStorage() {
            let val = localStorage.getItem(this.storageKey)
            val = !val ? {} : JSON.parse(val)
            return val
        },
        fracToDec(fractionalValue) {
            return parseFloat(`.${fractionalValue}`);
        },
        transformStrToDate(str) {
            if (!str) {
                return ''
            }
            let year = str.substring(0, 4)
            let month = str.substring(4, 6)
            let day = str.substring(6, 8)
            let hour = str.substring(8, 10)
            let minute = str.substring(10, 12)
            let second = str.substring(12, 14)
            const date = new Date(year, month - 1, day, hour, minute, second)
            return date
        },
        initFormData() {
            
            const { petUid, imageId, element, seriesId, apis } = this.$store.state.suvParams
            this.localStoreItem = this.getStorage()
            this.petUid  = petUid
            this.element = element
            this.imageId = imageId
            this.seriesId = seriesId
            this.apis = apis
            
            // 获取药物名称
            this.radiopharmaceutical = ''
            const tags = cornerstone.getImage(element)?.data
            if (tags) {
                this.radiopharmaceutical = tags.string('x00091037') || tags.string('x0008103e') || ''
            }

            // 获取 image 数据
            const patientStudyModule = cornerstone.metaData.get('patientStudyModule', imageId)
            const petIsotopeModule = cornerstone.metaData.get('petIsotopeModule', imageId)
            const radiopharmaceuticalInfo = petIsotopeModule.radiopharmaceuticalInfo

            if (!this.localStoreItem[petUid]) {
                
                const geModule = cloneDeep(cornerstone.metaData.get('geModule', imageId))
                const generalSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId)

                geModule.measuredTracerActivityDate = this.transformStrToDate(geModule.measuredTracerActivityDate)
                geModule.administreredDateTime = this.transformStrToDate(geModule.administreredDateTime)
                geModule.acquisitionDateTime = this.transformStrToDate(geModule.acquisitionDate + geModule.acquisitionTime)

                this.formData = {
                    geModule,
                    patientStudyModule: cloneDeep(patientStudyModule),
                    radiopharmaceuticalInfo: cloneDeep(radiopharmaceuticalInfo),
                    generalSeriesModule: cloneDeep(generalSeriesModule)
                }

            }else {
                // 从缓存中获取
                const { patientStudyModule, petIsotopeModule, generalSeriesModule, geModule } = this.localStoreItem[petUid]

                geModule.measuredTracerActivityDate = this.transformStrToDate(geModule.measuredTracerActivityDate)
                geModule.administreredDateTime = this.transformStrToDate(geModule.administreredDateTime)
                geModule.acquisitionDateTime = this.transformStrToDate(geModule.acquisitionDate + geModule.acquisitionTime)

                this.formData = {
                    geModule,
                    patientStudyModule,
                    generalSeriesModule,
                    radiopharmaceuticalInfo: petIsotopeModule.radiopharmaceuticalInfo
                }
            }
            this.isTotalDose = !!radiopharmaceuticalInfo.radionuclideTotalDose
            this.sizeUnit = patientStudyModule.patientSize < 4 ? 'M' : 'CM'
            // 保留两位小数会丢一部分贝可值（贝可-每秒衰变值）
            this.$set(this.formData, 'radionuclideMci', Number((this.formData.radiopharmaceuticalInfo.radionuclideTotalDose * 0.027027027 / 1000000).toFixed(2)))

            this.$set(this.formData, 'measuredTracerActivityDoseMci', Number((this.formData.geModule.measuredTracerActivityDose * 0.027027027).toFixed(2)))

            this.$set(this.formData, 'postInjectionMeasuredDoseMci', Number((this.formData.geModule.postInjectionMeasuredDose * 0.027027027).toFixed(2)))

            

        },
        onClickSave(reset = false) {
            const geModule = cloneDeep(this.formData.geModule)
            // console.log(geModule)
            try {
                geModule.measuredTracerActivityDate = format(geModule.measuredTracerActivityDate || 0, 'yyyyMMddHHmmss')
                geModule.administreredDateTime = format(geModule.administreredDateTime || 0, 'yyyyMMddHHmmss')
                geModule.acquisitionDate = format(geModule.acquisitionDateTime || 0, 'yyyyMMdd')
                geModule.acquisitionTime = format(geModule.acquisitionDateTime || 0, 'HHmmss')
            } catch (error) {
                
            }

            delete geModule.acquisitionDateTime

            const data = reset ? null : {
                geModule,
                patientStudyModule: this.formData.patientStudyModule,
                generalSeriesModule: this.formData.generalSeriesModule,
                petIsotopeModule: { radiopharmaceuticalInfo: this.formData.radiopharmaceuticalInfo }
            }

            this.localStoreItem[this.petUid] = data
            if (this.localStoreItem[this.petUid] === null) {
                delete this.localStoreItem[this.petUid]
            }
            localStorage.setItem(this.storageKey, JSON.stringify(this.localStoreItem));
            customLocalMeta.data = cloneDeep(this.localStoreItem)

            if (reset) {
                this.initFormData()
            }

            if (customLocalMeta.data[this.petUid]) {
                const imageId = `mpr:${this.petUid}`
                customLocalMeta.setImageIdMetaData(imageId)
            }

            // 设置 U,L
            const { w: ww, l: wc } = this.$fun.transformULtoVoiWL(this.voi.u, this.voi.l, this.imageId)
            const viewport = cornerstone.getViewport(this.element)
            viewport.voi.windowWidth = Number(ww.toFixed(2))
            viewport.voi.windowCenter = Number(wc.toFixed(2))
            cornerstone.setViewport(this.element, viewport);
            // 更新 MIP 图的窗宽窗位
            this.apis.forEach(api => {
                if (!api) {
                    return
                }
                const rgbTransferFunction = api.volumes[0].getProperty().getRGBTransferFunction(0)
                const renderWindow = api.genericRenderWindow.getRenderWindow()
                cornerstone.loadAndCacheImage(api.imageId).then(() => {
                    let imageId = api.imageId

                    if (customLocalMeta.data[api.seriesUID]) {
                        imageId = `mpr:${api.seriesUID}`
                        customLocalMeta.setImageIdMetaData(imageId)
                    }

                    const { windowWidth, windowCenter } = VoiMapping.getVoi(imageId, api.seriesUID);
                    const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter)
                    rgbTransferFunction.setMappingRange(lower, upper);

                    renderWindow.render();
                });
            })



            if (this.seriesId) {
                this.$Api.putKV({
                    key: `image-suvParams+${this.seriesId}`,
                    value: JSON.stringify(customLocalMeta.data)
                })
            }


            this.$message.success(reset ? '重置并保存成功！' : '保存成功！')
        },
    },
    mounted() {
        this.openDialog()
    }
}
</script>
<style lang="scss" scoped>
.c-content {
    ::v-deep .el-input {
        flex: 1;
    }
    h4 {
        font-size: 16px;
        font-weight: bold;
        padding: 10px 0 20px;
    }
    ul{
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 10px;
        li{
            width: 250px;
            height: 50px;
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            &.text-item {
                font-size: 14px;
                width: 300px;
                padding-left: 30px;
                font-weight: bold;
                margin-bottom: 0px;
            }
            &.date-li {
                width: 100%;
                .date-li-list {
                    width: 100%;
                    height: 50px;
                    display: flex;
                    flex: 1;
                    padding-left: 25px;
                    ::v-deep .el-input .el-input__inner {
                        text-align: center;
                    }
                    > div {
                        width: 110px;
                        height: 50px;
                        display: flex;
                        align-items: center;
                        margin-bottom: 30px;
                        > span {
                            display: inline-block;
                            width: 50px;
                            text-align: left;
                            padding-left: 6px;
                        }
                    }
                }
            }
            &.date-picker {
                width: 100%;
                span {
                    width: 360px;
                }
                .el-input {
                    width: 200px;
                    flex: inherit;
                }
            }
            span{
                display: inline-block;
                width: 100px;
                text-align: right;
                padding-right: 10px;
            }
        }
    }
}
.tips {
    display: inline-block;
    line-height: 40px;
    font-size: 13px;
    float: left;
    color: #666;
}
</style>