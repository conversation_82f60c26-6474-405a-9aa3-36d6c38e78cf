<template>
    <div style="height: 100%">
        <el-dialog append-to-body title="快捷键说明" custom-class="shortcut-table" :visible="visible"
            :close-on-click-modal="true" @open="openDialog" @close="closeDialog" width="720px">
            <div class="main-contain">
                <div style="padding-bottom: 10px;">
                    <span>工具栏显示快捷键说明：</span>
                    <el-switch
                        v-model="shortcutKey"
                        active-color="#13ce66"
                        inactive-color="#eeeeee"
                        @change="onChangeShortcutKey">
                    </el-switch>
                </div>
                <el-table
                    ref="shortcutList" border size="small"
                    :data="shortcutList" style="width: 100%" height="600">
                    <el-table-column prop="key" label="快捷键" align="center"></el-table-column> 
                    <el-table-column prop="name" label="功能说明" align="center"> 
                    </el-table-column> 
                </el-table>
            </div>
        </el-dialog>

    </div>
</template>
<script> 

export default {
    name: 'shortcut',
    props: {
        visible: {
            type: Boolean,
            default: () => {
                return false;
            }
        }
    }, 
    data() {
        return { 
            shortcutList: [
                { key: 'F1', name: '打开快捷键说明' },
                { key: 'F2', name: '布局界面切换为全屏' },
                { key: 'PageUp', name: '布局上一页' },
                { key: 'PageDown', name: '布局下一页' },
                { key: 'A', name: '工具切换为【选择】' },
                { key: 'W', name: '工具切换为【调窗】' },
                { key: 'U', name: '工具切换为【箭头】' },
                { key: 'I', name: '工具切换为【直线】' },
                { key: 'B', name: '工具切换为【双向线】' },
                { key: 'O', name: '工具切换为【圆】' },
                { key: 'Z', name: '工具切换为【透镜】' },
                { key: 'M', name: '工具切换为【矩形】' },
                { key: 'L', name: '工具切换为【勾画】' },
                { key: 'V', name: '工具切换为【体测量】' },
                { key: 'T', name: '工具切换为【标注】' },
                { key: 'P', name: '开启关闭【定位线】' },
                { key: 'S', name: '保存阅图（重建界面）' },
                { key: 'R', name: '图像重置' },
                { key: 'Q', name: '清除窗宽窗位' },
                { key: 'C', name: '清除标注' },
                { key: '1~9', name: '选中图像，不同设备预设窗宽窗位（重建界面）' },
                { key: '↑ 或者 ↓', name: '选中图像，上下切换图像（重建界面）' },
            ],
            shortcutKey: this.$store.state.showShortcutKey,

        }
    },
    mounted() { 
    },
    methods: {
        closeDialog() {
            this.$store.commit('setShortcutVisible', false) 
            this.$emit('closeDialog')
        },
        openDialog() {
            this.shortcutKey = this.$store.state.showShortcutKey;
        },
        onChangeShortcutKey(e) {
            this.$store.commit('setShowShortcutKey')
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    height: 440px;
    padding-top: 10px;
    > span{
        padding-right: 20px;
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>