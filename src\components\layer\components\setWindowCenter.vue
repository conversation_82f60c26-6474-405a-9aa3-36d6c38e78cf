<template>
    <div class="c-content">
        <div>
            <div class="top-table">
                <el-table
                    ref="modalityList" border @row-click="onClickModalityRow" size="mini" highlight-current-row
                    :data="modalityList" style="width: 100%" height="100%">
                    <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                    <el-table-column prop="name" label="名称" width="120" sortable align="center">
                        <template slot-scope="scope">
                            <el-input size="mini" v-model="scope.row.name"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="sModality" label="图像类型" align="center" sortable>
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.tags" multiple collapse-tags placeholder="请选择" size="mini">
                                <el-option
                                v-for="item in tags"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column
                    label="操作"
                    align="center"
                    width="80">
                    <template slot-scope="scope">
                        <el-button @click="onClickRemoveModality(scope.$index, modalityList, scope.row)" size="mini" plain style="padding: 4px 12px;"><i class="el-icon-delete"></i></el-button>
                    </template>
                    </el-table-column>
                </el-table>
            </div>
 
            <div class="c-item-02 first-item-02">
                <div>
                    <el-button size="mini" plain style="padding: 4px 12px;" @click="onClickDefault">默认配置</el-button>
                    <el-button size="mini" plain style="padding: 4px 12px;" @click="onClickRefresh">刷 新</el-button>
                </div>
                <div>
                    <el-button @click="onClickRowModality" size="mini" plain style="padding: 4px 12px;">添加行</el-button>
                    <el-button type="primary" @click="onClickSavaModality" size="mini" plain style="padding: 4px 12px;">保 存</el-button>
                </div>               
            </div>

            <div class="bottom-table">
                <el-table
                    ref="wLList" border size="mini"
                    :data="wLList" style="width: 100%" height="100%">
                    <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                    <el-table-column prop="name" label="描述" width="120" sortable align="center">
                        <template slot-scope="scope">
                            <el-input size="mini" v-model="scope.row.name"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="ww" label="窗宽" align="center" sortable>
                        <template slot-scope="scope">
                            <el-input size="mini" v-model="scope.row.ww" v-floorNumber></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="wl" label="窗位" align="center" sortable>
                        <template slot-scope="scope">
                            <el-input size="mini" v-model="scope.row.wl" v-floorNumber></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                    label="操作"
                    align="center"
                    width="80">
                    <template slot-scope="scope">
                        <el-button size="mini" plain style="padding: 4px 12px;" @click="onClickRemoveWL(scope.$index, wLList)"><i class="el-icon-delete"></i></el-button>
                    </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="c-item-02">
                <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
                <div>
                    <el-button size="mini" plain style="padding: 4px 12px;" @click="onClickRowWL">添加行</el-button>
                    <el-button type="primary" size="mini" plain style="padding: 4px 12px;" @click="onClickSaveWL">保 存</el-button>
                </div> 
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'setWindowCenter',
    data() {
        return {
            modalityList: [],
            wLList: [],
            wLListAll: [],
            curModality: 'CT',
            storageKey: 'configs-WL',
            defaultTemplate: {
                modalityList: [
                    {id: 1, name: 'CT', tags: ['CT']},
                    {id: 2, name: 'PT', tags: ['PT']},
                    {id: 3, name: 'NM', tags: ['NM']},
                    {id: 4, name: 'MR', tags: ['MR']},
                    {id: 5, name: '截屏图', tags: ['IMG']}
                ],
                wLList: {
                    1: [
                        {ww: '80', wl: '35', name: '脑'},
                        {ww: '1200', wl: '-600', name: '肺'},
                        {ww: '200', wl: '40', name: '肝脏'},
                        {ww: '400', wl: '40', name: '纵膈'},
                        {ww: '300', wl: '40', name: '腹'},
                        {ww: '150', wl: '40', name: '脊'},
                        {ww: '1500', wl: '450', name: '骨'}
                    ],
                    2: [
                        {ww: '6', wl: '0', name: 'Pet1'},
                        {ww: '10', wl: '0', name: 'Pet2'},
                        {ww: '15', wl: '0', name: 'Pet3'},
                        {ww: '20', wl: '0', name: 'Pet4'},
                    ],
                    3: [
                        {ww: '20000', wl: '10000', name: 'NM1'},
                        {ww: '75', wl: '35', name: 'NM2'},
                    ],
                    4: [
                        {ww: '500', wl: '100', name: 'MR1'},
                        {ww: '906', wl: '453', name: 'MR2'},
                        {ww: '1280', wl: '640', name: 'MR3'},
                        {ww: '4088', wl: '2044', name: 'MR4'},
                        {ww: '4286', wl: '2143', name: 'MR5'},
                        {ww: '5038', wl: '2519', name: 'MR6'},
                    ],
                    5: [
                        {ww: '256', wl: '128', name: 'IMG1'},
                    ],
                }
            },
            tags: [
                {value: 'CT', label: 'CT'},
                {value: 'PT', label: 'PT'},
                {value: 'PT-MIP', label: 'PT-MIP'},
                {value: 'NM', label: 'NM'},
                {value: 'MR', label: 'MR'},
                {value: 'IMG', label: 'IMG'},
                {value: 'ALL', label: 'ALL'}
            ]
        }
    },
    mounted() {
        this.getSetStorageConfigs();
    },
    methods: {
        getStorage(){
            // 获取浏览器缓存中的配置信息
            let val = localStorage.getItem(this.storageKey)
            if (!val){
                const obj = Object.assign({}, this.defaultTemplate)
                localStorage.setItem(this.storageKey, JSON.stringify(obj))
                val = obj
            }else {
                val = JSON.parse(val)
            }
            return val;
        },
        getSetStorageConfigs(){
            const val = this.getStorage();
            // 设备数据
            this.modalityList = val.modalityList
            // 选中相应列表
            this.wLList       = val.wLList[val.modalityList[0].id]
            this.$refs.modalityList.setCurrentRow(this.modalityList[0])
            // 全部列表
            this.wLListAll    = val.wLList
        },
        /** 
         * 点击设备项
         */
        onClickModalityRow(row){
            this.curModality = row.name
            this.wLList = this.wLListAll[row.id]
        },
        // 保存设备列表
        onClickSavaModality(){
            let storage = this.getStorage();
            storage.modalityList = this.modalityList;
            storage.wLList = this.wLListAll;

            localStorage.setItem(this.storageKey, JSON.stringify(storage))
            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        // 保存窗宽成为列表
        onClickSaveWL(){
            let storage = this.getStorage();
            storage.wLList = this.wLListAll; // 同地址，其它设备变化也一样会存储

            localStorage.setItem(this.storageKey, JSON.stringify(storage))
            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        // 点击添加窗宽窗位 list
        onClickRowWL(){
            console.log(this.wLList.length)
            this.wLList.push({ww: '', wl: '', name: this.curModality + this.wLList.length})
        },
        // 点击添加设备列表
        onClickRowModality(){
            const only = this.$fun.onlyValue();
            const item = {id: only, name: '', tags: []};
            this.modalityList.push(item)
            this.$refs.modalityList.setCurrentRow(item)

            this.wLListAll[only] = [
                {ww: '0', wl: '0', name: ''},
            ]
            this.wLList = this.wLListAll[only]
        },
        onClickRemoveWL(index, rows){
            rows.splice(index, 1);
        },
        onClickRemoveModality(index, rows, row){
            rows.splice(index, 1);
            delete this.wLListAll[row.id]
        },
        onClickRefresh(){
            this.getSetStorageConfigs();
        },
        onClickDefault(){
            this.$MessageBox.confirm('您确定要还原默认值吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
            }).then(() => {
                localStorage.setItem(this.storageKey, JSON.stringify(this.defaultTemplate));
                this.getSetStorageConfigs();
                this.$message({
                    type: 'success',
                    message: '还原成功！'
                });
            })
        },
        handleClick(row){
            console.log(row)
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    > div{
        flex: 1;
        overflow: hidden;
        .top-table{
            height: 210px;
        }
        .bottom-table{
            height: 210px;
        }
        .c-item-02{
            font-size: 13px;
            margin-top: 4px;
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            &.first-item-02{
                margin-bottom: 20px;
            }
            .c-tip{
                font-size: 15px;
            }
        }
    }
}
</style>