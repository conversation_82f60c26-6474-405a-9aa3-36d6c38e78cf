<template>
    <div class="inner-pc-plan pc-body" ref="pcBody" >
        <section class="c-body" ref="center">
			<AreaFit ref="areaFit" :class="styleFont" :isMuch="readLayoutFill" @resizeArea="setFontByWidthAndHeight" :showFooter="isMuch">
				<PrintThumb 
				id="PrintThumb" 
				v-if="isMuch && showPrintThumb" 
				:fullScreenUID="fullScreenUID" 
				:openIds="openIds" 
				:viewports="viewports"
				@selectThumb="selectThumb" 
				@reloadScreen="reloadScreen"></PrintThumb>
				<template>
					<draggable 
					element="div" 
					@add="onDragadd"
					@sort="onDragSort" 
					class="c-content" 
					:id="componentId" 
                    handle=".viewport-element"
					:disabled="activeTool != 'Airtools' || ( !isMuch && !isScreenshotContrast )" 
					v-model="showViewports"
					animation="300" group="view">
					<!-- group="view" force-fallback="true" -->
						<!-- cornerstone 显示图像 -->
						<CornerstoneViewport v-for="(item, index) in showViewports" :style="style" 
							@onMouseRight="onMouseRight"
							:key="item.uid"
							:state="viewportState(item)"
							:imageIds.sync="item.imageIds"
							:layerIds="item.layerIds"
							:class="{'i-active': (activeViewportIndex === index && printSelect.includes(index) || printSelect.includes(index)), 
							'xx-full-screen': fullScreenIndex === index ,
							'cvp-is-hidden': fullScreenIndex < 0 ? (index < stepVal) : (false),
							'xx-full-screen-preload': fullScreenIndex > -1 && fullScreenIndex !== index,}"
							:activeTool.sync="activeTool"
							:activeScroll="true"
							:activeSelect="windowIsActive && activeViewportIndex === index"
							:showAction="showAction === index"
							:isOverlayVisible.sync="isOverlayVisible"
							:isStackPrefetchEnabled="true"
							:remark="{isCaptrue: item.isCaptrue, isShowRemark: isShowRemark, sRemarkCn: item.sRemarkCn}"
							@renderCallBack="renderCallBack"
							@onClickIsShowRemark="onClickIsShowRemark"
							@setRemark="setRemark($event, index)"
							@onDblclickViewport="onDblclickViewport($event, index)"
							@onToolRenderCompleted="onToolRenderCompleted"
							@onElementEnabled="onElementEnabled"
							@clearSelectTool="clearSelectTool"
							@setViewportActive="setViewportActive(index, true, $event)"
							@selectTool="selectTool"
							@onSelectCurrentShift="onSelectCurrentShift(index)"
							@onSelectCurrentCtrl="onSelectCurrentCtrl(index)"
							@onSaveCropImage="onSaveCropImage">
							<div v-if="printSelect.includes(index)" noprint="true" class="c-select-shade c-select-shade-top"></div>
							<div v-if="printSelect.includes(index)" noprint="true" class="c-select-shade c-select-shade-right"></div>
							<div v-if="printSelect.includes(index)" noprint="true" class="c-select-shade c-select-shade-bottom"></div>
							<div v-if="printSelect.includes(index)" noprint="true" class="c-select-shade c-select-shade-left"></div>
							<span v-if="printSelect.includes(index)" class="i-print-select">SEL{{ printSelect.indexOf(index) + 1 }}/{{ printSelect.length }}</span>
							<ViewportSign :iIsGraphic="item.iIsGraphic" :iIsPrinted="item.iIsPrinted"></ViewportSign>
						</CornerstoneViewport>
							
						<!-- 数量不够的时候补位 -->
						<div v-for="(item, index) in popViewports" :style="style" class="viewportWrapper" oncontextmenu="return false"></div>

						<!-- 用于向前、向后推进补位 -->
						<div v-for="(item, index) in Array(stepVal)" :style="style" class="viewportWrapper"></div>
						
					</draggable>
				</template>
				<template v-if="isMuch" v-slot:footer>
					<div class="read-bottom-bar">
						<mouseToolState :activeTool="activeTool"></mouseToolState>
						<span> F1键：显示键盘快捷键说明</span>
						<span class="text-yellow">{{ countPrintText }}</span>
						<span class="text-yellow">图像总数：{{ viewports.length }}</span>
					</div>
				</template>
			</AreaFit>

			<div class="read-scroll">
				<Scrollbar :curVal="stepVal" :max="viewports.length - 1" :step="srollNum" @onInputCallback="onGlobalScroll"/>
			</div>
			<!-- <div class="c-right" ref="right"> -->
			<!-- 基本工具。旋转、点击工具、点击重置、点击反片事件 -->
			<BaseTools 
			:tabId="tabId"
			:seriesId="seriesId instanceof Array ? seriesId[0] : seriesId"
			:remark="remarkObj"
			ref="baseTools"
			:isMuch="isMuch"
			:layoutBox="{rows: 10, columns: 10}"
			:activeTool.sync="activeTool"
			:activeViewportIndex="activeViewportIndex"
			:viewportElements="viewportElements"
			:isOverlayVisible.sync="isOverlayVisible"
			:visiblePrint.sync="visiblePrint"
			:viewports="viewports"
			@selectThumb="selectThumb" 
			@clearMark="onMeasurements('del')"
			@onClickSavePrint="onClickSavePrint"
			@setRemark="setRemark"
			@onClickDelScreen="onClickDelScreen"
			@onClickBox="onClickBox"
			@onChangePage="onChangePage">
				<ImagePrintTemplate slot="print" :patientId="openIds" :seriesInfo="seriesInfo" v-model="visiblePrint" @reloadScreen="reloadScreen" :printSelect="printSelectSopUids"></ImagePrintTemplate>
			</BaseTools>
			<!-- </div> -->
        </section>

		<v-contextmenu ref="contextmenu" oncontextmenu="return false">
			<v-contextmenu-item @click="onClickImageSave(1)"><i class="el-icon-picture" style="padding-right:4px"></i>截屏另存...</v-contextmenu-item>
			<v-contextmenu-item @click="onClickImageSave(2)"><i class="el-icon-picture" style="padding-right:4px"></i>选中图像另存</v-contextmenu-item>
			<v-contextmenu-item divider></v-contextmenu-item>
			<v-contextmenu-item @click="copyAllRemark"><i class="el-icon-paperclip" style="padding-right:4px"></i>复制全部备注</v-contextmenu-item>
			<v-contextmenu-item divider></v-contextmenu-item>
			<v-contextmenu-item @click="onMeasurements('del')"><i class="el-icon-delete" style="padding-right:4px"></i>清除标注</v-contextmenu-item>
            <v-contextmenu-item @click="onMeasurements('singleDel')"><i class="el-icon-delete" style="padding-right:4px"></i>清除单页标注</v-contextmenu-item>
			<v-contextmenu-item divider></v-contextmenu-item>
			<v-contextmenu-submenu >
                <span slot="title"><i class="el-icon-folder-checked" style="padding-right:4px"></i>阅图对比</span>
                <v-contextmenu-item @click="onClickSelectContrast"><i class="el-icon-plus" style="padding-right:4px"></i>选择图像</v-contextmenu-item>
                <v-contextmenu-item @click="onClickClearContrast"><i class="el-icon-delete" style="padding-right:4px"></i>清除选择</v-contextmenu-item>
				<v-contextmenu-item divider></v-contextmenu-item>
				<v-contextmenu-item @click="onClickOpenContrast"><span style="display: inline-block;width: 13px;color: #53a6fb;">{{ contrastCount }}</span> 打开对比</v-contextmenu-item>
            </v-contextmenu-submenu>
			<v-contextmenu-item divider></v-contextmenu-item>
			<v-contextmenu-item @click="onClickGraphic"><i class="el-icon-s-claim" style="padding-right:4px"></i>标记图文打印</v-contextmenu-item>
			<v-contextmenu-item divider></v-contextmenu-item>
			<v-contextmenu-item @click="onClickDelScreen"><i class="el-icon-delete" style="padding-right:4px"></i>删除图像</v-contextmenu-item>
		</v-contextmenu>

		<SaveImage :element="saveImage.element" v-model="saveImage.visible"></SaveImage>
    </div>
</template>

<script>
import AreaFit from '$src/layout/AreaFit.vue'
import CornerstoneViewport from '$components/CornerstoneViewport.vue'
import LayoutBox from '$components/tools/LayoutBox'
import BaseTools from '$components/tools/BaseTools'
import PrintThumb from '$components/tools/PrintThumb'
import ImagePrintTemplate from '$components/layer/ImagePrintTemplate'
import draggable from 'vuedraggable'
import Scrollbar from "$components/Scrollbar.vue"
import ViewportSign from '$components/ViewportSign'
import SaveImage from '$components/layer/SaveImage'
import mouseToolState from '$components/tools/components/mouseToolState'

import event from '$src/event.js'

import SelectTool from "$src/mixin/SelectTool.js";
import Printscreen from "$src/mixin/Printscreen.js";
import getConfigByStorageKey from '$library/utils/configStorage.js';

export default {
	name: 'ReadDcm',
	mixins: [ SelectTool, Printscreen ],
	components: {
		CornerstoneViewport,
		LayoutBox,
		BaseTools,
		draggable,
		Scrollbar,
		AreaFit,
		ImagePrintTemplate,
		ViewportSign,
		SaveImage,
		mouseToolState,
		PrintThumb
	},
	props: {
		series: {
			type: Object,
			default: () => {}
		},
		seriesId: {        // 字段命名错误，应该是 study
			type: [String, Array],
			default: ''
		},
        tabId: {
            default: 'none',
        },
		viewportInof: {
			type: Object,
			default: () => {
				return {}
			}
		},
		reload: {
			type: Boolean,
			default: false
		},
		isMuch: {
			type: Boolean,
			default: false
		},
		isScreenshotContrast: {
			type: Boolean,
			default: false
		},
		openIds: {
			default: ''
		},
		windowIsActive: {
            default: false,
        }
	},
	data() {
		return {
			defaultLayout: getConfigByStorageKey('configs-rebuild-layout'),
			otherReadModality: getConfigByStorageKey('configs-show-read-modality'),
			saveImage: {		// 保存图像
				visible: false,
				element: null,
				rightSelectElement: null,
			},
			componentId: null, // 当前模块id
			stepVal: 0,
			viewports: [],
			popViewports: [],
			imageIds: [],
			layoutLoading: false,
			activeViewportIndex: 0,
			showAction: -1,
			beforeActiveIndex: 0,
			printSelect: [],
			timer: null,
			activeTool: 'Airtools',
			style: {
				width: '100%',
				height: '100%',
			},
			viewportElements: [],
			// originSeries: [],
			isOverlayVisible: true,
			// imgUrl: "dicomweb://" + window.configs.imageServe + '/web/view/getFile?sSOPInstanceUID=',// '',
			stepLazyIndex: 1,
			windowNum: 1,
			srollNum: 1,
			isSrollAllNum: getConfigByStorageKey('configs-readDcmSeting').isSrollAll,
            selectSync: getConfigByStorageKey('configs-readDcmSeting').selectSync,
            mergeMri: getConfigByStorageKey('configs-readDcmSeting').mergeMri,  // 大坪医院，阅图合并原厂截图
            mergeNo: getConfigByStorageKey('configs-readDcmSeting').mergeNo,
			params: {},       // 请求参数
			synchronizer: {
				coronal: Object,
				sagittal: Object,
				axial: Object,
			},
			fullScreenIndex: -1,      // 全屏下标
			fullScreenPosition: '',   // 打开全屏时候的 stepVal 位置
			fullScreenPointIndex: '', // 打开全屏时候的 选中位置
			styleFont: 'overlay-10',
			printSelectSopUids: [],
			toolRenderLast: true,
			curOpenSeriesId: '',
			isShowRemark: true,
			isClickRight: false, // 在点击右键出现右键目录时，不会清除原选中状态
			referenceLinesSynchronizer: Object,
			remarkObj: {
				sRemarkCn: '',
				sSOPInstanceUID: '',
				isCaptrue: false,
			},
			visiblePrint: false, // 打印打开
			lastLayout: {},
			firstLoadRemark: true, // 第一次加载备注
		}
	},
	watch: {
		seriesId: {
			handler() {
				this.curOpenSeriesId = this.seriesId
			},
			immediate: true
		},
		viewportInof: {
			handler(v) {
			    const { isOverlayVisible } = this.viewportInof
				if (isOverlayVisible !== undefined) {
					this.isOverlayVisible = isOverlayVisible
				}
			},
			immediate: true
		},
		reload() {
			// 触发重新加载
			const uids = this.series && this.series.uids;
			if (!uids) {
				// 确认有截图的
				if (this.isMuch) {
					this.openPrintscreen(true);
				}
				return;
			}
			// 打开相应的序列
			this.openShowSeries(uids)
		},
		activeViewportIndex: {
			handler() {
				// 改变点击，触发改变备注获取
				this.getNewRemark();
			},
			immediate: true,
		},
		showViewports() {
			// 第一次加载出 viewports 就加载备注
			if (this.firstLoadRemark) {
				this.getNewRemark()
				this.firstLoadRemark = false
			}
		},
		visiblePrint() {
			if (this.visiblePrint) {
				this.triggerPrint();
			}
		},
		listenCapture: {
			handler() {
				if (this.listenCapture) {
					setTimeout(() => {
                        this.openPrintscreen(true);
					    this.$store.state.listenCapture[this.openIds] = false;
                    }, 600);
				}	
			},
			deep: true
		}
	},
	computed: {
		fullScreenUID() {
			if (this.fullScreenIndex != -1) {
				return this.showViewports[this.fullScreenIndex].sSOPInstanceUID
			}
			return ''
		},
		// lazyIndex(){
		// 	// 懒加载-dom渲染
		// 	let newStep = this.stepVal + this.windowNum;
		// 	if (newStep > this.stepLazyIndex){
		// 		this.stepLazyIndex = newStep
		// 	}else if (this.windowNum > this.stepLazyIndex){
		// 		return this.windowNum;
		// 	}
		// 	return this.stepLazyIndex;
		// },
		showPrintThumb() {
            return this.$store.state.showPrintThumb
        },
		listenCaptureArr() {
			return this.$store.state.listenCapture
		},
		listenCapture() {
			if (this.windowIsActive && this.listenCaptureArr[this.openIds] && this.isMuch) {
				return true
			}
			return false
		},
		showViewports: {
			get: function (){
				//  return this.viewports.slice(0, this.lazyIndex || 1)  // 懒加载形式
				return this.viewports 
			},
			set: function (newValue){
				this.viewports.splice(0, newValue.length, ...newValue)
			}
		},
		// 截图对比数量
		contrastCount() {
			return this.$store.state.screenshotContrast.length;
		},
		seriesInfo() {
            return this.$store.state.seriesInfo
        },
		readDefaultLayout() {
			return this.$store.state.readDefaultLayout
		},
		// 临床控制，页面
		isClinic() {
			return this.$store.state.currentModule === 'Report' && this.$store.state.imageReadOnly
		},
        layoutFill() {
            return this.$store.state.layoutFill;
        },
		readLayoutFill() {
			return this.isMuch || this.isScreenshotContrast || this.layoutFill;
		},
		countPrintText() {
			const graphic = this.viewports.filter(item => item.iIsGraphic === 1 || item.iIsGraphic === '1').length
			const printed = this.viewports.filter(item => item.iIsPrinted === 1 || item.iIsPrinted === '1').length

			return `标记数：${graphic} ，打印数：${printed} `
		}
	},
	beforeDestroy() {
		const areaDOM = this.$refs.areaFit;
		areaDOM.$el.removeEventListener('mousewheel', this.areaMousewheel)
		
		// 销毁触发键盘监听
		event.$off('onKeyDown', this.onKeyDown)

		// 全身线销毁
		if (Object.keys(this.referenceLinesSynchronizer).length){
			this.referenceLinesSynchronizer.destroy()
			this.referenceLinesSynchronizer = null
		}
	},
	mounted() {
		this.isShowRemark = this.$store.state.isReadShowRemark; // 一开始是否显示备注在覆盖层上
		// 原图定位功能
		this.referenceLinesSynchronizer = new cornerstoneTools.Synchronizer(
			'cornerstonenewimage',
			cornerstoneTools.updateImageSynchronizer
		)

		// 其它组件(Main.vue)键盘监听，触发
		event.$on('onKeyDown', this.onKeyDown)

		// 右侧大滚轮
		const areaDOM = this.$refs.areaFit;
		areaDOM.$el.addEventListener('mousewheel', this.areaMousewheel, {passive: true})
		
		// 当前打开组件 id 唯一值
		this.componentId = this.$fun.onlyValue()
		// 设置 1x1 布局
		// this.onClickBox({column: 5, row: 5})

		const uids = this.series && this.series.uids;
		if (!uids) {
			// 确认有截图的
			if (this.isMuch) {
				this.openPrintscreen(true);
			}
			return;
		}
		// 截图对比
		if (this.isScreenshotContrast) {
			this.openContrastSeries(uids)
			return
		}
		// 打开相应的序列
		this.openShowSeries(uids)
	},
	methods: {
		// 打印截图缩略图选中事件
		selectThumb(uid) {

			const idx = this.showViewports.findIndex(item => item.sSOPInstanceUID == uid)
			
			if (idx != -1) {
                // 没有全屏
				if (this.fullScreenIndex == -1) {
					this.stepVal = idx
					this.onClickBox(this.lastLayout)
					return
				}
				this.onGlobalScroll(idx);
			}
		},
		// 刷新截图
		reloadScreen() {
			// 确认有截图的
			if (this.isMuch) {
				this.openPrintscreen(true);
			}
		},
		// 选择截图对比
        onClickSelectContrast() {
			const selectViewport = this.viewports[this.showAction];

			if (selectViewport) {
				const studyDate = selectViewport.iStudyDate + '';
				const yyyy = parseInt(studyDate.substring(0, 4), 10);
				const mm = parseInt(studyDate.substring(4, 6), 10);
				const dd = parseInt(studyDate.substring(6, 8), 10);
				const timestamp = new Date(yyyy, mm, dd).getTime()

				const v = {
					sStudyInstanceUID: selectViewport.sStudyInstanceUID,
					sSeriesInstanceUID: selectViewport.sSeriesInstanceUID,
					imageId: selectViewport.imageIds[0],
					iStudyDate: studyDate,
					timestamp,
					sSeriesDescription: selectViewport.sSeriesDescription,
					isCaptrue: selectViewport.isCaptrue,
					sSOPInstanceUID: selectViewport.sSOPInstanceUID,
					sRemarkCn: selectViewport.sRemarkCn
				}
				if (selectViewport.iIsGraphic != undefined) {
					v.iIsGraphic = selectViewport.iIsGraphic
				}
				if (selectViewport.iIsPrinted != undefined) {
					v.iIsPrinted = selectViewport.iIsPrinted
				}
				this.$store.commit('addScreenshotContrast', {
					data: v,
					callBack: (res) => {
						if (res) {
							this.$message.success('选择成功！')
						}else {
							this.$message.info('已经选择！')
						}
					}
				})
				
			}
        },
        // 清除截图对比
        onClickClearContrast() {
			this.$store.commit('clearScreenshotContrast');
			this.$message.success('操作成功');
        },
        // 打开截图对比
        onClickOpenContrast() {
			const screenshotContrast = this.$store.state.screenshotContrast;
			if (!this.contrastCount) {
				this.$message.info('请先选择，截图对比图');
				return;
			}
			// 打开截图对比
			event.$emit('onSelectUid', screenshotContrast, 'ReadDcm', screenshotContrast[0].sStudyInstanceUID, undefined, false, false, true)
        },
		onMeasurements(funName) {
			if (funName === 'del') {
				this.$refs.baseTools.onClickDelTool('hide');
			} else if (funName === 'singleDel') {
                const element = this.getEnabledElement(this.activeViewportIndex)
                this.$refs.baseTools.removeSinglePageTool(element)
            }
		},
		viewportState(item) {
			return { 
				uid: item.uid, 
				seriesId: item.sSeriesInstanceUID, 
				patientId: this.isMuch ? this.openIds : this.$store.state.seriesMap.get(this.seriesId instanceof Array ? this.seriesId[0] : this.seriesId).seriesInfo.key,
                imgIndex: Math.floor(item.imageIds.length / 2) // 阅图默认居中显示
			}
		},
		// 获取新备注
		getNewRemark() {
			const curItem = this.showViewports[this.activeViewportIndex];
			if (curItem) {
				this.remarkObj = {
					isCaptrue: curItem.isCaptrue,
					sSOPInstanceUID: curItem.sSOPInstanceUID,
					sRemarkCn: curItem.sRemarkCn
				};
			}else {
				this.remarkObj.isCaptrue = false;
			}
		},
		/**
		 * 根据打开的数量，匹配相应布局结构
		 */
		openAutoLayout(len) {
			// 有固定显示配置，是截图列表
			if (this.readDefaultLayout && this.isMuch) {
				this.onClickBox({column: this.readDefaultLayout.column, row: this.readDefaultLayout.row}, false)
				return;
			}
			const arr = [1, 4, 9, 16, 25, 36, 49, 64, 81, 100]
			let idx = arr.findIndex(item => {
				return len <= item;
			})
			idx += 1;
			if (len >= 100) {
				idx = 10;
			}
			const box = idx > 0 ? idx : 5;
			this.onClickBox({column: box, row: box}, false)
		},
		// 键盘监听
		onKeyDown(event, tabId){

			if (this.tabId === tabId){
				// ctrl + A 全选
				if (event.keyCode === 65 && event.ctrlKey){
					this.onSelectAll()
				}
            }
		},
		/**
		 * 一开始没有获取到信息时，做的打开截屏图
		 * 所有检查的序列放在一个tab 中
		 */
		openPrintscreen(isOpenLayer){
			if (this.openIds) {
				const requestInfo = this.$store.state.requestList.find(item => item.key === this.openIds);
				if (requestInfo) {
					this.$store.dispatch('loadPrintscreen', requestInfo).then(res => {
						if (res) {

                            // 开启合并设置
                            let mriUid = {};
                            let filterStudy = [];
                            // 是否合并 MR
                            if (this.mergeMri) {
                                const allStudy = this.$store.state.seriesMap.values();
                                // 过滤，多患者打开过滤
                                for (const item of allStudy) {
                                    if (item.seriesInfo.key ===  this.openIds) {
                                        const temp = Array.from(item.instanceList.values());
                                        filterStudy = filterStudy.concat(temp);
                                    }
                                }
                                // isMri = filterStudy.findIndex(item => item.sModality == 'MR')
                            }

							let captrues = res.filter(item => {
								return this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, false, item.imageType);
							})
                            // 开启合并
                            if (!this.mergeNo) {
                                this.mergeNo = ['screen', 'pages']
                            }
                            if (filterStudy.length) {
                                captrues = captrues.filter(item => {
                                    const _desc = item.sSeriesDescription?.toLocaleLowerCase()
                                    // if (_desc.includes('screen') || _desc.includes('pages')){
                                    if (this.mergeNo.find(item => _desc?.includes(item.toLocaleLowerCase())) ) {
                                        return true;
                                    }
                                    mriUid[item.sSeriesInstanceUID] = true

                                    return false
                                })
                            }
                            // 遍历添加标记的截图
                            for (const sSeriesInstanceUID in mriUid) {
                                filterStudy.forEach(item => {
                                    if (item.sSeriesInstanceUID == sSeriesInstanceUID) {
                                        const obj = this.$fun.deepClone(item);
                                        obj.isCaptrue = false;
                                        captrues.push(obj);
                                    }
                                })
                            }

							this.openAutoLayout(captrues.length);
							this.viewports = captrues;
							
							if (!captrues.length) {
								this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: true })
								setTimeout(() => {
									this.$message.info('没有截图');
								}, 300);
							}
						}else {
							if (isOpenLayer) {
								// 所有视窗都没有截屏图,打开选择弹窗
								this.$nextTick(() => {
									this.$refs.baseTools.onClickOpenDicom()
								})
							}
						}
						
						// 配置，阅图显示其它非截图设备
						if (this.otherReadModality && this.otherReadModality.length) {
							const allStudy = this.$store.state.seriesMap.values();
							let filterStudy = [];
							// 过滤，多患者打开过滤
							for (const item of allStudy) {
								if (item.seriesInfo.key ===  this.openIds) {

									const temp = Array.from(item.instanceList.values());
									filterStudy = filterStudy.concat(temp);
								}
							}
							// 过滤包含配置设备类型
							const series = filterStudy.filter(item => {
								if (!this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, false, item.imageType)) {
									// 包含
									if (this.otherReadModality.includes(item.sModality)) {
										return true;
									}
								}
								return false;
							})
							// 加入非截图图像，改变布局
							this.openAutoLayout(this.viewports.length + series.length);
							// 遍历加入到视图中
							series.map(item => {
								const obj = this.$fun.deepClone(item);
								obj.isCaptrue = false;
								this.viewports.push(obj);
							});
						}

						this.updateElement(); // 更新 UI 信息
					});
				}
			}
			// return this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, false, item.imageType);
		},
		// 改变显示的序列
		/**
		 * 改变显示的序列
		 * uids 需要显示的序列的 uid []
		 */
		openShowSeries(uids){
			// 清空显示的序列
			this.viewports.splice(0)
			// 只打开一个序列，把他们全部展现出来
			let tempVeiwport = [];
			let originSeries = []; 

			if (!(this.curOpenSeriesId instanceof Array)) {
				originSeries = Array.from(this.$store.state.seriesMap.get(this.curOpenSeriesId).instanceList.values())
			}else {
				for (let index = 0; index < this.curOpenSeriesId.length; index++) {
					const seriesId = this.curOpenSeriesId[index];
					const studySeries = Array.from(this.$store.state.seriesMap.get(seriesId).instanceList.values())
					originSeries = originSeries.concat(studySeries)
				}
			}

			originSeries.forEach(item => {
				if (uids.includes(item.uid)){
					const obj = this.$fun.deepClone(item);
					if (this.$fun.isCaptrue(obj.sSeriesDescription, obj.sSOPClassUID, false, obj.imageType)) { 
						obj.imageIds.forEach((imageId, index) => {
							let findImageInfo = this.getRemark(obj.data.instanceList, obj.data.sImgStudyDate, imageId)
							let pushItem = {
								iStudyDate: obj.dateTime,
								iInstanceCount: obj.iInstanceCount,
								sModality: obj.sModality,
								sSOPClassUID: obj.sSOPClassUID,
								sSeriesInstanceUID: obj.sSeriesInstanceUID,
								sSeriesDescription: obj.sSeriesDescription,
								uid: obj.uid +','+ index,
								isCaptrue: true,
								imageIds: [imageId],
								imageType: obj.imageType,

							}
							if (findImageInfo) {
								const series = this.$store.state.seriesMap.get(findImageInfo.sStudyInstanceUID);

								pushItem.iIsGraphic = findImageInfo.iIsGraphic
								pushItem.iIsPrinted = findImageInfo.iIsPrinted
								pushItem.sRemarkCn = findImageInfo.sRemarkCn
								pushItem.sSOPInstanceUID = findImageInfo.sSOPInstanceUID
								pushItem.sStudyInstanceUID = findImageInfo.sStudyInstanceUID
								pushItem.sFilePath = findImageInfo.sFilePath
								pushItem.sFileRootPath = findImageInfo.sFileRootPath
								if (series) {
									pushItem.sPatientID = series.seriesInfo.sPatientID;
									pushItem.sAccessionNumber = series.seriesInfo.sAccessionNumber;
								}
							}
							tempVeiwport.push(pushItem)
						})
					}else {
						obj.data = null;  // 删除多余数据
						tempVeiwport.push(obj);
					}

				}
			})

			setTimeout(() => {
				this.openAutoLayout(tempVeiwport.length)
				this.viewports = tempVeiwport
				// 更新显示
				this.updateElement();
			}, 0);
			
			this.stepVal = 0;
		},
		openContrastSeries(uids) {
			// console.log(uids)
			const sortItems = uids.sort((a, b) => a.timestamp - b.timestamp)
			this.viewports = sortItems.map(item => {
				return {...item, imageIds: [item.imageId]}
			})
			this.onClickBox({column: sortItems.length, row: 1})
		},

        getRemark(arr, iStudyDate, id) {
            const url = new URL(id)
			return arr.find(item => {
                if (item.sSOPInstanceUID == url.searchParams.get('sSOPInstanceUID') && iStudyDate == url.searchParams.get('iStudyDate')) {
                    return true
                }
                return false
			})
		},
		// 从视窗中通过下标获取 enabled 元素
		getEnabledElement(index){
			const el = this.viewportElements[index].getElementsByClassName('viewport-element')[0];
			return el;
		},
		// 元素视图遍历
		viewportMap(callBack){
			for (let index = 0; index < this.viewportElements.length; index++) {
				callBack && callBack(this.getEnabledElement(index), index)
			}
		},
		onToolRenderCompleted(sourceElement, sourceOrientation, status) {
			this.toolRenderLast = true
			if (status === 'removed') {
                this.toolRenderLast = false
            }
			// 添加
			if (status === 'completed' || status === 'removed' || status === 'modifiedAndState'){
				this.$refs.baseTools.getNewToolList()
			}
		},
		// 设置当前选中的视窗
		setViewportActive(index, isClear = true, button){
			if (this.isClickRight) {
				this.isClickRight = false
				// const idx = this.printSelect.indexOf(index);
				// if (idx === -1){
				// 	console.log('加入')
				// 	this.printSelect.push(index)
				// }	
				if (!this.printSelect.length) {
					this.printSelect.push(index);
				}
				this.showAction = index
				return;
			}
			let isClearActive = false // 清除当前选中打印

			if (this.activeViewportIndex !== index){
				this.beforeActiveIndex = this.activeViewportIndex
				this.activeViewportIndex = index
			}else {
				if (this.printSelect.length === 1) {
					isClearActive = true
				}
			}

			// 选中出现工具
            if (this.toolRenderLast) {
                this.toolRenderLast = false
                this.showAction = this.activeViewportIndex
            }else {
				// 在滚动的时候，隐藏工具栏
				if (button === undefined) {
					this.showAction = -1
					return;
				}

				if (button !== 0) {
					this.showAction = index;
					return;
				}

				// 不是“空”工具不清除选中打印效果
				if (this.activeTool !== 'Airtools') {
					isClearActive = false
				}
				this.showAction = this.activeTool === 'Airtools' && this.showAction === index ? -1 : index
			}

			// 清除多选
			if (isClear){
				// 开启清除(执行)
				this.timer = setTimeout(() => {
					this.printSelect = isClearActive ? [] : [index]
					if (!isClearActive) {
						this.showAction = index;
					}else {
						if (this.showAction != -1) {
							const idx = this.printSelect.indexOf(index);
							if (idx === -1){
								this.printSelect.push(index)
							}
						}
					}
				}, 0);
			}


		},
		// 设置选中
		onSelectCurrentCtrl(index){
			// 取消清除(执行)
			clearTimeout(this.timer);
			const idx = this.printSelect.indexOf(index);
			if (idx !== -1){
				this.printSelect.splice(idx, 1)
				this.activeViewportIndex = this.printSelect[0] // 选中当前打印第一个
				this.showAction = this.activeViewportIndex
			}else {
				this.printSelect.push(index)
			}
		},

		// shift 选择
		onSelectCurrentShift(index){
			// 取消清除(执行)
			clearTimeout(this.timer);
			let diffVal = -1;			// 二个选择相隔的数量
			let direction = 'down';		// 选择的方向，向上选、向下选
			if (index > this.beforeActiveIndex){
				diffVal = index - this.beforeActiveIndex;
			}else {
				direction = 'up'
				diffVal = this.beforeActiveIndex - index;
			}
			if (diffVal === -1) return;

			if (this.beforeActiveIndex === 0 && this.printSelect.length === 0) {
				const idx = this.printSelect.indexOf(index);
				if (idx === -1){
					this.printSelect.push(index)
				}
				return;
			};
			// 按照选择的相隔数量补充到打印选择
			for (let idx = 1; idx <= diffVal; idx++) {
				if (direction === 'down'){
					if (!this.printSelect.includes(this.beforeActiveIndex + idx)){
						this.printSelect.push(this.beforeActiveIndex + idx)
					}	
				}else {
					if (!this.printSelect.includes(this.beforeActiveIndex - idx)){
						this.printSelect.push(this.beforeActiveIndex - idx)
					}
				}
			}

		},
		// 全选
		onSelectAll(){
			let idx = this.stepVal;
			for (let index = 0; index < this.windowNum; index++) { // 之全选看见的 // this.showViewports.length
				if (!this.printSelect.includes(idx) && this.viewports[index]){
					this.printSelect.push(idx)
				}
				idx += 1
			}
		},
		// resize 大小
        onResize(el){
            cornerstone.resize(el);
        },
		// 开启元素回调，TODO 用不了
		onElementEnabled(evt){
			console.log(evt)
		},
		renderCallBack(sourceViewport, clayData) {

            if (!this.selectSync) {
                return
            }

			// 选中的序列，触发同步缩放、平移
			if (!this.printSelect.length || this.printSelect.length === 1) {
				return
			}
			this.printSelect.forEach(idx => {
				const el = this.getEnabledElement(idx)
				// 自己，不触发
				if (clayData.el === el) {
                    return
                }
				if (el) {
					const viewport = cornerstone.getViewport(el)
					if (
						sourceViewport.x !== viewport.translation.x ||
						sourceViewport.y !== viewport.translation.y ||
						sourceViewport.scale !== viewport.scale) {
						viewport.scale = sourceViewport.scale
						viewport.translation.x = sourceViewport.x
						viewport.translation.y = sourceViewport.y
						cornerstone.setViewport(el, viewport)
					}
				}
			});
		},
		// 改变布局
		onClickBox(layout, isUpdate = true){
			this.showAction = -1;
			if (this.fullScreenIndex != -1) {
				this.fullScreenIndex = -1
				this.stepVal = this.fullScreenPosition
			}
			// 加载状态 TODO 不是很理想
			this.layoutLoading = true;

			let width = layout.column;
			let height = layout.row;

			this.lastLayout = layout

			// 设置布局样式
 			this.style.width = 100 / width + '%'
        	this.style.height = 100 / height + '%'

			// 窗口数量
			const num = width * height;
			this.windowNum = num;
			this.srollNum = this.isSrollAllNum == 1 ? 1 : num;

			// 还原到第一个选择、打印也是第一个选择
			this.activeViewportIndex = this.stepVal
			this.beforeActiveIndex = this.stepVal
			this.printSelect = [] // 清除打印选中

			const diffVal = num - this.viewports.length;
			const len = this.popViewports.length
			if (diffVal > 0 && diffVal > len){
				// 视窗变多了，push
				for (let index = 0; index < diffVal - len; index++) {
					this.popViewports.push({})
				}
			}
			if (isUpdate) {
				this.updateElement();
			}

		},
		updateElement(){
			// const loading = this.$loading.service({
			// 	target: '.c-content'
			// })
			// 设置元素数量
			this.$nextTick(() => {
				// 获取所有视图元素（dom）结构
				this.$nextTick(() => {
					this.viewportElements = document.getElementById(this.componentId).getElementsByClassName('viewportWrapper');
					this.setFontByWidthAndHeight()
					// 遍历、resize 重置图像大小
					// 关闭加载状态
					setTimeout(() => {
						this.viewportMap(el => {
							if (el){
								this.onResize(el)
							}

						})	
						this.layoutLoading = false
						// loading.close()
						this.setLinesSynchronizer()
					}, 200);
					this.$refs.baseTools.onAllRender()
				})
			})
		},
		// 设置定位图定位线
		setLinesSynchronizer() {
			let allFristIds = []
			// 遍历取第一张图 id 
			for (let index = 0; index < this.viewports.length; index++) {
				allFristIds.push(this.viewports[index].imageIds[0])
			}
			// 全部 promise 数组
			const allPromises = allFristIds.map((imageId) => {
                return cornerstone.loadImage(imageId).then((image) => {
					if (image.data) {
						let imageType = image.data.string('x00080008') || ''
						// 返回是否是定位图 -- 只适合全身 TODO
						return imageType.includes('LOCALIZER')
					}else {
						return false;
					}

				})
            });
			// 在全部 promise 执行完毕后在执行
			Promise.all(allPromises).then(e => {
				// 存在定位图
				if (e.includes(true)) {
					// 清除之前的同步工具
					if (Object.keys(this.referenceLinesSynchronizer).length){
						this.referenceLinesSynchronizer.destroy()
					}
					// 遍历设置视图添加同步
					this.viewportMap(el => {
						if (el){
							this.referenceLinesSynchronizer.add(el)
							cornerstoneTools.addToolForElement(el, cornerstoneTools.ReferenceLinesTool)
							cornerstoneTools.addToolForElement(el, cornerstoneTools.CrosshairsTool)
						}
					})
					// 只有在定位图开启定位线
					e.forEach((item, idx) => {
						if (item) {
							cornerstoneTools.setToolEnabledForElement(this.getEnabledElement(idx), 'ReferenceLines', {
								synchronizationContext: this.referenceLinesSynchronizer,
							});
							cornerstoneTools.setToolActiveForElement(this.getEnabledElement(idx), 'Crosshairs', {
								mouseButtonMask: 1,
								synchronizationContext: this.referenceLinesSynchronizer,
							});
						}

					})

				}
			})

		},
		
		setFontByWidthAndHeight(index = -1) {
			this.$store.state.renderScroll = !this.$store.state.renderScroll
			try {
                const width = this.getEnabledElement(index === -1 ? 0 : index).clientWidth
                if (width >= 320) {
                    this.styleFont = 'overlay-10'
                }else if (width >= 240) {
                    this.styleFont = 'overlay-08'
                }else if (width >= 210) {
                    this.styleFont = 'overlay-07'
                }else if (width >= 160) {
                    this.styleFont = 'overlay-06'
                }else if (width >= 130) {
                    this.styleFont = 'overlay-05'
                }else if (width >= 105) {
                    this.styleFont = 'overlay-04'
                }else if (width >= 75) {
                    this.styleFont = 'overlay-03'
                }else if (width >= 50) {
                    this.styleFont = 'overlay-02'
                }else {
                    this.styleFont = 'overlay-01'
                }
            } catch (error) {
                // console.log(error)
            }
		},
		// 右侧滚动条滚动，设置滚动值
		onGlobalScroll(value){
			const offsetVal = value - this.stepVal
			this.stepVal = value
			this.setViewportActive(this.activeViewportIndex + offsetVal, false)

			// 打开了全屏

			if (this.fullScreenIndex >= 0){
				this.fullScreenIndex += offsetVal;
				// this.$nextTick(() => {
				// 	cornerstone.resize(this.getEnabledElement(this.fullScreenIndex))
				// 	const el = this.getEnabledElement(this.fullScreenIndex)
				// 	if (cornerstone.getImage(el)) {
				// 		cornerstone.updateImage(el)
				// 	} 
				// })
			}
			
			this.printSelect.forEach((item, index) => {
				this.printSelect[index] = item + offsetVal
			})
			
			// 跳切换-在改动窗口size，在回到中间某个元素、无法显示，触发size让浏览器加载
			// this.$nextTick(() => {
			// 	if (this.getEnabledElement(this.stepVal)) {
			// 		const dom = this.getEnabledElement(this.stepVal).getElementsByClassName('cornerstone-canvas')[0];
			// 		if (dom && dom.clientHeight == 0){
			// 			// canvas 在隐藏后，调整浏览器窗口后，在显示（v-show="true"）会看不见，重新渲染 canvas
			// 			const myEvent = new Event('resize');
			// 			window.dispatchEvent(myEvent);
			// 		}
			// 	}
			// })
		},

		// 区域滚轮
		areaMousewheel(e){
			const dom = e.target.parentNode

			// 左侧缩略图滚动，不在执行
			const isThumbImageArea = (dom) => {
				if (!dom || !dom.parentNode) {
					return false
				}
				if (dom.getAttribute('id') == 'PrintThumb') {
					return true
				}
				return isThumbImageArea(dom.parentNode)
			}
			if (isThumbImageArea(dom)) {
				return
			}

			try {
				// 如果鼠标所在区域，存在 cornerstone
				if (cornerstone.getEnabledElement(dom)){
					const allStack = cornerstoneTools.getToolState(dom, 'stack')
					// 多张图像，不在做区域滚动切换
					if (allStack && allStack.data[0].imageIds.length > 1){
						return;
					}
				}
			} catch (error) {
				// error
			}
			e.stopPropagation();
			// 滚轮滚动
			const direction = e.deltaY > 0 ? 'down' : 'up';
			const max = this.viewports.length - 1;
			if (direction === 'down' && this.stepVal < max){
				
				// 如果打开全屏，一张一张滚动
				if (this.fullScreenIndex >= 0) {
					this.onGlobalScroll(this.stepVal + 1);
					return;
				}

				if ((this.stepVal + this.srollNum) > max) {
					return;
				}
				// 向下滚动
				this.onGlobalScroll(this.stepVal + this.srollNum)
				return;
			}else if (direction === 'up' && this.stepVal > 0){
				
				// 如果打开全屏，一张一张滚动
				if (this.fullScreenIndex >= 0) {
					this.onGlobalScroll(this.stepVal - 1);
					return;
				}
				// 向上滚动
				let curVal = this.stepVal - this.srollNum;
				this.onGlobalScroll(curVal < 0 ? 0 : curVal)
			}
		},
		// 点击翻页按钮
		onChangePage(isUp = 1) {
			let target = this.stepVal
			let stepLength = this.srollNum
			if (this.fullScreenIndex >= 0) stepLength = 1
			const max = this.viewports.length - 1;

			if (isUp) {
				target = this.stepVal - stepLength;
			} else {
				// 向下滚动
				target = this.stepVal + stepLength;
			}
			if (target > max) {
				return;
			}
			this.onGlobalScroll(target < 0 ? 0 : target)
		},
		// 双击视图
		onDblclickViewport(el, index){
			if (this.activeTool === 'TextMarker') return;

			const allStack = cornerstoneTools.getToolState(el, 'stack')
			if (!allStack) return;

			try {
				// 大于等于 0 代表是全屏的，做收起操作，则全屏
				this.fullScreenIndex = this.fullScreenIndex >= 0 ? -1 : index;

				// 有全屏显示时处理
				if (this.fullScreenIndex >= 0){
					this.fullScreenPointIndex = index;
					this.fullScreenPosition = this.stepVal; // 当前位置赋值
					this.stepVal = this.fullScreenIndex;    // 设置当前显示位置
				}else {
					this.stepVal = this.fullScreenPosition; // 还原回之前位置
					// 还原选中
					setTimeout(() => {
						if (this.printSelect.indexOf(this.fullScreenPointIndex) === -1) {
							this.printSelect.push(this.fullScreenPointIndex);
							this.activeViewportIndex = this.fullScreenPointIndex;
						}
					}, 200);
				}
				this.$nextTick(() => {
					// cornerstone.resize(el)
					const myEvent = new Event('resize');
					window.dispatchEvent(myEvent);
					this.setFontByWidthAndHeight(index)
				})
				this.$store.state.renderScroll = !this.$store.state.renderScroll

			} catch (error) {
				
			}
		},

		onDragadd(e){
			this.setViewportActive(e.moved.newIndex)
		},
		// 拖拽保存
		onDragSort() {
			if (!this.isMuch || this.isClinic){
				return
			}
			const param = {
				AccessionNumber: this.seriesInfo.sAccessionNumber,
				patientId: this.seriesInfo.sPatientID,
				studyDate: this.seriesInfo.iStudyDate,
				InstanceOrderText: ''
			}
			let len = this.showViewports.length
			for (let index = 0; index < len; index++) {
				const instance = this.showViewports[index]
				if (instance.sSOPInstanceUID) {
					param.InstanceOrderText += `(${instance.sSOPInstanceUID})`
				}
			}
			this.$Api.updateInstanceOrder(param)
		},
		triggerPrint(){
			this.printSelectSopUids = this.getSelectPrintInfo();
		},
		// 点击删除截图
		onClickDelScreen() {
			if (!this.printSelect.length){
				this.$message({
					type: 'warning',
					message: '请选择需要删除的截屏图'
				})
				return;
			}
			// 判断截图、检查时间，[SOPUID]
			const {noCaptrue, iStudyDate, list} = this.getSelectScreenIds()
			if (noCaptrue) {
				this.$message({
					type: 'warning',
					message: '只能删除截屏图'
				})
				return;
			}

			this.$MessageBox.confirm(`您确定要删除（${list.length}）张吗？`, '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
            }).then(() => {
				this.$Api.delScreen(
					{
						iStudyDate,
						list
					}
				).then(res => {
					if (res.success){
						this.$message({
							type: 'success',
							message: '删除成功!'
						})
                        this.printSelect = []
						// this.$refs['baseTools'].onLoadStudy()
                        const newViewports = this.viewports.filter(item => { return !list.includes(item.sSOPInstanceUID) })


                        if (this.fullScreenIndex != -1) {
                            // 全屏时候，删除 + 1 显示，如果大于图像数就 - 1 显示
                            let currentIdx = this.fullScreenIndex >= newViewports.length ? this.fullScreenIndex - 1 : this.fullScreenIndex

                            if (currentIdx == -1) {
                                // 删除全部了
                                this.fullScreenIndex = -1
                                this.stepVal = 0
                                this.onClickBox({column: 1, row: 1})
                            }else {
                                this.fullScreenIndex = currentIdx
                                this.activeViewportIndex = currentIdx
                                this.stepVal = currentIdx
                            }
                        }
						this.viewports = newViewports
						return
					}
					this.$message({
						type: 'error',
						message: res.msg
					})
				})
            })
		},
		// 获取选中截屏全部 id
		getSelectScreenIds() {
			let list = []
			let noCaptrue = false
			let iStudyDate = ''
			// 遍历打印的位置
			this.printSelect.forEach(idx => {
				// 通过位置拿到了 uid
				const item = this.viewports[idx];
				// 后加的截图判断
				if (item && item.isCaptrue) {
					list.push(item.sSOPInstanceUID)
				}
				iStudyDate =  item.iStudyDate; // item.dateTime;// TODO
			})
			return {noCaptrue, iStudyDate, list};
		},
		getSelectPrintInfo(){
			let params = []
			// 遍历打印的位置
			this.printSelect.forEach(idx => {
				// 通过位置拿到了 uid
				const item = this.viewports[idx];
				if (item) {
					// 不是截图，不要
					if (!item.isCaptrue) {
						return;
					}
					params.push({
						studyDate: item.iStudyDate + '',
						sopInstanceUID: item.sSOPInstanceUID,
						sRemarkCn: item.sRemarkCn
					})
				} 
			})

			return params
		},
		onClickIsShowRemark(val) {
			this.isShowRemark = val
			// 更新-storeage
			localStorage.setItem('setting-read-show-remark', JSON.stringify(this.isShowRemark));
			this.$store.commit('setReadShowRemark', this.isShowRemark);
		},
		// 修改 viwport 新的备注
		setRemark(remark) {
			// TODO 打开新的 tab 备注还是之前的
			// 解决方法1、修改 vuex 中的 seriesMap 中的备注
			// 解决方法2、重新请求 getlistByPatientId 获取新的数据
			// this.$store.state.seriesMap.get(studyId).instanceList.get(seriesId).data.instanceList[未知下标-要遍历InstanceId判断].sRemarkCn
			this.viewports[this.activeViewportIndex].sRemarkCn = remark.sRemarkCn;
			// console.log(this.$store.state.seriesMap);
			// console.log(this.viewports[this.activeViewportIndex]);
			// console.log(this.$store.state.seriesMap.get(
			// 		this.viewports[this.activeViewportIndex].sStudyInstanceUID
			// 	).instanceList.get(
			// 		this.viewports[this.activeViewportIndex].sSeriesInstanceUID
			// 	)
			// );
		},
		// 修改 viewport 新的图文标记
		setNewGraphic(graphics, sopInstanceUids) {
			const arrGraphic = graphics.split('|')
			const arrSopInstanceUid = sopInstanceUids.split('|')

			arrSopInstanceUid.forEach((uid, idx) => {
				const index = this.viewports.findIndex(item => {
					return item.sSOPInstanceUID == uid
				})
				if (index != -1) {
					this.viewports[index].iIsGraphic = arrGraphic[idx]
				}
			})
		},
		// 右键
		onMouseRight(e, hide = false) {
			if (hide) {
                this.$refs.contextmenu.hide();
                return;
            }
			this.saveImage.rightSelectElement = this.getEnabledElement(this.activeViewportIndex);
			// 在点击右键出现右键目录时，不会清除原选中状态
			this.isClickRight = true
			this.$refs.contextmenu.handleReferenceContextmenu(e);
		},
		onClickImageSave(isScreen = 0) {
			this.saveImage.element = isScreen ? document.getElementById(this.componentId) : this.saveImage.rightSelectElement;
			const downloadImage = (element, name = '') => {
				try {
					if (cornerstone.getImage(element).getCanvas) {
						const dataCanvas = cornerstone.getImage(element).getCanvas()
						const dataURLorigin = dataCanvas.toDataURL("image/jpeg", 1);
						const linkEl = document.createElement('a')
						linkEl.href = dataURLorigin;
						linkEl.download = name + '_' + new Date().toLocaleString() + '.jpg';
						linkEl.click();
						linkEl.remove();
					}else {
						// 原始图
						cornerstoneTools.SaveAs(element, name + '_' + new Date().toLocaleString() + '.jpg');
					}

				} catch (error) {
					this.$message({
						type: 'error',
						message: '图像另存出错'
					})
				}
			}

			const filename = this.$store.state.seriesInfo.sPatientName

			const saveImagesSequentially = async () => {
				const downloadImageDelay = 10;
				const delayDuration = 1000;

				for (let i = 0; i < this.printSelect.length; i++) {
					const idx = this.printSelect[i];
					const item = this.getEnabledElement(idx);
					downloadImage(item, filename);

					if ((i + 1) % downloadImageDelay === 0) {
						// 延迟
						await new Promise(resolve => setTimeout(resolve, delayDuration));
					}
				}
			};

			if (isScreen === 0) {
				downloadImage(this.saveImage.element, filename)
			} else if (isScreen === 2) {
				saveImagesSequentially()
				
			} else {
				this.saveImage.visible = true;
			}
		},
		// 点击图文标记
		onClickGraphic() {
			if (this.isClinic) {
				this.$message({
					type: 'success',
					message: '没有权限！'
				})
				return;
			}
			let params = {
				isReverse: '',
				isGraphic: '',
				sopInstanceUid: ''
			}
			let notCatrue = 0;
			// 遍历打印的位置
			this.printSelect.forEach(idx => {
				// 通过位置拿到了 uid
				const item = this.viewports[idx];
				if (!item.isCaptrue) {
					notCatrue += 1;
					return;
				}
				const status = (item.iIsGraphic == undefined || item.iIsGraphic == 0 || item.iIsGraphic == '0') ? 1 : 0; // iIsGraphic 一开始可能是 undefined
				params.isGraphic += status + '|'
				params.sopInstanceUid  += item.sSOPInstanceUID + '|'
				params.isReverse += '0|'
				params.patientId = item.sPatientID
				params.studyDate = item.iStudyDate
			})
			if (!params.sopInstanceUid || params.sopInstanceUid === 'undefined|') {
				this.$message.info('无法标记打印！')
				return;
			}
			// console.log(this.printSelect)
			// const seriesData = this.originSeries.find(item => {
			// 	return item.data
			// })
			// params.patientId = seriesData.data.sImgPatientId
			// params.studyDate = seriesData.data.sImgStudyDate
			params.isGraphic = params.isGraphic.substr(0, params.isGraphic.length - 1)
			params.sopInstanceUid = params.sopInstanceUid.substr(0, params.sopInstanceUid.length - 1)
			params.isReverse = params.isReverse.substr(0, params.isReverse.length - 1)

			this.$Api.editSignGraph(params).then(res => {
				if (res.success){
					this.$message.closeAll();
					this.$message({
						type: 'success',
						message: '操作成功'
					})
					if (notCatrue) {
						setTimeout(() => {
							this.$message.info(`原始图无法标记，选中数量：${notCatrue}`)
						}, 200);
					}
					this.setNewGraphic(params.isGraphic, params.sopInstanceUid)
					return
				}
				this.$message({
					type: 'error',
					message: res.msg
				})
			})
		},
		copyAllRemark() {
			const allRmkStr = []
			this.viewports.forEach(element => {
				if (element.sRemarkCn) allRmkStr.push(element.sRemarkCn)
			});

			let oInput = document.createElement('textarea')
			oInput.value = allRmkStr.join('\n')
			document.body.appendChild(oInput)
			oInput.select()
			document.execCommand("Copy")
			this.$message({
				message: '复制成功',
				type: 'success'
			})
			oInput.remove()

		}
	}
}
</script>

<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
}
.read-scroll{
	width: 20px;
	position: relative;
	right: -1px;
	background: #ffffff80;
	// box-shadow: rgb(0 0 0 / 10%) 0px 1px 3px 0px, rgb(0 0 0 / 6%) 0px 1px 2px 0px;
}
.read-bottom-bar {
	display: flex;
	.text-yellow {
		color: yellow;
	}
}
</style>