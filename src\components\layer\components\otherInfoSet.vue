<template>
    <div class="c-content">
        <ul>
            <li>
                <span>鼠标滚轮(按下)：</span>
                <el-select v-model="mouseConfigs.mid"  size="small">
                    <el-option
                    v-for="item in toolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </li>
            <li>
                <span style="width: 125px">鼠标右键：</span>
                <el-select v-model="mouseConfigs.right"  size="small">
                    <el-option
                    v-for="item in toolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>
            </li>
            <li></li>
            <li class="checkbox">
                <el-checkbox v-model="toolRepeatUse">标注重复使用</el-checkbox>
                <el-checkbox v-model="toolNewImageUse">翻页后标注保持测量</el-checkbox>
                <el-checkbox v-model="toolSync">标注同步</el-checkbox>

                <el-checkbox v-model="scrollMouseWheelLoop">翻页循环</el-checkbox>

                <el-checkbox v-model="zoomToCenter">缩放功能以图像中心放大</el-checkbox>
                
            </li>

        </ul>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'otherInfoSet',
    data() {
        return {
            toolRepeatUse: false,                 // 当前值
            toolSync: false,
            toolNewImageUse: false,
            scrollMouseWheelLoop: true,
            zoomToCenter: true,
            mouseConfigs: {},
            toolOptions: [
                {
                    value: 'Wwwc',
                    label: '调节窗宽窗位'
                },
                {
                    value: 'Pan',
                    label: '平移'
                },
                {
                    value: 'Zoom',
                    label: '缩放'
                },
                {
                    value: 'Magnify',
                    label: '透镜'
                },
                {
                    value: 'Eraser',
                    label: '橡皮擦'
                },
                {
                    value: 'Airtools',
                    label: '选择(无)'
                }
            ],
            storageKey: {
                toolRepeatUse: 'configs-toolRepeatUse',
                mouseConfigs: 'configs-mouse',
                toolSync: 'configs-toolSync',
                toolNewImageUse: 'configs-toolNewImageUse',
                scrollMouseWheelLoop: 'configs-scrollMouseWheelLoop',
                zoomToCenter: 'configs-zoomToCenter',
            }
        }
    },
    mounted() {
        this.scrollMouseWheelLoop = getConfigByStorageKey(this.storageKey.scrollMouseWheelLoop);
        this.toolNewImageUse = getConfigByStorageKey(this.storageKey.toolNewImageUse);
        this.toolRepeatUse = getConfigByStorageKey(this.storageKey.toolRepeatUse);
        this.toolSync = getConfigByStorageKey(this.storageKey.toolSync);
        this.mouseConfigs = getConfigByStorageKey(this.storageKey.mouseConfigs);
        this.zoomToCenter = getConfigByStorageKey(this.storageKey.zoomToCenter);
    },
    methods: {
        onClickSave(){
            this.setScrollMouseWheelLoop();
            this.setToolRepeatUse();
            this.setToolSync();
            this.setToolNewImageUse();
            this.setMouseConfigs();

            localStorage.setItem(this.storageKey.zoomToCenter, JSON.stringify(this.zoomToCenter))

            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        setToolRepeatUse(){
            localStorage.setItem(this.storageKey.toolRepeatUse, JSON.stringify(this.toolRepeatUse))
            this.$store.commit('GET_TOOLREPEATUSE')
        },
        setToolSync(){
            localStorage.setItem(this.storageKey.toolSync, JSON.stringify(this.toolSync))
            this.$store.commit('GET_TOOLSYNC')
        },
        setToolNewImageUse(){
            localStorage.setItem(this.storageKey.toolNewImageUse, JSON.stringify(this.toolNewImageUse))
            this.$store.commit('GET_TOOLNEWIMAGEUSE')
        },
        setMouseConfigs(){
            localStorage.setItem(this.storageKey.mouseConfigs, JSON.stringify(this.mouseConfigs))
            // 获取最新的鼠标右键滚轮事件值
            this.$store.commit('GET_MOUSEEVENTS')
        },
        setScrollMouseWheelLoop() {
            localStorage.setItem(this.storageKey.scrollMouseWheelLoop, JSON.stringify(this.scrollMouseWheelLoop))
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    height: 100%;
    display: flex;
    flex-direction: column;
    > ul{
        flex: 1;
        .checkbox{
            display: flex;
            flex-direction: column;
            align-items: baseline;
            label{
                display: inline-block;
                height: 40px;
                line-height: 40px;
            }
        }
        li{
            height: 50px;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            span{
                padding-right: 10px;
            }
        }
    }
    > footer{
        height: 40px;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}
</style>