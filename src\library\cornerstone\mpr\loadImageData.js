import cornerstone from '$library/cornerstone/cornerstone';
import { requestPoolManager } from '$library/cornerstone/cornerstoneTools';
import insertSlice from '$library/vtk/lib/data/insertSlice.js';
import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js';

// TODO: If we attempt to load multiple imageDataObjects at once this will break.
export default function loadImageDataProgressively(imageDataObject) {
	if (imageDataObject.loaded || imageDataObject.isLoading) {
		// Returning instantly resolved promise as good to go.
		// Returning promise to be resolved by other process as loading.
		return;
	}

	const {
		imageIds,
		vtkImageData,
		metaDataMap,
		sortedDatasets,
	} = imageDataObject;

	const imageId0 = imageIds[0];

	const seriesModule = cornerstone.metaData.get(
		'generalSeriesModule',
		imageId0
	);

	// If no seriesModule is present will default to linear scaling function.
	const modality = seriesModule && seriesModule.modality;
	let modalitySpecificScalingParameters;

	if (modality === 'PT') {
		modalitySpecificScalingParameters = getPatientWeightAndCorrectedDose(
			imageId0
		);
	}

	imageDataObject.isLoading = true;

	// This is straight up a hack: vtkjs cries when you feed it data with a range of zero.
	// So lets set the first voxel to 1, which will be replaced when the first image comes in.
	const scalars = vtkImageData.getPointData().getScalars();
	const scalarData = scalars.getData();

	scalarData[0] = 1;

	const range = {
		max: Number.NEGATIVE_INFINITY,
		min: Number.POSITIVE_INFINITY,
	};

	const numberOfFrames = imageIds.length;
	let numberProcessed = 0;

	const reRenderFraction = numberOfFrames / 5;
	let reRenderTarget = reRenderFraction;
	/**
	 * 加载图像错误回调
	 * @param {*} error 
	 */
	const insertPixelDataErrorHandler = error => {
		numberProcessed++;
		imageDataObject._publishPixelDataInsertedError(error);

		if (numberProcessed === numberOfFrames) {
			// Done loading, publish complete and remove all subscriptions.
			imageDataObject._publishAllPixelDataInserted();
		}
	};
	/**
	 * 加载图像成功后执行的回调
	 * @param {*} image cornerstone 图像对象
	 */
	const insertPixelData = image => {
		const { imagePositionPatient } = metaDataMap.get(image.imageId);
		const sliceIndex = sortedDatasets.findIndex(
			dataset => dataset.imagePositionPatient === imagePositionPatient
		);

		insertSlice(
			vtkImageData,
			sliceIndex,
			image,
			modality,
			modalitySpecificScalingParameters
		);

		// if (max > range.max) {
		// 	range.max = max;
		// }

		// if (min < range.min) {
		// 	range.min = min;
		// }

		if (typeof image.maxPixelValue === 'number' &&  image.maxPixelValue > range.max) {
			range.max = image.maxPixelValue
		}

		if (typeof image.minPixelValue === 'number' &&  image.minPixelValue < range.min) {
			range.min = image.minPixelValue
		}

		const dataArray = vtkImageData.getPointData().getScalars();
		// console.log(range)
		dataArray.setRange(range, 1);
		numberProcessed++;

		if (numberProcessed > reRenderTarget) {
			reRenderTarget += reRenderFraction;

			vtkImageData.modified();
		}

		imageDataObject._publishPixelDataInserted(numberProcessed);
        
        setTimeout(() => {
		if (cornerstone.imageCache.getImageLoadObject(image.imageId)){
			const index = imageIds.indexOf(image.imageId)
			if (index > 2 && index < imageIds.length - 2) { // 需要取图片信息
                    cornerstone.imageCache.removeImageLoadObject(image.imageId) 
                }
            }
        }, 2000);  
            
		if (numberProcessed === numberOfFrames) {
			// Done loading, publish complete and remove all subscriptions.
			imageDataObject._publishAllPixelDataInserted();
		}
	};

	prefetchImageIds(imageIds, insertPixelData, insertPixelDataErrorHandler);
}

const requestType = 'prefetch';
const preventCache = false;
/**
 * 预加载图像序列
 * @param {*} imageIds 序列id
 * @param {*} insertPixelData 成功回调
 * @param {*} insertPixelDataErrorHandler 错误回调
 */
function prefetchImageIds(
	imageIds,
	insertPixelData,
	insertPixelDataErrorHandler
) {
	imageIds.forEach(imageId => {
		requestPoolManager.addRequest(
			{},
			imageId,
			requestType,
			preventCache,
			insertPixelData,
			insertPixelDataErrorHandler
		);
	});

	requestPoolManager.startGrabbing();
}
