@font-face {
  font-family: "iconfont"; /* Project id 1706250 */
  src: url('iconfont.woff2?t=1684306419689') format('woff2'),
       url('iconfont.woff?t=1684306419689') format('woff'),
       url('iconfont.ttf?t=1684306419689') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconduibi_o:before {
  content: "\eb5e";
}

.icontop-bottom:before {
  content: "\eb09";
}

.iconquxian:before {
  content: "\e636";
}

.iconleft-right:before {
  content: "\eb0d";
}

.iconvr:before {
  content: "\e87a";
}

.iconcutPage:before {
  content: "\e66a";
}

.iconmipRotate:before {
  content: "\e64d";
}

.iconbodyMeasure:before {
  content: "\e643";
}

.iconlink:before {
  content: "\e78c";
}

.iconunlink:before {
  content: "\e8c6";
}

.iconcelianggongju1:before {
  content: "\e608";
}

.iconai69:before {
  content: "\e6bb";
}

.icongaibianfangxiang:before {
  content: "\edda";
}

.iconfull-screen:before {
  content: "\ea0c";
}

.iconexit-full-screen:before {
  content: "\ea0d";
}

.iconduopingmianzhongjian1:before {
  content: "\ea32";
}

.iconportrait-film:before {
  content: "\e9cc";
}

.iconyonghuxinxi:before {
  content: "\e7df";
}

.iconyanseziduan:before {
  content: "\e63d";
}

.iconloop:before {
  content: "\ea1d";
}

.iconzhongjian:before {
  content: "\e654";
}

.iconxia:before {
  content: "\e653";
}

.iconshang:before {
  content: "\e652";
}

.iconjiantou:before {
  content: "\e651";
}

.iconwenben:before {
  content: "\e650";
}

.iconyuanquan:before {
  content: "\e64c";
}

.iconhou:before {
  content: "\e648";
}

.iconqian:before {
  content: "\e649";
}

.iconzuo:before {
  content: "\e64a";
}

.iconyou:before {
  content: "\e64b";
}

.iconyidong:before {
  content: "\e645";
}

.icontiaochuang:before {
  content: "\e642";
}

.iconsanwei:before {
  content: "\e633";
}

.iconshezhi1:before {
  content: "\e611";
}

.iconban:before {
  content: "\e623";
}

.iconzhongxin:before {
  content: "\e935";
}

.iconai233-01-copy:before {
  content: "\e9f5";
}

.iconai212-01-copy:before {
  content: "\e9f6";
}

.iconai212:before {
  content: "\e6c8";
}

.iconai233:before {
  content: "\e6dd";
}

.iconstop-circle:before {
  content: "\e86c";
}

.iconpause-01-copy:before {
  content: "\e9f4";
}

.iconpause:before {
  content: "\e7d9";
}

.iconth-large:before {
  content: "\e8b7";
}

.iconreorder:before {
  content: "\e802";
}

.iconstop1:before {
  content: "\e85a";
}

.iconth:before {
  content: "\e87b";
}

.iconeraser:before {
  content: "\e6ca";
}

.iconchuizhifanzhuan:before {
  content: "\e661";
}

.iconshuipingfanzhuan:before {
  content: "\e662";
}

.iconrotate-anticlockwise:before {
  content: "\e96b";
}

.iconrotate-clockwise:before {
  content: "\e971";
}

.iconmidpoint:before {
  content: "\e92c";
}

.iconbrush:before {
  content: "\e92e";
}

.iconreset:before {
  content: "\e92f";
}

.iconadjust-0:before {
  content: "\e930";
}

.iconellipse:before {
  content: "\e931";
}

.iconrotate:before {
  content: "\e932";
}

.iconrectangle:before {
  content: "\e933";
}

.iconarrows-1:before {
  content: "\e846";
}

.iconpre-frame:before {
  content: "\e9f3";
}

.icondedent-right:before {
  content: "\e6b3";
}

.icondedent-left:before {
  content: "\e9f2";
}

.iconangle:before {
  content: "\e8eb";
}

.iconturn:before {
  content: "\e921";
}

.iconplay-1:before {
  content: "\e923";
}

.iconcobb:before {
  content: "\e924";
}

.iconlens:before {
  content: "\e925";
}

.iconct-value:before {
  content: "\e926";
}

.iconfuse:before {
  content: "\e927";
}

.iconzoom:before {
  content: "\e928";
}

.iconinvert:before {
  content: "\e929";
}

.iconline:before {
  content: "\e92a";
}

.iconnext-frame:before {
  content: "\e92b";
}

