<template>
	<div class="c-mpr-content pc-body">
		<section class="c-left">
			<div v-if="volumes && volumes.length">
				<div class="c-item-01">
					<View3D
					ref="view3D"
					:volumes="volumes"
					:onCreated="storeApi(0)"
					/>
				</div>
			</div>
		</section>
		<Percentage :visible="loading" :total="total" :loadCount="loadCount"/>
	</div>
</template>
<script>
import { View3D, vtkVolumeController } from "$library/vtk";
import tryGetVtkVolumeForSeriesNumber from "$library/cornerstone/mpr/tryGetVtkVolumeForSeriesNumber.js";
import { loadFrameImage } from "$library/cornerstone/function/getImageIds.js";

import Percentage from '$components/tools/Percentage';

import vtkVolume from "vtk.js/Sources/Rendering/Core/Volume";
import vtkVolumeMapper from "vtk.js/Sources/Rendering/Core/VolumeMapper";
import vtkColorTransferFunction from 'vtk.js/Sources/Rendering/Core/ColorTransferFunction';
import vtkPiecewiseFunction from 'vtk.js/Sources/Common/DataModel/PiecewiseFunction';
import vtkBoundingBox from 'vtk.js/Sources/Common/DataModel/BoundingBox';

// 存放体地方
import appState from '$library/cornerstone/mpr/store/appState.js';
export default {
	name: "ViewVtkVR",
	components: {
		View3D,
		Percentage
	},
    props: {
        series: {
			type: Object,
			default: () => {}
		},
		seriesId: {         // 这个应该是 studyUID
			type: String,
			default: ''
		},
    },
	data() {
		return {
			uid: null,
			volumes: [],
			apis: [],
			total: 0,
			loadCount: 0,
			loading: false,
		};
	},
	async mounted(){
		const uids = this.series && this.series.uids;
		if (!uids) return;

		this.volumes = [];
		this.studyUID  = this.seriesId;
		this.seriesUID = uids;

		const imageIds = this.setVolumesSeries(this.studyUID, this.seriesUID);

		await loadFrameImage(imageIds);

		this.loadData(imageIds);
	},
	beforeDestroy() {
		this.volumes = [];
	},
	methods: {
		/**
         * 初始体相应序列。如果有体返回空。没有体，返回需要下载的序列 id
         */
        setVolumesSeries(studyUID, seriesUID) {
            
            if (!studyUID && !seriesUID) {
                return []
            }

            // 没有这个体信息，设置空对象
            if (!appState[seriesUID]) {
                appState[seriesUID] = {}
            }

            // 没有体数据, 添加体数据相应的序列
            if (!appState[seriesUID].vtkVolumes) {
                const instanceList = this.$store.state.seriesMap.get(studyUID).instanceList
                const imageIds = instanceList.get(seriesUID).imageIds

                appState[seriesUID].series = imageIds

                return imageIds
            }

            return []
        },
		// 加载的图像数据
		loadData(imageIds) {
			this.$nextTick(() => {
				this.loading = true
			})
			// 加载序列
			this.total = imageIds.length
			this.loadCount = 0

			if (window.configs.startLimitDownload) {
				const requestType = 'prefetch';
				const preventCache = false;
				
				if (this.total === 0) {
					this.total = 1
					this.loadCount = 1
					setTimeout(() => {
						this.loadVolumeData()
					}, 100);
				}

				imageIds.forEach(imageId => {
					cornerstoneTools.requestPoolManager.addRequest(
						{},
						imageId,
						requestType,
						preventCache,
						() => {
							this.loadCount += 1
							if (this.loadCount >= this.total) {
								setTimeout(() => {
									this.loadVolumeData()
								}, 100);
							}
						},
						(imageId) => {
							console.log('下载错误，错误请求头携带获取最新图像..')
						}
					);
				});
				cornerstoneTools.requestPoolManager.startGrabbing();
			}else {
				const imagePromise = imageIds.map((imageId) => {
					return cornerstone.loadAndCacheImage(imageId).then(() => {
						this.loadCount += 1
					});
				});

				Promise.all(imagePromise).then(() => {

					// 图已经加载过了，让进度走到 100%
					if (this.total === 0) {
						this.total = 1
						this.loadCount = 1
					}
					// 如果体已经有不需要加载图的时候，视图会卡一下，加个 setTimeout 让 UI 先渲染
					setTimeout(() => {
						this.loadVolumeData()
					}, 100);
				});
			}


		},
		async loadVolumeData() {

			const { vtkVolume: vtkVolumeObj } = await tryGetVtkVolumeForSeriesNumber(this.seriesUID);

			const imageData = vtkVolumeObj.vtkImageData;

			const lookupTable = vtkColorTransferFunction.newInstance();
			const piecewiseFunction = vtkPiecewiseFunction.newInstance();

			const dataRange = imageData
			.getPointData()
			.getScalars()
			.getRange();

			const mapper = vtkVolumeMapper.newInstance();
			const actor = vtkVolume.newInstance();
			
			mapper.setInputData(imageData);
			
			mapper.setMaximumSamplesPerRay(2000);
			mapper.setSampleDistance(1);

			actor.setMapper(mapper);

			// Configuration
			const sampleDistance =
			0.7 *
			Math.sqrt(
			imageData
				.getSpacing()
				.map((v) => v * v)
				.reduce((a, b) => a + b, 0)
			);
			mapper.setSampleDistance(sampleDistance);

			actor.getProperty().setInterpolationTypeToLinear();

			// - distance in world coordinates a scalar opacity of 1.0
			actor.getProperty().setScalarOpacityUnitDistance(
				0,
				vtkBoundingBox.getDiagonalLength(imageData.getBounds()) /
				Math.max(...imageData.getDimensions())
			);

			actor.getProperty().setGradientOpacityMinimumValue(0, 0);
			actor.getProperty().setGradientOpacityMaximumValue(0, (dataRange[1] - dataRange[0]) * 0.05);
			
			actor.getProperty().setRGBTransferFunction(0, lookupTable);
			actor.getProperty().setScalarOpacity(0, piecewiseFunction);

			// - Use shading based on gradient
			actor.getProperty().setShade(true);
			actor.getProperty().setUseGradientOpacity(0, true);
			// // - generic good default
			actor.getProperty().setGradientOpacityMinimumOpacity(0, 0.0);
			actor.getProperty().setGradientOpacityMaximumOpacity(0, 1.0);
			actor.getProperty().setAmbient(0.2);
			actor.getProperty().setDiffuse(0.7);
			actor.getProperty().setSpecular(0.3);
			actor.getProperty().setSpecularPower(8.0);

			this.volumes = [actor];

			this.loading = false;
		},
		// 保存控件Api
		storeApi(viewportIndex) {
			return (api) => {
				this.apis[viewportIndex] = api;

				const background = [0, 0, 0]
				const controllerWidget = vtkVolumeController.newInstance({
					size: [400, 150],
					rescaleColorMap: true,
				});

				const actor = api.volumes[0]

				const renderWindow = api.genericRenderWindow.getRenderWindow();
				renderWindow.getInteractor().setDesiredUpdateRate(30);

				const isBackgroundDark = background[0] + background[1] + background[2] < 1.5;
				controllerWidget.setContainer(this.$refs.view3D.$el);
				controllerWidget.setupContent(renderWindow, actor, isBackgroundDark);
				console.log(controllerWidget)
			};
		},
	},
};
</script>
<style lang="scss" scoped>
.c-mpr-content {
	height: 100%;
	display: flex;
	overflow: hidden;
	.c-left {
		background: #eee;
		flex: 1;
		display: flex;
		background-color: black;
		> div{
			height: 100%;
			width: 100%;
			display: flex;
		}
		.c-item-01 {
			flex: 2;
		}
		.c-item-02 {
			flex: 1;
			display: flex;
			flex-direction: column;
			> div {
				height: 50%;
			}
		}
	}
	.c-right {
		width: 68px;
		height: 100%;
		background: white;
	}
}
.c-slider-default{
	padding-bottom: 15px;
	padding-top: 0px !important;

	h6{
		padding: 6px 0px;
		margin-bottom: 14px;
		background: #6294B7;
		color: white;
	}
	.el-slider{
		padding-left: 15px;
	}
}
</style>
