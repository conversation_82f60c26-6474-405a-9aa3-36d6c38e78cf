<template>
    <div class="ViewportOverlay" saveimage="true" style="pointer-events: none;">
        <div :class="['i-top-left', 'i-overlay', textColor]">
            <p v-for="(text, index) in leftTop" :key="index">
                {{ text }}
            </p>
        </div>
        <div :class="['i-top-right', 'i-overlay', textColor]">
            <p v-for="(text, index) in rightTop" :key="index">
                {{ text }}
            </p>
        </div>
        <div :class="['i-bottom-right', 'i-overlay', textColor]">
            <p v-for="(text, index) in rightBottom" :key="index">
                {{ text }}
            </p>
        </div>
        <div :class="['i-bottom-left', 'i-overlay', textColor]">
            <p v-for="(text, index) in leftBottom" :key="index">
                {{ text }}
            </p>
        </div>
    </div>
</template>
<script>
import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js';
import customLocalMeta from '$library/cornerstone/mpr/store/customLocalMeta.js';
export default {
    props: {
        imageId: {
            type: String,
            default: null
        },
        modality: {
            type: String,
            default: null
        },
        element: {
            type: [Array, HTMLCollection, HTMLDivElement],
            default: () => { [] }
        },
        renderOverlay: {
            type: Object,
            default: () => {
                return {
                    scale: null,
                    windowCenter: null,
                    windowWidth: null
                }
            }
        },
        imageIndex: 0,
        stackSize: 0,
        invert: {
            type: Boolean,
            default: false
        },
        initLoad: {
            type: Boolean,
            default: false
        },
        thickness: {
            default: ''
        },
        volumes: { 
            type: Array,
            default: () => []
        },
        seriesUID: {
            default: ''
        }
    },
    data() {
        return {
            info: {
                ManufacturersModelName: '',
                StudyID: '',
                SeriesNumber: '',
                sliceLocation: '',
                instanceNumber: '',
                wwwc: '',
                ImagePositionPatient: '',
                XRayTubeCurrent: '',
                Exposure: '',
                KVP: '',
                InstitutionName: '',
                AccessionNumber: '',
                PatientsName: '',
                PatientsSexAgeID: '',
                PatientsSex: '',
                PatientsAge: '',
                PatientID: '',
                PatientsBirthDate: '',
                StudyDate: '',
                StudyTime: '',
                AcquisitionTime: '',
                PatientPosition: '',
                SeriesDescription: '',
                SeriesDate: '',
                SeriesTime: '',
                Modality: '',
                sliceThickness: '',
                zoom: '',
                PatientsName: '',
                PercentPhaseFieldOfView: '',
                SequenceName: '',
                RepetitionTime: '',
                EchoTime: '',
                Radiopharmaceutical: '',
                RadionuclideName: '',
                TracerName: '',
                BatchDescription: ''
            },
            showWwwc: true,  // 控制隐藏显示 wwwc 显示
            loadOnce: false,
            only: '',
            vtkInvert: false,
            weightAndCorrectedDose: {
                patientWeight: null,
                correctedDose: null
            }
        }
    },
    mounted() {
        this.only = this.$fun.onlyValue().replace(/-/g, '');
    },
    watch: {
        imageId: {
            handler(later) {
                requestAnimationFrame(() => {
                    if (later != null) {
                        // cornerstone 渲染
                        if (this.element != null) {
                            this.$nextTick(() => {
                                if (!this.loadOnce) {
                                    this.loadTags();
                                    this.loadOnce = true;
                                }
                                this.getImagePlaneModule();
                            });
                        }
                        // vtk 渲染
                        if (this.volumes[0]) {
                            this.$nextTick(() => {
                                if (!this.loadOnce) {
                                    this.loadTags()
                                    
                                    // 判断反片
                                    setTimeout(() => {
                                        const rgbTransferFunction = this.volumes[0].getProperty().getRGBTransferFunction(0)
                                        let p = []
                                        rgbTransferFunction.getNodeValue(0, p)
                                        if ( p[1] === 1 && p[2] === 1 && p[3] === 1 ) {
                                            this.$emit('update:invert', true)
                                            this.vtkInvert = true
                                        }
                                    }, 600)
                                    
                                    this.loadOnce = true
                                }
                            });
                        }
                    }
                })
            },
            immediate: true
        },
        initLoad(later) {
            if (!later) {
                this.$nextTick(() => {
                    this.loadTags();
                    this.getImagePlaneModule();
                })
            }

        },
        'renderOverlay.scale': {
            handler() {
                if (!this.renderOverlay.scale) {
                    this.info.zoom = '';
                    return '';
                };
                const zoom = `Zoom:${this.renderOverlay.scale.toFixed(4)}`;
                this.info.zoom = zoom;
            },
            immediate: true,
        },
        instanceNumber: {
            handler() {
                requestAnimationFrame(() => {
                    // 当前图像数/总数
                    if (this.instanceNumber.total) {
                        this.info.instanceNumber = `Im:${this.instanceNumber.index}/${this.instanceNumber.total}`;
                    } else {
                        this.info.instanceNumber = '';
                    }
                })
            },
            immediate: true
        },
        wwwc: {
            handler() {
                requestAnimationFrame(() => {

                    if (!this.wwwc.ww) {
                        this.info.wwwc = '';
                        return '';
                    }
                    let ww = this.wwwc.ww;
                    let wl = this.wwwc.wl;
                    let str = `W:${Number(ww).toFixed(1)}/L:${Number(wl).toFixed(1)}`;

                    if (this.modality === 'PT') {
                        // 没有获取就获取，vtk渲染的再次获取，需要重新下图，会读取不到
                        let imageId = this.imageId
                        if (this.seriesUID && customLocalMeta.data[this.seriesUID]) {
                            imageId = `mpr:${this.seriesUID}`
                            customLocalMeta.setImageIdMetaData(imageId)
                        }
                        if (!this.weightAndCorrectedDose.patientWeight || !this.weightAndCorrectedDose.correctedDose) {
                            const { patientWeight, correctedDose } = getPatientWeightAndCorrectedDose(imageId)
                            this.weightAndCorrectedDose.patientWeight = patientWeight
                            this.weightAndCorrectedDose.correctedDose = correctedDose
                        }
                        if (this.weightAndCorrectedDose.patientWeight && this.weightAndCorrectedDose.correctedDose) {
                            const { u, l } = this.$fun.transformVoiWLToUL(ww, wl, imageId)
                            
                            ww = u
                            wl = l

                            str = `U:${ww.toFixed(2)}/L:${wl.toFixed(2)}`;
                        } else {
                            str = `W:${ww.toFixed(2)}/L:${wl.toFixed(2)}`;
                        }
                    }
                    this.info.wwwc = str;
                })
            },
            immediate: true
        },
        modality: {
            handler() {
                this.info.Modality = this.modality;
                // 融合判断
                if (this.info.isMpr) {
                    const layer = cornerstone.getLayers(this.element);
                    if (layer && layer.length) {
                        this.info.Modality = 'FUSE';
                    }
                }
            },
            immediate: true
        }
    },
    computed: {
        // 获取当前设备四个角配置值
        modalityTagItems() {
            // 使用计算属性，在修改配置后能同步变化。如卡顿，可以在初始化时获取。
            const modalityTagItems = this.$store.state.modalityTagItems[this.modality] || this.$store.state.modalityTagItems['default'];
            return modalityTagItems;
        },
        // 调窗
        wwwc() {
            return { ww: this.renderOverlay.windowWidth, wl: this.renderOverlay.windowCenter }
        },
        // 当前数/总数
        instanceNumber() {
            return { index: this.imageIndex + 1, total: this.stackSize }
        },

        // 四个角 html 项
        leftTop() {
            return this.modalityTagItems.leftTop
                .map(item => this.info[item.value]).filter(i => i != null);
        },
        rightTop() {
            return this.modalityTagItems.rightTop
                .map(item => this.info[item.value]).filter(i => i != null);
        },
        rightBottom() {
            return this.modalityTagItems.rightBottom
                .map(item => this.info[item.value]).filter(i => i != null);
        },
        leftBottom() {
            return this.modalityTagItems.leftBottom
                .map(item => this.info[item.value]).filter(i => i != null);
        },
        textColor() {
            if (this.invert || this.vtkInvert) return 'black'
            return 'white'
        }
    },
    methods: {
        // dicom tag 值
        async loadTags() {

            let image = null

            if (this.element) {
                image = cornerstone.getImage(this.element)
            }else if (this.volumes[0]) {
                image = await cornerstone.loadImage(this.imageId)
            }

            if (!image) {
                return
            }

            const data = image.data;
            this.info.isMpr = image.isMpr || false;
            this.info.Modality = this.modality;

            // 融合判断
            if (this.info.isMpr) {
                const layer = cornerstone.getLayers(this.element);
                if (layer && layer.length) {
                    this.info.Modality = 'FUSE';
                }
            }

            if (this.volumes.length == 2) {
                this.info.Modality = 'FUSE'
            }


            if (data) {
                this.info.ManufacturersModelName = data.string('x00081090') || '';
                this.info.StudyID = `Ex:${data.string('x00200010') || ''}`;
                this.info.SeriesNumber = `Se:${data.string('x00200011') || ''}`;

                this.info.XRayTubeCurrent = `XR:${data.string('x00181151') || ''}`;
                this.info.Exposure = `mA:${data.string('x00181152') || ''}`;
                this.info.KVP = `KV:${data.string('x00531066') || ''}`;

                // this.info.InstitutionName =  data.string('x00080080') || '';
                this.info.AccessionNumber = data.string('x00080050') || '';
                // this.info.PatientsName = data.string('x00100010') || '';
                this.info.PatientsSexAgeID = `${data.string('x00100040') || ''} ${data.string('x00101010') || ''} ${data.string('x00100020') || ''}`;

                this.info.PatientsSex = data.string('x00100040') || '';
                this.info.PatientsAge = data.string('x00101010') || '';
                this.info.PatientID = data.string('x00100020') || '';


                this.info.PatientsBirthDate = data.string('x00100030') || '';
                this.info.StudyDate = data.string('x00080020') || '';
                
                this.info.StudyTime = handleTimeStr(data.string('x00080030') || '')
                this.info.AcquisitionTime = handleTimeStr(data.string('x00080032') || '');

                this.info.SeriesDescription = data.string('x0008103e') || '';

                this.info.SeriesDate = data.string('x00080021') || '';
                this.info.SeriesTime = handleTimeStr(data.string('x00080031') || '');

                // this.info.Modality = data.string('x00080060') || '';
                // this.info.SliceThickness = data.string('x00150050') || '';
                // this.info.SliceLocation = `SP:${data.string('x00201041') || ''}`;

                this.info.PercentPhaseFieldOfView = data.string('x00180094') || '';
                this.info.SequenceName = `SE:${data.string('x00180024') || ''}`;
                this.info.RepetitionTime = `TR:${data.string('x00180080') || ''}`;
                this.info.EchoTime = `TE:${data.string('x00180081') || ''}`;

                // PT 药物
                const radiopharmaceuticalInfo = data.elements.x00540016
                if (radiopharmaceuticalInfo) {
                    const dataSet = radiopharmaceuticalInfo.items[0].dataSet
                    this.info.Radiopharmaceutical = dataSet.string('x00180031') || ''
                }

                
                this.info.RadionuclideName = data.string('x0009103e') || '';
                this.info.TracerName = data.string('x00091036') || '';
                this.info.BatchDescription = data.string('x00091037') || '';
                

                // vtk 渲染的不要这种 tag
                if (!this.volumes[0]) {
                    this.info.PatientPosition = `Position:${data.string('x00185100') || ''}`; // TODO 重建后是否需要变化
                }

                try {
                    // 这种方式，有些图像没办法解析
                    this.info.InstitutionName = decodeURI(escape(data.string('x00080080')));
                    this.info.PatientsName = decodeURI(escape(data.string('x00100010')));
                } catch (error) {
                    // 这种方式解析有些乱
                    await this.gbkconvert(escape(data.string('x00080080'))).then(res => {
                        this.info.InstitutionName = res;
                    })
                    await this.gbkconvert(escape(data.string('x00100010'))).then(res => {
                        this.info.PatientsName = res;
                    })
                    this.clearNode()
                }
            }
        },
        clearNode() {
            setTimeout(() => {
                let node = document.getElementsByClassName('gbkconvert' + this.only)
                for (let item of node) {
                    item.remove()
                }
                window['gbkconvertCb' + this.only] = null
            }, 4000);
        },
        gbkconvert(gbkStr) {
            return new Promise(resolve => {
                const script = document.createElement('script');
                script.className = 'gbkconvert' + this.only;
                script.src = 'data:text/javascript;charset=gbk,gbkconvertCb' + this.only + '("' + gbkStr + '");';
                document.body.appendChild(script);
                window['gbkconvertCb' + this.only] = (res) => {
                    setTimeout(() => {
                        resolve(res);
                    }, 100);
                };
            })
        },
        // 获取元数据
        getImagePlaneModule() {
            const imagePlaneModule = cornerstone.metaData.get('imagePlaneModule', this.imageId) || {};
            if (imagePlaneModule) {
                this.info.ImagePositionPatient = 'SP:' + (imagePlaneModule.sliceLocation != undefined && imagePlaneModule.sliceLocation != null ? Number(imagePlaneModule.sliceLocation).toFixed(2) : '');

                if (this.info.ImagePositionPatient === 'SP:') {
                    this.info.ImagePositionPatient = '';
                }

                this.info.sliceLocation = 'SN:' + (imagePlaneModule.sliceLocation != undefined && imagePlaneModule.sliceLocation != null ? Number(imagePlaneModule.sliceLocation).toFixed(3) : '');

                this.info.sliceThickness = 'ST:' + (imagePlaneModule.sliceThickness != undefined && imagePlaneModule.sliceThickness != null ?
                 Number(imagePlaneModule.sliceThickness).toFixed(2) : this.thickness);  // 重建切片没层厚数据
            }
        },
    }
}

function handleTimeStr(params) {
    const studyTimeStr = String(params || '')
    return `${studyTimeStr.slice(0,2)}:${studyTimeStr.slice(2,4)}:${studyTimeStr.slice(4,6)}`;
}
</script>
<style lang="scss" scoped>
.ViewportOverlay {
    // text-shadow: 1px 1px gray;
}

@media (max-width: 1920px) {
    .ViewportOverlay {
        .i-overlay {
            p {
                font-size: 12px;
            }
        }
    }
}

@media(min-width: 1921px) and (max-width: 3000px) {
    .ViewportOverlay {
        .i-overlay {
            p {
                font-size: 14px;
            }
        }
    }
}
</style>
