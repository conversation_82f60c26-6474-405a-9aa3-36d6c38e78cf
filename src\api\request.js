import axios from 'axios'
import store from '$src/store'
import { MessageBox } from 'element-ui'

// 开发模式下请求的接口
if (process.env.NODE_ENV === 'development') {
    window.configs.imageServe = '192.168.1.201:19102/image'
    window.configs.reportServe = '192.168.1.201:19102/report'
}

const service = axios.create({
    timeout: 60000      // 超时请求
})
function getToken() {
    return sessionStorage.getItem('token')
}
const project = window.configs.project || {token_expired: [1008001, 1000002, 1000003] }
const headerName = project.name != 'Report' ? 'token' : 'sCookie'
const codeName   = project.name != 'Report' ? 'code' : 'data'
// 请求
service.interceptors.request.use(
    config => {
        // 添加请求头
        config.headers[headerName] = getToken()
        return config
    },
    error => {
        return Promise.reject(error)
    }
)

// 响应
service.interceptors.response.use(
    response => {
        const res = response.data
        if (!res.success && project.token_expired.includes(res[codeName])) {
            MessageBox.alert('登录信息失效，功能操作受限！', '提示', {
                confirmButtonText: '确定',
                type: 'warning',
                callback: () => {
                    // store.commit('removeUserTokenInfo')
                    // window.location.href = window.location.origin + window.location.pathname + '#/error';
                }
            });
            return;
        }
        return res
    },
    error => {
        return Promise.reject(error)
    }
)

export default service