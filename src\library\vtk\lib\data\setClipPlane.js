import vtkPlane from 'vtk.js/Sources/Common/DataModel/Plane';
import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';

export default (mapper, clipMip) => {
    const clipPlane1 = vtkPlane.newInstance();
    const clipPlane2 = vtkPlane.newInstance();

    const clipPlane1Normal = [0, 0, 1];
    const clipPlane2Normal = [0, 0, 1];
    const clipPlane1Position = clipMip.min;
    const clipPlane2Position = clipMip.max;

    const clipPlane1Origin = [
        clipPlane1Position * clipPlane1Normal[0],
        clipPlane1Position * clipPlane1Normal[1],
        clipPlane1Position * clipPlane1Normal[2],
    ];
    const clipPlane2Origin = [
        clipPlane2Position * clipPlane2Normal[0],
        clipPlane2Position * clipPlane2Normal[1],
        clipPlane2Position * clipPlane2Normal[2],
    ];
    vtkMatrixBuilder
        .buildFromDegree()
        .rotate(180, [0, 1, 0])
        .apply(clipPlane2Normal);

    clipPlane1.setNormal(clipPlane1Normal);
    clipPlane1.setOrigin(clipPlane1Origin);

    clipPlane2.setNormal(clipPlane2Normal);
    clipPlane2.setOrigin(clipPlane2Origin);

    mapper.addClippingPlane(clipPlane1);
    mapper.addClippingPlane(clipPlane2);
}