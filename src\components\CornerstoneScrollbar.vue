<template>
    <div class="scroll">
        <el-slider
          vertical
          :height="height"
          @input="onChange"
          v-model="current"
          class="c-progress"
          :show-tooltip="false"
          :max="max">
        </el-slider>
    </div>
</template>
<script>
// 改 element-ui 做的滚动条
export default {
    props: {
        curVal: {
            type: [Number, String],
            default: 0,
        },
        max: {
            type: [Number, String],
            default: 0,
        },
        height: {
            type: String,
            default: '100%',
        }
    },
    data() {
        return {
          current: 0
        }
    },
    watch: {
      curVal: {
        handler() {
          this.current = this.curVal
        },
        immediate: true
      }
    },
    methods: {
        onChange(value) {
            this.$emit('onInputCallback', value)
        },
    },
}
</script>