import vtkMath from 'vtk.js/Sources/Common/Core/Math'

import findNearIndex from '$library/cornerstone/tools/math/findNearIndex.js';
import getIppByPoint from '$library/cornerstone/tools/math/getIppByPoint.js';
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    data() {
        return {
            renderAbsoluteThrottle: {       // 渲染节流
                renderEvents: false,
                renderElement: null,
                timer: null
            }, 
            timerSynchronizerCrosshairs: null,
            lockSync: getConfigByStorageKey('configs-dragLockSync'),
        }
    },
    methods: {
        /**
         * viewport 信息变化就会进入该方法
         * @param {*} sourceViewport 
         * @param {*} clayData 
         * @param {*} type 
         * @param {*} isSend 
         * @returns 
         */
        renderCallBack(sourceViewport, clayData, type, isSend = false){
            // 在下载图像时，最小化、或切换其它 tab 页在缩放后，回到看图页后，融合不正确。
            if (!sourceViewport.width){
                return;
            }

            // 同步操作-分为相对，绝对。
            if (type === 'absolute'){ 
                // 绝对，赋值一样的信息
                // this.mriColorRender(sourceViewport, clayData)

                // 节流操作，避免多次执行
                if (this.renderAbsoluteThrottle.renderEvents && this.renderAbsoluteThrottle.renderElement !== clayData.el){
                    this.setRenderAbsoluteThrottle()
                    return
                }
                this.renderAbsoluteThrottle.renderElement = clayData.el
                this.renderAbsoluteThrottle.renderEvents = true
                this.renderAbsolute(sourceViewport, clayData)
            } else {
                // 相对 按照比例规模赋值
                this.renderRelative(sourceViewport, clayData)
            }

        },

        // 重置vtkAndCsRenderState.curRender
        setvtkAndCsRenderThrottle() {
            clearTimeout(this.vtkAndCsRenderState.timer);
            this.vtkAndCsRenderState.timer = setTimeout(() => {
                this.vtkAndCsRenderState.curRender = 'none';
            }, 0);
        },

        onNewImage(imageStack, lifecycle = {}) {
            // 设置当前渲染为 cornerstone ，在vtk反向改变截断
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'cs') {
                this.vtkAndCsRenderState.curRender = 'cs'
                this.csChangeVtkIndex(imageStack.el)
                this.setvtkAndCsRenderThrottle()
            }

            // 同步滚动
            const syncScroll = this.lockSync.start && this.lockSync.scroll

            // 切面更新，改变(切换切面冠、矢、轴、融合算首次加载)
            // 当前切面(序列)第一次显示
            if (lifecycle.change) {
                clearTimeout(this.timerSynchronizerCrosshairs)
                this.timerSynchronizerCrosshairs = setTimeout(() => {
                    this.addSynchronizerCrosshairs(imageStack.el)
                }, 100);


                const tempActiveIdx = this.activeViewportIndex
                this.activeViewportIndex = -1
             
                
                const initCreate = lifecycle.create // 创建时（组件创建时候 = true，图像改变后 = false）
                this.$nextTick(() => {

                    this.activeViewportIndex = tempActiveIdx == -1 ? 0 : tempActiveIdx
                    // 如果是第一个就跳到剧中显示
                    // 如果不是第一个，就以第一个切位最近显示
                    const refMprItem  = this.$refs.mprItem
                    let referRefViewport = {} // 存参照显示层
                    let sameElementRef   = {} // 跟自己相同的显示

                    refMprItem.map(item => {
                        const mprViewport = item.$refs.mprViewport
                        if (mprViewport) {
                            const csViewport = mprViewport.$refs.csViewport
                            // 相同切面
                            if (imageStack.orientation === csViewport.state.orientation) {
                                // 确定是第一个切面
                                if (!Object.keys(referRefViewport).length) {
                                    if (initCreate) {
                                        // 组件第一次显示
                                        referRefViewport = csViewport
                                    }else {
                                        // 组件改变融合、切面时候，用其它相同切面做参照
                                        if (imageStack.el === csViewport.element) {
                                            sameElementRef = csViewport
                                        }else {
                                            referRefViewport = csViewport
                                        }
                                    }
                                }
                            }
                        }
                    })
                    
                    // 如果没有参照，自己做参照
                    if (!Object.keys(referRefViewport).length) { 
                        referRefViewport = sameElementRef
                    }

                    // 当前渲染的是第一个切面
                    if (referRefViewport.element === imageStack.el) {
                        let currentIndex = 0
                        // 创建时才显示中间
                        if (!initCreate) {
                            // 组件已经创建，获取当前切面最后位置
                            currentIndex = lifecycle.data[imageStack.orientation]
                        }
                        // 当前切面最后显示 -1 从未显示过，或者组件创建时，设置中间显示
                        if (currentIndex === -1 || initCreate) {
                            currentIndex = Math.ceil(referRefViewport.imageIds.length / 2) - 1
                        }

                        cornerstoneTools.scrollToIndex(imageStack.el, currentIndex)
                    }else {
                        // 显示的布局中有相同切面，就以它来显示相应位置
                        let orientationIndex   = 2 // 横截面 z
                        if (imageStack.orientation == 'x') {
                            orientationIndex = 0
                        }else if (imageStack.orientation == 'y') {
                            orientationIndex = 1
                        }
                        const sourceImage      = cornerstone.getImage(referRefViewport.element)
                        if (!sourceImage) {
                            return
                        }

                        const imagePlaneModule = cornerstone.metaData.get('imagePlaneModule', sourceImage.imageId) || {}
                        const sliceLocation    = imagePlaneModule.sliceLocation
                        
                        let allStack = cornerstoneTools.getToolState(imageStack.el, 'stack')
                        if (!allStack || !allStack.data.length) { 
                            return 
                        }

                        const currentIndex = this.findRenderIndex(allStack.data[0].imageIds, sliceLocation, orientationIndex)
                        cornerstoneTools.scrollToIndex(imageStack.el, currentIndex);
                    }
                    // 以参照层渲染其它 viewport
                    setTimeout(() => {
                        if (referRefViewport.state){
                            referRefViewport.syncCallBack('relative')
                        }
                    }, 200); // 太低延时，融合会有问题（融合一大一小）
                })
                return
            }
            

            // 节流操作，避免多次执行
            if (this.renderAbsoluteThrottle.renderEvents && this.renderAbsoluteThrottle.renderElement !== imageStack.el){
                this.setRenderAbsoluteThrottle()
                return
            }
            this.renderAbsoluteThrottle.renderElement = imageStack.el
            this.renderAbsoluteThrottle.renderEvents = true

            const sourceImage      = cornerstone.getImage(imageStack.el)
            const imagePlaneModule = cornerstone.metaData.get('imagePlaneModule', sourceImage.imageId) || {}
            const sliceLocation    = imagePlaneModule.sliceLocation
            const elements         = this.$refs['center'].getElementsByClassName('viewport-element')
            let orientationIndex   = 2 // 横截面 z

            if (!elements) {
                return
            }

            if (imageStack.orientation == 'x') {
                orientationIndex = 0
            }else if (imageStack.orientation == 'y') {
                orientationIndex = 1
            }

            for (let index = 0; index < elements.length; index++) {
                const element = elements[index]
                if (!element || imageStack.el === element) {
                    continue
                }
                let allStack = cornerstoneTools.getToolState(element, 'stack')
                if (!allStack || !allStack.data.length) { 
                    continue 
                }
                const state = allStack.data[0].state
                
                // 不同切面,不执行
                if (state.orientation !== imageStack.orientation) { 
                    continue 
                }
                // TODO 需要验证，是否正确
                // const currentIndex = findNearIndex(allStack.data[0].imageIds, imageStack.orientation, 
                //     {x: imagePlaneModule.imagePositionPatient[0], y: imagePlaneModule.imagePositionPatient[1], z: imagePlaneModule.imagePositionPatient[2]}
                //     )
                if (syncScroll) {
                    const currentIndex = this.findRenderIndex(allStack.data[0].imageIds, sliceLocation, orientationIndex)
                    cornerstoneTools.scrollToIndex(element, currentIndex);
                }

            }
        },
        // 设置节流
        setRenderAbsoluteThrottle() {
            clearTimeout(this.renderAbsoluteThrottle.timer);
            this.renderAbsoluteThrottle.timer = setTimeout(() => {
                this.renderAbsoluteThrottle.renderEvents = false;
            }, 0);
        },
        findRenderIndex(images, sliceLocation, orientationIndex) {
            let minValue = null;
            let index = null;
            // 遍历当前操作的图像序列
            images.some((_, idx) => {
                // 绝对值
                const differ = Math.abs(this.getImageIppByString(_, orientationIndex) - sliceLocation);
                // 差距越来越小，赋值最新差值
                if (differ < minValue || minValue === null){
                    minValue = differ;
                    index = idx;
                }else {
                    // 差距值越来越大，直接退出
                    return true;
                }
            })
            return index;
        },
        getImageIppByString(imageId = '', index = 0){
            // 这个也能获取到 imagePositionPatient 位置
            if (imageId.includes('mpr')){
                // 一种获取方式 
                const arr = imageId.split(':');
                return arr[3].split(',').map(Number)[index];
            }
            const metaData = cornerstone.metaData.get('imagePlaneModule',imageId)
            return metaData.sliceLocation;
        },
        // 相对渲染，在操作完成后触发（缩放、移动）
        renderRelative(sourceViewport, clayData){
            const sourceOrientation = clayData.orientation;

            const elements = this.$refs['center'].getElementsByClassName('viewport-element')
            if (!elements) {
                return
            }
            // 同步移动缩放
            const syncMoveZoom = this.lockSync.start && this.lockSync.moveZoom

            for (let index = 0; index < elements.length; index++) {
                const element = elements[index]
                if (!element || clayData.el === element) {
                    continue
                }

                const viewport = cornerstone.getViewport(element)
                if (!viewport || !viewport.displayedArea) {
                    continue
                }

                const orientation = element.getAttribute('orientation')
                if (sourceOrientation === orientation && syncMoveZoom){

                    const scaleX = sourceViewport.columnPixelSpacing / viewport.displayedArea.columnPixelSpacing
                    const scaleY = sourceViewport.rowPixelSpacing / viewport.displayedArea.rowPixelSpacing

                    const scaleRatio = sourceViewport.width >= sourceViewport.height ? scaleX : scaleY

                    if (viewport.translation.x !== sourceViewport.x * scaleX ||
                        viewport.translation.y !== sourceViewport.y * scaleY ||
                        viewport.scale         !== sourceViewport.scale / scaleRatio)
                    {    
                        viewport.translation.x = sourceViewport.x * scaleX
                        viewport.translation.y = sourceViewport.y * scaleY
                        viewport.scale         = sourceViewport.scale / scaleRatio
                        cornerstone.setViewport(element, viewport)
                    }
                }
            }
        },
        // 绝对渲染，赋值一样的（窗宽窗位、反片、翻转）
        renderAbsolute(sourceViewport, clayData) {
            const sourceModality = clayData.modality      // 当前设备类型
            const sourceOrientation = clayData.orientation
            const sourceImage = cornerstone.getImage(clayData.el)

            const sourceSequenceName = sourceImage.data.string('x00180024') || ''

            const ww = sourceViewport.windowWidth
            const wc = sourceViewport.windowCenter
            
            // 同步启动
            const syncWindowColor = this.lockSync.start && this.lockSync.windowColor
            const syncConvert     = this.lockSync.start && this.lockSync.convert

            const elements = this.$refs['center'].getElementsByClassName('viewport-element')
            if (!elements) {
                return
            }
            for (let index = 0; index < elements.length; index++) {
                const element = elements[index]
                if (!element || clayData.el === element) {
                    continue
                }

                const img = cornerstone.getImage(element)            // 图像信息
                if (!img) {
                    continue
                }
                const modality     = img.data.string('x00080060') || ''  // 设备类型
                const sequenceName = img.data.string('x00180024') || ''
                const orientation  = element.getAttribute('orientation')
                const viewport = cornerstone.getViewport(element)
                const layers   = cornerstone.getLayers(element)

                // 相同切面 设置旋转、翻转
                if (sourceOrientation === orientation && syncConvert) {
                    if (viewport.hflip !== sourceViewport.hflip ||
                        viewport.vflip !== sourceViewport.vflip ||
                        viewport.rotation !== sourceViewport.rotation)
                    {
                        viewport.hflip = sourceViewport.hflip;
                        viewport.vflip = sourceViewport.vflip;
                        viewport.rotation = sourceViewport.rotation;
                        cornerstone.setViewport(element, viewport);
                    } 
                }

                // 相同设备，并且是单层(非融合) 设置反片
                if (modality === sourceModality && !layers.length && syncConvert){
                    // 原(用户操作的视图)是等于1(非融合的) 不一样，赋值一样
                    if (clayData.el && cornerstone.getLayers(clayData.el).length == 0 && viewport.invert !== sourceViewport.invert) {
                        viewport.invert = sourceViewport.invert;
                        cornerstone.setViewport(element, viewport);
                    }
                }
                
                // 融合
                if (layers && layers.length) {
                    layers.forEach(_ => {
                        const layer = cornerstone.getLayer(element, _.layerId)
                        const modality     = layer.image.data.string('x00080060') || ''
                        const sequenceName = layer.image.data.string('x00180024') || ''
                        const viewport = layer.viewport
    
                        if (sourceModality.toLocaleUpperCase() === modality.toLocaleUpperCase() && sourceSequenceName === sequenceName && syncWindowColor){
                            if (viewport.voi.windowWidth  === ww && 
                                viewport.voi.windowCenter === wc ){
                                return
                            }
                            viewport.voi.windowWidth = ww
                            viewport.voi.windowCenter = wc
                            cornerstone.updateImage(element)
                        }
                    })
                }else {
                    // 相同设备，相同图像细分类型(t1,t2,弥散)，TODO 不同 PT 也要不一样，要以 SUV 值做相同处理
                    if (sourceModality.toLocaleUpperCase() === modality.toLocaleUpperCase() && sourceSequenceName === sequenceName && syncWindowColor){

                        if (viewport.voi.windowWidth  === ww &&
                            viewport.voi.windowCenter === wc ){
                            continue
                        }
                        viewport.voi.windowWidth = ww
                        viewport.voi.windowCenter = wc
    
                        cornerstone.setViewport(element, viewport)
                    }
                }
            }
            this.setRenderAbsoluteThrottle()  // 不加同步旋转有可能会失效
        },

        // 点击vtk视图时候,设置其它 cornerstone 视图  
        vtkChangeCsIndex(worldPos, id, camera, volume) {
            // cs 改变 vtk vtk会重新反过来改变 cs 截断执行
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'vtk') {
                this.vtkAndCsRenderState.curRender = 'vtk'
                this.setvtkAndCsRenderThrottle()
            }
            // 如果是 cornerstone 渲染，就不在返回渲染 cs
            if (this.vtkAndCsRenderState.curRender == 'cs') {
                return
            }

            let ippVec3 = []
            if (worldPos[0].length){
                ippVec3 = [...worldPos[0]]
            }else {
                ippVec3 = worldPos
            }


            const elements = [...this.$refs['center'].getElementsByClassName('viewport-element')]
            if (!elements.length) {
                return
            }
            // 多个MIP存在，每个都会改变渲染 cs.只有选中的 mip 才改变 cs 显示
            if (this.viewports[this.activeViewportIndex].id != id) {
                return
            }

            const updateAllScrollToIndex = (new3dPo) => {
                // 更新所有viewport的图片位置
                elements.forEach((targetElement, i) => {
                    const stackToolDataSource = cornerstoneTools.getToolState(targetElement, 'stack');
                    if (stackToolDataSource === undefined) {
                        return;
                    }

                    const stackData = stackToolDataSource.data[0];
                    //  按序列区分 （没用，因为十字线同步器是共用的
                    // if (!stackData.state || stackData.state.ctUid !== vtkViewport.seriesUID) {
                    //     return;
                    // }
                    const orientation = stackData.state.orientation
                    const index = findNearIndex(stackData.imageIds, orientation, new3dPo)

                    cornerstoneTools.scrollToIndex(targetElement, index)

                    const tool = cornerstoneTools.getToolForElement(targetElement, 'NuclearCrosshairs')
                    // 用这个方法更新十字线位置
                    tool && tool.getInitialPoint(targetElement, this.synchronizerCrosshairs)
                });
            }

            const switchOn = getConfigByStorageKey('configs-mipClickLocation') 
            if (!switchOn) {
                updateAllScrollToIndex({ x: ippVec3[0], y: ippVec3[1], z: ippVec3[2] })
                return
            }

            // vtk图像点击后三维定位功能，从vtk的viewport数据中获取点击那个切面的图像数据，计算出三维点
            const vtkViewportIndex = this.viewports.findIndex(item =>  item.viewType === 'vtk' && item.id === id) 
            const inputData = volume.getMapper().getInputData() 
            const bounds = volume.getBounds();
            const Spacings = inputData.getSpacing()
            const [xMin, xMax, yMin, yMax, zMin, zMax] = bounds;
            if (vtkViewportIndex > -1) {
                const vtkViewport = this.viewports[vtkViewportIndex] 
                const volumeInfo = vtkViewport.volumeInfo // viewport里预先赋值的图像信息
                const zBoundMax = bounds[5]
                const zBoundMin = bounds[4]
                const zTarget = ippVec3[2]
                const indexRatio = 1 - (zTarget - zBoundMin) / (zBoundMax - zBoundMin)

                const imageIdsGroup = volumeInfo.imageIdsGroup // 体数据的源图id的数组，融合图像默认第一个是PT 第二CT
                const targetImageIds = imageIdsGroup[0]
                const toIndex = Math.round(indexRatio * targetImageIds.length)

                const targetImageId = targetImageIds[toIndex]

                if (targetImageId) {
                    cornerstone.loadAndCacheImage(targetImageId).then(image => {

                        // 参考cornerstone3d的方法，获取点击坐标所在射线穿过的点的集合，计算suv值最大的点
                        let _vec = [0, 0, 0]
                        let cameraPosition = [0, 0, 0]
                        const directionOfProjection = camera.getDirectionOfProjection();
                        cameraPosition = camera.getPosition();
                        cameraPosition[0] = directionOfProjection[0] * 65536
                        cameraPosition[1] = directionOfProjection[1] * 65536
                        cameraPosition[2] = ippVec3[2]
                        vtkMath.subtract(ippVec3, cameraPosition, _vec);

                        let pickedPoint = [];

                        for (let n = 0; n < 2; n++) {
                            for (let pointT = bounds[n*2]; pointT <= bounds[n*2 + 1]; pointT = pointT + Spacings[n]) {
    
                                // 6.1 Calculating the point x location
                                let point = [0, 0, 0];
                                point[n] = pointT
                                // 6.2 Calculating the point y,z location based on the line equation
                                let idxArr = [0, 1, 2]
                                const t = (pointT - cameraPosition[n]) / _vec[n];
                                idxArr = idxArr.filter(j => j !== n)
                                point[idxArr[0]] = t * _vec[idxArr[0]] + cameraPosition[idxArr[0]];
                                idxArr.shift()
                                point[idxArr[0]] = t * _vec[idxArr[0]] + cameraPosition[idxArr[0]];
    
                                // 6.3 Checking if the points is inside the bounds
                                if (point[0] > xMin &&
                                    point[0] < xMax &&
                                    point[1] > yMin &&
                                    point[1] < yMax &&
                                    point[2] > zMin &&
                                    point[2] < zMax) {
                                    pickedPoint.push(point)
                                }
                            }
                        }


                        let maxValue2Dpoint = { x: 0, y: 0 };
                        let maxValue2D = 0;
                        let x, y, value;
                        pickedPoint.forEach(p => { 
                            value = inputData.getScalarValueFromWorld(p)
                            if (value > maxValue2D) {
                                maxValue2D = value
                                maxValue2Dpoint = { x: p[0], y: p[1], z: p[2] }
                            }
                        })

                        const new3dPoint = maxValue2Dpoint

                        // 将三维坐标点传给十字线同步器
                        this.synchronizerCrosshairs.patientPoint = new3dPoint

                        updateAllScrollToIndex(new3dPoint)
                    })
                } 
            }
        },

        csChangeVtkIndex(element) {
            // console.log('cs-change-vtk')
            const ipp = getIppByPoint(element)

            const refMprItem  = this.$refs.mprItem
            
            refMprItem.forEach(item => {
                const vtkViewport = item.$refs.vtkViewport
                if (vtkViewport) {
                    const api = vtkViewport.api
                    const apis = [api]
                    const apiIndex = 0
                    if (api.svgWidgets) {
                        
                        api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, apis, apiIndex);
                    }
                }
            })
        },

        // 同步 
        onClickLockSync(attr) {
            this.lockSync[attr] = !this.lockSync[attr];
            if (attr === 'start') {
                this.$refs.contextmenu.hide();
            }

            localStorage.setItem('configs-dragLockSync', JSON.stringify(this.lockSync))
        }
    },
}