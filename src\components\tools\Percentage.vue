<template>
    <div class="c-shade" v-show="visible">
        <div class="i-shade"></div>
        <el-progress
        :percentage="percentage" 
        :color="colors"
        :format="format"></el-progress>
    </div>
</template>

<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: true
        },
        total: {
            type: Number,
            default: 0,
        },
        loadCount: {
            type: Number,
            default: 0,
        },
        message: {
            type: String,
            default: '下载'
        }
    },
    data() {
        return {
            colors: [
                {color: '#f56c6c', percentage: 20},
                {color: '#e6a23c', percentage: 40},
                {color: '#5cb87a', percentage: 60},
                {color: '#1989fa', percentage: 80},
                {color: '#67c23a', percentage: 100}
            ]
        }
    },
    computed: {
        percentage(){
            if (!this.total || !this.loadCount) return 0;
            const val = Math.floor((this.loadCount / this.total) * 100);
            return val
        }
    },
    methods: {
        format(percentage) {
            return `${this.message}${percentage}%`;
        },
    }
};
</script>
<style lang="scss" scoped>
.c-shade{
    position: fixed;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding-top: 40vh;
    z-index: 1001;
    .i-shade{
        position: absolute;
        background: #f1f1f1e6;
        width: 100%;
        height: 100%;
        top: 0;
    }
    .el-progress{
        width: 200px;
        margin: 0 auto;
    }
}
</style>