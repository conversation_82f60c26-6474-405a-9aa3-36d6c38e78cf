import macro from 'vtk.js/Sources/macro';
import vtkInteractorStyleMPRSlice from './vtkInteractorStyleMPRSlice.js';
import Constants from 'vtk.js/Sources/Rendering/Core/InteractorStyle/Constants';
import vtkCoordinate from 'vtk.js/Sources/Rendering/Core/Coordinate';

const { States } = Constants;

function vtkInteractorStyleProbe(publicAPI, model) {
  model.classHierarchy.push('vtkInteractorStyleProbe');

  function getCoordFromCalldata(callData) {
    const { apis, apiIndex } = model;
    const api = apis[apiIndex];
    const pos = callData.position;
    const renderer = callData.pokedRenderer;
    const volumeObj = api.volumes[0]
    const volMapper = volumeObj.getMapper()
    const volInputData = volMapper.getInputData()

    const dPos = vtkCoordinate.newInstance();
    dPos.setCoordinateSystemToDisplay();

    dPos.setValue(pos.x, pos.y, 0);
    let worldPos = dPos.getComputedWorldValue(renderer);

    const camera = renderer.getActiveCamera();
    const directionOfProjection = camera.getDirectionOfProjection();

    const halfSlabThickness = api.getSlabThickness() / 2;

    // Add half of the slab thickness to the world position, such that we select
    // The center of the slice.

    for (let i = 0; i < worldPos.length; i++) {
      worldPos[i] += halfSlabThickness * directionOfProjection[i];
    }
    const [xMin, xMax, yMin, yMax, zMin, zMax] = volInputData.getBounds()

    if (!(worldPos[0] > xMin &&
      worldPos[0] < xMax &&
      worldPos[1] > yMin &&
      worldPos[1] < yMax &&
      worldPos[2] > zMin &&
      worldPos[2] < zMax)) {
      return
    }

    const pointValue = volInputData.getScalarValueFromWorld(worldPos)
    if (pointValue) {
      return {worldPos, pointValue}
    }
  }

  function moveProbePoint(callData) {
    const { apis, apiIndex } = model;
    const thisApi = apis[apiIndex];
    const coordData = getCoordFromCalldata(callData)
    if (!coordData) {
      thisApi.svgWidgets.probeWidget.cancelTempPoint(thisApi)
      return
    }
    thisApi.svgWidgets.probeWidget.moveProbe(
      coordData.worldPos,
      coordData.pointValue,
      thisApi
    );
    model.cachedCallData = callData

    publicAPI.invokeInteractionEvent({ type: 'InteractionEvent' });
  }

  const superHandleMouseMove = publicAPI.handleMouseMove;
  // 鼠标放在视图层移动，触发的事件
  publicAPI.handleMouseMove = callData => {
    // if (model.state === States.IS_WINDOW_LEVEL) {
      moveProbePoint(callData)
    // }

    if (superHandleMouseMove) {
      superHandleMouseMove(callData);
    } 
  };

  function handleMouseUp(callData) {
    const { apis, apiIndex } = model;
    const thisApi = apis[apiIndex];

    // thisApi.svgWidgets.probeWidget.setProbeDisplay(false)
    const coordData = getCoordFromCalldata(callData)
    if (!coordData) {
      thisApi.svgWidgets.probeWidget.cancelTempPoint(thisApi)
      return
    }
    thisApi.svgWidgets.probeWidget.addProbePointData(
      coordData.worldPos,
      coordData.pointValue,
      thisApi
    );
    thisApi.svgWidgets.probeWidget.cancelTempPoint(thisApi)
  }

  function handleMouseRightClick(callData) {
    const { apis, apiIndex } = model;
    const thisApi = apis[apiIndex];
    const coordData = getCoordFromCalldata(callData)
    if (!coordData) {
      thisApi.svgWidgets.probeWidget.cancelTempPoint(thisApi)
      return
    }
    thisApi.svgWidgets.probeWidget.deletePointStorage(
      coordData.worldPos, 
      thisApi
    );
    thisApi.svgWidgets.probeWidget.cancelTempPoint(thisApi)
  }

  const superHandleLeftButtonPress = publicAPI.handleLeftButtonPress;
  // 鼠标左键按下时的事件
  publicAPI.handleLeftButtonPress = callData => {
    // console.log('鼠标左键按 ')
    if (model.volumeActor) {
      publicAPI.startWindowLevel();
      // moveProbePoint(callData)
    } else if (superHandleLeftButtonPress) {
      superHandleLeftButtonPress(callData);
    }
  };

  publicAPI.superHandleLeftButtonRelease = publicAPI.handleLeftButtonRelease;
  // 鼠标左键松开时的事件
  publicAPI.handleLeftButtonRelease = callData => {
    switch (model.state) {
      case States.IS_WINDOW_LEVEL:
        publicAPI.endWindowLevel();
        // handleMouseUp(callData) // 取消点击标注
        
        break;
      default:
        publicAPI.superHandleLeftButtonRelease();
        break;
    }
  };


  publicAPI.superHandleRightButtonPress = publicAPI.handleRightButtonPress;
  publicAPI.handleRightButtonPress = callData => {
    handleMouseRightClick(callData)
    publicAPI.superHandleRightButtonPress(callData);
  };

  publicAPI.onEndInteractionEvent(() => {
    if (model.cachedCallData) moveProbePoint(model.cachedCallData)
  })
}

// ----------------------------------------------------------------------------
// Object factory
// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {
  Object.assign(model, DEFAULT_VALUES, initialValues);

  // Inheritance
  vtkInteractorStyleMPRSlice.extend(publicAPI, model, initialValues);

  macro.setGet(publicAPI, model, [
    'apis',
    'apiIndex'
  ]);

  // Object specific methods
  vtkInteractorStyleProbe(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(
  extend,
  'vtkInteractorStyleProbe'
);

// ----------------------------------------------------------------------------

export default Object.assign({ newInstance, extend });
