<template>
    <div :class="['i-bottom-center', textColor]" saveimage="true" v-html="scaleHtml" ref="scale"></div>
</template>
<script>
export default {
    props: {
        detail: {
            type: Object,
            default: () => {}
        },
        invert: {
            type: Boolean,
            default: false
        },
    },
    watch: {
        detail: {
            handler(){
                this.renderScale();
            },
            deep: true
        }
    },
    data() {
        return {
            scaleHtml: ''
        }
    },
    computed: {
        textColor() {
            if (this.invert) return 'black'
            return 'white'
        }
    },
    mounted() {
        if (Object.keys(this.detail).length) {
            this.renderScale();
        }
    },
    methods: {
        renderScale(){
            const scaleDom = this.$refs.scale;
            const width = scaleDom.parentElement.clientWidth / 2;
            const verticalIntervalScale = (10.0 / this.detail.columnPixelSpacing) * this.detail.scale;
            if (verticalIntervalScale === Infinity || !this.detail.columnPixelSpacing){
                this.scaleHtml = '';
                return;
            }
            let str = '<ul>', len = 0;
            for (let index = 0; index < 10; index++) {
                if (verticalIntervalScale * (index + 1) > width){
                    break;
                }
                len += 1
                str += `<li style="width: ${verticalIntervalScale}px"></li>`
            }
            str += `</ul><span>${len}cm</span>`;
            this.scaleHtml = str;
        }
    }
}
</script>