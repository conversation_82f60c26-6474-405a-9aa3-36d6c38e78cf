
import csTools from '$library/cornerstone/cornerstoneTools'
const imagePointToPatientPoint = csTools.import('util/imagePointToPatientPoint')
const projectPatientPointToImagePlane = csTools.import('util/projectPatientPointToImagePlane')


import appState from '$library/cornerstone/mpr/store/appState.js';
import fun from '$library/utils/function.js';

/**
 * 
 * @param {*} iopArray     iop值
 * @param {*} key         序列 id
 * @returns 
 */
export function getClipPlaneXY(iopArray, key){
    // 通过法线拿到最靠近位置
    const slicesType = fun.getNormal(iopArray, true)
    if (!slicesType) return [];
    // clipXY  返回 裁剪 xy平面图像区域
    // clipIpp 裁剪参考的原点坐标
    // slicesType 切面类型（冠、矢、横）
    const clipXY  = appState[key] && appState[key].clipXY[slicesType]
    const clipIpp = appState[key] && appState[key].clipIpp[slicesType]
    const clipAllPixelSpacing = appState[key] && appState[key].clipAllPixelSpacing[slicesType]
    return { 
        clipXY, 
        clipIpp,
        clipAllPixelSpacing,
        slicesType
    };
}

// 获取&设置裁剪平面xy
/**
 * PT 按照 CT/MR 大小裁剪，并以对象形式：序列id.角度 存入 appState 中
 * @param {*} ctPlane  cornerstone.metaData.get('imagePlaneModule', image)
 * @param {*} ptPlane  cornerstone.metaData.get('imagePlaneModule', image)
 * @param {*} angle    角度
 * @param {*} key      序列 id key  PT + CT id
 * @returns 
 */
export default function setClipPlaneXY(ctId, ptId, key) {

    const ctPlane = cornerstone.metaData.get('imagePlaneModule', ctId);
    const ptPlane = cornerstone.metaData.get('imagePlaneModule', ptId);
    const angle = ctPlane ? fun.getNormal(ctPlane.imageOrientationPatient, true) : 'axial';

    if (!Object.keys(ctPlane).length || !Object.keys(ptPlane).length) return {};

    // 左上角0,0平面位置转换成 患者坐标位置
    let ippStart = imagePointToPatientPoint({x: 0, y: 0}, ctPlane);
    // 右下角平面位置转换成 患者坐标位置
    const ippEnd = imagePointToPatientPoint({x: ctPlane.columns, y: ctPlane.rows}, ctPlane);

    // 通过患者坐标位置，转换成为图像坐标位置
    const crossPointStart = projectPatientPointToImagePlane(ippStart, ptPlane);
    const crossPointEnd   = projectPatientPointToImagePlane(ippEnd, ptPlane);

    // 裁剪区域
    let clipExtent = [crossPointStart.x, crossPointEnd.x, crossPointStart.y, crossPointEnd.y];

    const [PTUID, CTUID] = key.split('&')
    const CTVolumeData = appState[CTUID]
    const PTVolumeData = appState[PTUID]
    if (!PTVolumeData || !CTVolumeData) return {};

    // MR 图像直接返回
    if (!CTVolumeData.vtkVolumes) {
        try {
            if (!appState[key]) {
                appState[key] = {}
            }
    
            if (!appState[key].clipXY){
                appState[key].clipXY = {}
            }
            if (!appState[key].clipIpp){
                appState[key].clipIpp = {}
            }
            if (!appState[key].clipAllPixelSpacing){
                appState[key].clipAllPixelSpacing = {}
            }
            // 存储裁剪区域
            appState[key].clipXY[angle]  = clipExtent;
            // 存储参考裁剪的 ipp 圆点位置
            appState[key].clipIpp[angle] = ippStart;
            // 存储 CT 物理像素间距
            appState[key].clipAllPixelSpacing[angle] = {
                columnPixel: ctPlane.columnPixelSpacing * ctPlane.columns,
                rowPixel:    ctPlane.rowPixelSpacing    * ctPlane.rows,
                diffInfo: {}
            }
        } catch (error) {
            console.info(error);
        }
        return clipExtent;
    }
    
    const diffInfo = {}
    const CTVolumeSpacing = (CTVolumeData.vtkVolumes.vtkImageData.getSpacing())
    const PTVolumeSpacing = (PTVolumeData.vtkVolumes.vtkImageData.getSpacing())
    const CTVolumeExtent = (CTVolumeData.vtkVolumes.vtkImageData.getExtent())
    const PTVolumeExtent = (PTVolumeData.vtkVolumes.vtkImageData.getExtent())
    const CTVolumeBounds = CTVolumeData.vtkVolumes.vtkImageData.getBounds()
    const PTVolumeBounds = PTVolumeData.vtkVolumes.vtkImageData.getBounds()
    const CTVolumeOrigin = CTVolumeData.vtkVolumes.vtkImageData.getOrigin()
    const PTVolumeOrigin = PTVolumeData.vtkVolumes.vtkImageData.getOrigin()

    const CTVolumeDirection = CTVolumeData.vtkVolumes.vtkImageData.getDirection()
    const PTVolumeDirection = PTVolumeData.vtkVolumes.vtkImageData.getDirection()

    const isDirectionEqual = (a = [], b = []) => {
        let result = false 
        try {
            result = a.map(i => String(Math.round(i))).join(',') == b.map(i => String(Math.round(i))).join(',')
        
        } catch (error) {
            console.error(error)
        }
        return result
    }
 

    // 判断PT图像实际尺寸是否大于CT
    const sizeAllowance = 10
    const PTrealWidth = PTVolumeSpacing[0] * (PTVolumeExtent[1] - PTVolumeExtent[0])
    const CTrealWidth = CTVolumeSpacing[0] * (CTVolumeExtent[1] - CTVolumeExtent[0])
    const PTrealHeight = PTVolumeSpacing[1] * (PTVolumeExtent[3] - PTVolumeExtent[2])
    const CTrealHeight = CTVolumeSpacing[1] * (CTVolumeExtent[3] - CTVolumeExtent[2])
    
    let isPtPlaneLarger = (PTrealWidth - CTrealWidth) > sizeAllowance 
        && (PTrealHeight - CTrealHeight) > sizeAllowance 

    // console.log((PTVolumeBounds[5] - PTVolumeBounds[4]) , (CTVolumeBounds[5] - CTVolumeBounds[4]))

    if ((PTVolumeBounds[5] - PTVolumeBounds[4]) > (CTVolumeBounds[5] - CTVolumeBounds[4]) * 2.25) {
        isPtPlaneLarger = false
    }
    
    if ((CTVolumeBounds[5] - CTVolumeBounds[4]) > (PTVolumeBounds[5] - PTVolumeBounds[4]) * 2.25) {
        isPtPlaneLarger = false
    }


    const CTOLDDirection = CTVolumeData.vtkVolumes.oldDirection
    const PTOLDDirection = PTVolumeData.vtkVolumes.oldDirection
    let isSpecialDirection = !isDirectionEqual(CTOLDDirection, [1, 0, 0, 0, 1, 0, 0, 0, 1])
        || !isDirectionEqual(PTOLDDirection, [1, 0, 0, 0, 1, 0, 0, 0, 1])
    // isSpecialDirection 判断图像的轴向是否不等于标准正方向

    if (isPtPlaneLarger && isSpecialDirection) {
        const widthDiff = PTrealWidth - CTrealWidth
        const HeightDiff = PTrealHeight - CTrealHeight
        PTVolumeData.vtkVolumes.vtkImageData.setOrigin([
            CTVolumeOrigin[0] - widthDiff/2,
            CTVolumeOrigin[1] - HeightDiff/2,
            PTVolumeOrigin[2]
        ])

    }

    try {
        if (!appState[key]) {
            appState[key] = {}
        }

        if (!appState[key].clipXY){
            appState[key].clipXY = {}
        }
        if (!appState[key].clipIpp){
            appState[key].clipIpp = {}
        }
        if (!appState[key].clipAllPixelSpacing){
            appState[key].clipAllPixelSpacing = {}
        }
        // 存储裁剪区域
        appState[key].clipXY[angle]  = clipExtent;
        // 存储参考裁剪的 ipp 圆点位置
        appState[key].clipIpp[angle] = ippStart;
        // 存储 CT 物理像素间距
        appState[key].clipAllPixelSpacing[angle] = {
            columnPixel: ctPlane.columnPixelSpacing * ctPlane.columns,
            rowPixel:    ctPlane.rowPixelSpacing    * ctPlane.rows,
        }
    } catch (error) {
        console.info(error);
    }
    return clipExtent;
}