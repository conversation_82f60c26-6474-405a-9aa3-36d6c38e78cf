<template>
    <div @click="_click" class="m-container" :class="{'active': sSOPInstanceUID == viewportData.sSOPInstanceUID}">
        <div class="m-head">
            <span :title="form.sopInstanceUid">{{ form.sopInstanceUid }}</span>
            <span>
                <transition name="fade">
                    <i class="fa" :class="[icons[iconIndex]]"></i>
                </transition>
            </span>
        </div>
        <el-input
        type="textarea"
        placeholder=""
        :rows="4"
        @blur="handerBlur"
        @input="handerInput"
        v-model="form.InstanceRemark">
        </el-input>
    </div>
</template>
<script>
export default {
    props: {
        viewportData: {
            type: Object,
            default: () => {}
        },
        sSOPInstanceUID: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            form: {
                sopInstanceUid: '',
                InstanceRemark: '',
                InstanceRemarkEn: ''
            },
            initRemark: '',
            iconIndex: 0,
            icons: ['', 'el-icon-cloudy', 'el-icon-check'],
            timer: false,
            requestTimer: false,
        }
    },
    watch: {
        viewportData: {
            handler() {
                this.initData();
            },
            immediate: true,
            deep: true
        },
        'form.InstanceRemark': {
            handler() {
                clearTimeout(this.timer)
                if (this.form.InstanceRemark == this.initRemark) {
                    this.iconIndex = 0
                    return
                }
                this.iconIndex = 1
            }
        }

    },
    methods: {
        initData() {
            this.form.sopInstanceUid = this.viewportData.sSOPInstanceUID
            this.form.InstanceRemark = this.viewportData.sRemarkCn

            this.initRemark = this.viewportData.sRemarkCn
        },
        handerBlur() {
            // 清除自动保存
            clearTimeout(this.requestTimer)
            // 触发保存
            this.triggerRemark()
        },
        handerInput() {
            // 3 秒后也会自动保存
            clearTimeout(this.requestTimer)
            this.requestTimer = setTimeout(() => {
                this.triggerRemark()
            }, 3000)
        },

        // 触发备注
        triggerRemark() {
            if (this.form.InstanceRemark == this.initRemark) {
                // 一样数据不修改
                return
            }
            this.$Api.editInstanceRemark(this.form).then(res => {
                if (res.success) {
                    this.viewportData.sSOPInstanceUID = this.form.sopInstanceUid
                    this.viewportData.sRemarkCn = this.form.InstanceRemark

                    this.iconIndex = 2

                    this.timer = setTimeout(() => {
                        this.iconIndex = 0
                    }, 1500);
                    return
                }
                // this.initData()
            }).catch(() => {
				this.$message({
					type: 'error',
					message: '备注失败！'
				})
			})
        },
        _click() {
            this.$emit('_click')
        }
    }
}
</script>
<style lang="scss" scoped>
.m-container {
    border: 1px solid #DCDFE6;
    margin: 10px;
    border-radius: 0 0 4px 4px;
    &.active {
        position: relative;
        border-color: #409eff;
        &::before {
            z-index: 0;
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            width: calc(100% + 4px);
            height: calc(100% + 4px);
            background: #409eff;
            border-radius: 0 0 4px 4px;
        }
    }
    .m-head {
        position: relative;
        z-index: 1;
        display: flex;
        height: 34px;
        line-height: 34px;
        background: #f7f7f7;
        border-bottom: 1px solid #DCDFE6;
        > span {
            &:first-child {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding-left: 4px;
            }
            &:last-child {
                width: 50px;
            }
            .fa {
                font-size: 20px;
                line-height: 33px;
                // transition: all 0.6s linear;
            }
        }
    }
    ::v-deep .el-textarea__inner {
        border-radius: 0;
        border: none;
    }
}
@keyframes slideInRight {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity .5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>