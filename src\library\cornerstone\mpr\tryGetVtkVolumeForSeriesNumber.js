import appState from './store/appState.js';  // 序列图像
import createVtkVolumeAsync from './createVtkVolumeAsync.js'
/**
 * 获取 vtk 体
 * 
 * @param {*} seriesNumber 序列数（序列位置）
 */
export default async function(seriesNumber){
    let vtkVolume = appState[seriesNumber] && appState[seriesNumber].vtkVolumes;
    const imageIds = appState[seriesNumber].series
    // 序列没有创建 vtk 体
    if(!vtkVolume){
        // 创建体
        vtkVolume = await createVtkVolumeAsync(imageIds);
        appState[seriesNumber].vtkVolumes = vtkVolume; // 存储vtk容积,下次打开直接使用该容积
    }
    
    return {
        imageIds,
        vtkVolume,
    } 
}