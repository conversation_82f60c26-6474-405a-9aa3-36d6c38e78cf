<template>
    <div class="inner-pc-plan pc-body" ref="pcBody">
        <section class="c-body">
            <AreaFit :class="styleFont" @resizeArea="setFontByWidthAndHeight" :isMuch="layoutFill">
                <div ref="center" class="c-content" :style="gridLayoutStyle.containerStyle">
                    <template v-for="(item, index) in viewports">
                        <!-- :isStackPrefetchEnabled="true" 使用预加载 -->
                        <CornerstoneViewport
                            :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style"
                            v-if="!item.vtk" innnerClass="crosshairs-parent" ref="csViewport" :key="item.uid"
                            :state="viewportState(item)" :modality="item.sModality" :imageIds="item.imageIds"
                            :layerIds="item.layerIds"
                            :class="{ 'i-active': activeViewportIndex === index, 'xx-full-screen': fullScreenIndex === index }"
                            :activeTool.sync="activeTool" :crosshairsTool.sync="crosshairsTool.mipShow"
                            :isOverlayVisible.sync="isOverlayVisible" toolType="slice" :tabId="tabId"
                            :activeSelect="windowIsActive && activeViewportIndex === index"
                            :showAction="showAction === index"
                            :showCoordinateTools="showCoordinateTools && coordinateToolsIndex === index"
                            :showRotationTools="showRotationTools && rotationToolsIndex === index"
                            @onDblclickViewport="onDblclickViewport($event, index)"
                            @onToolRenderCompleted="onToolRenderCompleted" @renderCallBack="renderCallBack"
                            @onNewImage="onNewImage" @onOffsetChange="onOffsetChange" @clearSelectTool="clearSelectTool"
                            @selectTool="selectTool" @setViewportActive="setViewportActive(index, $event)"
                            @triggerToolGroupRender="triggerToolGroupRender"
                            @triggerLesionAreaGroupRender="triggerLesionAreaGroupRender"
                            @triggerLesionAreaSTAT="triggerLesionAreaSTAT" @setCrosshairs="activeCrosshairs"
                            @onMouseRight="onMouseRight">
                            <!-- <svg slot="inner" class="crosshairs" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" version="1.1" xmlns="http://www.w3.org/2000/svg"></svg> -->
                        </CornerstoneViewport>

                        <View2D v-else-if="item.vtk && item.volumes"
                            :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style"
                            class="vtk-viewport"
                            :class="{ 'i-active': activeViewportIndex === index, 'xx-full-screen': fullScreenIndex === index }"
                            :key="item.uid" @setViewportActive="setViewportActive(index)"
                            @vtkWwwcChange="(wwwc) => onVtkWwwcChange(item, index, wwwc)" :onCreated='storeApi(index)'
                            :volumes="item.volumes" :modality="item.modality" :layoutData="allModalityIndex"
                            :layoutId="item.layoutId" :scrollRotate="scrollRotate" :orientation="item.orientation"
                            :imageId="item.imageId" :clipZ="item.vtkClipZ" :isOverlayVisible.sync="isOverlayVisible"
                            :isOverlay="false">
                            <ul noprint="true" class="c-action">
                                <li @click="onClickRotate(0, index)" title="正面">F</li>
                                <li @click="onClickRotate(180, index)" title="背面">B</li>
                                <li @click="onClickRotate(271, index)" title="左侧">L</li>
                                <li @click="onClickRotate(91, index)" title="右侧">R</li>
                                <li @click="onClickVTKFullscreen(index)" title="全屏"> <i class="el-icon-full-screen"></i>
                                </li>
                            </ul>
                        </View2D>
                    </template>
                </div>
                <div v-if="+preloadPercentage !== 100" class="preload-tip" noprint="true">
                    预加载{{ preloadPercentage }}%
                    <i class="el-icon-loading"></i>
                </div>

            </AreaFit>
            <!-- 基本工具。旋转、点击工具、点击重置、点击反片事件 -->
            <BaseTools ref="baseTools" toolType="slice" :tabId="tabId" :seriesId="seriesId" :allowAngel="allowAngel"
                :activeTool.sync="activeTool" :activeViewportIndex="activeViewportIndex"
                :viewportElements="viewportElements" :isOverlayVisible.sync="isOverlayVisible" :groupId="groupId"
                :apis="apis" :crosshairsTool.sync="crosshairsTool.mipShow" @playClip="vtkPlayClip"
                @screenToRebuild="screenToRebuild" @onChangeImg="onChangeImg" @onClickBox="onClickBox"
                @onReset="toggleToReset" @syncSizeRender="syncSizeRender" @onClickSavePrint="onClickSavePrint"
                @onInvert="toggleToInvert" @setCrosshairs="activeCrosshairs" @onClickChangeSeries="onClickChangeSeries"
                @clearMark="onMeasurements('del')" @onChangePage="onChangePage">
                <div slot="header" class="c-tool-header">
                    <div class="c-layout">
                        <ul class="c-box c-box-01">
                            <li v-for="(item, index) in defaultLayoutShow" :key="item.id"
                                :class="{ 'i-active': item.id === selectLayout }"
                                @click="onClickChangeLayout(item.options, item.id, true)"><i
                                    :style="{ backgroundImage: 'url(' + require(`$assets/` + item.img) + ')' }"></i></li>
                        </ul>
                        <ul class="c-box c-box-01-right ">
                            <li v-for="item in layoutList" :key="item.id" :class="{ 'i-active': item.id === selectLayout }"
                                @click="onClickChangeLayout(item.options, item.id, true, item.showLayout)">
                                <i v-if="item.imageType"
                                    :style="{ backgroundImage: 'url(' + require(`$assets/` + item.img) + ')' }"></i>
                                <i v-else :style="{ backgroundImage: 'url(' + item.img + ')' }"></i>
                            </li>
                            <p class="i-tip-set" v-if="!layoutList.length" @click="onClickLayoutSet">设置</p>
                        </ul>
                    </div>
                </div>
            </BaseTools>
            <Percentage :visible="percentage.loading" :message="percentage.message" :total="percentage.total"
                :loadCount="percentage.loadCount" />
        </section>

        <v-contextmenu ref="contextmenu" oncontextmenu="return false">
            <v-contextmenu-item @click="onClickImageSave(1)"><i class="el-icon-picture"
                    style="padding-right:4px"></i>截屏另存...</v-contextmenu-item>
            <v-contextmenu-item @click="onClickImageSave(0)"><i class="el-icon-picture"
                    style="padding-right:4px"></i>图像另存...</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <!-- <v-contextmenu-item @click="onClickShowHideLines"><i :class="[showReferenceLines ? 'el-icon-check' : 'el-icon-close']"></i> 定位线</v-contextmenu-item> -->
            <!-- <v-contextmenu-item divider></v-contextmenu-item> -->
            <v-contextmenu-item @click="onMeasurements('del')"><i class="el-icon-delete"
                    style="padding-right:4px"></i>清除标注</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-submenu>
                <span slot="title"><i class="el-icon-folder-checked" style="padding-right:4px"></i>序列工具</span>
                <v-contextmenu-item @click="onMeasurements('flush')">刷新</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onMeasurements('save')">保存</v-contextmenu-item>
            </v-contextmenu-submenu>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-submenu>
                <span slot="title"><i class="iconfont iconsanwei" style="padding-right:4px"></i>其它重建</span>
                <v-contextmenu-item @click="onClickOhterRebuild('2D')">二维 斜切</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickOhterRebuild('3D')">三维 体渲染</v-contextmenu-item>
            </v-contextmenu-submenu>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-submenu>
                <span slot="title"><i class="iconfont iconmidpoint" style="padding-right:4px"></i>定位线样式</span>
                <v-contextmenu-submenu>
                    <span slot="title">CT \ MR</span>
                    <v-contextmenu-item @click="onClickCrosshairs('CT', 'small')">小定位线</v-contextmenu-item>
                    <v-contextmenu-item divider></v-contextmenu-item>
                    <v-contextmenu-item @click="onClickCrosshairs('CT', 'large')">大定位线</v-contextmenu-item>
                </v-contextmenu-submenu>
                <v-contextmenu-submenu>
                    <span slot="title">PT \ NM</span>
                    <v-contextmenu-item @click="onClickCrosshairs('PT', 'small')">小定位线</v-contextmenu-item>
                    <v-contextmenu-item divider></v-contextmenu-item>
                    <v-contextmenu-item @click="onClickCrosshairs('PT', 'large')">大定位线</v-contextmenu-item>
                </v-contextmenu-submenu>
                <v-contextmenu-submenu>
                    <span slot="title">融合</span>
                    <v-contextmenu-item @click="onClickCrosshairs('FUSE', 'small')">小定位线</v-contextmenu-item>
                    <v-contextmenu-item divider></v-contextmenu-item>
                    <v-contextmenu-item @click="onClickCrosshairs('FUSE', 'large')">大定位线</v-contextmenu-item>
                </v-contextmenu-submenu>
                <v-contextmenu-submenu>
                    <span slot="title">MIP</span>
                    <v-contextmenu-item @click="onClickCrosshairs('MIP', 'small')">小定位线</v-contextmenu-item>
                    <v-contextmenu-item divider></v-contextmenu-item>
                    <v-contextmenu-item @click="onClickCrosshairs('MIP', 'large')">大定位线</v-contextmenu-item>
                </v-contextmenu-submenu>
                <!-- <v-contextmenu-item @click="onClickCrosshairs('small')">小定位线</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickCrosshairs('large')">大定位线</v-contextmenu-item> -->
            </v-contextmenu-submenu>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-submenu>
                <span slot="title" @click.top="onClickWebSync('start')"><i
                        :class="[synchronization.start ? 'el-icon-check' : 'el-icon-close']"
                        style="padding-right:4px"></i>浏览器关联</span>
                <v-contextmenu-item @click="onClickWebSync('position')"><i style="padding-right:4px"
                        :class="[synchronization.position ? 'el-icon-check' : 'el-icon-close']"></i>滚动翻页</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickWebSync('zoomPan')"><i style="padding-right:4px"
                        :class="[synchronization.zoomPan ? 'el-icon-check' : 'el-icon-close']"></i>缩放&平移</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickWebSync('window')"><i style="padding-right:4px"
                        :class="[synchronization.window ? 'el-icon-check' : 'el-icon-close']"></i>窗宽窗位</v-contextmenu-item>
            </v-contextmenu-submenu>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onClickRebuildSyncZoom"><i
                    :class="[rebuildSyncZoom ? 'el-icon-check' : 'el-icon-close']" style="padding-right:4px"></i>冠、矢同步
            </v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="toggleCoordinateMoving"><i :class="coordinateMenuText"
                    style="padding-right:4px"></i>隐藏坐标微调</v-contextmenu-item>
            <v-contextmenu-item @click="toggleRotationMoving"><i :class="rotationMenuText"
                    style="padding-right:4px"></i>隐藏图像微调</v-contextmenu-item>

        </v-contextmenu>

        <SaveImage :element="saveImage.element" v-model="saveImage.visible"></SaveImage>
    </div>
</template>
<script>
// import html2canvas from "html2canvas"
import CornerstoneViewport from '$components/CornerstoneViewport.vue'
import LayoutBox from '$components/tools/LayoutBox'
import BaseTools from '$components/tools/BaseTools'
import AreaFit from '$src/layout/AreaFit.vue'
import SaveImage from '$components/layer/SaveImage'

import tryGetVtkVolumeForSeriesNumber from '$library/cornerstone/mpr/tryGetVtkVolumeForSeriesNumber.js';
import appState from '$library/cornerstone/mpr/store/appState.js';
import setClipPlaneXY from '$library/cornerstone/mpr/getClipPlaneXY.js'

import Percentage from '$components/tools/Percentage'

import {
    View2D, vtkSVGCrosshairsWidget, invertVolume, vtkInteractorStyleMPRSlice,
    vtkInteractorStyleMPRCrosshairs,
    vtkSVGProbeWidget,
    vtkInteractorStyleProbe,
    vtkSVGMarkWidget,
    vtkInteractorStyleMark,
    vtkInteractorStyleMPRWindowLevel
} from "$library/vtk";
// import vtkVolumeMapper from "vtk.js/Sources/Rendering/Core/VolumeMapper";
// import vtkVolume from "vtk.js/Sources/Rendering/Core/Volume";
import setClipPlane from '$library/vtk/lib/data/setClipPlane.js';

import event from '$src/event.js'

import findNearIndex from '$library/cornerstone/tools/math/findNearIndex.js';
import getIppByPoint from '$library/cornerstone/tools/math/getIppByPoint.js';
import VoiMapping from '$library/cornerstone/function/VoiMapping.js';

import { getNewImageIds, getVolumeSliceNumber, loadFrameImage } from '$library/cornerstone/function/getImageIds.js';
import getVtkVolumeBySeriesUid from '$library/cornerstone/function/getVtkVolumeBySeriesUid.js'
import toolCoordTransition, { updateToolCoord } from '$library/cornerstone/function/toolCoordTransition.js'

import VtkTool from '$src/mixin/VtkTool.js';
import SelectTool from "$src/mixin/SelectTool.js";
import Printscreen from "$src/mixin/Printscreen.js";
// 多开关联
import broadcastManager from '$library/broadcast';
// import vtkImageCropFilter from 'vtk.js/Sources/Filters/General/ImageCropFilter';
// import { getClipPlaneXY } from '$library/cornerstone/mpr/getClipPlaneXY.js'

// import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';
import { dbLayoutSet } from '$library/db';
import getConfigByStorageKey from '$library/utils/configStorage.js';
import vtkMath from 'vtk.js/Sources/Common/Core/Math'
import { createImage } from '$library/cornerstone/mprImageLoader.js'
import { debounce } from 'lodash-es';

import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js';

const calculateSUV = cornerstoneTools.importInternal('util/calculateSUV');
const freehandUtils = cornerstoneTools.importInternal('util/freehandUtils');

export default {
    name: 'ViewSlice',
    mixins: [SelectTool, VtkTool, Printscreen],
    components: {
        CornerstoneViewport,
        LayoutBox,
        BaseTools,
        Percentage,
        View2D,
        AreaFit,
        SaveImage
    },
    props: { 
        tabId: {
            default: 'none',
        },
        windowIsActive: {
            default: false,
        }
    },
    data() {
        return {
            layoutList: [],     // 配置布局
            saveImage: {		// 保存图像
                visible: false,
                element: null,
                rightSelectElement: null,
            },
            // componentId: '',    // 当前模块 id
            series: false, // 加载状态
            seriesId: 'false', // 加载状态
            loadingFull: false, // 加载状态
            scrollRotate: true, // 滚轮旋转
            viewports: [],
            activeViewportIndex: 0,
            activeTool: 'Airtools', // MprCrosshairs,
            crosshairsTool: {
                mipShow: true
            },
            vtkActiveTool: 'Wwwc',
            style: {
                width: '100%',
                height: '100%',
            },
            viewportElements: [],
            isOverlayVisible: true,
            ctUid: null,
            petUid: null,
            ctImageIds: [],
            petImageIds: [],
            loading: false,
            percentage: {         // 进度条数据
                loading: false,
                total: 0,
                loadCount: 0,
                message: '下载'
            },
            preloadPercentage: 100,
            thickness: 3,         // 切片层间隔（单词错误）  
            imageOrientationPatient: [], // 角度
            originSeries: [
                // 这个存储的原始数据位置是固定的
                // 0 , 1 y 冠. 2,3 x 矢. 4,5 z 横. 
                // 0,2,4 ct、mr
                // 1,3,5 pt、nm
            ],
            defaultLayout: [
                // CT/MR、PT/NM     TODO 注意不要改这些存储位置
                // 0、1冠     y
                // 2、3矢     x
                // 4、5横     z
                // 6、7mip
                // 8 冠融合  1,0
                // 9 矢融合  3,2
                // 10 横融合 5,4
                // 11 mip融合 6 7
                // 由于 MIP 是通过 vtk.js 实现的。 cornerstone 没有 MIP 图
            ],
            selectLayout: '2x2axial',
            loadShowCount: 0, // 加载显示的图像
            originSeriesSliceType: 'axial',  // (CT|MR)原序列切片类型
            apis: [],         // vtk 控制的apis(组件)
            isCrosshairsCallback: false, // 控制vtk定位线回调
            showReferenceLines: false,
            showCoordinateTools: false,
            showRotationTools: false,
            coordinateToolsIndex: -1,
            rotationToolsIndex: -1,
            referenceIds: [],             // 参考的ids，需要清除
            layoutMatrix: {
                column: 2,
                originalIdx: [],            // 原始改变布局 defaultLayout 值
                matrix10: [],               // 基于 original 存储10x10布局 defaultLayout 的值
                isReload: true,             // 是否获取新的 matrix10 存储值
                curColumn: 2,
                curRow: 2,
            },
            renderAbsoluteThrottle: {       // 渲染节流
                renderEvents: false,
                renderElement: null,
                timer: null
            },
            ignoreFiredEvents: false,       // 渲染新图像忽略事件
            firedEvents: null,              // 忽略事件时，用户操作的元素
            // scrollThrottle: false,       // 滚动节流
            toolThrottle: null,         // 工具渲染节流
            toolRenderLast: false,       // 用于控制，在工具渲染后，不在触发点选状态（直接赋值选中）
            allowAngel: 'all',           // all 允许全部角度， x, y, z
            allModalityIndex: {
            },
            fullScreenIndex: -1,         // 用于判断全屏
            styleFont: 'overlay-10',     // 响应字体变化
            showAction: -1,
            curOpenSeriesId: '',
            synchronizerCrosshairs: Object, // 十字线同步器
            taskNumber: 0,
            ctOffset: '0,0,0',
            ptOffset: '0,0,0',
            vtkAndCsRenderState: {
                curRender: 'none',
                timer: null
            }, // 谁在渲染，截断处理
            groupModality: '', // 当前打开重建设备类型（打开多个就拼接多个 PTMR PTCT CT MR...）
            gridLayoutStyle: {
                containerStyle: {},
                itemStyle: []
            },
            errCount: 0,      // 下图错误次数
            preloaderId: null,
            timerAddCrosshairs: null, // 添加定位线清除句柄
            timerSetCrosshairs: null
        }
    },
    watch: {
        // 监听工具改变
        activeTool: {
            handler(later) {
                if (later === 'Wwwc') {
                    this.vtkToggleWL('Wwwc')
                } else if (later === 'vtkInteractorStyleMPRSlice') {
                    this.vtkToggleToMPRSlice('vtkInteractorStyleMPRSlice')
                } else if (later === 'DragProbe') {
                    this.vtkToggleToProbe('DragProbe')
                } else if (later === 'TextMarker') {
                    this.vtkToggleToProbe('TextMarker')
                } else {
                    this.vtkToggleToCrosshairs('NuclearCrosshairs')
                }
            },
            // immediate: true  // 进组件就执行了，没有用，要等加载出来才有用
        },
        'crosshairsTool.mipShow': {
            handler(later) {

                if (later) {
                    // 开启线条绘制
                    this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: false })
                    this.activeCrosshairs()
                    setTimeout(() => {
                        this.activeTool = 'Airtools';
                    }, 0);

                } else {
                    this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: true })
                    const tempTool = this.activeTool
                    this.activeTool = 'temp'
                    setTimeout(() => {
                        this.activeTool = tempTool
                    }, 0);
                }

                const showMipCross = !this.$store.state.hideReferenceLines[this.tabId] // 定位线按钮是否开启
                // 控制定位线显示与否
                this.apis.forEach(api => {
                    const { svgWidgetManager, svgWidgets } = api;
                    this.vtkAndCsRenderState.curRender = 'cs'
                    this.$nextTick(() => {
                        svgWidgets.crosshairsWidget.setDisplay(showMipCross)
                        svgWidgetManager.render()
                        this.setvtkAndCsRenderThrottle()
                    })
                })

                // 在改变定位线时候，要更新图像(开启关闭定位线更新)
                this.viewportMap(el => {
                    if (el) {
                        cornerstone.updateImage(el)
                    }
                })

                if (later) {
                    this.vtkToggleToCrosshairs('NuclearCrosshairs')
                }

            },
        },
        seriesId: {
            handler() {
                this.curOpenSeriesId = this.seriesId
            },
            immediate: true
        },
        triggerLayoutSet(later) {
            // vuex 中的布局设置弹窗打开后的状态
            if (!later) {
                // 弹窗关闭，尝试获取新的布局
                this.getLayout();
            }
        }
    },
    computed: {
        showCsViewportLen() {
            return this.viewports.filter(item => { return item.sModality && item.imageIds.length }).length;
        },
        defaultLayoutShow() {
            return this.defaultLayout.filter(_ => {
                return !_.hidden
            })
        },
        groupId() {
            // 当前打开重建组 id
            return this.petUid + '&' + this.ctUid;
        },
        triggerLayoutSet() {
            return this.$store.state.triggerLayoutSet;
        },
        rebuildSyncZoom() {
            return this.$store.state.rebuildSyncZoom;
        },
        synchronization() {
            return this.$store.state.synchronization;
        },
        coordinateMenuText() {
            return this.showCoordinateTools && this.coordinateToolsIndex === this.activeViewportIndex ? 'el-icon-close' : 'el-icon-check'
        },
        rotationMenuText() {
            return this.showRotationTools && this.rotationToolsIndex === this.activeViewportIndex ? 'el-icon-close' : 'el-icon-check'
        },
        layoutFill() {
            return this.$store.state.layoutFill;
        },
    },
    created() {
        // const status = getConfigByStorageKey('configs-crosshairs-show');
        // if (!status) {
        //     this.crosshairsTool = false
        // }
    },
    mounted() {
        const status = getConfigByStorageKey('configs-crosshairs-show');
        if (!status) {
            this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: true })
            this.crosshairsTool.mipShow = false
        }
        this.getLayout();
        // 设置显示与否
        // this.setReferenceLines(this.showReferenceLines)
        this.setSynchronizerCrosshairs()
        // this.componentId = this.$fun.onlyValue()
        // 开启 emit changeSliceSeries 事件
        // event.$on('changeSliceSeries', this.setSeries)
        event.$on('broadcastRender', this.broadcastRender)
        event.$on('broadcastScroll', this.broadcastScroll)
        this.series = {
                uids: {
                    ct: '1.2.840.113619.2.281.41200.1578223.1689125425',
                    pet: '********.1107.5.1.4.48438.2.0.48663716751252'
                }
            }
            this.seriesId = '1.2.826.0.1.3680043.2.109.5.20210705081649223.450892108.1'
        // 获取要打开的 series uid
        const uids = this.series && this.series.uids;
        // if (!uids) {
        //     // 没有 uid 表示第一次打开，打开弹窗，弹窗有自动重建功能
        //     // 打开弹窗，弹窗状态存在 vuex 中
        //     // this.$refs.baseTools.onClickOpenDicom()
        //     // 序列选择，刷新，获取自动重建
        //     this.$store.commit('SET_OPENSLECTSERIES', { refresh: true })
        //     // 在当前 tab 组件渲染
        //     event.$emit('currentShowSeries')
        //     return
        // }
        // 打开相应的序列

        
        this.setSeries(uids)

        // if (!this.$store.state.seriesMap.size){

        //     const loading = this.$loading.service({
        //         target: '.c-content'
        //     })
        // 	try {
        //         this.$store.dispatch('loadStudy').then(() => {

        //             // 打开弹窗，弹窗状态存在 vuex 中
        //             this.$refs.baseTools.onClickOpenDicom()
        //             // 在当前 tab 组件渲染
        //             event.$emit('currentShowSeries')

        //             loading.close()

        //         }).catch(err => {
        //             let msg = '重建失败！请联系管理员'
        //             if (err) {
        //                 msg = err.msg
        //             }
        //             this.$message({
        //                 type: 'error',
        //                 message: msg
        //             })
        //             loading.close()
        //         })
        //     } catch (err) {
        //         loading.close()
        //     }
        // }else {
        //     const uids = this.series && this.series.uids;
        // 	if (!uids) return;
        // 	// 打开相应的序列
        // 	this.setSeries(uids)
        // }

    },
    beforeDestroy() {
        // event.$off('changeSliceSeries', this.setSeries)
        event.$off('broadcastRender', this.broadcastRender)
        event.$off('broadcastScroll', this.broadcastScroll)
        this.updateLoadTabTask('del');

        window.clearInterval(window['imagePreloadCounter' + this.preloaderId])
    },
    methods: {
        onChangePage(isUp = 1) {

            if (this.activeViewportIndex == -1) {
                return
            }
            let type = ''
            const viewport = this.viewports[this.activeViewportIndex]
            // vtk 的不管
            if (viewport.vtk) {
                return
            }
            if (viewport.layerIds) {
                type = 'FUSE' + viewport.orientation
            } else {
                type = viewport.sModality + viewport.orientation
            }

            let firstElment = null
            let firstIndex = 0
            let lastIndex = 0
            let num = 0
            let imageIdLen = 0
            this.viewportMap(el => {
                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state
                const curIdx = allStack.data[0].currentImageIdIndex
                imageIdLen = allStack.data[0].imageIds.length
                if (state.viewportType === type) {
                    if (!firstElment) {
                        firstIndex = curIdx
                        firstElment = el
                    }
                    lastIndex = curIdx
                    num += 1
                }
            })

            let scrollIndex = firstIndex
            if (isUp) {
                // 向上切换，最后只显示第一张，不会重复
                scrollIndex = Math.max(firstIndex - num, 0)
            } else {
                // 向下切换，可以一直重复
                scrollIndex = lastIndex + 1 >= imageIdLen ? 0 : lastIndex + 1
            }

            cornerstoneTools.scrollToIndex(firstElment, scrollIndex)

        },
        // 点击打开其它重建
        onClickOhterRebuild(type) {
            if (type === '2D') {
                event.$emit('onSelectUid', this.series.uids, 'ViewVtkFuseMpr', this.seriesId);
            } else {
                const imageIds = this.viewports[this.activeViewportIndex].imageIds;
                if (imageIds && imageIds[0]) {
                    const [, seriesNumber] = imageIds[0].split(':');
                    event.$emit('onSelectUid', seriesNumber, 'ViewVtkVR', this.seriesId);
                }
            }
        },
        updateLoadTabTask(type = 'add') {
            if (type === 'add') {
                this.taskNumber += 1;
                event.$emit('updateLoadTabTask');
            } else if (type === 'del' && this.taskNumber) {
                this.taskNumber -= 1;
                event.$emit('updateLoadTabTask', 'del');
            }
        },
        // 获取布局列表
        getLayout() {
            // 默认写死布局
            this.defaultLayout = getConfigByStorageKey('configs-rebuild-layout')

            this.layoutList = [
                {
                    img: 'images/Layout3D_12.png',
                    imageType: 'loca',
                    text: '',
                    id: '20220517-0',
                    options: {
                        layout: {
                            row: 3,
                            column: 3,
                            num: 7
                        },
                        selectValue: [7, 4, 2, 0, 10, 9, 8], // originSeries 对应 下标位置
                        containerStyle: {
                            flexDirection: 'column',
                        },
                        itemStyle: [
                            { "height": "100%", "width": "33.333333333333336%" },
                            { "width": "33.333333333333336%", "height": "33.333333333333336%" },
                            { "width": "33.333333333333336%", "height": "33.333333333333336%" },
                            { "width": "33.333333333333336%", "height": "33.333333333333336%" },
                            { "width": "33.333333333333336%", "height": "33.333333333333336%" },
                            { "width": "33.333333333333336%", "height": "33.333333333333336%" },
                            { "width": "33.333333333333336%", "height": "33.333333333333336%" }
                        ]
                    }
                },
                {
                    img: 'images/Layout3D_13.png',
                    imageType: 'loca',
                    text: '',
                    id: '20220517-1',
                    options: {
                        layout: {
                            column: 2,
                            row: 2
                        },
                        selectValue: [
                            1, 0,
                            8, 7,
                        ],
                    }
                },
                {
                    img: 'images/Layout3D_14.png',
                    imageType: 'loca',
                    text: '',
                    id: '20220517-2',
                    options: {
                        layout: {
                            column: 2,
                            row: 2
                        },
                        selectValue: [
                            3, 2,
                            9, 7,
                        ],
                    }
                },
                {
                    img: 'images/Layout3D_15.png',
                    imageType: 'loca',
                    text: '',
                    id: '20220517-3',
                    options: {
                        layout: {
                            column: 5,
                            row: 5
                        },
                        selectValue: [
                            10, 10, 10, 10, 10,
                            10, 10, 10, 10, 10,
                            10, 10, 10, 10, 10,
                            10, 10, 10, 10, 10,
                            10, 10, 10, 10, 10,
                        ],
                    }
                },
            ];


            // 配置可添加布局
            dbLayoutSet.then(e => {

                // 1 重建
                e.getGroup(1).then(e => {
                    if (e.success) {
                        const list = e.data.sort((a, b) => {
                            return a.position - b.position;
                        });
                        this.layoutList = this.layoutList.concat(list);
                    }
                })
            });
        },
        viewportState(item) {
            return {
                uid: item.uid,
                sModality: item.sModality,
                orientation: item.orientation,
                seriesId: 'mpr:' + this.petUid + '&&' + this.ctUid + this.thickness + item.orientation,
                ctUid: this.ctUid,
                petUid: this.petUid,
                imgIndex: this.allModalityIndex[item.sModality + item.orientation] || 0,
                thickness: this.thickness,
                patientId:  this.seriesId ,
                scaleParams: Object.assign({}, item.scaleParams),
                layoutId: String(item.layoutId),
                defaultViewport: {
                    current: this.allModalityIndex[item.sModality + item.orientation + 'Viewport'] || {},
                    fuse: this.allModalityIndex[`${item.sModality}${item.orientation}ViewportFuse`], // 不需要默认对象
                    layer: this.allModalityIndex[`CT${item.orientation}Viewport`], // 不需要默认对象
                },
            }
        },
        // 设置十字线同步器
        setSynchronizerCrosshairs(isNull = false) {
            // 先进行判断销毁
            if (Object.keys(this.synchronizerCrosshairs).length) {
                this.synchronizerCrosshairs.destroy()
                this.synchronizerCrosshairs = Object
                if (isNull) {
                    this.synchronizerCrosshairs = null
                    return
                }
            }
            this.synchronizerCrosshairs = new cornerstoneTools.Synchronizer(
                'cornerstonenewimage',
                cornerstoneTools.updateImageSynchronizer
            )
        },
        // 双击全屏
        onDblclickViewport(el, index) {
            if (this.activeTool === 'TextMarker') return;
            // if (this.activeTool !== 'Airtools') return;

            // 全屏
            this.fullScreenIndex = this.fullScreenIndex === index ? -1 : index

            // 全屏后，触发让 cornerstone 相应调整
            this.$nextTick(() => {
                // cornerstone.resize(el)
                if (el) {
                    cornerstone.resize(el); // 之前为什么我要换 new Event ?
                    this.setFontByWidthAndHeight(index);
                } else {
                    const myEvent = new Event('resize');
                    window.dispatchEvent(myEvent);
                }
            })

            // 同步其它视图
            setTimeout(() => {

                if (el) {
                    let offsetVal = 0;
                    for (let i = 0; i < index; i++) {
                        const viewport = this.viewports[i];
                        if (viewport.vtk) {
                            offsetVal += 1;
                        }
                    }
                    const item = this.$refs.csViewport[index - offsetVal] || {}
                    if (item.state) {
                        item.syncCallBack('relative')
                    }
                }

                // 执行 baseTools 组件中的方法
                this.$refs.baseTools.onAllRender()
            }, 100);
        },
        onClickVTKFullscreen(index) {
            this.onDblclickViewport(null, index)
        },

        // 获取选中的序列，设置切片的序列。
        async setSeries(uid) {
            this.ctUid = uid.ct;
            this.petUid = uid.pet;
            // || {
            // pet: '********.1107.5.1.4.48438.2.0.48663716751252'
        // }
            // const allSeries = this.$store.state.seriesMap.get(this.curOpenSeriesId).instanceList;

             
            const pathList = require.context('../../public/dicom/W001891/NM', true, /\.dcm$/).keys().map(i => 'dicomweb:/dicom/W001891/NM' + i.replace('.', '')) 
            const pathList2 = require.context('../../public/dicom/W001891/CT', true, /\.dcm$/).keys().map(i => 'dicomweb:/dicom/W001891/CT' + i.replace('.', '')) 
                // console.log(pathList, pathList2)
            if (this.petUid) {
                // const series = allSeries.get(this.petUid)

                this.petImageIds = pathList
 
 

                this.groupModality = 'NM' // 当前选中打开序列设备类型

                if (!appState[this.petUid]) {
                    appState[this.petUid] = {}
                }
                if (!appState[this.petUid].series) {
                    appState[this.petUid].series = this.petImageIds
                }
            }

            if (this.ctUid) {
                // const series = allSeries.get(this.ctUid)

                this.ctImageIds = pathList2
                this.groupModality = 'CT' // 合并设备类型如：PTCT
                // 重建的序列, 存入 appState 让 vtk 及 cornerstone 使用
                if (!appState[this.ctUid]) {
                    appState[this.ctUid] = {};
                }
                // 没有加载图像列表，添加
                if (!appState[this.ctUid].series) {
                    appState[this.ctUid].series = this.ctImageIds
                }
            }

            // 存储到对比数据中
            if (this.ctUid && this.petUid) {
                this.$store.commit('SET_CONTRASTUID', this.ctUid + ',' + this.petUid);
            }
            //  nm 图像拆解
            if (this.petImageIds.length === 1) {
                const imageIds = await loadFrameImage(this.petImageIds)
                // 更新
                appState[this.petUid].series = imageIds
                this.petImageIds = appState[this.petUid].series
            }

            this.percentage.loading = true;
            this.percentage.loadCount = 0;
            this.percentage.total = this.ctImageIds.length + this.petImageIds.length;

            let ctPromises = [], petPromises = [];
            const errImageIds = [];
            // 不存在体数据
            if (this.ctUid && !appState[this.ctUid].vtkVolumes) {

                // 限制最大下载
                if (window.configs.startLimitDownload) {
                    ctPromises = this.ctImageIds
                }else {
                    ctPromises = this.ctImageIds.map((imageId) => {
                        return cornerstone.loadAndCacheImage(imageId).then(() => {
                            this.percentage.loadCount += 1
                        }).catch(() => {
                            errImageIds.push(imageId)
                        });
                    });
                }
            } else {
                this.percentage.loadCount += this.ctImageIds.length;
            }
            // 不存在体数据
            if (this.petUid && !appState[this.petUid].vtkVolumes) {

                // 限制最大下载
                if (window.configs.startLimitDownload) {
                    petPromises = this.petImageIds
                }else {
                    petPromises = this.petImageIds.map((imageId) => {
                        console.log(imageId)
                        return cornerstone.loadAndCacheImage(imageId).then(() => {
                            this.percentage.loadCount += 1
                        }).catch(() => {
                            errImageIds.push(imageId)
                        });
                    });
                }

            } else {
                this.percentage.loadCount += this.petImageIds.length;
            }

            this.updateLoadTabTask();

            if (window.configs.startLimitDownload) {
                this.prefetchLoadImageIds(ctPromises.concat(petPromises))
            }else {
                // 全部序列加载“下载”完成
                this.loadPromiseAll(ctPromises.concat(petPromises), errImageIds);
            }

        },
        prefetchLoadImageIds(imageIds) {

            const requestType = 'prefetch';
            const preventCache = false;

            if (this.percentage.loadCount >= this.percentage.total) {
                this.loaderSeries()
                return
            }

            imageIds.forEach(imageId => {
                cornerstoneTools.requestPoolManager.addRequest(
                    {},
                    imageId,
                    requestType,
                    preventCache,
                    () => {
                        this.percentage.loadCount += 1
                        if (this.percentage.loadCount >= this.percentage.total) {
                            this.loaderSeries();
                        }
                    },
                    (imageId) => {
                        console.log('下载错误，错误请求头携带获取最新图像..')
                        this.$message.closeAll();
                        this.$message.info('下载图像失败');
                        // 不应该这样做
                        this.percentage.loadCount += 1
                        if (this.percentage.loadCount >= this.percentage.total) {
                            this.loaderSeries();
                        }
                    }
                );
            });
            cornerstoneTools.requestPoolManager.startGrabbing();
        },
        // 统一下载图像
        loadPromiseAll(imageIds, errImageIds) {
            const errMaxValue = 6
            Promise.all(imageIds).then(() => {
                // 错误
                if (errImageIds.length && this.errCount < errMaxValue) {
                    this.errCount += 1;
                    this.loadError(errImageIds);
                    return;
                }
                this.errCount = 0;
                this.loaderSeries();
            }).catch(() => {
                if (errImageIds.length && this.errCount < errMaxValue) {
                    this.errCount += 1;
                    this.loadError(errImageIds);
                }
            })
        },
        async loadError(imageIds) {
            console.log('图像错误，再次下载');
            const errImageIds = [];
            const imagePromise = imageIds.map((imageId) => {
                // 错误的图像，加上不缓存标识，从服务器获取最新。
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                }).catch(() => {
                    errImageIds.push(imageId)
                });
            });
            this.loadPromiseAll(imagePromise, errImageIds)
        },
        // 广播调用的方法
        broadcastRender(data) {
            this.renderCallBack(data.info.sourceViewport, data.info.clayData, data.info.type, true);
        },
        // 遍历一个相同位置的图像做切换
        broadcastScroll(data) {
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index);
                if (!el) {
                    continue;
                }

                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state;

                // 不同切片位置
                if (data.info.orientation !== state.orientation) {
                    continue;
                }

                // 滚动切换
                cornerstoneTools.scrollToIndex(el, data.info.currentImageIdIndex);
                break;
            }
        },
        // 组装数据，发送广播
        postBroadcastRender(source, data, type) {

            if (!this.synchronization.start ||                             // 没启动同步
                (!this.synchronization.window && type === 'absolute') ||   // 没启动窗宽窗位
                (!this.synchronization.zoomPan && type === 'relative')) {  // 平移缩放
                return;
            }

            // colormap, el 属性没办法通过广播发送
            const { colormap, ...sourceViewport } = source;
            const { el, ...clayData } = data;

            if (colormap) {
                let value = 'gray';
                value = typeof colormap == 'string' ? colormap : colormap.getId();

                sourceViewport.colormap = value;
            }

            broadcastManager.postMessage(
                {
                    info: { sourceViewport, clayData, type },
                    sendTabId: this.tabId,
                    fun: 'broadcastRender',
                }
            );
        },
        postBroadcastScroll(data) {
            // 开启同步，开启位置同步，才发送广播
            // 未开启，结束
            if (!this.synchronization.start || !this.synchronization.position) {
                return;
            }
            const { el, ...obj } = data;
            if (!broadcastManager.isBroadcastScroll()) {
                broadcastManager.postMessage(
                    {
                        info: obj,
                        sendTabId: this.tabId,
                        fun: 'broadcastScroll',
                    }
                );
            }

        },
        // 渲染回调
        /**
         * 渲染的原视窗 viewport 中的数据信息
         * 图像信息
         * 需要同步类型 
         * isSend true 为广播调用
         */
        renderCallBack(sourceViewport, clayData, type, isSend = false) {
            // 在下载图像时，最小化、或切换其它 tab 页在缩放后，回到看图页后，融合不正确。
            if (!sourceViewport.width) {
                return;
            };
            // 同步操作-分为相对，绝对。
            if (type === 'absolute') {
                // 绝对，赋值一样的信息 TODO 这个功能突然用不了
                // this.mriColorRender(sourceViewport, clayData);

                // 节流操作，避免多次执行
                if (this.renderAbsoluteThrottle.renderEvents && this.renderAbsoluteThrottle.renderElement !== clayData.el) {
                    this.setRenderAbsoluteThrottle();
                    return;
                }
                this.renderAbsoluteThrottle.renderElement = clayData.el;
                this.renderAbsoluteThrottle.renderEvents = true;
                this.renderAbsolute(sourceViewport, clayData);
            } else {
                // 相对 按照比例规模赋值
                this.renderRelative(sourceViewport, clayData)
            }
            // 非广播调用，触发广播
            if (!isSend) {
                this.postBroadcastRender(sourceViewport, clayData, type);
            }
        },
        // 触发绘制其它组同张工具
        triggerToolGroupRender(data) {

            // 文本不同步
            if (data.toolName === 'TextMarker') {
                return;
            }
            // 遍历视图
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }

                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state;
                const imageIdIndex = allStack.data[0].currentImageIdIndex;
                // 不相等，另外一同步设备 && 相同切面 && 相同位置 index 
                if (data.sModality !== state.sModality
                    && data.orientation === state.orientation
                    && data.imageIdIndex === imageIdIndex) {

                    if (data.status === 'add') {
                        const theComponent = this.$refs.csViewport[index]
                        const seriesId = theComponent.seriesId
                        const imageId = cornerstone.getImage(el).imageId
                        // 坐标转换
                        const measurement = toolCoordTransition(data.element, data.toolName, data.measurementData, el)
                        // 添加
                        cornerstoneTools.addToolState(el, data.toolName, measurement);
                        cornerstoneTools.imageIdStateManager.addToolState(this.seriesId, seriesId, imageId, data.toolName, measurement)
                        cornerstoneTools.imageIdStateManager.triggerRender()

                        // PT绘制工具完成后，融合会出现工具会出现触碰效果，等待 0.5 秒 渲染清除触碰工具效果
                        this.renderUpdateImage();
                        // 手动触发 tool 渲染事件
                        // const eventData = {
                        //     toolName: data.toolName,
                        //     toolType: data.toolName,
                        //     element: el,
                        //     measurementData: measurement,
                        //     notRenderGroup: true
                        // };
                        // cornerstoneTools.triggerEvent(el, cornerstoneTools.EVENTS.MEASUREMENT_COMPLETED, eventData);
                    } else if (data.status === 'removed') {
                        const theComponent = this.$refs.csViewport[index]
                        const seriesId = theComponent.seriesId
                        const list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
                        list.forEach(toolItem => {
                            if (!toolItem.toolData) return
                            // 是否有相同组，需要移除的
                            if (toolItem.toolData.connectId === data.measurementData.connectId) {
                                // 移除原生工具状态
                                cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(toolItem.imageId, toolItem.toolName, toolItem.toolData)
                                // 移除在原生基础工具状态
                                cornerstoneTools.imageIdStateManager.delToolState(seriesId, toolItem.imageId, toolItem.toolName, toolItem.toolData.uuid)
                            }
                        })

                        cornerstoneTools.triggerEvent(el, cornerstoneTools.EVENTS.MEASUREMENT_REMOVED, {});

                        (() => {
                            for (let index = 0; index < this.viewportElements.length; index++) {
                                const el = this.getEnabledElement(index)
                                if (!el) {
                                    continue
                                }
                                cornerstone.updateImage(el);
                            }
                        })()
                    } else {
                        // 坐标转换
                        const toolList = cornerstoneTools.globalImageIdSpecificToolStateManager.get(el, data.toolName);
                        if (!toolList) {
                            return;
                        }
                        const isMeasurement = toolList.data.find(item => {
                            return item.connectId === data.measurementData.connectId;
                        })
                        if (isMeasurement) {
                            // 坐标转换
                            const measurement = toolCoordTransition(data.element, data.toolName, data.measurementData, el, false)
                            updateToolCoord(measurement, isMeasurement, data.toolName)
                        }
                    }
                    return;
                }

            }
        },
        // 触发体测量的显示
        triggerLesionAreaGroupRender(data) {

            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }

                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state;
                // const imageIdIndex = allStack.data[0].currentImageIdIndex;
                // 不相等，另外一同步设备 && 相同切面 && 相同位置 index 
                if (data.sModality !== state.sModality
                    && data.orientation === state.orientation) {
                    // add时： 分发到不同的序列后再根据范围重新计算，ct计算hu值，pt已有值，fuse已有值，所以只用处理ct
                    // removed: ct-pt互相触发，都要处理
                    if (data.status === 'add') {
                        const theComponent = this.$refs.csViewport[index]
                        const seriesId = theComponent.seriesId
                        const series = allStack.data[0].imageIds || [];
                        const imageId = series[data.imageIdIndex]
                        const measurement = toolCoordTransition(data.element, data.toolName, data.measurementData, el)
                        const patientId = this.$store.state.seriesMap.get(this.seriesId).seriesInfo.key
                        measurement.seriesId = seriesId

                        const polyBoundingBox = measurement.polyBoundingBox
                        const pixels = cornerstone.getPixels(
                            el,
                            polyBoundingBox.left,
                            polyBoundingBox.top,
                            polyBoundingBox.width,
                            polyBoundingBox.height
                        );

                        measurement.meanStdDev = freehandUtils.calculateFreehandStatistics(
                            pixels,
                            polyBoundingBox,
                            measurement.handles.points
                        );
                        measurement.meanStdDevSUV = null
                        measurement.modality = 'CT'
                        measurement._modality = 'CT'
                        measurement.uuid = this.$fun.onlyValue()

                        measurement.TLG = ''
                        // measurement.text = `Area:0.29cm²\n\t\t\t\tMax:53.64\n\t\t\t\tAvg:50.42 MTV=3.89 TLG=249.87`

                        cornerstoneTools.imageIdStateManager.addToolState(patientId, seriesId, imageId,
                            'LesionArea', measurement, `体测量(Im=${data.imageIdIndex + 1})`)
                        cornerstoneTools.globalImageIdSpecificToolStateManager.addImageIdToolState(
                            imageId,
                            'LesionArea',
                            measurement
                        )


                    } else if (data.status === 'removed') {
                        const lesionAreaId = data.measurementData.lesionAreaId
                        const theComponent = this.$refs.csViewport[index]
                        const seriesId = theComponent.seriesId
                        const list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
                        list.forEach(toolItem => {
                            if (!toolItem.toolData) return
                            if (toolItem.toolData.lesionAreaId === lesionAreaId) {
                                cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(toolItem.imageId, toolItem.toolName, toolItem.toolData)
                                cornerstoneTools.imageIdStateManager.delToolState(seriesId, toolItem.imageId, toolItem.toolName, toolItem.toolData.uuid)
                            }
                        })

                        cornerstoneTools.triggerEvent(el, cornerstoneTools.EVENTS.MEASUREMENT_REMOVED, {});

                        for (let index = 0; index < this.viewportElements.length; index++) {
                            const el = this.getEnabledElement(index)
                            if (!el) {
                                continue
                            }
                            cornerstone.updateImage(el);
                        }
                    }
                }
                this.renderUpdateImage();
                cornerstoneTools.imageIdStateManager.triggerRender()
            }

        },
        // 触发体测量的最大值计算
        triggerLesionAreaSTAT() {
            // 先统计CT的HU值再传给融合图
            const lesionAreaIdTOTALMap = {}
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }
                const viewport = this.viewports[index]
                if (viewport.sModality !== 'CT') continue

                const theComponent = this.$refs.csViewport[index]
                const seriesId = theComponent.seriesId
                const tooldataList = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
                const lesionAreaDataList = tooldataList.filter(item => item.toolName === 'LesionArea')
                const lesionAreaIdMap = {}
                lesionAreaDataList.forEach(item => {
                    if (!lesionAreaIdMap[item.toolData.lesionAreaId]) lesionAreaIdMap[item.toolData.lesionAreaId] = []
                    lesionAreaIdMap[item.toolData.lesionAreaId].push(item)
                })
                Object.keys(lesionAreaIdMap).forEach(lesionAreaId => {
                    let toolData, meanStdDev, area, maxHU = 0, minHU = 10E8, totalHU = 0, MTV, avgHU = 0
                    const ctStatData = lesionAreaIdMap[lesionAreaId]
                    ctStatData.forEach(item => {
                        toolData = item.toolData || {}
                        meanStdDev = toolData.meanStdDev || {}
                        maxHU = Math.max(maxHU, meanStdDev.max)
                        minHU = Math.min(minHU, meanStdDev.min)
                        totalHU += meanStdDev.mean * toolData.area * toolData.sliceThickness * 0.1 // mm -> cm
                        MTV = +toolData.volume // cm3
                    })
                    avgHU = totalHU / MTV
                    // console.log(maxHU, minHU, avgHU)
                    ctStatData.forEach(item => {
                        toolData = item.toolData
                        toolData.totalHUmax = maxHU
                        toolData.totalHUmin = minHU
                        toolData.totalHUavg = avgHU
                        toolData.text = `Area:${item.toolData.area.toFixed(2)}cm²\n\t\t\t\tMax:${maxHU}\n\t\t\t\tAvg:${avgHU.toFixed(2)} MTV=${MTV}`
                        cornerstoneTools.imageIdStateManager.updateToolState(seriesId, item.imageId, item.toolName, toolData)
                    })

                    if (!lesionAreaIdTOTALMap[lesionAreaId]) lesionAreaIdTOTALMap[lesionAreaId] = {}
                    const lesionAreaDataObj = lesionAreaIdTOTALMap[lesionAreaId]
                    lesionAreaDataObj.totalHUmax = maxHU
                    lesionAreaDataObj.totalHUmin = minHU
                    lesionAreaDataObj.totalHUavg = avgHU
                })
            }

            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }
                const viewport = this.viewports[index]
                // 只对融合图像处理，但是融合图和pt是同一组数据
                if (viewport.layoutId != '8' && viewport.layoutId != '9' && viewport.layoutId != '10') continue
                const theComponent = this.$refs.csViewport[index]
                const seriesId = theComponent.seriesId
                const tooldataList = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
                const lesionAreaDataList = tooldataList.filter(item => item.toolName === 'LesionArea')
                let dataObj, lesionId, toolData
                lesionAreaDataList.forEach(item => {
                    toolData = item.toolData || {}
                    lesionId = toolData.lesionAreaId
                    dataObj = lesionAreaIdTOTALMap[lesionId]
                    if (!dataObj) return
                    toolData.totalHUmax = dataObj.totalHUmax
                    toolData.totalHUmin = dataObj.totalHUmin
                    toolData.totalHUavg = dataObj.totalHUavg
                })
            }
        },
        renderUpdateImage: debounce(function () {
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }
                cornerstone.updateImage(el);
            }
        }, 600),
        /**
         * 工具渲染处理
         * sourceElement 操作渲染的 dom 
         * sourceOrientation 操作渲染的 方向 x,y,z
         */
        onToolRenderCompleted(sourceElement, sourceOrientation, status) {
            this.toolRenderLast = true
            if (status === 'removed') {
                this.toolRenderLast = false
            }
            clearTimeout(this.toolThrottle)
            this.toolThrottle = setTimeout(() => {
                this.viewportMap((el) => {
                    // if (sourceElement === el) return;
                    // 获取当前显示的 dom 切面
                    const orientation = el.getAttribute('orientation');

                    // 不同切面的退出
                    if (orientation !== sourceOrientation) return;

                    cornerstone.updateImage(el);
                })
            }, 0);
            // 添加
            if (status === 'completed' || status === 'removed' || status === 'modifiedAndState') {
                this.$refs.baseTools.getNewToolList() // 侧边栏测量值列表更新
            }
        },
        // MR上色，触发相应融合的mr上色，pt取消上色
        mriColorRender(sourceViewport, clayData) {
            if (clayData.modality === 'MR' && sourceViewport.colormap && clayData.el) {
                const sourceImg = cornerstone.getImage(clayData.el);
                this.viewportMap((el) => {
                    if (!el) return;
                    // 获取融合图像全部层
                    const layers = cornerstone.getLayers(el)
                    if (layers.length) {
                        layers.forEach((_, index) => {
                            // 当前渲染的图像跟当前layers层是一样的
                            if (sourceImg.imageId == _.image.imageId) {
                                _.viewport.colormap = sourceViewport.colormap  // MR 赋值 融合 MR上色
                                // 如果MR没上色
                                if (sourceViewport.colormap == 'gray' || sourceViewport.colormap.getId() == 'gray') {
                                    const colormap = layers[index === 1 ? 0 : 1].viewport.colormap;
                                    // MR灰色，赋值另外一个层为 hot 
                                    if (!colormap || colormap == 'gray' || (colormap.constructor === Object && colormap.getId() == 'gray')) {
                                        layers[index === 1 ? 0 : 1].viewport.colormap = 'hot'
                                    }
                                } else {
                                    // MR上色，另外一个图层赋灰色
                                    layers[index === 1 ? 0 : 1].viewport.colormap = 'gray'
                                }
                            }
                        })
                        cornerstone.updateImage(el, true);
                    }
                })
            }
        },
        // 绝对渲染-实时触发
        renderAbsolute(sourceViewport, clayData) {
            // console.log(sourceViewport, clayData)
            const sourceModality = clayData.modality;      // 当前设备类型
            const sourceOrientation = clayData.orientation;

            const ww = sourceViewport.windowWidth
            const wc = sourceViewport.windowCenter

            const isLayer = clayData.el ? cornerstone.getLayers(clayData.el).length : 0;
            // if (isLayer) {
            //     let colormap = 'hot';
            //     if (typeof sourceViewport.colormap === 'string') {
            //         colormap = sourceViewport.colormap;
            //     }else if (sourceViewport.colormap && sourceViewport.colormap.getId()){
            //         console.log('1')
            //         colormap = sourceViewport.colormap.getId();
            //     }
            //     console.log(clayData.el)
            //     console.log(colormap)
            // }

            this.viewportMap((el) => {
                if (!el) return;

                if (clayData.el === el) {
                    return;
                }
                const img = cornerstone.getImage(el);                // 图像信息
                if (!img) return;
                const modality = img.data.string('x00080060') || ''; // 设备类型
                const viewport = cornerstone.getViewport(el)
                const orientation = el.getAttribute('orientation');
                const layers = cornerstone.getLayers(el);
                // 相同方向
                if (sourceOrientation === orientation) {
                    if (viewport.hflip !== sourceViewport.hflip ||
                        viewport.vflip !== sourceViewport.vflip ||
                        viewport.rotation !== sourceViewport.rotation) {
                        viewport.hflip = sourceViewport.hflip;
                        viewport.vflip = sourceViewport.vflip;
                        viewport.rotation = sourceViewport.rotation;
                        cornerstone.setViewport(el, viewport);
                    }
                }

                // 系统设备，并且是单层的
                if (modality === sourceModality && !layers.length) {
                    // 原(用户操作的视图)是等于1(非融合的) 不一样，赋值一样
                    if (clayData.el && cornerstone.getLayers(clayData.el).length == 0) {
                        if (viewport.invert !== sourceViewport.invert) {
                            viewport.invert = sourceViewport.invert;
                        }
                        if (viewport.colormap !== sourceViewport.colormap) {
                            viewport.colormap = sourceViewport.colormap;
                        }
                        cornerstone.setViewport(el, viewport);
                    }
                }

                if (layers.length && isLayer && (clayData.modality == 'PT' || clayData.modality == 'NM')) {

                    let colormap = 'hot';
                    if (typeof sourceViewport.colormap === 'string') {
                        colormap = sourceViewport.colormap;
                    } else if (sourceViewport.colormap && sourceViewport.colormap.getId()) {
                        colormap = sourceViewport.colormap.getId();
                    }
                    if (viewport.colormap !== colormap) {
                        viewport.colormap = colormap;
                        cornerstone.setViewport(el, viewport);
                    }
                }

                // 有多层的时候要循环多层
                layers.forEach(_ => {
                    const layer = cornerstone.getLayer(el, _.layerId);
                    const modality = layer.image && layer.image.data.string('x00080060') || '';
                    const viewport = layer.viewport;

                    if (sourceModality.toLocaleUpperCase() === modality.toLocaleUpperCase()) {
                        if (viewport.voi.windowWidth === ww &&
                            viewport.voi.windowCenter === wc) {
                            return;
                        }
                        viewport.voi.windowWidth = ww;
                        viewport.voi.windowCenter = wc;
                        cornerstone.updateImage(el)
                    }
                });

                // 相同设备
                if (sourceModality.toLocaleUpperCase() === modality.toLocaleUpperCase()) {

                    if (viewport.voi.windowWidth === ww &&
                        viewport.voi.windowCenter === wc) {
                        return;
                    }
                    viewport.voi.windowWidth = ww;
                    viewport.voi.windowCenter = wc;

                    cornerstone.setViewport(el, viewport);
                }
            })
            // 同步 PT 改变 MIP
            // this.syncSetVtkWwc(sourceModality, ww, wc)

            this.setRenderAbsoluteThrottle();
        },
        // 同步窗宽窗位到 vtk 中
        syncSetVtkWwc(sourceModality, ww, wc) {
            this.apis.forEach(api => {

                const modality = sourceModality.toLocaleUpperCase() == 'NM' ? 'PT' : sourceModality.toLocaleUpperCase()
                if (modality === api.modality) {
                    const { windowWidth, windowCenter } = api.getVoi()

                    if (windowWidth === ww && windowCenter === wc) {
                        return;
                    }
                    const rgbTransferFunction = api.volumes[0]
                    .getProperty()
                    .getRGBTransferFunction(0)
                    const renderWindow = api.genericRenderWindow.getRenderWindow()
                    
                    const { lower, upper } = VoiMapping.toLowHighRange(ww, wc)
                    rgbTransferFunction.setMappingRange(lower, upper)
                    renderWindow.render()
                    api.updateVOI(ww, wc)
                }
            })
        },

        // 设置节流还原
        setRenderAbsoluteThrottle() {
            clearTimeout(this.renderAbsoluteThrottle.timer);
            this.renderAbsoluteThrottle.timer = setTimeout(() => {
                this.renderAbsoluteThrottle.renderEvents = false;
            }, 0);
        },
        // 相对渲染，在操作完成后触发
        renderRelative(sourceViewport, clayData) {
            const sourceOrientation = clayData.orientation;

            // const width = sourceViewport.width
            // const height = sourceViewport.height

            this.viewportMap((el) => {
                if (!el) return;
                if (clayData.el === el) {
                    return;
                }

                const viewport = cornerstone.getViewport(el);
                if (!viewport || !viewport.displayedArea) return;
                const orientation = el.getAttribute('orientation');

                // 冠、矢同步
                let isSync = false;
                if (this.rebuildSyncZoom && ['x', 'y'].includes(sourceOrientation) && ['x', 'y'].includes(orientation)) {
                    isSync = true;
                }
                // 相同方向
                if (sourceOrientation === orientation || isSync) {
                    // 比例
                    // const scaleX = viewport.displayedArea.brhc.x / width;
                    // const scaleY = viewport.displayedArea.brhc.y / height;

                    const scaleX = sourceViewport.columnPixelSpacing / viewport.displayedArea.columnPixelSpacing
                    const scaleY = sourceViewport.rowPixelSpacing / viewport.displayedArea.rowPixelSpacing
                    // const scaleRatio = sourceViewport.scale / viewport.scale
                    // const scaleRatio = sourceViewport.width > sourceViewport.height ? scaleX : scaleY
                    const scaleRatio = sourceViewport.width >= sourceViewport.height ? scaleX : scaleY;

                    if (viewport.translation.x !== sourceViewport.x * scaleX ||
                        viewport.translation.y !== sourceViewport.y * scaleY ||
                        viewport.scale !== sourceViewport.scale / scaleRatio) {
                        viewport.translation.x = sourceViewport.x * scaleX;
                        viewport.translation.y = sourceViewport.y * scaleY;
                        viewport.scale = sourceViewport.scale / scaleRatio;
                        cornerstone.setViewport(el, viewport);
                    }
                }
            })
        },
        setvtkAndCsRenderThrottle() {
            clearTimeout(this.vtkAndCsRenderState.timer);
            this.vtkAndCsRenderState.timer = setTimeout(() => {
                this.vtkAndCsRenderState.curRender = 'none';
            }, 200);
        },
        // 相同切片同步滚动
        onNewImage(obj) {

            // 初始渲染，添加定位线
            if (obj.initRenderImage) {
                clearTimeout(this.timerAddCrosshairs);
                this.timerAddCrosshairs = setTimeout(() => {
                    this.initAddCrosshairs();
                }, 100);
            }

            // 设置当前渲染为 cornerstone ，在vtk反向改变截断
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'cs') {
                this.vtkAndCsRenderState.curRender = 'cs'
                this.setvtkAndCsRenderThrottle()
            }

            // 这种节流不是很好？ TODO
            // 不执行事件
            if (this.ignoreFiredEvents && this.firedEvents !== obj.el) {
                // 通过 js dom 节点属性也能获取到当前视图角度
                const firstDataSource = cornerstoneTools.getToolState(this.firedEvents, 'stack');
                const targetDataSource = cornerstoneTools.getToolState(obj.el, 'stack');
                if (firstDataSource.data[0].state.orientation === targetDataSource.data[0].state.orientation) {
                    return;
                }
            };
            this.firedEvents = obj.el;
            this.updateAllModalityIndex(obj.currentImageIdIndex, obj.viewportType, obj.orientation, obj.el, obj.seriesLength)
            this.postBroadcastScroll(obj)
            let changeIndex = Object.assign({}, this.allModalityIndex)

            this.viewportMap((el) => {
                // if (obj.el === el) return;
                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state;

                // 不同切面的退出
                if (state.orientation !== obj.orientation) { return }

                const index = changeIndex[state.viewportType]
                if (index === undefined || index === null) { return }

                const maxIndex = allStack.data[0].imageIds.length - 1
                if (index > maxIndex) {
                    // console.log(index)
                    this.syncScrollToIndex(el, index - maxIndex - 1)
                } else {
                    this.syncScrollToIndex(el, index < 0 ? 0 : index)
                }
                changeIndex[state.viewportType] += 1


            })

            // 改变 MIP 定位线位置
            if (this.vtkAndCsRenderState.curRender != 'vtk') {
                this.csChangeVtkIndex(obj.el)
            }
        },
        // 打开序列位置
        syncScrollToIndex(el, index) {
            this.ignoreFiredEvents = true;
            cornerstoneTools.scrollToIndex(el, index);
            setTimeout(() => {
                this.ignoreFiredEvents = false;
            }, 0);
        },
        // 更新序列下标
        updateAllModalityIndex(curIndex, type, orientation, el, seriesLength) {
            // 设置改变值
            let preNumber = 0;
            // 获取当前滚动时，相同序列前面有多少个
            for (let index = 0; index < this.viewportElements.length; index++) {
                const dom = this.getEnabledElement(index)
                if (!dom || dom === el) {
                    break;
                }
                let allStack = cornerstoneTools.getToolState(dom, 'stack');
                if (allStack && allStack.data.length && allStack.data[0].state.viewportType === type) {
                    preNumber += 1;
                }
            }
            // 获取最前面显示位置
            let oneIndex = curIndex
            for (let index = 0; index < preNumber; index++) {
                if (oneIndex == 0) {
                    oneIndex = seriesLength - 1
                } else {
                    oneIndex -= 1
                }
            }
            // let oneIndex = curIndex - preNumber;
            // 更新序列第一位值
            for (const key in this.allModalityIndex) {
                if (key.includes(orientation) && !key.includes('Viewport')) {
                    this.allModalityIndex[key] = oneIndex
                }
            }
        },
        // 过滤掉多余的原图 (与参照的切面位置对比)
        async filterPTImage() {
            // CT | MR
            let series0 = appState[this.ctUid || this.petUid].series;
            // PT
            // let series1 = appState[this.petUid].series;

            const lastIdx = series0.length - 1;

            await cornerstone.loadAndCacheImage(series0[0]);
            await cornerstone.loadAndCacheImage(series0[1]);
            await cornerstone.loadAndCacheImage(series0[lastIdx]);

            // CT | MR 第一张跟第二张图
            let startMeta = cornerstone.metaData.get('imagePlaneModule', series0[0]);
            let startMeta1 = cornerstone.metaData.get('imagePlaneModule', series0[1]);

            // 获取当前 CT | MR 序列原图所处面（冠、矢、横）
            this.originSeriesSliceType = this.$fun.getNormal(startMeta.imageOrientationPatient, true);

            if (this.originSeriesSliceType === 'axial') {

                let startMetaLast = cornerstone.metaData.get('imagePlaneModule', series0[lastIdx]);

                // MIP 裁剪
                if (!appState[this.groupId]) {
                    appState[this.groupId] = {}
                }
                if (!appState[this.groupId].clipMip) {
                    appState[this.groupId].clipMip = {}
                }
                // 没有放入
                if (!Object.keys(appState[this.groupId].clipMip).length) {
                    appState[this.groupId].clipMip = {
                        max: Math.max(startMeta.imagePositionPatient[2], startMetaLast.imagePositionPatient[2]),
                        min: Math.min(startMeta.imagePositionPatient[2], startMetaLast.imagePositionPatient[2])
                    };
                }
            }

            // 设置切片层厚 第一张图切片位置减第二张图切片位置
            let spacingSlice = Math.abs(startMeta.sliceLocation - startMeta1.sliceLocation) || 2.5;
            this.thickness = Number(spacingSlice.toFixed(2));
            this.imageOrientationPatient = startMeta.imageOrientationPatient;
        },
        // 改变层厚，数量
        async onChangeImg(value, key, el, offset) {
            if (!this.ctUid) return;
            let numberValue = Number(value);

            // 修改切片数量不能小于 0 
            if (numberValue <= 0) {
                this.$message.info('不能小于 1');
                return;
            }

            // 改的是层厚
            const { vtkVolume } = await tryGetVtkVolumeForSeriesNumber(this.ctUid);
            const vtkImageData = vtkVolume.vtkImageData;
            const [x0, y0, z0] = vtkImageData.getOrigin();  // x,y,z 原点
            const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
            const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

            let xStart = x0 // + xSpacing * (xMax - xMin);  // x最大值(ipp 切片x最大位)
            let yStart = y0 // + ySpacing * (yMax - yMin);  // y最大值
            let zStart = z0 // + zSpacing * (zMax - zMin);  // z最大值

            let xNmax = xSpacing * (xMin + xMax);
            let yNmax = ySpacing * (yMin + yMax);
            let zNmax = zSpacing * (zMin + zMax) + zSpacing;
            let xCount = numberValue
            let yCount = numberValue
            let zCount = numberValue

            const img = cornerstone.getImage(el)
            if (!img) return;
            const plane = cornerstone.metaData.get('imagePlaneModule', img.imageId);
            const angle = this.$fun.getNormal(plane.imageOrientationPatient, true);

            // 改变的是数量-计算出相应的层厚
            if (key === 'amount') {
                switch (angle) {
                    case 'sagittal':
                        this.thickness = xNmax / xCount;
                        break;
                    case 'coronal':
                        this.thickness = yNmax / yCount;
                        break;
                    case 'axial':
                        this.thickness = zNmax / zCount;
                        break;
                }
            } else {
                // 修改的层间隔大于有效区域不在变化
                if (
                    (angle === 'sagittal' && numberValue > xNmax) ||
                    angle === 'coronal' && numberValue > yNmax ||
                    angle === 'axial' && numberValue > zNmax
                ) {
                    this.$message.info('层间隔大于有效区域');
                    return;
                }
                this.thickness = numberValue;
            }
            const beforeIndex = this.activeViewportIndex
            // 记录布局信息
            this.updateLayoutModalityIndex()

            xCount = Math.floor(xNmax / this.thickness);
            yCount = Math.floor(yNmax / this.thickness);
            zCount = Math.floor(zNmax / this.thickness);

            let [sagittalsX, sagittalsXPT] = getNewImageIds('sagittal', { xCount, xStart }, this.thickness, this.ctUid, this.petUid, undefined, offset)
            let [coronalY, coronalYPT] = getNewImageIds('coronal', { yCount, yStart }, this.thickness, this.ctUid, this.petUid, undefined, offset)
            let [axialZ, axialZPT] = getNewImageIds('axial', { zCount, zStart }, this.thickness, this.ctUid, this.petUid, undefined, offset)


            // // 清除重建序列缓存
            this.originSeries.forEach(_ => {
                if (_ && _.imageIds) {
                    _.imageIds.forEach(img => {
                        // 存在缓存，清除缓存
                        if (cornerstone.imageCache.getImageLoadObject(img)) {
                            cornerstone.imageCache.removeImageLoadObject(img)
                        }
                    })
                }
            })
            this.originSeries = [
                // 冠状位
                { sModality: 'CT', orientation: 'y', imageIds: coronalY },
                { sModality: 'PT', orientation: 'y', imageIds: coronalYPT },
                // 矢状位
                { sModality: 'CT', orientation: 'x', imageIds: sagittalsX },
                { sModality: 'PT', orientation: 'x', imageIds: sagittalsXPT },
                // 轴状位
                { sModality: 'CT', orientation: 'z', imageIds: axialZ },
                { sModality: 'PT', orientation: 'z', imageIds: axialZPT }
            ]

            this.viewports.forEach(viewport => {
                // 跳出本次循环
                if (viewport.vtk) return;
                const obj = this.viewportFindOriginSeries(viewport);
                if (obj) {
                    viewport.imageIds = obj.imageIds.slice(0)
                    // 存在第二层，序列
                    if (viewport.layerIds) {
                        // 与第二层相反设备类型，同角度
                        const layerObj = this.viewportFindOriginSeries({ sModality: this.modalityFindOpposite(viewport.sModality), orientation: viewport.orientation })
                        if (layerObj) {
                            viewport.layerIds = layerObj.imageIds.slice(0)
                        }
                    }
                    // 更新id
                    viewport.uid = this.$fun.onlyValue();
                }
            });

            // 重置
            this.setInitModalityIndex() // 放在外面，放在 setTimeout 里面数目小于当前位置，会错误
            setTimeout(() => {
                this.setCurrentViewportIndex()
                this.viewportElements = this.$refs['center'].getElementsByClassName('viewportWrapper');
                // 执行 baseTool 组件方法
                this.$refs.baseTools.onAllRender()

                this.activeViewportIndex = beforeIndex
            }, 0);

            // 更新渲染
            // this.$forceUpdate();
        },
        /**
         * viewport = {sModality: 设备, orientation: 角度（x,y,z）}
         * 视窗中查找原始序列中相应的序列
         */
        viewportFindOriginSeries(viewport) {
            return this.originSeries.find(item => {
                return item.sModality === viewport.sModality && item.orientation === viewport.orientation
            })
        },
        // 获取层，相反融合层 原始序列遍历存储位置来判断的，所以不要改 originSeries push 的位置
        modalityFindOpposite(modality) {

            // 原始序列，数组[0] 数组[1].想法可融合层
            if (this.originSeries[1].sModality === modality) {
                return this.originSeries[0].sModality;
            }

            return this.originSeries[1].sModality;

        },
        // 通过加载的序列切片生成新的图
        async loaderSeries() {
            this.percentage.message = '正在重建体数据...';

            setTimeout(() => {
                this.percentage.loading = false;
                this.updateLoadTabTask('del');
            }, 1000);

            try {
                // 过滤 pt 图像
                await this.filterPTImage();
                this.allowAngel = 'all'; // 不控制角度
                // 清除展示原序列
                this.originSeries.splice(0);
                // 原图不是横切面
                if (this.originSeriesSliceType !== 'axial') {
                    // 设置截面数据
                    await this.setSectionData();
                    const coronal = this.originSeriesSliceType === 'coronal';
                    // 当前截面角度
                    this.allowAngel = coronal ? 'y' : 'x';
                    const layout = this.defaultLayout[coronal ? 1 : 2]; // 
                    // 设置布局
                    this.onClickChangeLayout(layout.options, layout.id);
                    this.setInitModalityIndex();
                } else {
                    // 获取体数据的冠、矢、横可切序列数量
                    const coronals = await this._getSliceSeries('coronal', this.thickness)

                    const sagittals = await this._getSliceSeries('sagittal', this.thickness)

                    const axials = await this._getSliceSeries('axial', this.thickness)



                    this.originSeries = [
                        { sModality: 'CT', orientation: 'y', imageIds: coronals[0] },
                        { sModality: 'PT', orientation: 'y', imageIds: coronals[1] },
                        { sModality: 'CT', orientation: 'x', imageIds: sagittals[0] },
                        { sModality: 'PT', orientation: 'x', imageIds: sagittals[1] },
                        { sModality: 'CT', orientation: 'z', imageIds: axials[0] },
                        { sModality: 'PT', orientation: 'z', imageIds: axials[1] }
                    ]

                    // 获取默认布局
                    const selectLayout = this.getDefaultLayout()

                    this.setInitModalityIndex();

                    const scheme = await this.$store.dispatch('getMatchScheme', this.series.uids)
                    // 是否有方案
                    if (Object.keys(scheme).length) {
                        // 设置默认Viewport , 如果只重建 CT，没有 PT 序列
                        const ptImage = this.originSeries[1].imageIds[0]
                        if (ptImage) {
                            await cornerstone.loadAndCacheImage(ptImage);
                        }
                        this.setDefaultViewportData(scheme, ptImage)
                    } else {
                        this.setDefaultViewportData({})
                    }
                    // 设置布局
                    this.onClickChangeLayout(selectLayout.options, selectLayout.id);

                    if (this.petImageIds[0] && this.groupModality.includes('PT')) {
                        const { patientWeight } = getPatientWeightAndCorrectedDose(this.petImageIds[0]);

                        if (!patientWeight) {
                            this.$message({
                                type: 'info',
                                message: '无患者体重信息，无法计算 SVU 值',
                            })
                        }
                    }

                }
            } catch (error) {
                console.log(error)
                this.$message({
                    type: 'error',
                    message: '重建失败，检查序列是否可重建！',
                })
            }


        },

        // 获取默认显示那个布局
        getDefaultLayout() {
            if (!this.petUid && this.ctUid) {
                return {
                    id: '1x1axial',
                    options: {
                        layout: {
                            column: 1,
                            row: 1
                        },
                        selectValue: [4],
                    }
                }
            } else if (!this.ctUid && this.petUid) {
                return {
                    id: '1x1axial',
                    options: {
                        layout: {
                            column: 1,
                            row: 1
                        },
                        selectValue: [5],
                    }
                }
            }
            // 默认布局列表
            const defaultLayout = getConfigByStorageKey('configs-default-layout');

            let selectLayout = this.defaultLayout[0]
            for (let index = 0; index < defaultLayout.length; index++) {
                const layout = defaultLayout[index];

                // 按照当前打开的设备类型区分使用那个布局
                if (layout.key == this.groupModality) {
                    const findObj = this.defaultLayoutShow.concat(this.layoutList).find(item => item.id == layout.value)
                    if (findObj) {
                        selectLayout = findObj
                    }
                    break
                }
            }
            return selectLayout
        },
        // 初始化默认下标位置值
        setInitModalityIndex() {

            let yIdx = Math.max(Math.floor(this.originSeries[this.ctUid ? 0 : 1].imageIds.length / 2) - 1, 0)
            let xIdx = Math.max(Math.floor(this.originSeries[this.ctUid ? 2 : 3].imageIds.length / 2) - 1, 0)
            let zIdx = Math.max(Math.floor(this.originSeries[this.ctUid ? 4 : 5].imageIds.length / 2) - 1, 0)

            this.allModalityIndex.CTy = yIdx
            this.allModalityIndex.PTy = yIdx
            this.allModalityIndex.FUSEy = yIdx

            this.allModalityIndex.CTx = xIdx
            this.allModalityIndex.PTx = xIdx
            this.allModalityIndex.FUSEx = xIdx

            this.allModalityIndex.CTz = zIdx
            this.allModalityIndex.PTz = zIdx
            this.allModalityIndex.FUSEz = zIdx
        },
        // 设置截面原数据
        async setSectionData() {
            const noPtSeries = appState[this.ctUid || this.petUid].series
            let ptSeries = []

            // 有 PT 融合才创建图像
            if (this.petUid) {
                noPtSeries.forEach(imageId => {
                    const metaData = cornerstone.metaData.get('imagePlaneModule', imageId);
                    // TODO 也不太多，有偏差
                    let ipp = metaData.imagePositionPatient.join(',')
                    if (this.originSeriesSliceType === 'coronal') {
                        // 冠
                        ipp = metaData.imagePositionPatient[0] + ',' + metaData.sliceLocation + ',' + metaData.imagePositionPatient[2]
                    }else {
                        // 矢
                        ipp = metaData.sliceLocation + ',' + metaData.imagePositionPatient[1] + ',' + metaData.imagePositionPatient[2]
                    }
                    ptSeries.push('mpr:' + this.petUid + ':' + metaData.imageOrientationPatient.join(',') + ':' + ipp + ':clip:' + this.ctUid) // : + this.componentId
                })
            }

            if (this.originSeriesSliceType === 'coronal') {
                // 冠状位   
                this.originSeries.push({
                    sModality: 'CT',
                    orientation: 'y',
                    imageIds: noPtSeries.slice(0)
                })
                this.originSeries.push({
                    sModality: 'PT',
                    orientation: 'y',
                    imageIds: ptSeries
                })
                this.originSeries.push({ sModality: 'CT', orientation: 'x', imageIds: [] })
                this.originSeries.push({ sModality: 'PT', orientation: 'x', imageIds: [] })
                this.originSeries.push({ sModality: 'CT', orientation: 'z', imageIds: [] })
                this.originSeries.push({ sModality: 'PT', orientation: 'z', imageIds: [] })

            } else if (this.originSeriesSliceType === 'sagittal') {
                // 按照布局结构矢状位，在 originSeries 3,2 下标位置
                this.originSeries.push({ sModality: 'CT', orientation: 'y', imageIds: [] })
                this.originSeries.push({ sModality: 'PT', orientation: 'y', imageIds: [] })
                // 矢状位
                this.originSeries.push({
                    sModality: 'CT',
                    orientation: 'x',
                    imageIds: noPtSeries.slice(0)
                })
                this.originSeries.push({
                    sModality: 'PT',
                    orientation: 'x',
                    imageIds: ptSeries
                })
                this.originSeries.push({ sModality: 'CT', orientation: 'z', imageIds: [] })
                this.originSeries.push({ sModality: 'PT', orientation: 'z', imageIds: [] })
            }

            // 重建的 pt 图像都会被裁剪，把 clip 裁剪标记去掉..加载一张不裁剪的图像
            if (this.ctUid && this.petUid) {
                const ptId = ptSeries[0].replace('clip', 'reference');
                this.referenceIds.push(ptId)
                // 创建读取裁剪参考图像
                await cornerstone.loadAndCacheImage(noPtSeries[0]);
                await cornerstone.loadAndCacheImage(ptId);
                // 设置裁剪信息
                setClipPlaneXY(noPtSeries[0], ptId, this.petUid + '&' + this.ctUid);
            }
        },
        /**
         * 获取ct及pet的切片序列
         * @param {*} direction 切片位（冠、矢、横） coronal, sagittal, axial
         * @param {*} thickness 层厚
         */
        async _getSliceSeries(direction = 'coronal', thickness = 3) {

            let ctData = await getVolumeSliceNumber(this.ctUid || this.petUid, thickness)
            if (this.petUid) await getVolumeSliceNumber(this.petUid, thickness)

            // 当两组图像原点偏差小的时候 设为同一个原点
            if (this.ctUid && this.petUid) {
                const vDataPt = appState[this.petUid]
                const vDataCt = appState[this.ctUid]
                const ptOrigin = vDataPt.vtkVolumes.origin
                const ctOrigin = vDataCt.vtkVolumes.origin
                const originXDiff = Math.abs(ptOrigin[0] - ctOrigin[0])
                const originYDiff = Math.abs(ptOrigin[1] - ctOrigin[1])
                if (vDataPt.machineOffset && vDataPt.machineOffset[2] && vDataPt.machineOffset[1]
                    || vDataCt.machineOffset && vDataCt.machineOffset[2] && vDataCt.machineOffset[1]) {
                    // 存在机器校准参数时，不进行偏移补偿
                } else {
                    if (originXDiff < 5 && originYDiff < 5) {
                        const set = (vDataCt.vtkVolumes.vtkImageData.setOrigin)
                        // 有些 CT Z 起始在胸，PT 起始在 头，会出现头跟胸融合。所以用 CT 本身的 z
                        set && set([ptOrigin[0], ptOrigin[1], ctOrigin[2]])
                    }
                }
            }

            // let ptData = {}
            // 通过vtk容积，vtk方式获取图像的一些信息
            // 获取图像重建id信息

            let [ctIds, ptIds] = getNewImageIds(direction, ctData, thickness, this.ctUid, this.petUid);
            if (this.ctUid && this.petUid) {
                const ctId = ctIds[0] + ':reference';
                const ptId = ptIds[0].replace('clip', 'reference');

                this.referenceIds.push(ctId);
                this.referenceIds.push(ptId);

                // 存入裁剪比较
                await createImage(ctId);
                await createImage(ptId);

                // 设置裁剪信息
                setClipPlaneXY(ctId, ptId, this.petUid + '&' + this.ctUid);


            }

            return [ctIds, ptIds];
        },
        /**
         * 更新所有设备角度第一个下标位置
         * 知道当前布局没有的方向，如果当前布局只有 z 轴那么就只改变 x,y
         * 以第一个 viewport 坐标改变获取其它方向最近的切片位置
         * 更新 allModalityIndex 第一个位置
         */
        updateLayoutModalityIndex() {
            this.activeViewportIndex = -1;
            this.showAction = -1;
            let otherOrientation = ['x', 'y', 'z']  // 所有方向
            let viewportIdx = null                    // 显示的第一个下标
            this.viewports.forEach((item, index) => {
                if (item.vtk) {
                    // 暂存vtk图像窗宽
                    const vtkApi = this.apis[index]
                    if (!vtkApi) return
                    const wwwc = vtkApi.getVoi()
                    const key = `mip_${item.layoutId}_data`;
                    let data = this.allModalityIndex[key]
                    if (!data) data = this.allModalityIndex[key] = {}
                    data.windowCenter = wwwc.windowCenter
                    data.windowWidth = wwwc.windowWidth
                    // data.parallelScale = vtkApi.genericRenderWindow.getRenderer().getActiveCamera().getParallelScale()
                    return
                }
                // 角度是字符串的是 cornerstone 显示的
                if (typeof item.orientation === 'string' && viewportIdx === null) {
                    viewportIdx = index
                }
                // 当前布局没有的切面方向
                const idx = otherOrientation.indexOf(item.orientation)
                if (idx != -1) {
                    otherOrientation.splice(idx, 1)
                }

                // 存储当前图像 viewport 信息
                if (typeof item.orientation === 'string') {
                    let modality = item.sModality;

                    const sourceElement = this.getEnabledElement(index);
                    if (!sourceElement) {
                        return;
                    }
                    const enabledElement = cornerstone.getEnabledElement(sourceElement);

                    const oldCanvasWidth = enabledElement.canvas.width;
                    const oldCanvasHeight = enabledElement.canvas.height;

                    if (item.layerIds) {
                        const layers = cornerstone.getLayers(sourceElement);
                        layers.forEach((layer, index) => {
                            this.saveViewportState(index === 0 ? 'PT' : 'CT', layer.viewport, 'fuseColor', 
                            { 
                                orientation: item.orientation, 
                                firstImageId: item.imageIds[0], 
                                oldCanvasWidth, 
                                oldCanvasHeight 
                            });
                        })
                    } else {
                        this.saveViewportState(modality, enabledElement.viewport, undefined, 
                            { 
                                orientation: item.orientation,
                                firstImageId: item.imageIds[0], 
                                oldCanvasWidth,
                                oldCanvasHeight
                            });
                    }

                    // scale: viewport.scale,
                    // x: viewport.translation.x,
                    // y: viewport.translation.y,
                }

            })
            // 按照这个 viewport 下标 改变其它方向的图像
            if (viewportIdx !== null) {
                const sourceElement = this.getEnabledElement(viewportIdx)
                const toolState = cornerstoneTools.getToolState(sourceElement, 'NuclearCrosshairs')
                if (toolState && toolState.data[0]) {
                    const toolCrosshairs = toolState.data[0]
                    // 当前布局没有的方向
                    otherOrientation.forEach(orientation => {
                        // 获取方向 imageIds
                        const originSerie = this.originSeries.find(item => orientation == item.orientation)

                        if (originSerie) {
                            // 该方向，最接近的图像序列位置
                            const oneIndex = findNearIndex(originSerie.imageIds, orientation, toolCrosshairs.synchronizationContext.patientPoint)
                            // 更新序列第一位值
                            for (const key in this.allModalityIndex) {
                                if (key.includes(orientation) && !key.includes('Viewport')) {
                                    this.allModalityIndex[key] = oneIndex
                                }
                            }
                        }
                    })

                }
            }
        },
        // 有多个视窗执行多少次，视窗有多层也会执行多层
        async saveViewportState(modality, viewport, isFue, moreParams) {
            if (!viewport) {
                return;
            }
            const { orientation, firstImageId, oldCanvasWidth, oldCanvasHeight } = moreParams
            await cornerstone.loadAndCacheImage(firstImageId);

            ['x', 'y', 'z'].forEach(itemOrientation => {
                const key = `${modality}${itemOrientation}Viewport`;

                let { windowWidth, windowCenter } = viewport.voi

                if (!this.allModalityIndex[key]) {
                    return
                }
                this.allModalityIndex[key].windowWidth = windowWidth;
                this.allModalityIndex[key].windowCenter = windowCenter;

                if (itemOrientation == orientation) {
                    this.allModalityIndex[key].hflip = viewport.hflip;
                    this.allModalityIndex[key].vflip = viewport.vflip;
                    // this.allModalityIndex[key].invert = viewport.invert; // 反片有问题 TODO 
                    this.allModalityIndex[key].rotation = viewport.rotation;
                    this.allModalityIndex[key].scale = viewport.scale;
                    this.allModalityIndex[key].x = viewport.translation.x;
                    this.allModalityIndex[key].y = viewport.translation.y;

                    this.allModalityIndex[key].oldCanvasWidth = oldCanvasWidth;
                    this.allModalityIndex[key].oldCanvasHeight = oldCanvasHeight;
                }

                if (isFue) {
                    if (this.allModalityIndex[key + 'Fuse']) {
                        this.allModalityIndex[key + 'Fuse'].colormap = viewport.colormap;
                    }
                } else {
                    this.allModalityIndex[key].colormap = viewport.colormap;
                }
            })
        },
        // 选择布局模式
        /**
         * options 布局参数
         * id      布局主键
         */
        onClickChangeLayout(options, id, isPassive = false, showLayout) {

            // 人为点击改变布局 
            if (isPassive) {
                // 按照当前布局更新相应设备第一位显示张数
                this.updateLayoutModalityIndex()
            }

            if (!this.originSeries.length) return;

            this.selectLayout = id
            //  清除 vtk显示的 apis
            this.isCrosshairsCallback = false
            this.apis.splice(0)

            // 特殊布局的 css 信息
            if (options.containerStyle) {
                this.gridLayoutStyle.containerStyle = options.containerStyle;
            } else {
                this.gridLayoutStyle.containerStyle = {}
            }

            if (options.itemStyle && options.itemStyle.length) {
                this.gridLayoutStyle.itemStyle = options.itemStyle;
            } else {
                this.gridLayoutStyle.itemStyle = [];
            }


            // 改变布局
            this.onClickBox(options.layout);
            // 设置布局数据
            this.setViewportByLayout(options.selectValue, showLayout);
            // 提前加载图像
            // this.loadOpenSeries()

            if (this.allowAngel !== 'all') {
                // 更新渲染
                this.$forceUpdate();

                setTimeout(() => {
                    this.onClickChangeSeries('orientation', this.allowAngel)
                }, 200);
            } else {
                // 更新渲染
                this.$forceUpdate();
                // 设置布局原始矩阵
                this.setLayoutOriginalMatrix();
                // 重置
                // const delay = !isPassive ? 1200 : 200
                // this.resetViewport(delay);
            }

        },
        // 改变棋盘布局添入相应图像
        changeLayoutPushView(layouts) {
            if (!this.originSeries.length) return;
            //  清除 vtk显示的 apis
            this.isCrosshairsCallback = false
            this.apis.splice(0)
            // 设置布局数据
            this.setViewportByLayout(layouts);
            // 更新渲染
            // this.$forceUpdate();
            // 重置
            // this.resetViewport();

        },

        // 改变 viewport 数据
        setViewportByLayout(layout, showLayout = []) {
            // 融合值对应序列下标
            // PT/NM 第一层
            const fuseIndex = {
                8: [1, 0],
                9: [3, 2],
                10: [5, 4],
                11: [7, 6]
            }
            // this.originSeries 对应的下标位置
            layout.forEach((v, index) => {
                if (v === '' || v === null) {
                    this.viewports[index] = {}
                    return;
                }
                // MIP 图
                if ([6, 7, 11].includes(v)) {
                    this.setViewportVtkMIP(v, index, showLayout[index]);
                    return;
                }
                if (v < 8) {
                    // 单个序列
                    this.viewports[index] = this.$fun.deepClone(this.originSeries[v]);
                } else {

                    const imageLayer = this.$fun.deepClone(this.originSeries[fuseIndex[v] ? fuseIndex[v][1] : 0]).imageIds
                    // 融合序列
                    // if (imageLayer.length) {
                    this.viewports[index] = this.$fun.deepClone(this.originSeries[fuseIndex[v] ? fuseIndex[v][0] : 1])
                    this.viewports[index].layerIds = imageLayer
                    // }
                }
                this.viewports[index].layoutId = v
                this.viewports[index].uid = this.$fun.onlyValue();
            })
        },
        // 通过设备+方向+融合或者mip状态 获取相应布局显示 id
        getLayoutId(modality, orientation, statusValue = '') {
            const allLayoutId = {
                'CTy': 0, 'PTy': 1,
                'CTx': 2, 'PTx': 3,
                'CTz': 4, 'PTz': 5,
                'MIPCTy': 6, 'MIPPTy': 7,
                'FUSECTy': 8, 'FUSEPTy': 9, 'FUSEPTy': 10,
                'FUSEMIPCTy': 11
            }
            const layoutId = allLayoutId[statusValue + modality + orientation]

            if (layoutId != undefined) {
                return layoutId
            }

            return ''
        },
        // 点击改变序列，通过角度||设备
        /**
         * type = orientation | modality 点击的方向，或者点击设备
         * value= 方向值是:x,y,z 设备的是 1 pet，0 ct、mr，2 融合
         */
        onClickChangeSeries(type, value) {
            this.activeViewportIndex = -1;
            this.showAction = -1;
            // 点击角度才更新
            if (type === 'orientation') {
                // 按照当前布局更新相应设备第一位显示张数
                this.updateLayoutModalityIndex()
            }
            if (!this.originSeries.length) return;
            if (type === 'orientation') {
                // 点击角度
                this.viewports.forEach(viewport => {
                    // VTK 视窗退出
                    if (viewport.vtk) return;
                    // 一样的角度退出
                    if (viewport.orientation === value) return;

                    // 当前视窗、以及传递过来的角度
                    const obj = this.viewportFindOriginSeries({ sModality: viewport.sModality, orientation: value })

                    if (obj) {
                        viewport.imageIds = obj.imageIds.slice(0)
                        viewport.orientation = value // 更新角度
                        viewport.layoutId = this.getLayoutId(obj.sModality, obj.orientation)
                        // 存在多层（融合的）
                        if (viewport.layerIds) {
                            // 与第二层相反设备类型，同角度
                            const layerObj = this.viewportFindOriginSeries({ sModality: this.modalityFindOpposite(viewport.sModality), orientation: value })
                            if (layerObj) {
                                viewport.layoutId = this.getLayoutId(layerObj.sModality, layerObj.orientation, 'FUSE')
                                viewport.layerIds = layerObj.imageIds.slice(0)
                            }
                        }
                        // 更新id
                        viewport.uid = this.$fun.onlyValue();
                    }
                })
            } else {
                // 点击设备
                // 融合
                if (value === 2) {
                    this.viewports.forEach((viewport, index) => {
                        // VTK 视窗退出
                        if (viewport.vtk) return;
                        // 相应角度，相应原始序列下标
                        const fuseIndex = {
                            y: [1, 0],
                            x: [3, 2],
                            z: [5, 4],
                        }
                        const angle = viewport.orientation;

                        this.viewports[index] = this.$fun.deepClone(this.originSeries[fuseIndex[angle][0]])
                        this.viewports[index].layerIds = this.$fun.deepClone(this.originSeries[fuseIndex[angle][1]]).imageIds
                        // 更新id
                        this.viewports[index].uid = this.$fun.onlyValue();
                    })
                } else {
                    // 获取设备
                    const modality = this.originSeries[value].sModality;
                    // 遍历视窗
                    this.viewports.forEach(viewport => {
                        // VTK 视窗退出
                        if (viewport.vtk) return;

                        // 删除第二层
                        if (viewport.layerIds) {
                            delete viewport.layerIds
                            // 更新id
                            viewport.uid = this.$fun.onlyValue();
                        };

                        // 当前模块是一样的设备，不在查找替换
                        if (viewport.sModality === modality) return;
                        // 查找替换
                        const obj = this.viewportFindOriginSeries({ sModality: modality, orientation: viewport.orientation })
                        if (obj) {
                            viewport.imageIds = obj.imageIds.slice(0)
                            viewport.sModality = modality;
                            // 更新id
                            viewport.uid = this.$fun.onlyValue();
                        }
                    })
                }
            }
            // 更新渲染
            this.$forceUpdate();
            // 设置布局原始矩阵
            this.setLayoutOriginalMatrix();
            // 重置窗宽信息
            // this.resetViewport();
        },

        // 设置布局原始矩阵 index
        setLayoutOriginalMatrix(isReload = true) {
            // CT/MR、PT/NM
            // 0、1冠 y
            // 2、3矢 x
            // 4、5横 z
            // 6、7mip
            // 8 冠融合  1,0
            // 9 矢融合  3,2
            // 10 横融合 5,4
            // 11 mip融合 6 7
            const lutTable = {
                'CTy': 0, 'MRy': 0,
                'PTy': 1, 'NMy': 1,
                'CTx': 2, 'MRx': 2,
                'PTx': 3, 'NMx': 3,
                'CTz': 4, 'MRz': 4,
                'PTz': 5, 'NMz': 5,
                'CTmip': 6, 'MRmip': 6,
                'PTmip': 7, 'NMmip': 7,
                'FUSEy': 8,
                'FUSEx': 9,
                'FUSEz': 10,
                'FUSEmip': 11,
            }

            let originalIdx = []
            this.viewports.forEach(viewport => {
                // vtk MIP 视图
                if (viewport.vtk) {
                    originalIdx.push(viewport.layoutId)
                } else if (viewport.layerIds) {
                    // 是否存在融合
                    originalIdx.push(lutTable[`FUSE${viewport.orientation}`])
                } else {
                    originalIdx.push(lutTable[`${viewport.sModality}${viewport.orientation}`])
                }
            })
            if (isReload) {
                this.layoutMatrix.isReload = true;
            }

            this.layoutMatrix.column = Math.round(100 / parseInt(this.style.width))
            this.layoutMatrix.originalIdx = originalIdx;
        },

        /**
         * 预加载回调执行
         */
        // prefetchSuccess(){
        //     this.percentage.loading = true
        //     this.percentage.loadCount += 1
        //     if (this.percentage.loadCount == this.percentage.total) this.percentage.loading = false;
        // },
        /**
         * 加载当前模式布局下所有序列
         */
        // loadOpenSeries(){
        //     // 获取需要加载的序列
        //     let loadList = []
        //     // 打开的序列
        //     this.viewports.forEach(item => {
        //         // 显示层添加
        //         if (item.imageIds && 
        //             item.imageIds[1] && 
        //             !cornerstone.imageCache.getImageLoadObject(item.imageIds[1]) &&
        //             !loadList.includes(item.imageIds[1])
        //         ){
        //             loadList = loadList.concat(item.imageIds)
        //         }
        //         // 融合层添加
        //         if (item.layerIds && 
        //             item.layerIds[1] && 
        //             !cornerstone.imageCache.getImageLoadObject(item.layerIds[1]) &&
        //             !loadList.includes(item.layerIds[1])
        //         ){
        //             loadList = loadList.concat(item.layerIds)
        //         }
        //     })

        //     // 有重建图像
        //     if (loadList.length){
        //         this.percentage.loading = true
        //         this.percentage.loadCount = 0
        //         this.percentage.total = loadList.length
        //         this.percentage.message = '重建'
        //     }

        //     // 预加载方式加载图像 Tools 中的方法
        //     loadList.forEach(imageId => {
        //         cornerstoneTools.requestPoolManager.addRequest(
        //             {},
        //             imageId,
        //             'prefetch',
        //             false,
        //             this.prefetchSuccess,
        //             this.prefetchSuccess,
        //         );
        //     });
        //     cornerstoneTools.requestPoolManager.startGrabbing();
        // },

        // 设置 viewport 添加 vtk展示
        async setViewportVtkMIP(status, index, moveParams) {
            setTimeout(() => {
                this.viewports[index] = {}
                this.viewports[index].orientation = { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] }
                // status 6 = ct mr；7 = pt；11 = 融合
                const getMIPvolumeData = () => {
                    if (status == 11) {
                        return this.getVtkVolumeBySeriesUid(this.petUid, this.ctUid)
                    }
                    return this.getVtkVolumeBySeriesUid(status == 7 ? this.petUid : this.ctUid)
                }
                const getVtkClipZ = () => {
                    if (status == 6) {
                        return null
                    }
                    if (!this.ctUid || !this.petUid || !appState[this.ctUid] || !appState[this.petUid]) {
                        return null
                    }
                    if (!appState[this.ctUid].vtkVolumes) {
                        return null
                    }
                    const volDataExtentCT = appState[this.ctUid].vtkVolumes.vtkImageData.getExtent()
                    // const volDataExtentPT = appState[this.petUid].vtkVolumes.vtkImageData.getExtent()
                    const volDataOriginCT = appState[this.ctUid].vtkVolumes.vtkImageData.getOrigin()
                    const volDataOriginPT = appState[this.petUid].vtkVolumes.vtkImageData.getOrigin()
                    const volDataSpacingCT = appState[this.ctUid].vtkVolumes.vtkImageData.getSpacing()

                    const extent = (volDataExtentCT[5] - volDataExtentCT[4]) * volDataSpacingCT[2]
                    const diff = (volDataOriginPT[2] - volDataOriginCT[2])
                    // console.log( extent, diff)
                    return {
                        extent,
                        diff
                    }
                }
                getMIPvolumeData().then(res => {
                    if (res) {
                        let { volumes, imageId, imageIdsGroup } = res
                        this.viewports[index].volumes = volumes
                        this.viewports[index].imageId = imageId
                        this.viewports[index].imageIdsGroup = imageIdsGroup
                        this.viewports[index].modality = status == 6 ? 'CT' : 'PT'
                        this.viewports[index].vtkClipZ = getVtkClipZ()
                    }
                    // 异步加载完成，在渲染vtk mip图
                    this.$forceUpdate()
                    // 设置偏移旋转
                    setTimeout(() => {
                        const piece = 360 / 32
                        if (moveParams && moveParams.offset) {
                            const num = parseInt(moveParams.offset)
                            if (!isNaN(num)) {
                                this.onClickRotate(piece * num, index)
                            }
                        }
                    }, 200);
                })
                this.viewports[index].layoutId = status
                // 放外面-vue会先渲染
                this.viewports[index].vtk = true
                this.viewports[index].uid = this.$fun.onlyValue()

                // 将Z轴PT CT图序列数据存下来 用于三维定位
                this.viewports[index].originSeriesZ = this.originSeries.filter(item => item.orientation === 'z' && item.imageIds.length > 0)
                this.viewports[index].originSeriesY = this.originSeries.filter(item => item.orientation === 'y' && item.imageIds.length > 0)

                // 设置布局原始矩阵
                this.setLayoutOriginalMatrix(false)
            }, 200);
        },

        // 保存控件Api
        storeApi(viewportIndex) {
            return (api) => {
                this.apis[viewportIndex] = api;
                const apis = this.apis;
                // 获取渲染的窗口
                const renderWindow = api.genericRenderWindow.getRenderWindow();

                // 添加 svg 工具- 定位线名称 crosshairsWidget...用于控制显示等
                api.addSVGWidget(
                    vtkSVGCrosshairsWidget.newInstance(this.crosshairsTool),
                    "crosshairsWidget"
                );

                api.addSVGWidget(
                    vtkSVGProbeWidget.newInstance(),
                    "probeWidget"
                );

                api.addSVGWidget(
                    vtkSVGMarkWidget.newInstance(),
                    "markWidget"
                );

                const istyle = vtkInteractorStyleMPRCrosshairs.newInstance({ scrollRotate: this.scrollRotate });
                // 操作功能
                api.setInteractorStyle({
                    istyle,
                    configuration: { apis, apiIndex: viewportIndex },
                });

                // 设置最大密度投影
                const mapper = api.volumes[0].getMapper();
                if (mapper.setBlendModeToMaximumIntensity) {
                    mapper.setBlendModeToMaximumIntensity();
                }

                const clipMip = appState[this.groupId].clipMip;
                // 设置裁剪
                // if (clipMip) {
                //     setClipPlane(mapper, clipMip);
                // }
                // 把裁剪位置信息存入到当前
                const newZPosition = clipMip ? { max: clipMip.max, min: clipMip.min } : {};
                api.newZPosition = newZPosition;

                // 密谱图厚度 先快速加载一个小的厚度，再更新
                api.setSlabThickness(1);
                const targetThickness = api.getMaxLabThickness() // 最大厚度
                const intervalNum = 100; // 间隔时间

                setTimeout(() => {
                    api.setSlabThickness(Math.round(targetThickness));
                }, intervalNum);

                // 缩放到适应窗口
                this.$fun.fill2DView(api.genericRenderWindow, api.getSliceNormal(), newZPosition);
                this.triggerScaleSyanc(viewportIndex)
                // 是否反片
                cornerstone.loadAndCacheImage(api.imageId).then(image => {
                    const seriesModule = cornerstone.metaData.get('generalSeriesModule', image.imageId);
                    // PT、NM 才默认反片
                    const isInvert = api.volumes.length > 1 ? false :
                        seriesModule && ['PT', 'NM'].includes(seriesModule.modality);

                    if (isInvert) {
                        invertVolume(api.volumes[0], (isInvert) => {
                            api.genericRenderWindow.setBackground(isInvert ? [255, 255, 255] : [0, 0, 0])
                            api.genericRenderWindow.getRenderWindow().render();
                        });
                    }
                    // 动画事件
                    api.genericRenderWindow.getRenderWindow().getInteractor().onAnimation(() => {
                        this.triggerScaleSyanc(viewportIndex)
                    })
                    
                    // 渲染窗口
                    renderWindow.render();
                    // const apiObj = apis[viewportIndex];
                    // apiObj.svgWidgets.crosshairsWidget.resetCrosshairs(apis, viewportIndex);
                    this.vtkToggleToCrosshairs('NuclearCrosshairs', true)
                });

            }
        },
        // 触发同步
        triggerScaleSyanc(index) {
            requestAnimationFrame(() => {

                const apis = this.apis
                const sourceCamera = apis[index].genericRenderWindow.getRenderer().getActiveCamera()

                apis.forEach((api, apiIndex) => {
                    if (apiIndex === index) {
                        return
                    }
                    const renderWindow = api.genericRenderWindow.getRenderWindow()

                    const targetCamera = api.genericRenderWindow.getRenderer().getActiveCamera()
                    // targetCamera.setPosition(...sourceCamera.getPosition())
                    targetCamera.setFocalPoint(...sourceCamera.getFocalPoint())
                    targetCamera.setViewUp(...sourceCamera.getViewUp())
                    // targetCamera.setParallelProjection(sourceCamera.getParallelProjection())
                    targetCamera.setParallelScale(sourceCamera.getParallelScale())
                    // targetCamera.setDistance(sourceCamera.getDistance())
                    // targetCamera.setThickness(sourceCamera.getThickness())
                    // targetCamera.setWindowCenter(...sourceCamera.getWindowCenter())
                    targetCamera.setViewAngle(sourceCamera.getViewAngle())
                    // 渲染窗口
                    renderWindow.render()
                })
            })
        },
        // 窗宽窗位
        vtkToggleWL(name) {
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name
            const apis = this.apis;

            apis.forEach((api, apiIndex) => {
                let istyle = vtkInteractorStyleMPRWindowLevel.newInstance({ scrollRotate: this.scrollRotate });

                const callbacks = {
                    setOnLevelsChanged: debounce((voi) => {
                        const { windowWidth, windowCenter } = voi;
                        api.updateVOI(windowWidth, windowCenter)
                        api.genericRenderWindow.getRenderWindow().render();
                    }, 1 / 30)
                }

                api.setInteractorStyle({
                    istyle,
                    callbacks,
                    configuration: { apis, apiIndex },
                });
            });
        },
        // 点测量
        vtkToggleToProbe(name) {

            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.activeTool = name;
            const apis = this.apis;
            apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets } = api;
                svgWidgetManager.render();
                const interactor = name === 'TextMarker' ? vtkInteractorStyleMark : vtkInteractorStyleProbe
                let istyle = interactor.newInstance({ scrollRotate: this.scrollRotate });

                api.setInteractorStyle({
                    istyle,
                    configuration: {
                        apis, apiIndex, endCallback: () => {
                            if (name === 'TextMarker') {
                                this.activeTool = 'Airtools';
                            }
                        }
                    },
                });

            });

        },
        vtkHideCrosshairs() {
            // 无效 早于activeTool的watch事件
            // console.log('vtkHideCrosshairs')
            this.apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets } = api;
                this.$nextTick(() => {
                    svgWidgets.crosshairsWidget.setDisplay(false);
                    svgWidgetManager.render();
                })
            })
        },
        vtkToggleToMPRSlice(name) {
            if (this.activeTool === this.vtkActiveTool) {
                return
            }
            this.vtkActiveTool = name
            const apis = this.apis
            apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets } = api
                svgWidgets.crosshairsWidget.setDisplay(false)
                svgWidgetManager.render()

                this.$nextTick(() => {
                    const istyle = vtkInteractorStyleMPRSlice.newInstance({ scrollRotate: this.scrollRotate });
                    api.setInteractorStyle({
                        istyle,
                        configuration: { apis, apiIndex },
                    });
                })


            })
        },
        // 定位线
        vtkToggleToCrosshairs(name, reset = false) {
            if (this.activeTool === this.vtkActiveTool && !reset) {
                return
            }
            this.vtkActiveTool = name
            const apis = this.apis;
            apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets } = api;
                if (reset) {
                    this.$nextTick(() => {
                        const ipp = this.synchronizerCrosshairs.patientPoint
                        svgWidgets.crosshairsWidget.setDisplay(true)
                        if (ipp) {
                            // 初始化mip图的定位线 从cs图的定位同步器中取世界坐标点
                            svgWidgets.crosshairsWidget.moveCrosshairs([ipp.x, ipp.y, ipp.z], [api], 0)
                        } else {
                            svgWidgets.crosshairsWidget.resetCrosshairs(apis, apiIndex)
                        }


                        if (!api.vtkClickEvtBinded) {
                            api.vtkClickEvtBinded = true
                            api.svgWidgets.crosshairsWidget.onModified(res => {
                                // 这些是用来计算三维定位坐标的参数
                                const renderer = api.genericRenderWindow.getRenderer();
                                const camera = renderer.getActiveCamera();
                                const cameraPosition = camera.getPosition();
                                const bounds = api.volumes[0].getBounds();

                                const worldPos = res.getWorldPos();
                                // 触发执行事件，改变 cornerstone 
                                this.vtkChangeCsIndex(worldPos, api.uid, cameraPosition, bounds)
                            });
                        }
                    })
                } else {
                    const istyle = vtkInteractorStyleMPRCrosshairs.newInstance({ scrollRotate: this.scrollRotate });
                    // 操作功能
                    api.setInteractorStyle({
                        istyle,
                        configuration: { apis, apiIndex },
                    })

                    // svgWidgets.crosshairsWidget.setDisplay(true)
                    svgWidgetManager.render()
                }

            });

        },
        // 点击其它面
        onClickRotate(angle, index) {
            const api = this.apis[index];
            if (!api) {
                return;
            }
            const renderWindow = api.genericRenderWindow.getRenderWindow();
            renderWindow.getInteractor().getInteractorStyle().getViewport().rotateAbsolute(0, angle);
            renderWindow.render();
            api.svgWidgetManager.render()
        },
        // 点击vtk视图时候,设置其它 cornerstone 视图
        vtkChangeCsIndex(worldPos, uid, cameraPosition, bounds) {
            if (!worldPos.length) {
                return
            }
            if (isNaN(worldPos[0][0])) {
                return
            }
            // cs 改变 vtk vtk会重新反过来改变 cs 截断执行
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'vtk') {
                this.vtkAndCsRenderState.curRender = 'vtk'
                this.setvtkAndCsRenderThrottle()
            }
            // 如果是 cornerstone 渲染，就不在返回渲染 cs
            if (this.vtkAndCsRenderState.curRender == 'cs') {
                return
            }
            // 不知道为什么 数组结构，会在嵌套一层数组
            let ippVec3 = []
            if (worldPos[0].length) {
                ippVec3 = [...worldPos[0]]
            } else {
                ippVec3 = worldPos
            }
            // 多个MIP存在，每个都会改变渲染 cs.只有选中的 mip 才改变 cs 显示
            if (this.apis[this.activeViewportIndex] && this.apis[this.activeViewportIndex].uid != uid) {
                return
            }

            // vtk图像点击后三维定位功能，从vtk的viewport数据中获取点击那个切面的图像数据，计算出三维点
            const vtkViewportIndex = this.viewports.findIndex(item => item.vtk)

            if (vtkViewportIndex > -1) {
                const vtkViewport = this.viewports[vtkViewportIndex]
                const isZaxisData = vtkViewport.originSeriesZ.length > 0 // 原图是不是Z方向上序列
                const originSeriesArr = isZaxisData ?
                    vtkViewport.originSeriesZ : vtkViewport.originSeriesY // viewport里预先赋值的图像信息 仅支持y\z轴数据 

                const targetSeriesIndex = originSeriesArr.findIndex(item => item.imageIds.length > 0)

                if (targetSeriesIndex > -1) {
                    const targetSeriesData = originSeriesArr[1] || originSeriesArr[0] // 体数据的源图id的数组，默认第一个是PT 第二CT
                    const targetImageIds = targetSeriesData.imageIds

                    // 有坐标偏移时，将坐标点补回来
                    const offsetString = ['PT', 'NM'].includes(vtkViewport.modality) ? this.ptOffset : this.ctOffset
                    const OffsetArr = offsetString.split(',')
                    ippVec3[0] -= Number(OffsetArr[0])
                    ippVec3[1] -= Number(OffsetArr[1])
                    ippVec3[2] -= Number(OffsetArr[2])

                    const toIndex = findNearIndex(targetImageIds, isZaxisData ? 'z' : 'y',
                        { x: ippVec3[0], y: ippVec3[isZaxisData ? 1 : 2], z: ippVec3[isZaxisData ? 2 : 1] }
                    )
                    const targetImageId = targetImageIds[toIndex]
                    cornerstone.loadAndCacheImage(targetImageId).then(image => {
                        const pixelData = image.getPixelData()
                        const imagePlaneModule = cornerstone.metaData.get('imagePlaneModule', targetImageId)
                        const projectPatientPointToImagePlane = cornerstoneTools.import('util/projectPatientPointToImagePlane')
                        const convertToVector3 = cornerstoneTools.import('util/convertToVector3')

                        // 参考cornerstone3d的方法，获取点击坐标所在射线穿过的点的集合，计算suv值最大的点
                        let _vec = [0, 0, 0]
                        vtkMath.subtract(ippVec3, cameraPosition, _vec);

                        let pickedPoint = [];
                        const step = Math.max(parseFloat(this.thickness) * 0.7, 0.5);
                        const loopxMin = bounds[0];
                        const loopxMax = bounds[1];
                        const loopyMin = bounds[2];
                        const loopyMax = bounds[3];
                        const [xMin, xMax, yMin, yMax, zMin, zMax] = bounds;

                        // 6. Iterating over the line from the lower bound to the upper bound, with the
                        // specified step size
                        // x
                        for (let pointT = loopxMin; pointT <= loopxMax; pointT = pointT + step) {

                            // 6.1 Calculating the point x location
                            let point = [pointT, 0, 0];
                            // 6.2 Calculating the point y,z location based on the line equation
                            const t = (pointT - cameraPosition[0]) / _vec[0];
                            point[1] = t * _vec[1] + cameraPosition[1];
                            point[2] = t * _vec[2] + cameraPosition[2];

                            // 6.3 Checking if the points is inside the bounds
                            if (point[0] > xMin &&
                                point[0] < xMax &&
                                point[1] > yMin &&
                                point[1] < yMax &&
                                point[2] > zMin &&
                                point[2] < zMax) {
                                pickedPoint.push(point)
                            }
                        }
                        // y
                        for (let pointT = loopyMin; pointT <= loopyMax; pointT = pointT + step) {

                            // 6.1 Calculating the point x location
                            let point = [0, pointT, 0];
                            // 6.2 Calculating the point y,z location based on the line equation
                            const t = (pointT - cameraPosition[1]) / _vec[1];
                            point[0] = t * _vec[0] + cameraPosition[0];
                            point[2] = t * _vec[2] + cameraPosition[2];

                            // 6.3 Checking if the points is inside the bounds
                            if (point[0] > xMin &&
                                point[0] < xMax &&
                                point[1] > yMin &&
                                point[1] < yMax &&
                                point[2] > zMin &&
                                point[2] < zMax) {
                                pickedPoint.push(point)
                            }
                        }

                        let maxValue2Dpoint = { x: 0, y: 0 };
                        let maxValue2D = 0;
                        let x, y, value;
                        pickedPoint.forEach(p => {
                            const pointIn2d = projectPatientPointToImagePlane(convertToVector3(p), imagePlaneModule)
                            if ((Math.round(pointIn2d.x) == x) && (Math.round(pointIn2d.y) == y)) return
                            x = Math.round(Math.abs(pointIn2d.x))
                            y = Math.round(Math.abs(pointIn2d.y))
                            value = pixelData[x + y * image.columns]
                            if (value > maxValue2D) {
                                maxValue2D = value
                                maxValue2Dpoint = { x, y }
                            }
                        })

                        // 将计算的点转为三维坐标点
                        const imagePointToPatientPoint = cornerstoneTools.import('util/imagePointToPatientPoint')
                        const new3dPoint = imagePointToPatientPoint(maxValue2Dpoint, imagePlaneModule)

                        // // 有坐标偏移时，将坐标点补回来
                        // const offsetString = ['PT', 'NM'].includes(vtkViewport.modality) ? this.ptOffset : this.ctOffset
                        // const OffsetArr = offsetString.split(',')
                        // new3dPoint.x -= Number(OffsetArr[0])
                        // new3dPoint.y -= Number(OffsetArr[1])
                        // new3dPoint.z -= Number(OffsetArr[2])

                        // 将三维坐标点传给十字线同步器
                        this.synchronizerCrosshairs.patientPoint = new3dPoint

                        // 更新所有viewport的图片位置
                        let allAngle = ['x', 'y', 'z']
                        this.viewports.forEach((viewport, i) => {
                            const targetElement = this.getEnabledElement(i)
                            // 跳过 vtk 显示图
                            if (targetElement === undefined) {
                                return;
                            }
                            const stackToolDataSource = cornerstoneTools.getToolState(targetElement, 'stack');
                            // 跳过未显示 cornerstone 图
                            if (stackToolDataSource === undefined) {
                                return;
                            }

                            const stackData = stackToolDataSource.data[0];
                            const orientation = stackData.state.orientation

                            const targetAngleIdx = allAngle.indexOf(orientation)

                            // 相同角度只变化第一个
                            if (targetAngleIdx != -1) {
                                allAngle.splice(targetAngleIdx, 1)
                                // 遍历序列中的全部图像。在元素的堆栈中找到离所选位置最近的图像平面
                                const index = findNearIndex(stackData.imageIds, orientation, new3dPoint)
                                // 滚动到对应位置
                                cornerstoneTools.scrollToIndex(targetElement, index)
                            }
                        });
                    })
                }
            }
        },
        csChangeVtkIndex(el) {
            const apis = this.apis;
            const modality = (el.parentNode.getAttribute('modality'))
            const offsetVal = ['PT', 'NM'].includes(modality) ? this.ptOffset : this.ctOffset
            const OffsetArr = offsetVal.split(',')
            const ipp = getIppByPoint(el)
            ipp[0] += Number(OffsetArr[0])
            ipp[1] += Number(OffsetArr[1])
            ipp[2] += Number(OffsetArr[2])
            apis.forEach((api, apiIndex) => {
                api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, apis, apiIndex);
            })
        },
        // vtk 播放 mip 图
        vtkPlayClip(status, speed, callBack) {
            // 获取 vtk 的 api
            const api = this.apis[this.activeViewportIndex];
            const rotate = 8;
            const ms = 1000 / speed;
            if (!api) {
                return;
            }
            const renderWindow = api.genericRenderWindow.getRenderWindow();
            const viewport = renderWindow.getInteractor().getInteractorStyle().getViewport();
            if (status === 'prev') {
                // 上
                viewport.rotateRelative(0, rotate);
                renderWindow.render();
            } else if (status === 'next') {
                // 下
                viewport.rotateRelative(0, -rotate);
                renderWindow.render();
            } else if (status === 2) {
                // 需要播放
                clearInterval(api.intervalId) // 清除播放
                api.intervalId = setInterval(() => {
                    viewport.rotateRelative(0, -rotate);
                    renderWindow.render();
                }, ms);
            } else if (status === 1) {
                // 需要暂停
                clearInterval(api.intervalId)
                api.intervalId = null;
            } else {
                // 执行改变播放图标状态
                callBack && callBack(api.intervalId ? 2 : 1)
            }
        },
        /**
         * 通过序列id获取vtk容积
         */
        getVtkVolumeBySeriesUid,
        // 从视窗中通过下标获取 enabled 元素
        getEnabledElement(index) {
            if (!this.viewportElements[index]) {
                return false;
            }
            const viewportEle = this.viewportElements[index].getElementsByClassName('viewport-element')
            if (!viewportEle) {
                return false
            }
            return viewportEle[0];
        },
        // 元素视图遍历
        viewportMap(callBack) {
            for (let index = 0; index < this.viewportElements.length; index++) {
                const dom = this.getEnabledElement(index)
                if (!dom) {
                    continue
                }

                const enable = cornerstone.getEnabledElement(dom)

                if (!enable.image) {
                    continue
                }

                callBack && callBack(dom, index)
            }
        },
        // 重置 视图
        /**
         * isPassive = true 被动人为
         */
        resetViewport(delay = 100) {
            // this.scrollThrottle = true;
            setTimeout(() => {
                this.viewportElements = this.$refs['center'].getElementsByClassName('viewportWrapper');
                this.setCurrentViewportIndex((el, isSetViewport) => {
                    setTimeout(() => {
                        try {
                            let viewport = cornerstone.getViewport(el);             // 获取视图
                            if (!viewport) return;
                            if (!isSetViewport) {
                                let obj = cornerstone.getEnabledElement(el)             // 获取开启的元素
                                viewport.voi.windowWidth = obj.image.windowWidth;
                                viewport.voi.windowCenter = obj.image.windowCenter;
                                cornerstone.setViewport(el, viewport);
                            }

                            this.loadShowCount += 1;
                            if (this.loadShowCount === this.showCsViewportLen) {
                                this.loadShowCount = 0;
                                this.setElementImageScale();
                            }
                        } catch (error) {
                            this.$refs.baseTools.onAllRender();
                            //   TODO 本来没有 tool 错误，现在不知为何出现！
                        }
                    }, 0);
                })
            }, delay); // 时间太少，定位线不出现
        },

        async setCurrentViewportIndex(callBack) {
            let changeIndex = Object.assign({}, this.allModalityIndex)
            this.viewportMap(el => {
                if (!el) { return }
                let allStack = cornerstoneTools.getToolState(el, 'stack')
                if (!allStack) { return }

                let isSetViewport = false;
                const state = allStack.data[0].state;

                const key = state.viewportType
                const index = changeIndex[key]
                if (index === undefined || index === null) { return }

                if (allStack.data[0].imageIds.length === index) {
                    return;
                }

                this.awaitScrollToIndex(el, index < 0 ? 0 : index);
                changeIndex[state.viewportType] += 1;


                // // 还原上一个布局的 viewport
                const beforeViewport = this.allModalityIndex[`${state.sModality}${state.orientation}Viewport`];

                if (beforeViewport && Object.keys(beforeViewport).length) {
                    isSetViewport = true;
                }

                callBack && callBack(el, isSetViewport);
            })

        },
        async awaitScrollToIndex(el, index) {
            // this.scrollThrottle = true;
            await cornerstoneTools.scrollToIndex(el, index)
        },
        // 设置元素图像缩放
        setElementImageScale() {
            this.$refs.csViewport.map(item => {
                // TODO 因为 sModality 写死的
                if (item.state && (item.state.sModality === 'CT' || item.state.sModality === 'MR')) {
                    item.syncCallBack('relative')
                }
            })
            const tempTool = this.activeTool

            this.activeTool = 'temp'
            setTimeout(() => {
                this.activeTool = tempTool
            }, 100);
            // 执行 baseTools 组件中的方法
            this.$refs.baseTools.onAllRender()
        },
        // 设置定位线同步器
        setAddSynchronizerCrosshairs() {
            // if (Object.keys(this.synchronizerCrosshairs).length){
            //     this.synchronizerCrosshairs.destroy()
            // }
            // setTimeout(() => {
            //     this.viewportMap(el => {
            //         if (el){
            //             this.synchronizerCrosshairs.add(el)
            //             // console.log(cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs'))
            //             if (!cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')) {
            //                 cornerstoneTools.addToolForElement(el, cornerstoneTools.NuclearCrosshairsTool, { configuration: {  tabId: this.tabId } })
            //             }
            //         }
            //     })
            // }, 0);
        },
        // 初始化定位线
        initAddCrosshairs() {
            // 有清除
            if (Object.keys(this.synchronizerCrosshairs).length) {
                this.synchronizerCrosshairs.destroy()
            }
            // 重新获取 dom
            this.viewportElements = this.$refs['center'].getElementsByClassName('viewportWrapper');

            this.viewportMap(el => {
                if (el) {
                    // 添加到定位线同步器
                    this.synchronizerCrosshairs.add(el);
                    if (!cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')) {
                        cornerstoneTools.addToolForElement(el, cornerstoneTools.NuclearCrosshairsTool, { configuration: { tabId: this.tabId } })
                    }
                }
            })
            // 开启定位线
            if (this.crosshairsTool.mipShow) {
                this.activeCrosshairs('Active');
            } else {
                this.activeCrosshairs('Passive');
            }
            this.resetViewport();
        },
        // 工具活跃  Passive  Active
        activeCrosshairs(mode = 'Active') {
            clearTimeout(this.timerSetCrosshairs);
            this.timerSetCrosshairs = setTimeout(() => {
                this.viewportMap(el => {
                    if (el) {
                        if (!el || !cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')) {
                            return
                        }
                        cornerstoneTools['setTool' + mode + 'ForElement'](el, 'NuclearCrosshairs', {
                            mouseButtonMask: 1,
                            synchronizationContext: this.synchronizerCrosshairs
                        });
                    }
                })
            }, 200)
        },
        // setReferenceLines(status) {
        //     cornerstoneTools.store.state.showReferenceLines = status
        //     this.viewportMap(el => {
        //         if (el){
        //             cornerstone.updateImage(el)
        //         }
        //     })
        // },
        // 工具点击了重置size，同步其它缩放大小渲染
        syncSizeRender() {
            setTimeout(() => {
                // 当前选中的视图
                const curViewport = this.viewports[this.activeViewportIndex];
                // 当前视图的唯一值
                const uid = curViewport && curViewport.uid
                // 遍历组件
                this.$refs.csViewport.map(item => {
                    // 组件与选中的视图一样
                    if (item.state && (item.state.uid === uid)) {
                        // 触发改组件的方法
                        item.syncCallBack('relative')
                    }
                })
            }, 20);
        },
        /**
         * 按照原始矩阵下标，绘制出10X10矩阵
         * original 按照这个数组为矩阵基础放大
         * n        列数量
         */
        setMatrix10(original, n) {
            if (!original || !original.length || !n) {
                return [];
            }

            let matrix10 = []
            let len = original.length;
            // let n = 4;
            let lineNum = len % n === 0 ? len / n : Math.floor((len / n) + 1);

            // 转为二维数组
            for (let i = 0; i < lineNum; i++) {
                let row = original.slice(i * n, i * n + n);
                // 填充一行中的列，一行补充到10个
                let fillLen = Math.ceil(10 / n);
                for (let j = 0; j < fillLen; j++) {
                    row = row.concat(row)
                }
                // 切掉多余的
                row = row.splice(0, 10)
                matrix10.push(row)
            }

            // 填充行
            let fillLen = Math.ceil(10 / matrix10.length)
            for (let i = 0; i < fillLen; i++) {
                matrix10 = matrix10.concat(matrix10)
            }
            matrix10 = matrix10.splice(0, 10)
            return matrix10;
        },
        /**
         * 获取显示的矩阵（布局）
         */
        getViewMatrixIndex(matrix, x, y) {
            let res = []
            for (let i = 0; i < x; i++) {
                for (let j = 0; j < y; j++) {
                    res.push(matrix[i][j])
                }
            }
            return res;
        },
        // 改变布局
        onClickBox(layout, isPassive = false) {

            if (isPassive && Object.keys(this.gridLayoutStyle.containerStyle).length) {
                this.$message.info('该布局下无法修改，请切换其它布局！在做修改');
                return;
            }

            if (isPassive) {
                this.updateLayoutModalityIndex()
            }

            if (this.fullScreenIndex != -1) {
                this.fullScreenIndex = -1
            }
            let width = 1;
            let height = 1;
            if (!layout.num) {
                width = layout.column;
                height = layout.row;
            }

            // 记录当前布局行列
            this.layoutMatrix.curColumn = width;
            this.layoutMatrix.curRow = height;

            // 设置布局样式
            this.style.width = 100 / width + '%'
            this.style.height = 100 / height + '%'

            // 窗口数量
            const num = !layout.num ? width * height : layout.num;

            // 改变布局
            this.viewports.splice(0)
            for (let index = 0; index < num; index++) {
                this.viewports.push({})
            }

            // 被动，人为操作
            if (isPassive) {
                // 是否加载最新的 10x10结构
                if (this.layoutMatrix.isReload) {
                    this.layoutMatrix.matrix10 = this.setMatrix10(this.layoutMatrix.originalIdx, this.layoutMatrix.column)
                    this.layoutMatrix.isReload = false
                }
                if (!this.layoutMatrix.matrix10.length) return;
                const layoutIdx = this.getViewMatrixIndex(this.layoutMatrix.matrix10, height, width)
                this.changeLayoutPushView(layoutIdx)
            }

            this.updateElement();
        },

        updateElement() {
            // const loading = this.$loading.service({
            // 	target: '.c-content'
            // })
            // 设置元素数量
            // 获取所有视图元素（dom）结构
            this.$nextTick(() => {
                try {
                    this.viewportElements = this.$refs['center'].getElementsByClassName('viewportWrapper');

                    this.setFontByWidthAndHeight()
                    // 遍历、resize 重置图像大小
                    // 关闭加载状态
                    // 切换布局，会重置所以注释，不知道会有什么影响
                    // setTimeout(() => {
                    //     this.viewportMap(el => {
                    //         if (el) {
                    //             cornerstone.resize(el);
                    //         }
                    //     })
                    //     // loading.close()	
                    // }, 200);
                } catch (error) {
                    // loading.close()	
                }
            })
        },
        setFontByWidthAndHeight(index = -1) {
            this.$store.state.renderScroll = !this.$store.state.renderScroll
            try {
                const dom = this.getEnabledElement(index === -1 ? 0 : index)
                if (!dom) {
                    return
                }
                const width = dom.clientWidth
                if (width >= 320) {
                    this.styleFont = 'overlay-10'
                } else if (width >= 240) {
                    this.styleFont = 'overlay-08'
                } else if (width >= 210) {
                    this.styleFont = 'overlay-07'
                } else if (width >= 160) {
                    this.styleFont = 'overlay-06'
                } else if (width >= 130) {
                    this.styleFont = 'overlay-05'
                } else if (width >= 105) {
                    this.styleFont = 'overlay-04'
                } else if (width >= 75) {
                    this.styleFont = 'overlay-03'
                } else if (width >= 50) {
                    this.styleFont = 'overlay-02'
                } else {
                    this.styleFont = 'overlay-01'
                }
            } catch (error) {
            }
        },
        // 设置当前选中的视窗
        setViewportActive(index, button) {
            this.activeViewportIndex = index
            // 要放前面
            this.coordinateToolsIndex = this.activeViewportIndex
            this.rotationToolsIndex = this.activeViewportIndex

            if (this.toolRenderLast) {
                this.toolRenderLast = false
                this.showAction = this.activeViewportIndex
                return;
            }
            if (button !== 0) {
                this.showAction = index;
                return;
            }
            // 选中出现工具
            this.showAction = this.activeTool === 'Airtools' && !this.crosshairsTool.mipShow && this.showAction === index ? -1 : index
        },
        onVtkWwwcChange(sourceItem, index, { windowCenter, windowWidth }) {

            // 选中的图像是vtk的，触发改变 cornerstone 显示图像
            if (this.apis[this.activeViewportIndex]) {
                const api = this.apis[this.activeViewportIndex]

                // 注释，该功能（MIP 窗宽窗位变化改变 PT）
                // this.viewportMap((el) => {
                //     if (!el) return;
                //     const img = cornerstone.getImage(el)
                //     if (!img) return;
                //     let modality = img.data.string('x00080060') || ''
                //     modality = modality == 'NM' ? 'PT' : modality
                //     if (api.modality === modality) {
                //         const viewport = cornerstone.getViewport(el)
                //         if (viewport.voi.windowWidth === windowWidth &&
                //             viewport.voi.windowCenter === windowCenter) {
                //             return;
                //         }
                //         viewport.voi.windowWidth = windowWidth;
                //         viewport.voi.windowCenter = windowCenter;

                //         cornerstone.setViewport(el, viewport);
                //     }
                // })

                // console.log( sourceItem)
                let targetLayoutId = 11
                let imageId = sourceItem.imageId
                switch (sourceItem.layoutId) {
                    case 6: // CT 
                        break
                    case 7: // PT
                        break
                    case 11: // FUSE
                        targetLayoutId = 7
                        break
                    default:
                        break
                }
                this.viewports.forEach((item, _ind) => {
                    if (!item.vtk) return;
                    if (item.uid === sourceItem.uid) return;
                    if (item.layoutId != targetLayoutId) return;
                    if (item.imageId != imageId && sourceItem.layoutId != 6) return;

                    const api = this.apis[_ind]
                    if (!api) return

                    const volumeIndex = sourceItem.layoutId == 6 ? 1 : 0
                    const volume = api.volumes[volumeIndex]
                    if (!volume) return
                    // console.log(volume, sourceItem.volumes)

                    const srcVol = sourceItem.volumes[0]
                    if (!srcVol) return

                    if (String(volume.getBounds()) != String(srcVol.getBounds())) return
                    // console.log('TARGET', volume, srcVol)

                    const rgbTransferFunction = volume.getProperty().getRGBTransferFunction(0)
                    const range = volume.getMapper().getInputData().getPointData().getScalars().getRange()

                    rgbTransferFunction.setRange(range[0], range[1]);

                    cornerstone.loadAndCacheImage(api.imageId).then(() => {
                        const renderWindow = api.genericRenderWindow.getRenderWindow();
                        const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter)
                        rgbTransferFunction.setMappingRange(lower, upper);

                        renderWindow.render();
                        if (volumeIndex != 1) api.updateVOIwithoutCB(windowWidth, windowCenter)
                    });
                });
            }
        },

        // 右键
        onMouseRight(e, hide = false) {
            if (hide) {
                this.$refs.contextmenu.hide();
                return;
            }
            this.$refs.contextmenu.handleReferenceContextmenu(e);
            // 直接右键目标, activeViewportIndex 变化没那么快
            setTimeout(() => {
                this.saveImage.rightSelectElement = this.getEnabledElement(this.activeViewportIndex);
            }, 100);
        },
        onClickImageSave(isScreen = 0) {
            if (isScreen) {
                this.saveImage.element = this.$refs.center;
                this.saveImage.visible = true;
            } else {
                const name = this.$store.state.seriesInfo.sPatientName;
                cornerstoneTools.SaveAs(this.saveImage.rightSelectElement, name + '_' + new Date().toLocaleString() + '.jpg');
            }
        },
        // 更新图像
        forceImageUpdate(element) {
            const enabledElement = cornerstone.getEnabledElement(
                element
            );

            if (enabledElement.image) {
                cornerstone.updateImage(element);
            }
        },
        // onClickShowHideLines() {
        //     this.showReferenceLines = !this.showReferenceLines
        //     this.setReferenceLines(this.showReferenceLines)
        //     this.viewportMap((el) => {
        //         if (!el) return;
        //         this.forceImageUpdate(el)
        //     })

        // },
        // 点击保存序列工具
        onMeasurements(funName) {
            if (funName === 'flush') {
                this.$refs.baseTools.getNewToolList()
            } else if (funName === 'save') {
                this.$refs.baseTools.onclickSaveTool('active')
            } else {
                // this.$refs.baseTools.onClickDelTool('hide')
                this.clearMark(this.saveImage.rightSelectElement)
            }
        },
        clearMark(_element) {
            const element = _element || this.getEnabledElement(this.activeViewportIndex)
            if (!element) {
                return
            }
            const stack = cornerstoneTools.getToolState(element, 'stack');
            if (stack && stack.data && stack.data[0]) {
                const { ctUid, petUid, thickness } = stack.data[0].state;
                this.$refs.baseTools.removeAllTabTool(petUid, ctUid, thickness);

                setTimeout(() => {
                    for (let index = 0; index < this.viewportElements.length; index++) {
                        const el = this.getEnabledElement(index)
                        if (!el) {
                            continue
                        }
                        cornerstone.updateImage(el);
                    }
                }, 0);
            }
        },
        // 点击打开布局设置弹窗
        onClickLayoutSet() {
            this.$store.state.triggerLayoutSet = true;
        },
        toggleCoordinateMoving() {
            this.showCoordinateTools = !this.showCoordinateTools
        },
        toggleRotationMoving() {
            this.showRotationTools = !this.showRotationTools
        },
        onOffsetChange(isCT, offset, element) {
            if (isCT) {
                this.ctOffset = offset
            } else {
                this.ptOffset = offset
            }
            this.changeImageOffset(this.ptOffset + ',' + this.ctOffset)
        },
        async changeImageOffset(offset) {
            if (!this.ctUid) return;
            this.updateLayoutModalityIndex()
            const { vtkVolume } = await tryGetVtkVolumeForSeriesNumber(this.ctUid);
            const vtkImageData = vtkVolume.vtkImageData;
            const [x0, y0, z0] = vtkImageData.getOrigin();  // x,y,z 原点
            const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
            const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

            let xStart = x0; // + xSpacing * (xMax - xMin);  // x最大值(ipp 切片x最大位)
            let yStart = y0; // + ySpacing * (yMax - yMin);  // y最大值
            let zStart = z0; // + zSpacing * (zMax - zMin);  // z最大值

            let xNmax = xSpacing * (xMin + xMax)
            let yNmax = ySpacing * (yMin + yMax)
            let zNmax = zSpacing * (zMin + zMax) + zSpacing;

            let xCount = Math.round(xNmax / this.thickness);
            let yCount = Math.round(yNmax / this.thickness);
            let zCount = Math.round(zNmax / this.thickness);

            let [sagittalsX, sagittalsXPT] = getNewImageIds('sagittal', { xCount, xStart }, this.thickness, this.ctUid, this.petUid, undefined, offset)
            let [coronalY, coronalYPT] = getNewImageIds('coronal', { yCount, yStart }, this.thickness, this.ctUid, this.petUid, undefined, offset)
            let [axialZ, axialZPT] = getNewImageIds('axial', { zCount, zStart }, this.thickness, this.ctUid, this.petUid, undefined, offset)


            this.originSeries.forEach(_ => {
                if (_ && _.imageIds) {
                    _.imageIds.forEach(img => {
                        if (cornerstone.imageCache.getImageLoadObject(img)) {
                            cornerstone.imageCache.removeImageLoadObject(img)
                        }
                    })
                }
            })

            this.originSeries = [
                { sModality: 'CT', orientation: 'y', imageIds: coronalY },
                { sModality: 'PT', orientation: 'y', imageIds: coronalYPT },
                { sModality: 'CT', orientation: 'x', imageIds: sagittalsX },
                { sModality: 'PT', orientation: 'x', imageIds: sagittalsXPT },
                { sModality: 'CT', orientation: 'z', imageIds: axialZ },
                { sModality: 'PT', orientation: 'z', imageIds: axialZPT }
            ]




            const viewportDataArr = []
            this.viewportMap((el) => {
                const viewportData = cornerstone.getViewport(el);
                if (viewportData) {
                    viewportDataArr.push(viewportData)
                } else {
                    viewportDataArr.push(null)
                }
            })



            this.viewports = this.viewports.map((viewport, index) => {
                if (viewport.vtk) {
                    // 将Z轴PT CT图序列数据存下来 用于三维定位
                    viewport.originSeriesZ = this.originSeries.filter(item => item.orientation === 'z' && item.imageIds.length > 0)
                    viewport.originSeriesY = this.originSeries.filter(item => item.orientation === 'y' && item.imageIds.length > 0)
                    return viewport;
                }
                const obj = this.viewportFindOriginSeries(viewport);
                if (obj) {
                    viewport.imageIds = obj.imageIds.slice(0)
                    if (viewport.layerIds) {
                        const layerObj = this.viewportFindOriginSeries({ sModality: this.modalityFindOpposite(viewport.sModality), orientation: viewport.orientation })
                        if (layerObj) {
                            viewport.layerIds = layerObj.imageIds.slice(0)
                        }
                    }
                    viewport.uid = this.$fun.onlyValue();

                    const scaleParams = viewportDataArr[index]
                    if (scaleParams) {
                        // 传入更新前的scale参数，解决偏移之后scale重置问题
                        viewport.scaleParams = {
                            translation: {
                                x: scaleParams.translation.x,
                                y: scaleParams.translation.y
                            },
                            scale: scaleParams.scale
                        }
                    }
                }
                return viewport
            });
        },
        setDefaultViewportData(scheme, imageId) {
            const ptDef = scheme.pt || {}
            const ctDef = scheme.ct || {}
            const fuseDef = scheme.fuse || {}

            const ptMipDef = ptDef.mip || {}
            const ctMipDef = ctDef.mip || {}

            const ptDefVp = {
                windowWidth: ptDef.w === '' ? null : ptDef.w,
                windowCenter: ptDef.l === '' ? null : ptDef.l,
                colormap: ptDef.colormap || 'gray',
                invert: ptDef.invert  // true, false , undefined = 默认反片
            }

            const ctDefVp = {
                windowWidth: ctDef.w === '' ? null : ctDef.w,
                windowCenter: ctDef.l === '' ? null : ctDef.l,
                colormap: ctDef.colormap || 'gray',
                invert: !!ctDef.invert
            }
            const fuseDefVp = {
                // windowWidth: fuseDef.w,
                // windowCenter: fuseDef.l,
                // invert: !!fuseDef.invert
                colormap: fuseDef.colormap
            }

            const ptMipDefVp = {
                windowWidth: ptMipDef.w === '' ? null : ptMipDef.w,
                windowCenter: ptMipDef.l === '' ? null : ptMipDef.l,
                colormap: ptMipDef.colormap,
                invert: !!ptMipDef.invert
            }

            const ctMipDefVp = {
                windowWidth: ctMipDef.w === '' ? null : ctMipDef.w,
                windowCenter: ctMipDef.l === '' ? null : ctMipDef.l,
                colormap: ctMipDef.colormap,
                invert: !!ctMipDef.invert
            }

            // pt图像默认窗宽窗位是U 、L 需要转换, NM跟PT不同
            if (ptDefVp.windowWidth && ptDefVp.windowCenter && imageId && !this.groupModality.includes('NM')) {
                try {
                    const { w, l } = this.$fun.transformULtoVoiWL(ptDefVp.windowWidth, ptDefVp.windowCenter, imageId)
                    ptDefVp.windowWidth = w
                    ptDefVp.windowCenter = l

                } catch (error) {
                    console.log(error)
                }
            }

            this.allModalityIndex.CTxViewport = { ...ctDefVp };
            this.allModalityIndex.CTyViewport = { ...ctDefVp };
            this.allModalityIndex.CTzViewport = { ...ctDefVp };

            this.allModalityIndex.PTxViewport = { ...ptDefVp };
            this.allModalityIndex.PTyViewport = { ...ptDefVp };
            this.allModalityIndex.PTzViewport = { ...ptDefVp };

            this.allModalityIndex.PTxViewportFuse = { ...fuseDefVp };
            this.allModalityIndex.PTyViewportFuse = { ...fuseDefVp };
            this.allModalityIndex.PTzViewportFuse = { ...fuseDefVp };

            this.allModalityIndex.PTmipViewport = { ...ptMipDefVp };
            this.allModalityIndex.CTmipViewport = { ...ctMipDefVp };
        },
        onClickCrosshairs(type, val) {
            const crosshairsType = cornerstoneTools.store.state.crosshairsType;
            crosshairsType[type] = val;

            localStorage.setItem('configs-crosshairs-type', JSON.stringify(crosshairsType));
            this.$nextTick(() => {
                // 触发渲染
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent);
                // vtk svg 渲染
                this.apis.forEach(api => {
                    const { svgWidgetManager } = api;
                    svgWidgetManager.render();
                })
            })
        },
        onClickRebuildSyncZoom() {
            this.$store.commit('setRebuildSyncZoom')
        },
        // 点击浏览器关联
        onClickWebSync(attr) {
            // 更新当前浏览器tab信息
            this.$store.commit('setSynchronization', attr);
            // 存储到初始化 storage 中 
            localStorage.setItem('synchronization', JSON.stringify(this.synchronization))
            // 发送给其它浏览器tab
            broadcastManager.postMessage(
                {
                    info: this.synchronization,
                    fun: 'setSynchronization',
                }
            );

            if (attr === 'start') {
                this.$refs.contextmenu.hide();
            }
        }

    },
}
</script>
<style lang="scss" scoped>
.c-content {
    display: flex;
    flex-wrap: wrap;

    >div {
        position: relative;
    }

    .vtk-viewport {
        &:hover {
            .c-action {
                display: block;
            }
        }
    }

    .c-action {
        display: none;
        position: absolute;
        top: 50%;
        right: 2px;
        margin-top: -64px;
        height: 128px;

        li {
            width: 30px;
            height: 30px;
            line-height: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            margin-bottom: 2px;

            &:hover {
                background: #eee;
            }

            i {
                font-size: 18px;
            }
        }
    }
}

.c-tool-header {
    .c-layout {
        display: flex;
        padding-left: 1px;
        border-bottom: 1px solid #d2d2d2;
    }

    .c-box {
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-around;
        width: 98px;
    }

    .c-box-01,
    .c-box-01-right {
        padding: 2px 0px 2px 0px;

        >li {
            float: left;
            width: 48px;
            height: 48px;
            line-height: 48px;
            /* border: 1px solid #d2d2d2; */
            margin-right: 0;
            margin-bottom: 0;
            cursor: pointer;
            transition: all 0.2s;
            background: #fff;
            border-radius: 4px;

            i {
                display: inline-block;
                width: 100%;
                height: 100%;
                border-radius: 2px;
                background-size: cover;
            }

            &:hover {
                box-shadow: 2px 2px 4px 0px #c0c0c0;
            }

            &.i-active {
                border-color: #6294B7;
                color: white;
            }
        }

    }

    .c-box-01-right {
        width: 148px;
        border-left: 1px solid #aaa;
    }
}

.i-tip-set {
    cursor: pointer;
    color: #d2d2d2;
    line-height: 230px;
}

.preload-tip {
    position: absolute;
    top: auto;
    bottom: 0;
    left: 0;
    padding: 5px;
    color: floralwhite;
    font-size: 12px;
    background: rgb(84, 116, 149);

    i {
        color: floralwhite;
    }
}
</style>