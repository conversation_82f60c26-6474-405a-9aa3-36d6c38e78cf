<template>
    <div class="body" :style="{ background: background }">
        <div class="box-parent" @mouseleave="onLeave" @contextmenu="contextmenuShow">
            <div class="box-parent-item" :class="{'box-parent-item10': rows == 10}" v-for="row in rows">
                <span v-for="column in columns" :key="column"
                :class="[row <= curRow && column <= curColumn ? 'i-hover' : '' ]"
                @mouseleave="onLeaveBox" 
                @mouseover="onOverBox(row, column)" 
                @mousemove="onMoveBox"
                @click="onClick(row, column)" ></span>
            </div>
        </div>
        <div class="c-tip" v-show="tipShow" :style="tipStyle">{{ curRow + 'x' + curColumn }}</div>

        <v-contextmenu ref="contextmenu">
            <v-contextmenu-item @click="onClickSetDefaultLayout">固定 {{ fixed.row }} x {{ fixed.column }} 布局</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onClickSetAutoLayout">自动布局</v-contextmenu-item>
        </v-contextmenu>
    </div>
</template>

<script>
export default {
    name: "LayoutBox",
    props: {
        rows: {
            type: Number,
            default: 7
        },
        columns: {
            type: Number,
            default: 7
        },
        background: {
            type: String,
            default: 'white'
        },
        toolType: {
            type: String,
            default: 'read'
        },
    },
    data() {
        return {
            curRow: 0,
            curColumn: 0,
            tipShow: false,
            tipStyle: {
                top: '-100px',
                left: '-100px'
            },
            fixed: {
                row: 0,
                column: 0
            },
            storageKey: 'configs-read-default-layout'
        }
    },
    methods: {
        onLeave(){
            this.curRow = 0;
            this.curColumn = 0
        },
        onLeaveBox(){
            this.tipShow = false
        },
        onOverBox(row, column){
            this.curRow = row
            this.curColumn = column
        },
        onMoveBox(e){
            this.tipShow = true
            this.tipStyle.top = e.clientY + 22 + 'px'
            this.tipStyle.left = e.clientX - 4 + 'px'
        },
        onClick(row, column){
            this.$emit('clickBox', {row, column})
        },
        onClickSetDefaultLayout() {
            localStorage.setItem(this.storageKey, JSON.stringify({row: this.fixed.row, column: this.fixed.column}));
            this.$message({
                message: '设置成功！',
                type: 'success'
            });
        },
        onClickSetAutoLayout() {
            localStorage.setItem(this.storageKey, JSON.stringify(''));
            this.$message({
                message: '设置成功！',
                type: 'success'
            });
        },
        contextmenuShow(ev) {
            if (this.toolType != 'read') {
                return
            }
            this.fixed.row = this.curRow
            this.fixed.column = this.curColumn
            const postition = {
				top: ev.clientY,
				left: ev.clientX
			}
            this.$refs.contextmenu.show(postition)
        }
    }
};
</script>
<style lang="scss" scoped>
    .body{
        .box-parent{
            cursor: pointer;
            .box-parent-item{
                height: 16px;
                span{
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    // border: 1px solid #eee;
                    border-right: 1px solid #eee;
                    border-bottom: 1px solid #eee;
                    box-sizing: border-box;
                    &.i-hover{
                        background-color: #6294B7;
                    }
                }
                &.box-parent-item10{
                    height: 14px;
                    span{
                        width: 14px;
                        height: 14px;
                    }
                }
            }
        }
    }
    .c-tip{
        position: fixed;
        background: white;
        color: #666;
        border: 1px solid #666;
        padding: 2px 4px;
        z-index: 11;
    }
</style>