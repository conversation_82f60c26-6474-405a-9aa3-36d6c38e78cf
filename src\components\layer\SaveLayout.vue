<template>
    <el-dialog append-to-body title="保存布局显示" :visible.sync="visible" @close="closeDialog" :close-on-click-modal="false"
    width="820px" custom-class="my-dialog config-set-dialog">
        <div class="box-content">
            <div class="form-item">
                <span>布局名称</span>
                <el-input style="width: 294px" size="mini" v-model="layout.text">
                </el-input>
                <span>  布局位置</span>
                <el-input style="width: 294px" size="mini" v-model="layout.position">
                </el-input>
            </div> 
        </div>
        <p style="padding-top: 20px;">布局显示</p>
        <div class="list-content inner-pc-plan">
            <div class="left ">
                <AreaFit :isMuch="true" class="c-content">
                    <div class="box-area" :style="gridLayoutStyle.containerStyle" >
                        <img :src="faceUrl" class="face"/>
                        <!-- <img :src="thumbUrl" class="face"/> -->
                        
                        <template v-for="(item, index) in viewports">
                            <ViewMPRItem
                                ref="mprItem"
                                :propIsOverlayVisible="false"
                                :propCrosshairsTool.sync="crosshairsTool"
                                :key="item.id" 
                                :index="index" 
                                :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style"
                                :imageInfo="item"
                                :id="item.id"
                                tabId="none"
                                :isFun="false"
                                :activeSelect="activeViewportIndex === index"
                                @setViewportActive="setViewportActive(index, $event)"
                                :showAction="false">
                            </ViewMPRItem>
                        </template>
                    </div>
                </AreaFit>
            </div>
            <div class="right">
                <div class="form-item">
                    <span>图像类型</span>
                    <el-input size="mini" style="width: 160px"
                        v-model="selectObj.modality">
                    </el-input>
                </div>
                <div class="form-item">
                    <span>序列描述</span>
                    <el-input size="mini" style="width: 160px"
                        v-model="selectObj.seriesDesc">
                    </el-input>
                </div>
                <div class="form-item">
                    <span>序号</span>
                    <el-input size="mini" style="width: 160px" v-floorNumber
                        v-model="selectObj.seriesNumber">
                    </el-input>
                </div>

                <div v-if="selectObj.modality1" style="border-top: 1px solid #eee;padding-top: 14px;">
                    <div class="form-item">
                        <span>图像类型</span>
                        <el-input size="mini" style="width: 160px"
                            v-model="selectObj.modality1">
                        </el-input>
                    </div>
                    <div class="form-item">
                        <span>序列描述</span>
                        <el-input size="mini" style="width: 160px"
                            v-model="selectObj.seriesDesc1">
                        </el-input>
                    </div>
                    <div class="form-item">
                        <span>序号</span>
                        <el-input size="mini" style="width: 160px" v-floorNumber
                            v-model="selectObj.seriesNumber1">
                        </el-input>
                    </div>
                </div>
            </div>
        </div>

        <div class="c-item-03">
            <el-button type="small" @click="onClickSave" icon="el-icon-picture-outline" >保 存</el-button>
            
        </div>
    </el-dialog>
</template>
<script>
import html2canvas from "html2canvas"

import AreaFit from '$src/layout/AreaFit.vue'
import ViewMPRItem from '$components/ViewMPRItem'
import { dbLayoutSet } from '$library/db';
export default {
    name: "SaveLayout",
    components: {
        ViewMPRItem,
        AreaFit
    },
    props: {
        title: {
            type: String,
            default: "系统设置",
        },
        dialogVisible: {
            type: Boolean,
            default: false
        },
        layout: {
            type: Object,
            default: () => ({})
        },
        gridLayoutStyle: {
            type: Object,
            default: () => {
                return {
                    containerStyle: {},
                    itemStyle: [],
                }
            }
        },
        viewports: {
            type: Array,
            default: () => ([])
        },
        screenshotDom: {
            default: () => ({})
        },
    },
    data() {
        return {
            visible: false,
            activeViewportIndex: -1,
            style: {
				width: '50%',
				height: '50%',
			},
            crosshairsTool: {
                mipShow: false
            },
            selectObj: {
                modality: '',
                seriesDesc: '',
                seriesNumber: '',
            },
            customLayout: [],
            faceUrl: '',
            thumbUrl: '',
        }
    },
    watch: {
        dialogVisible: {
            handler() {
                this.visible = this.dialogVisible;
            },
            immediate: true
        }
    },
    methods: {
        closeDialog() {
            this.$emit('update:dialogVisible', false);
        },
        // 设置当前选中的视窗
		setViewportActive(index){
            this.activeViewportIndex = index
            this.selectObj = this.customLayout[index]
		},
        onClickSave() {
            this.layout.customLayout = this.customLayout
            const data = { ...this.layout }
            data.img = this.thumbUrl
            dbLayoutSet.then(e => {
                e.update(data).then(() => {
                    this.$store.dispatch('saveAllConfig').finally(() => {
                        this.$emit('refresh')
                    })
                })
            });
        },
        getFace() {
            html2canvas(this.screenshotDom,
            {
                scale: 1,
                ignoreElements: (element) => { 
                    
                    const classn = element.getAttribute('class')
                    // console.log(classn)
                    // const white = ['box-area', 'c-content', 'mpr-item', 'item-view',
                    // 'cornerstone-canvas',
                    // 'viewport-element',
                    // 'viewportWrapper'].find(str => (classn || '').indexOf(str) > -1)
                    // if (!white) {
                    //     return true;
                    // };
                    
                    if (element.getAttribute('noprint')) {
                        return true;
                    };
                    if (element.getAttribute('saveimage')) {
                        return true;
                    };
                    const black = ['item-list-action','layer-tools', 'mpr-crosshairs',
                        'vtkSVGWidgetManager-1'
                    ].find(str => (classn || '').indexOf(str) > -1)
                    if (black) {
                        return true;
                    };
                }
            }).then(canvas => {
                let extra_canvas = document.createElement("canvas");
                extra_canvas.setAttribute('width',48);
                extra_canvas.setAttribute('height',48);
                let ctx = extra_canvas.getContext('2d');

                ctx.strokeStyle = 'black';
                ctx.lineJoin = 'round';
                ctx.lineWidth = 1;
                ctx.fillRect(4, 4, 40, 40);

                ctx.drawImage(canvas,0,0,canvas.width, canvas.height,5, 5, 38, 38);
                // let dataURL = 
                this.faceUrl = canvas.toDataURL("image/png", 1);
                this.thumbUrl = extra_canvas.toDataURL("image/png", 1);
                // callBack && callBack(item);
            });
        }
    },
    mounted() {
        // this.layout.customLayout = null
        this.getFace()
        setTimeout(() => {
            this.layout.customLayout = []
            const refMprItem  = this.$refs.mprItem

            const setCurrent = (p, viewport, index) => {
                let item = {}

                if (p.length) {
                    p.forEach((_, index) => {

                        const seriesDesc = _.image.data.string('x0008103e') || null
                        const seriesNumber = _.image.data.string('x00200011') || null
                        const modality = _.image.data.string('x00080060') || ''

                        if (index === 0) {
                            item.seriesDesc = seriesDesc
                            item.seriesNumber = seriesNumber
                            item.modality = modality
                        }else {
                            item['seriesDesc'+index] = seriesDesc
                            item['seriesNumber'+index] = seriesNumber
                            item['modality'+index] = modality
                        }
                    })


                }else {
                    item = {
                        seriesDesc: p.data.string('x0008103e') || null,
                        seriesNumber: p.data.string('x00200011') || null,
                        modality: p.data.string('x00080060') || ''
                    }
                    // 特殊展开的图
                    if (viewport.unfold != undefined) {
                        item.unfold = viewport.unfold
                    }
                }
                item.viewType = viewport.viewType
                

                this.customLayout[index] = item
                
            }

            this.viewports.forEach(async (item, index) => {
                const itemComponent = refMprItem[index]
                if (itemComponent) {
                    const mprViewport = itemComponent.$refs.mprViewport
                    if (mprViewport) {
                        const csViewport = mprViewport.$refs.csViewport
                        const element = csViewport.element

                        const layers = cornerstone.getLayers(element)

                        const img = cornerstone.getImage(element)

                        if (layers.length) {
                            // 融合图
                            setCurrent(layers, item, index)
                        }else {
                            setCurrent(img, item, index)
                        }
                    }else {
                        const vtkViewport = itemComponent.$refs.vtkViewport
                        if (vtkViewport) {
                            const img = await cornerstone.loadImage(vtkViewport.imageIdsGroup[0][0])

                            if (vtkViewport.imageIdsGroup[1]) {
                                const img1 = await cornerstone.loadImage(vtkViewport.imageIdsGroup[1][0])

                                setCurrent([{image: img}, {image: img1}], item, index)
                            }else {
                                setCurrent(img, item, index)
                            }
                        }
                    }
                }

                // 默认选中第一个
                if (item.id && this.activeViewportIndex === -1) {
                    this.activeViewportIndex = index

                    this.selectObj = this.customLayout[index]
                }
            })

            
            
            
        }, 500)
    }
}
</script>
<style lang="scss" scoped>
.config-set-dialog {
    background: white;
}
.box-content{
    display: flex;
    flex-wrap: wrap;
    .form-item{
        margin-bottom: 14px;
        > span {
            padding-right: 10px;
        }
        &:last-child{
            margin-bottom: 0px;
        }
    }
}
.list-content {
    display: flex;
    flex-direction: row;
    padding: 20px 0;
    background: white;
    .left {
        display: flex;
        width: 480px;
        height: 480px;
        .box-area{
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            background: #000;
        }
    }
    .right {
        flex: 1;
        overflow: hidden;
        padding-left: 14px;
        .form-item{
            margin-bottom: 14px;
            > span {
                display: inline-block;
                min-width: 70px;
            }
        }
    }
}
.c-item-03 {
    text-align: right;
    padding: 10px 24px 10px 0; 
    background: white;
}
.face {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 8;
    pointer-events: none;
    background: #000;
}
</style>