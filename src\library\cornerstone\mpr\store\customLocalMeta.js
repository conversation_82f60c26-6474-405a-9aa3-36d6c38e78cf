import mprMetaDataStore from '$library/cornerstone/mpr/mprMetadata/mprMetaDataStore.js';

function getSuvParams() {
    let val = localStorage.getItem('setting-suv-params')
    val = !val ? {} : JSON.parse(val)
    return val
}

let data = getSuvParams()

// 设置 cornerstone.metaData 值 这样就能通过 imageId 方式获取
export function setImageIdMetaData(imageId) {
    const [ , seriesInstanceUID ] = imageId.split(':') // mpr:${seriesInstanceUID}

    if (!data[seriesInstanceUID]) {
        return
    }

    const generalSeriesModule = cornerstone.metaData.get(
        'generalSeriesModule',
        imageId
    )
    if (!generalSeriesModule) {
        mprMetaDataStore.set(imageId, data[seriesInstanceUID])
    }
}

export default {
    data,
    setImageIdMetaData
}