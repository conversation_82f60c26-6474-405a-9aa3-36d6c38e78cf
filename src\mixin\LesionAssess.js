export default {
    data() {
        return {
            lesionTools: ['LesionArea', 'FreehandRoi', 'CircleRoi', 'EllipticalRoi', 'RectangleRoi'],
            lesionList: [],
        }
    },
    methods: {
        lesionSave() {
            this.lesionList = [];
            const elements = this.$refs['center'].getElementsByClassName('viewportWrapper');
            const curElement = elements[this.activeViewportIndex].getElementsByClassName('viewport-element')[0];
            const curImage   = cornerstone.getImage(curElement);
            if (curImage) {
                const curImageId = curImage.imageId;
                const curAllTool = cornerstoneTools.globalImageIdSpecificToolStateManager.toolState[curImageId];
                if (curAllTool) {

                    // 查找需要保存的工具
                    const toolName = this.lesionTools.find(toolName => {
                        const toolData = curAllTool[toolName];

                        if (toolData && toolData.data && toolData.data.length) {
                            return true;
                        }
                    })
                    // 查找到这个工具有
                    if (toolName) {
                        cornerstone.getImage(curElement);
                        
                        let curAllStack = cornerstoneTools.getToolState(curElement, 'stack');
                        if (!curAllStack || !curAllStack.data.length) { 
                            // 当前图像没有
                            return;
                        }
                        const [ curGroupIndex, curOrientation, curShowType ] =  curAllStack.data[0].state.layoutId.split(',');
                        const curThickness = curAllStack.data[0].state.thickness;

                        this.addToolItem(curAllTool[toolName].data[0], curGroupIndex, curThickness, curElement);

                        const targetElements = this.$refs['center'].getElementsByClassName('viewport-element');
                        if (!targetElements) {
                            return;
                        }
                        for (let index = 0; index < targetElements.length; index++) {
                            const el = targetElements[index];
                            
                            if (!el){
                                continue;
                            }

                            let allStack = cornerstoneTools.getToolState(el, 'stack');
                            if (!allStack || !allStack.data.length) { 
                                continue;
                            }
                            const data = allStack.data[0];
                            const [ targetGroupIndex, targetOrientation, targetShowType ] = data.state.layoutId.split(',');
                            if (curGroupIndex != targetGroupIndex && curOrientation == targetOrientation && curShowType == targetShowType) {
                                // 获取其它组相同工具
                                const targetImage = cornerstone.getImage(el);
                                const targetImageId = targetImage.imageId;
                                const targetAllTool = cornerstoneTools.globalImageIdSpecificToolStateManager.toolState[targetImageId];
                                
                                // 其它组也有相同绘制工具在视图上
                                if (targetAllTool && targetAllTool[toolName] && targetAllTool[toolName].data && targetAllTool[toolName].data.length) {
                                    this.addToolItem(targetAllTool[toolName].data[0], targetGroupIndex, data.state.thickness, el);
                                }
                            }
                        }

                        // 显示弹窗，把 lesionList 丢给弹窗
                        this.saveLesion.lesionList = this.lesionList;
                        this.saveLesion.visible = true;
                    }else {
                        this.$message.success('视图中没有工具');
                    }
                }else {
                    this.$message.success('视图中没有工具');
                }
                
            }
        },
        addToolItem(tool, groupIndex, thickness, el) {
            const item = {};
            let data = {};
            let area, max, min, avg;
            let unit = tool.unit;

            if (tool.cachedStats) {
                area = tool.cachedStats.area;
                max = tool.cachedStats.max;
                min = tool.cachedStats.min;
                avg = tool.cachedStats.mean;
            }
            // 圆，矩形结构是这样的
            if (tool.cachedStats.meanStdDevSUV) {
                const meanStdDevSUV = tool.cachedStats.meanStdDevSUV;
                max = meanStdDevSUV.max;
                min = meanStdDevSUV.min;
                avg = meanStdDevSUV.mean;
                unit = 'SUV';
            }else if (tool.meanStdDev) {
                const meanStdDev = tool.meanStdDev;
                max = meanStdDev.max;
                min = meanStdDev.min;
                avg = meanStdDev.mean;
                unit = ''; // TODO 什么单位？
            }else if (tool.meanStdDevSUV) {
                // 有 SUV
                const meanStdDevSUV = tool.meanStdDevSUV;
                max = meanStdDevSUV.max;
                min = meanStdDevSUV.min;
                avg = meanStdDevSUV.mean;
                unit = 'SUV';
            }

            if (tool.area)  area = tool.area;
            if (tool.TLG)   data.TLG = tool.TLG;

            if (groupIndex >= 0) {
                const groupIds = this.series.uids[groupIndex];
                if (groupIds) {
                    const [ seriesId ] = groupIds.split(',');

                    const studyList = Array.from(this.$store.state.seriesMap.values());
                    const study = studyList.find(study => {
                        const instance = Array.from(study.instanceList.values());
                        return instance.find(series => {
                           return series.sSeriesInstanceUID == seriesId;
                        })
                    })
                    if (study) {
                        item.checkDesc = study.seriesInfo.sStudyDescription;
                        item.checkDate = study.seriesInfo.iStudyDate;
                    }
                    
                } 
            }

            data[`${unit}_MAX`] = max.toFixed(2);
            data[`${unit}_MIN`] = min.toFixed(2);
            data[`${unit}_AVG`] = avg.toFixed(2);
            
            if (unit === 'SUV' && area && thickness) {
                data.MTV = (area * thickness).toFixed(2);
                if (data.SUV_AVG) {
                   data.TLG = (data.MTV * data.SUV_AVG).toFixed(2);
                }
            }
            
            item.checkName = '检查' + (Number(groupIndex) + 1);
            item.thickness = thickness;
            item.measurements = data;
            item.area = area;
            item.el   = el;
            
            this.lesionList.push(item);
        }
    },
}