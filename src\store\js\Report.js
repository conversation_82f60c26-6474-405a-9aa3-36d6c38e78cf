import Api from '$api'
import getConfigByStorageKey from '$library/utils/configStorage.js'
import Fun from '$library/utils/function'
import { getAllStorageInJSON, setAllStorageInJSON } from '$library/utils/readwriteStorage.js'
import Message from 'element-ui/lib/message.js'
import Notification from 'element-ui/lib/notification'

export default {
	state: {
		visitedViews: [],
		seriesMap: new Map(),
		sPatientId: null,    // 病例患者 id
		deviceTypeId: null,  // 设备类型 id 打印的时候需要设备类型
		accessNo: null,      // 无报告看图查询接口使用参数）
		patientId: null,     // 图像患者 id
		studyDate: null,     // 检查时间（无报告看图查询接口使用参数）
		userId: null,        // 打印 pdf 使用的参数
        userName: null,      // 登录用户名
        ip: null,            // ip
		token: null,         // 权限验证 token
		sWorkStationId: '',  // 工作站 id
		seriesInfo: {},      // 检查信息
		hideTools: false,    // 隐藏右侧工具栏
        hideRragTools: false,// 隐藏左侧拖拽工具栏
		disabledTool: false, // 关闭工具
		rotationSyncTool: false, // 旋转微调同步
		rebuildContrastShow: false, // 点击显示重建对比
        imageReadOnly: false, // 图像只读
		// 鼠标事件
		mouseEvents: {
			mid: 'Pan',
			right: 'Zoom'
		},
		// 工具连续使用
		toolRepeatUse: false,
		toolSync: false,
		toolNewImageUse: false,
		openSlectSeries: {
			dialogVisible: false,
			limitType: 1,       // 阅图，重建, 2 3D MPR
			refresh: false,
			title: '患者序列'
		},
		renderScroll: true,  // 用于渲染滚动条高度
		userInfo: {},
		contrastUId: new Set(),
		listenTool: {
			value: false,
			seriesId: null
		},
		listenCapture: {},    // 截图刷新
		currentModule: 'Report',
		modalityTagItems: {},
		triggerLayoutSet: null,
		synchronization: {
			start: false,
			position: false,
			zoomPan: false,
			window: false
		},
		requestList: [],
		readDefaultLayout: '',
		checkModality: '',      // 当前最后打开检查设备
		isRemark: false,        // 是否截图备注
		isReadShowRemark: true, // 阅图是否在图像上显示备注
		showRebuild: {},
		excludeDescriptions: [], // 排除的序列描述
		defaultColormap: 'hot',
		layoutFill: false,
		shortcutVisible: false,
		dicomTagsVisible: false, // DICOM Tags 弹窗
		suvParamsVisible: false, // suv 参数设置 弹窗
		suvParams: {},           // suv 参数设置 弹窗-参数
		dicomTagsElement: {},
		hideReferenceLines: {},
		heartbeat: {
			handle: null,
			delay: 1000 * 140
		},
		autoRebuilds: [],        // 自动重建序列
		schemeRebuilds: [],      // 重建方案
		screenshotContrast: [],  // 截图对比，添加的截图对比图信息
		systemSetList: [], // 系统配置数据列表
		rebuildSyncZoom: false, // 重建冠、矢同步缩放
		showShortcutKey: true,
		showPrintThumb: false, // 显示缩略图打印
        keyboardForceSync: false, // 键盘强制同步
        showDirection: true,   // 显示方向信息
	},
	mutations: {
		SET_OPENSLECTSERIES: (state, payload) => {
			const { dialogVisible, limitType, refresh } = payload
			state.openSlectSeries.dialogVisible = dialogVisible
			state.openSlectSeries.limitType = limitType || 1
			
			// 是否刷新，改变状态，在组件中，可以监听这个变化，做刷新操作
			if (refresh) {
				state.openSlectSeries.refresh = !state.openSlectSeries.refresh
			}
		},
		SET_HIDETOOLS: (state, value) => {
			state.hideTools = value
		},
        SET_HIDERAGTOOLS: (state, value) => {
            state.hideRragTools = value
        },
		ADD_VIEW: (state, view) => {
			if (state.visitedViews.some(v => v.path === view.path)) return;
			const obj = Object.assign({}, view, {
				title: view.meta.title || 'no-name'
			})
			obj.matched = [] // 奇怪，不清空会堆栈

			//   if (['ReadDcm', 'ViewSlice'].includes(view.name)){
			//     obj.affix = true
			//   }
			if (obj.name === 'Select') return;
			state.visitedViews.push(obj)
		},
		DEL_VIEW: (state, view) => {
			for (const [i, v] of state.visitedViews.entries()) {
				if (v.path === view.path) {
					state.visitedViews.splice(i, 1)
					break
				}
			}
		},
		SET_SERIES: (state, payload) => {
			state.seriesMap.set(payload.key, payload.value)
		},
		CLEAR_SERIES: (state) => {
			state.seriesMap.clear()
		},
		SET_CONTRASTUID: (state, value) => {
			state.contrastUId.add(value)
		},
		GET_MOUSEEVENTS: (state) => {
			// 从 storage 获取最新的配置
			const newEvents = getConfigByStorageKey('configs-mouse');
			if (!newEvents) return;
			state.mouseEvents.mid   = newEvents.mid;
			state.mouseEvents.right = newEvents.right;
		},
		GET_TOOLREPEATUSE: (state) => {
			// 从 storage 获取最新的配置
			const status = getConfigByStorageKey('configs-toolRepeatUse');
			state.toolRepeatUse   = status;
		}, 
		GET_TOOLSYNC: (state) => {
			const status = getConfigByStorageKey('configs-toolSync');
			state.toolSync   = status;
		}, 
		GET_TOOLNEWIMAGEUSE: (state) => {
			const status = getConfigByStorageKey('configs-toolNewImageUse');
			state.toolNewImageUse   = status;
		}, 
		GET_MIPCROSSHAIRDISPLAY: (state) => {
			const status = getConfigByStorageKey('configs-mipCrosshairDisplay');
			state.mipCrosshairDisplay   = status;
		},
		GET_INIT_PARAMS: (state, payload) => {
			const { sPatientId, userId, userName, ip, deviceTypeId, token, accessNo, patientId, studyDate, sWorkStationId, imageReadOnly } = payload
			state.userId = userId
			if (userName) {
            	state.userName = decodeURI(userName)
			}
            state.ip = ip
			state.token = token

			// 报告系统患者
			state.sPatientId = sPatientId
			if (deviceTypeId == 'null') {
				state.deviceTypeId = null;
			}else {
				state.deviceTypeId = deviceTypeId
			}

			// 打印工作站 id
			state.sWorkStationId = sWorkStationId

            // 图像只读
            state.imageReadOnly = !!imageReadOnly

			// 无报告系统患者

			state.patientId = patientId
			state.accessNo = accessNo
			state.studyDate = studyDate

		},
		getRebuildSyncZoom: (state) => {
			const status = getConfigByStorageKey('configs-rebuild-sync-zoom');
			state.rebuildSyncZoom = status;
		},
		setRebuildSyncZoom: (state) => {
			state.rebuildSyncZoom = !state.rebuildSyncZoom
			localStorage.setItem('configs-rebuild-sync-zoom', state.rebuildSyncZoom);
		},
		getShowShortcutKey: (state) => {
			const status = getConfigByStorageKey('configs-show-shortcut-key');
			state.showShortcutKey = status;
		},
		setShowShortcutKey: (state) => {
			state.showShortcutKey = !state.showShortcutKey
			localStorage.setItem('configs-show-shortcut-key', state.showShortcutKey);
		},
		setUserTokenInfo: (state, payload) => {
			state.userInfo = payload
			state.token    = payload.token
			sessionStorage.setItem("userInfo", JSON.stringify(payload))
			sessionStorage.setItem("token", payload.token)
		},
		setPrintThumb: (state) => {
			state.showPrintThumb = !state.showPrintThumb
			localStorage.setItem("layout-print-thumb", state.showPrintThumb)
		},
		getPrintThumb: (state) => {
			const val = localStorage.getItem("layout-print-thumb")
			state.showPrintThumb = JSON.parse(val) || false
		},
		removeUserTokenInfo: (state) => {
			state.userInfo = ''
			state.token    = ''
			sessionStorage.removeItem("userInfo")
		},
		setTagItems: (state, payload) => {
			state.modalityTagItems = payload;
		},
		setSynchronization: (state, value) => {
			if (typeof value === 'object') {
				state.synchronization = value;
			}else {
				state.synchronization[value] = !state.synchronization[value];
			}
		},
		addRequestList: (state, payload) => {
			let params = {}
			let apiName = 'getStudy'
			let key = ''
			// 报告系统患者
            if (payload?.sId) {
                params = {
					iType: 0,
					sPatientId: payload.sId
				}
				key = payload.sId
            }else if (state.sPatientId) {
				params = {
					iType: 0,
					sPatientId: state.sPatientId
				}
				key = params.sPatientId
			}else {
				// 没有写报告的患者
				params = {
					iType: 0,
					patientId: state.patientId,
					accessNo: state.accessNo,
					studyDate: state.studyDate
				}
				apiName = 'getNoCaseStudy'
				key = params.patientId + params.accessNo + params.studyDate
			}
			// 没有这个请求信息
			if (!state.requestList.find(item => item.key === key)) {
				state.requestList.push({
					key,
					params,
					apiName,
					toolLoad: false, // true 为工具已加载
				})
			}
		},
		setReferenceLines: (state, payload) => {
			if (!cornerstoneTools.store.state.hideReferenceLines) {
				cornerstoneTools.store.state.hideReferenceLines = {}
			}
			// 隐藏状态 true
			cornerstoneTools.store.state.hideReferenceLines[payload.tabId] = payload.hide
			state.hideReferenceLines[payload.tabId] = payload.hide
		},
		setReadDefaultLayout: (state, payload) => {
			state.readDefaultLayout = payload
		},
		setRemark: (state, payload) => {
			state.isRemark = payload
		},
		setReadShowRemark: (state, payload) => {
			state.isReadShowRemark = payload
		},
		setShowRebuild: (state, payload) => {
			state.showRebuild = payload
		},
		setLayoutFill: (state, payload) => {
			state.layoutFill = payload
		},
		setAutoRebuilds: (state, payload) => {
			state.autoRebuilds = payload
		},
		setExcludeDescriptions: (state, payload) => {
			state.excludeDescriptions = payload
		},
		setSchemeRebuilds: (state, payload) => {
			state.schemeRebuilds = payload
		},
		setSystemSetList: (state, payload) => {
			if (Array.isArray(payload)) {
				state.systemSetList = payload
			}
		},
		setShortcutVisible: (state, payload) => {
			state.shortcutVisible = payload
		},

		
		addScreenshotContrast: (state, payload) => {
			const { callBack, data } = payload
			const findObj = state.screenshotContrast.find(item => {
				if (item.imageId === data.imageId) {
					return item
				}
			})
			if (!findObj) {
				state.screenshotContrast.push(data)
			}
			callBack(!findObj)
		},
		clearScreenshotContrast: (state) => {
			state.screenshotContrast = []
		},
	},
	actions: {
		// 获取匹配的方案
		getMatchScheme({ state }, payload) {
			return new Promise(resolve => {
				const mergeUid = `${payload.ct || ''}${payload.pet || ''}`;
				let scheme = 0;
				state.autoRebuilds.find(study => {
					if (study.instanceList) {
						return study.instanceList.find(series => {
							if (series.mergeUid === mergeUid) {
								scheme = series.scheme
								return series
							}
						})
					}
				})
				// 有方案，获取方案
				if (scheme) {
					const schemeObj = state.schemeRebuilds.find(item => item.id == scheme)
	
					if (schemeObj) {
						resolve(schemeObj)
					}
				}
				resolve({})
			})
		},
		addView({
			commit
		}, view) {
			commit('ADD_VIEW', view)
		},
		delView({
			commit,
			state
		}, view) {
			return new Promise(resolve => {
				commit('DEL_VIEW', view)
				resolve({
					visitedViews: [...state.visitedViews]
				})
			})
		},
		// 设置心跳
		setHeartbeat({state}) {
			if (!sessionStorage.getItem('token') || sessionStorage.getItem('token') == 'undefined') return
			clearInterval(state.heartbeat.handle)
			state.heartbeat.handle = setInterval(() => {
				Api.loginHeartbeat({
					sCookie: sessionStorage.getItem('token')
				})
			}, state.heartbeat.delay);
		},
		loadPrintscreen({ state }, payload) {
			let apiName = 'findAllShotInstance';
			let params = {};

			if (payload.apiName === 'getNoCaseStudy') {
				apiName = 'findAllShotInstanceWithoutCase';
				params = payload.params;
			}else {
				params = {
					sPatientInfoId: payload.params.sPatientId
				}
			}
			return new Promise(resolve => {
				Api[apiName](params).then(res => {
					if (res.success) {
						res.data.forEach(serie => {
							serie.imageIds = [
								`${window.configs.protocol + '//'}${state.seriesInfo.hostIpPort}/image/web/view/getPngFile?sSOPInstanceUID=${serie.sSOPInstanceUID}&iStudyDate=${serie.iStudyDate}`
							];
							serie.checkbox  = false;
							serie.isCaptrue = true;
							serie.uid = serie.sSOPInstanceUID;
							const series = state.seriesMap.get(serie.sStudyInstanceUID);
							if (series) {
								serie.sPatientID = series.seriesInfo.sPatientID;
								serie.sAccessionNumber = series.seriesInfo.sAccessionNumber;
								const instance = series.instanceList.get(serie.sSeriesInstanceUID);
								if (instance) {
									serie.imageType = instance.imageType;
									serie.sSeriesDescription = instance.sSeriesDescription;
								}
							}

						})
						resolve(res.data);
					}
					resolve();
				}).catch(err => {
					console.log(err)
					resolve()
				})
			})
		},
		// 加载序列
		loadStudy({ commit, state }) {
			const requestList = state.requestList

			return new Promise((resolve, reject) => {
				Promise.all(requestList.map(request => {
					return Api[request.apiName](request.params)
				})).then(allResponse => {
					// 清空序列
					commit('CLEAR_SERIES')	
                    const patientName = new Set();
					allResponse.forEach((res, index) => {
						if (res.success) {
							// 一次检查有多个 study。study 有多个 series
							let checkModality = []
							
							res.data.forEach(_ => {
								// study 
								// 患者基本信息
								let host = '';
								if (_.hostIp !== undefined && _.hostIp !== null && _.hostIp !== '') {
									host = _.hostIp + ':' + _.hostPort;
								}else {
									host = window.location.host;
								}
								// 不是使用服务器ip下载
								if (window.configs.useApiDownloadIp === false) {
									host = window.location.host
								}
								if (process.env.NODE_ENV === 'development') {
									host = '*************:19102'
								}
								const seriesInfo = {
									iStudyDate: _.iStudyDate,
									sAccessionNumber: _.sAccessionNumber,
									sPatientAge: _.sPatientAge,
									sPatientID: _.sPatientID,
									sPatientSex: _.sPatientSex,
									sPatientName: _.sPatientName,
									sPatientWeight: _.sPatientWeight,
									sStudyInstanceUID: _.sStudyInstanceUID,
									sStudyDescription: _.sStudyDescription,
									hostIpPort: host,
									key: requestList[index].key // 当前患者激活关键值（在重建匹配、阅图中需要）（区分多开患者）
								}
                                patientName.add(seriesInfo.sPatientName);

								if (!state.seriesInfo.iStudyDate) {
									state.seriesInfo = seriesInfo;
								}else {
									const beforeDate = state.seriesInfo.iStudyDate + ''; // 上一个序列时间
									const afterDate = seriesInfo.iStudyDate + '';
									
									let yyyyB = parseInt(beforeDate.substring(0, 4), 10);
									let mmB = parseInt(beforeDate.substring(4, 6), 10);
									let ddB = parseInt(beforeDate.substring(6, 8), 10);

									let yyyyA = parseInt(afterDate.substring(0, 4), 10);
									let mmA = parseInt(afterDate.substring(4, 6), 10);
									let ddA = parseInt(afterDate.substring(6, 8), 10);

									if (Fun.isValidDate(ddA, mmA, yyyyA) && Fun.isValidDate(ddB, mmB, yyyyB)) {
										const tA = new Date(yyyyA,mmA,ddA).getTime();
										const tB = new Date(yyyyB,mmB,ddB).getTime();
										// 当前序列时间比上一个新,覆盖
										if (tA > tB) {
											state.seriesInfo = seriesInfo;
										}
									}
								}
							
								let instanceList = new Map();
								let imgUrl = '';
								// 多个序列
								_.seriesList.forEach(series => {
									if (Fun.isCaptrue(series.sSeriesDescription, (series.instanceList[0] || {}).sSOPClassUID, false, series.sImageType)) {
										// 图片
										// imgUrl = "dicomweb://" + _.hostIp + ':' + _.hostPort + '/image/web/view/getFile?sSOPInstanceUID=';
										imgUrl = window.configs.protocol + '//' + host + '/image/web/view/getPngFile?sSOPInstanceUID=';
									}else {
										imgUrl = "dicomweb://" + host + '/image/web/view/getFile?sSOPInstanceUID=';
									}
									// 序列有多个 实例(多张图像)
									if (series.instanceList.length) {
										// 单个图像

                                        let instanceListSort = series.instanceList
                                        // 开启排序
                                        if (window.configs.webSort) {
                                            // sSOPInstanceUID 排序
                                            instanceListSort = series.instanceList.sort((a, b) => {
                                                const numA = a.sSOPInstanceUID.split('.').map(Number);
                                                const numB = b.sSOPInstanceUID.split('.').map(Number);
                                                
                                                for (let i = 0; i < Math.max(numA.length, numB.length); i++) {
                                                    if (numA[i] !== numB[i]) {
                                                        return (numA[i] || 0) - (numB[i] || 0);
                                                    }
                                                }
                                                return 0;
                                            })
                                        }
                                        const stack = instanceListSort.map(image => {
                                            // isImgPatient 需要加上这个参数--后端分表
                                            return (
                                                imgUrl + image.sSOPInstanceUID + `&iStudyDate=${_.iStudyDate}`
                                                // `dicomweb:http://localhost:8802/dicom/test/${series.sSeriesInstanceUID}/${idx + 1}.dcm`
                                            )
                                        })

										instanceList.set(
											series.sSeriesInstanceUID, 
											{
												imageIds: stack,
												uid: series.sSeriesInstanceUID,
												dateTime: series.iSeriesDate,
												sModality: series.sModality,
												sSeriesDescription: series.sSeriesDescription,
												sSeriesInstanceUID: series.sSeriesInstanceUID,
												iInstanceCount: series.iInstanceCount,
												sSOPClassUID: series.instanceList[0].sSOPClassUID,
												sStudyInstanceUID: _.sStudyInstanceUID,
												seriesNumber: series.iSeriesNumber,
												imageType: series.sImageType ? series.sImageType.replace(/\\/g, '|') : '',
                                                sSOPInstanceUID: series.instanceList[0].sSOPInstanceUID,
												// 信息放在这，会卡？
												data: {
													instanceList: series.instanceList,
													sImgAccessionNumber: _.sAccessionNumber,
													sImgStudyDate: _.iStudyDate,
													sImgPatientId: _.sPatientID
												}
											}
										)

										// checkModality 第一位放 PT,NM
										if (!Fun.isCaptrue(series.sSeriesDescription, series.instanceList[0].sSOPClassUID, false, series.sImageType)) {
											checkModality[ ['PT', 'NM'].includes(series.sModality) ? 0 : 1 ] = series.sModality
										}

									}
		
								})
								
								commit('SET_SERIES', {
									key: _.sStudyInstanceUID,
									value: {
										seriesInfo,
										instanceList
									}
								})
		
							});
							// 当前检查设备类型拼接
							state.checkModality = checkModality.join('')
							
							// 工具没有过加载，加载工具
							if (!requestList[index].toolLoad) {
								requestList[index].toolLoad = true // 设置为已加载
								// 获取工具
								cornerstoneTools.imageIdStateManager.getDataAndRender(state.seriesInfo.key)
								
								Api.getKV({
									key: `image-suvParams+${state.seriesInfo.key}`
								}).then(res => {
									if (res.data) {
										localStorage.setItem('setting-suv-params', res.data);
									}
								})
								// 获取存储2d偏移值
								Api.getKV({
									key: `image-offsetMap+${state.seriesInfo.key}`
								}).then(res => {
									if (res.data) {
										const obj = JSON.parse(res.data) || {}
										if (obj.offsetRotationMap) {
											cornerstone.offsetRotationMap = Object.assign({},
												obj.offsetRotationMap,
												cornerstone.offsetRotationMap)
										}
										if (obj.translationMap) {
											cornerstone.translationMap = Object.assign({},
												obj.translationMap,
												cornerstone.translationMap)
										}
									}
								})
							}
						}
					});
                    // 名称不同，大于等于2个，就提醒
                    if (patientName.size >= 2) {

                        let str = '<p style="height: 10px;"></p>'
                        for (const item of patientName.values()) {
                            str += '<p>姓名：<strong style="color: #d48d22">' + item + '</strong></p>'
                        }
                        str += '<p style="margin: 10px 0;">如果不是对以上患者进行对比，建议关闭 图像界面 在重新打开！</p>'
                        Notification({
                            title: '同时打开多个不同名称患者',
                            dangerouslyUseHTMLString: true,
                            message: str,
                            duration: 0,
                            type: 'warning'
                        });
                    }
					resolve(state.seriesMap)
				}).catch(err => {
					reject({msg: err})
				})
			})
		},

		async saveAllConfig({state}) {
			const sId = localStorage.getItem('configs-userSetId') || ''
            const itemNow = state.systemSetList.find(item => item.sId === sId) 
            if(!itemNow) return

            const name = itemNow.sReadSetName
            const storage = await getAllStorageInJSON()
            const json = storage.json
            if(!json) return

            const params = {
                sId,
                sReadSetName: name,
                sReadSetJson: json,
                iIsEnable: 1,
                sLoginUserId: state.userId || '00000000'
            }
 
            let requestResult
            try {
                requestResult = await Api.apiSet.editOne(params) 
            } catch (error) {
                Message.error('请求出错')
                
            }
            if (requestResult) {
                if (requestResult.success) {
                    Message.success('配置保存成功！')
                }
            } else {
                Message.error('请求出错')
            }
 
		}

	},
	modules: {}
}
