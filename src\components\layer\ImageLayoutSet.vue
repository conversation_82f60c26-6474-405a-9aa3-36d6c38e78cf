<template>
    <el-dialog
        append-to-body
        title="布局编辑"
        :visible.sync="innerVisible"
        :close-on-click-modal="false"
        @close="closeDialog"
        @open="openDialog"
        width="720px"
        fullscreen
        custom-class="my-dialog">
        <div slot="title" class="c-title">
            <span class="c-info">布局编辑</span>
            <div class="plan-action">
                <el-dropdown @command="handleCommand">
                    <span class="el-dropdown-link">
                        默认布局<i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="rebuild">重建默认布局</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
                <el-dropdown @command="handleCommand">
                    <span class="el-dropdown-link">
                        默认模式<i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="rebuildModule">重建默认模式</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
        </div>
        <div class="c-main">
            <div class="c-left">
                <AreaFit>
                    <div ref="center" class="c-content" :style="containerStyle">
                        <div v-for="(item, index) in viewport" 
                        :key="index" 
                        :style="item.itemStyle && Object.keys(item.itemStyle).length ? item.itemStyle : style" 
                        class="box"
                        :class="[whiteList.includes(item.value) ? 'i-white' : '']"
                        @contextmenu.prevent="onMenuLayoutBox($event)"
                        @click.prevent.ctrl="onClickLayoutBox(item, index, 'ctrl')"
                        @click="onClickLayoutBox(item, index)">
                            <i :style="{backgroundImage: item.value != undefined ? `url(${imageList[item.value]})` : ''}"></i>
                            <div class="i-select" saveimage="true" :class="{ active: active.includes(index) }"></div>
                            <div class="c-note" saveimage="true">
                                <p>偏 移：{{ item.offset }}</p>
                                <p>序列分组：{{ options.groups[item.group].label }}</p>
                                <p>序列备注：{{ item.remark }}</p>
                                <!-- <p>行：{{ item.rowIdx }}；列：{{ item.columnIdx }}</p> -->
                            </div>
                        </div>
                    </div>
                </AreaFit>
            </div>
            <div class="c-right">
                <ul class="c-from">
                    <li class="i-item" style="height: 30px; line-height: 30px;">
                        <span>排列方式</span>
                        <el-radio v-model="containerStyle.flexDirection" label="row">行排</el-radio>
                        <el-radio v-model="containerStyle.flexDirection" label="column">列排</el-radio>
                    </li>
                    <li class="i-item">
                        <span>行　数</span>
                        <el-input-number v-model="row" :min="1" :max="10" label="" size="small" style="width: 100px"></el-input-number>
                        <el-button size="mini" class="i-button" @click="onClickCreate">新建</el-button>
                    </li>
                    <li>
                        <span>列　数</span>
                        <el-input-number v-model="column" :min="1" :max="10" label="" size="small" style="width: 100px"></el-input-number>
                    </li>
                    <li class="i-item">
                        <span>宽　度</span>
                        <el-input-number v-model="selectData.width" @change="onLayoutItem('width')" controls-position="right" :min="0" :max="100" label="" size="small" class="number-input"></el-input-number> %
                    </li>
                    <li class="i-item">
                        <span>高　度</span>
                        <el-input-number v-model="selectData.height" @change="onLayoutItem('height')" controls-position="right" :min="0" :max="100" label="" size="small" class="number-input"></el-input-number> %
                    </li>
                    <li>
                        <span>序列分组</span>
                        <el-select v-model="selectData.group" size="mini" style="width: 100px" placeholder="" @change="onEnterData('group')">
                            <el-option
                            v-for="item in options.groups"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                        <!-- <el-input v-model="selectData.group" size="mini" style="width: 100px" @keyup.enter.native="onEnterData('group')"></el-input> -->
                    </li>
                    <li>
                        <span>MIP偏移</span>
                        <el-input v-model="selectData.offset" v-floorNumber size="mini" style="width: 162px" @keyup.enter.native="onEnterData('offset')"></el-input>
                    </li>
                    <li>
                        <span>序列备注</span>
                        <el-input v-model="selectData.remark" size="mini" style="width: 162px" @keyup.enter.native="onEnterData('remark')"></el-input>
                    </li>
                </ul>
                <div class="c-box">
                    <span>冠状</span>
                    <span>矢状</span>
                    <span>横状</span>
                    <span>MIP</span>
                    <div class="c-box-content">
                        <ul>
                            <li v-for="item in layoutList" :key="item.id" @click="onClickItemToLayout(item)" :class="{'i-disable': item.isHide}">
                                <i :style="{ backgroundImage: `url(${imageList[item.value]})` }"></i>
                            </li>
                        </ul>
                        <div>
                            <span>PET/NM</span>
                            <span>CT/MR</span>
                            <span>Fused</span>
                        </div>
                    </div>
                </div>
                <div class="c-template">
                    <ul class="c-from">
                        <li>
                            <span>模板类型</span>
                            <el-select v-model="selectItem.templateType" @change="getData" size="mini" style="width: 162px" placeholder="">
                                <el-option
                                v-for="item in options.templateType"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                                </el-option>
                            </el-select>
                        </li>
                        <li>
                            <span>位置</span>
                            <el-input v-model="selectItem.position" v-floorNumber size="mini" style="width: 100px"></el-input>
                        </li>
                        <li>
                            <span>名称</span>
                            <el-input v-model="selectItem.name" size="mini" style="width: 162px"></el-input>
                        </li>
                    </ul>
                    <div class="c-action">
                        <el-button size="mini" :disabled="!selectItem.id" @click="onClickUpdate">修 改</el-button>
                        <el-button size="mini" :disabled="!selectItem.templateType" @click="onOhterSave">另 存</el-button>
                    </div>
                    <p style="padding-left: 16px; padding-bottom: 6px">当前显示：{{ selectItem.showText }}</p>
                </div>
                <div class="table-box">
                    <el-table 
                        ref="mainTable"
                        :data="tableData" 
                        size="mini" 
                        border
                        highlight-current-row 
                        :row-class-name="tableRowClassName"
                        @row-dblclick="onDbclickRow"
                        @row-contextmenu="rowContextMenu"
                        tooltip-effect="dark" style="width: 100%" height="100%">
                        <el-table-column label="显示" prop="isHide" width="50" align="center">
                            <template slot-scope="scope">
                                <i :class="[scope.row.isHide ? 'el-icon-close' : 'el-icon-check']"></i>
                            </template>
                        </el-table-column>
                        <el-table-column label="位置" width="60" align="center" prop="position"></el-table-column>
                        <el-table-column label="名　称" prop="text"></el-table-column>
                    </el-table>
                </div>
            </div>
            <v-contextmenu ref="contextmenu">
                <!-- <v-contextmenu-item @click="onClickChangeState(false)" >显 示</v-contextmenu-item>
                <v-contextmenu-item @click="onClickChangeState(true)">隐 藏</v-contextmenu-item> -->
                <v-contextmenu-item @click="onClickDel">删 除</v-contextmenu-item>
            </v-contextmenu>
            <v-contextmenu ref="addLayoutContextmenu">
                <v-contextmenu-item @click="onClickSelect('all')">全选</v-contextmenu-item>
                <v-contextmenu-item @click="onClickSelect('rowIdx')">选中同行</v-contextmenu-item>
                <v-contextmenu-item @click="onClickSelect('columnIdx')">选中同列</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickCell('add')">添加单元格</v-contextmenu-item>
                <v-contextmenu-item @click="onClickCell('del')">删除单元格</v-contextmenu-item>
            </v-contextmenu>
        </div>
        <!-- <div slot="footer" style="font-size: 12px;">
            *配置数据仅保存在本地，若需要长期保存请在“系统设置”中点击【配置方案 - 保存至服务器】 
        </div> -->

        <defaultLayout v-model="defaultLayoutVisible"></defaultLayout>
        <defaultLayoutModule v-model="defaultLayoutModuleVisible"></defaultLayoutModule>
    </el-dialog>
</template>
<script>
import html2canvas from "html2canvas"
import AreaFit from '$src/layout/AreaFit.vue';
import ModeDialog from "$src/mixin/ModeDialog.js";
import { defaultLayout, defaultLayoutModule } from '$components/layer/components';


import { dbLayoutSet } from '$library/db';
export default {
    mixins: [ ModeDialog ],
    components: {
        AreaFit,
        defaultLayout,
        defaultLayoutModule
    },
    props: {
    },
    data() {
        return {
            defaultLayoutVisible: false,
            defaultLayoutModuleVisible: false,
            row: 1,        // 布局行
            column: 1,     // 布局列
            num: null,     // 布局总数
            tableData: [], // 表格数据
            lastActive: -1, // 最后选中
            active: [],    // 选中序列位置
            keyCtrl: false, // 
            selectData: {  // 选中序列
                group: 0,
                remark: '',
                row: 1,
                column: 1,
                offset: null,
                width: 0,
                height: 0,
                columnIdx: 0,
                rowIdx: 0
            },
            selectItem: {  // 选中table布局项目
                templateType: 1,
                position: '',
                name: '',
                id: '',
                isHide: false,
                showText: '',
            },
            rightMenu: {   // 右键删除 table 中的布局
                id: '',
                index: ''
            },
            options: {
                templateType: [
                    { value: 1, label: '重建布局' },
                    { value: 2, label: '重建对比' },
                    { value: 3, label: '拖拽重建' }
                ],
                groups: [
                    {value: 0, label: '序列1'},
                    {value: 1, label: '序列2'},
                    {value: 2, label: '序列3'},
                    {value: 3, label: '序列4'},
                    {value: 4, label: '序列5'},
                    {value: 5, label: '序列6'},
                    {value: 6, label: '序列7'},
                    {value: 7, label: '序列8'},
                    {value: 8, label: '序列9'},
                ]
            },
            // CT/MR、PT/NM TODO 注意不要改这些存储位置
            // 0、1冠
            // 2、3矢
            // 4、5横
            // 6、7mip
            // 8 冠融合  1,0
            // 9 矢融合  3,2
            // 10 横融合 5,4
            // 11 mip融合 6 7
            imageList: [
                // 不要改位置
                require(`$assets/images/layout/5.png`),
                require(`$assets/images/layout/1.png`),
                require(`$assets/images/layout/6.png`),
                require(`$assets/images/layout/2.png`),
                require(`$assets/images/layout/7.png`),
                require(`$assets/images/layout/3.png`),
                require(`$assets/images/layout/8.png`),
                require(`$assets/images/layout/4.png`),
                require(`$assets/images/layout/9.png`),
                require(`$assets/images/layout/10.png`),
                require(`$assets/images/layout/11.png`),
                require(`$assets/images/layout/12.png`),
            ],
            whiteList: [1,3,5,7],
            layoutList: [
                {
                    id: 1, value: 1, angle: 'y', type: 'two'
                },
                {
                    id: 2, value: 3, angle: 'x', type: 'two'
                },
                {
                    id: 3,  value: 5, angle: 'z', type: 'two'
                },
                {
                    id: 4, value: 7, angle: 'y', type: 'mipTwo'
                },
                {
                    id: 5, value: 0, angle: 'y', type: 'one'
                },
                {
                    id: 6, value: 2, angle: 'x', type: 'one'
                },
                {
                    id: 7, value: 4, angle: 'z', type: 'one'
                },
                {
                    id: 8, value: 6, angle: 'z', type: 'mipOne'
                },
                {
                    id: 9, value: 8, angle: 'y', type: 'fuse'
                },
                {
                    id: 10,  value: 9, angle: 'x', type: 'fuse'
                },
                {
                    id: 11, value: 10, angle: 'z', type: 'fuse'
                },
                {
                    id: 12, value: 11, angle: 'y', type: 'mipFuse'
                }
            ],
            viewport: [],
            style: {
                width: '100%',
                height: '100%'
            },
            containerStyle: {
                flexDirection: 'row'
            }
        }
    },
    computed: {
        styleWidth() {
            const columns = this.viewport.map(item =>  item.columnIdx );
            const len = new Set(columns).size;
            return 100 / len + '%';
        }
    },
    methods: {
        handleCommand(command) {
            if (command === 'rebuild') {
                this.defaultLayoutVisible = true
            }else if (command === 'rebuildModule') {
                this.defaultLayoutModuleVisible = true
            }
        },
        // 点击其它保存
        onOhterSave() {
            this.saveData(
                (item) => {
                dbLayoutSet.then(e => {
                    e.add(item).then(() => {
                        this.$store.dispatch('saveAllConfig')
                        this.getData(1);
                    })
                });
            })
        },
        onClickUpdate() {
            this.saveData((item) => {
                dbLayoutSet.then(e => {
                    e.update(item).then(() => {
                        this.$store.dispatch('saveAllConfig')
                        this.getData(2);
                    })
                });
            }, 'update');
        },
         saveData(callBack, type) {
            // 重建布局
            let selectValue = [];
            if (this.selectItem.templateType === 1) {
                selectValue = this.viewport.map(item => {
                    return item.value;
                });

            }

            const item = {
                templateType: this.selectItem.templateType,
                text: this.selectItem.name,
                position: this.selectItem.position,
                isHide: this.selectItem.isHide,
                showLayout: this.viewport,
                options: {
                    layout: { row: this.selectData.row, column: this.selectData.column },
                    selectValue
                }
            }

            let isSet = false // 是否设置非田字格，布局
            const itemStyle = this.viewport.map(item => {
                // 设置过宽、高
                if (item.width || item.height) {
                    isSet = true
                }
                // 获取单元格样式
                if (toString.call(item.itemStyle).slice(8, -1) === 'Object' && Object.keys(item.itemStyle).length) {
                    return item.itemStyle
                }
                return this.style
            })

            // 宽高与现在数量不同，改过
            if (this.selectData.row * this.selectData.column != selectValue.length) {
                isSet = true
                item.options.layout.num = selectValue.length
            }
            item.options.containerStyle = this.containerStyle
            // 设置过，添加样式
            if (isSet) {
                item.options.itemStyle = itemStyle
            }

            if (type === 'update' && Number(this.selectItem.id) >= 0) {
                item.id = this.selectItem.id;
            }
            // 如果没有位置，设置位置值
            if (this.selectItem.position === '' || this.selectItem.position === undefined) {
                const len = this.tableData.length;
                if (len > 0) {
                   item.position = Number(this.tableData[len - 1].position) + 1;
                }else {
                    item.position = 1;
                }
            }

            html2canvas(this.$refs.center,
            {
                ignoreElements: (element) => {
                    if (element.getAttribute('saveimage')) {
                        return true;
                    };
                }
            }).then(canvas => {
                // const dataURL = canvas.toDataURL("image/png", 1);

                let extra_canvas = document.createElement("canvas");
                extra_canvas.setAttribute('width',48);
                extra_canvas.setAttribute('height',48);
                let ctx = extra_canvas.getContext('2d');

                ctx.strokeStyle = 'black';
                ctx.lineJoin = 'round';
                ctx.lineWidth = 1;
                ctx.fillRect(4, 4, 40, 40);

                ctx.drawImage(canvas,0,0,canvas.width, canvas.height,5, 5, 38, 38);
                let dataURL = extra_canvas.toDataURL("image/png", 1);
                item.img = dataURL;
                callBack && callBack(item);
            });
        },
        /**
         * isSelect 0 不选择。 1 选择最后一个。 2，选择之前选择的
         */
        getData(isSelect) {

            dbLayoutSet.then(e => {
                e.getGroup(this.selectItem.templateType).then(e => {
                    if (e.success) {
                        this.tableData = e.data.sort((a, b) => {
                            return a.position - b.position;
                        });
                        let idx = 0

                        if (isSelect == 1) {
                            idx = this.tableData.length - 1
                        }else if (isSelect == 2) {
                            const findIndex = this.tableData.findIndex(item => item.id == this.selectItem.id)
                            if (findIndex != -1) {
                                idx = findIndex
                            }
                        }

                        if (isSelect) {
                            const row = this.tableData[idx];
                            if (row) {
                                this.$refs.mainTable.setCurrentRow(row);
                                this.selectItem.id = row.id;
                                this.selectItem.showText = row.text;
                            }

                        }
                        return;
                    }
                })
            });
            // setTimeout(() => {
            //     dbLayoutSet.getGroup(this.selectItem.templateType, e => {
            //         if (e.success) {
            //             this.tableData = e.data.sort((a, b) => {
            //                 return a.position - b.position;
            //             });
            //             if (isSelect === true) {
            //                 const row = this.tableData[this.tableData.length - 1];
            //                 this.$refs.mainTable.setCurrentRow(row);
            //                 this.selectItem.id = row.id;
            //                 this.selectItem.showText = row.text;
            //             }
            //             return;
            //         }
            //     })
            // }, 100);
        },
        // 点击创建布局
        onClickCreate(clearId) {
            
            let width = Number(this.column);
            let height= Number(this.row);
            this.num = 0

 			this.style.width = 100 / width + '%';
        	this.style.height = 100 / height + '%';

            this.selectData.column = width;
            this.selectData.row    = height;

            this.viewport = [];

            const num = !this.num ? width * height : this.num;
            for (let index = 0; index < num; index++) {
                const idx = index+1
                const rowIdx = (idx % height) || height
                const columnIdx    = Math.ceil(idx / height)
                this.viewport.push({
                    id: this.$fun.onlyValue(),
                    value: '',
                    group: 0,
                    remark: '',
                    offset: null,
                    width: 0,
                    height: 0,
                    columnIdx,
                    rowIdx,
                    itemStyle: {}
                });
            };
            this.active = [];
            // if (clearId !== true) {
            //     this.selectItem.id = '';
            // }

        },
        // 回车 序列分组、备注
        onEnterData(key) {
            this.active.forEach(item => {
                this.$set(this.viewport[item], key, this.selectData[key]);
            });
        },
        // 点击添加，删除单元格
        onClickCell(action) {
            const type = this.containerStyle.flexDirection === 'column' ? 'height' : 'width'

            const selectViewport = this.active.map(i => {
                return {
                    rowIdx: this.viewport[i].rowIdx,
                    columnIdx: this.viewport[i].columnIdx
                }
            })
            if (action === 'add') {

                for(let i = this.viewport.length - 1; i >= 0; i--) {

                    if (this.active.includes(i)) {
                        const item = this.$fun.deepClone(this.viewport[i])
                        item.itemStyle = {}
                        item.width = 0
                        item.height = 0
                        this.viewport = [...this.viewport.slice(0, i+1), item, ...this.viewport.slice(i+1)]

                        this.computeLayoutRowColumn(item.columnIdx)
                        // 添加单元格
                        this.computeLayoutSize(type, 0, i)
                        this.computeLayoutSize('width', 0, i)

                        // 还原选中
                        this.active = [];

                        selectViewport.forEach(({ rowIdx, columnIdx }) => {
                            const idx = this.viewport.findIndex(item => {
                                if (item.rowIdx === rowIdx && item.columnIdx === columnIdx) {
                                    return true;
                                }
                            })
                            if (idx != -1) {
                                this.active.push(idx);
                            }
                        });
                    }
                }

            }else {
                for(let i = this.viewport.length - 1; i >= 0; i--) {

                    if (this.active.includes(i)) {
                        
                        const columnIdx = this.viewport[i].columnIdx;
                        this.viewport.splice(i, 1);

                        // 重新更新 行，列
                        this.computeLayoutRowColumn(columnIdx);
                        this.computeLayoutSize(type, 0, i);
                        this.computeLayoutSize('width', 0, i);
                    }
                }
                this.active = [];
            }
        },
        onClickSelect(type) {
            if (type === 'all') {
                this.active = this.viewport.map((item, index) => index);
                return;
            }

            if (this.lastActive != -1) {
                const setViewport = this.viewport[this.lastActive];

                this.viewport.forEach((item, index) => {
                    if (item[type] === setViewport[type] && !this.active.includes(index)) {
                        this.active.push(index)
                    }
                })
            }

        },
        // 点击改变宽高
        onLayoutItem(key) {
            this.active.forEach(item => {
                let val = this.selectData[key]
                
                // 没有值，默认 0
                if (val === undefined) {
                    val = 0
                    this.selectData[key] = val
                }
                // 设置到视图中
                this.$set(this.viewport[item], key, val);

                this.computeLayoutSize(key, val, item)
            });
        },
        computeLayoutRowColumn(columnIdx) {
            
            let currentRow = 1;
            // 更新 rowId
            this.viewport.forEach(item => {
                if (item.columnIdx === columnIdx) {
                    item.rowIdx = currentRow;
                    ++currentRow;
                }
            });

            let i = 1;
            let currentColumnIdx = this.viewport[0].columnIdx;
            this.viewport.forEach(r => {
                if (r.columnIdx == currentColumnIdx) {
                    r.columnIdx = i;
                } else {
                    currentColumnIdx = r.columnIdx;
                    r.columnIdx = ++i;
                }
            })
        },
        /**
         * 计算布局宽高
         * type width | height
         * val  设置的值
         * 
        **/
        computeLayoutSize(type, val, index) {

            let idx = index;
            if (!this.viewport[idx]) {
                idx = index - 1;
            }
            // 获取点击视图项数据
            const { rowIdx, columnIdx, id } = this.viewport[idx]
            // 列排序下，的宽高改变
            if (this.containerStyle.flexDirection === 'column') {
                // 改变高度
                if (type === 'height') {
                    // 获取相同列
                    const otherItem = this.viewport.filter(item => {
                        return item.columnIdx === columnIdx && (item.id != id || val === 0)
                    })

                    let countHeigth = 0  // 人为设置总高度
                    
                    otherItem.forEach(item => {
                        if (item.height) {
                            // 有设置高度，累加人为设置总高度
                            countHeigth += item.height
                        }
                    })
                    // 设置当前选中的高度
                    if (val) {
                        let itemStyle = this.viewport[idx].itemStyle
                        this.viewport[idx].itemStyle = {
                            height: val ? val + '%' : this.style.height,
                            width: itemStyle.width ? itemStyle.width : this.style.width
                        }
                    }
                    // 累加当前选中高度
                    countHeigth += val

                    // 剩余高度
                    const residueVal = 100 - parseFloat(countHeigth)
                    // 未设置高度单元格
                    const residueItems = otherItem.filter(item => {
                        if (!item.height) {
                            return item
                        }
                    });
                    // 提醒
                    if (residueVal < 0) {
                        this.$message.info('提醒：设置的列单元格宽度总量超过100%')
                    }
                    // 有未设置单元格项
                    if (residueItems.length) {
                        // 剩余的平均高度
                        const itemVal = residueVal / residueItems.length
                        // 赋值高度样式
                        residueItems.forEach(item => {
                            item.itemStyle.height = itemVal + '%'
                            item.itemStyle.width  = item.itemStyle.width ? item.itemStyle.width : this.style.width
                        })
                    }
                }else {
                    // 当前选中的列
                    const columnItem = this.viewport.filter(item => {
                        return item.columnIdx === columnIdx
                    })
                    // 设置当前选中的列为相同宽度
                    columnItem.forEach(item => {
                        item.width = val
                        item.itemStyle = {
                            height: item.itemStyle.height ? item.itemStyle.height : this.style.height,
                            width: val ? val + '%' : this.style.width
                        }
                    })
                    // 其它列，如果选中的列为0，也做其它列计算
                    const rowItem = this.viewport.filter(item => {
                        return item.rowIdx === rowIdx && (item.id != id || val === 0)
                    })
                    let countWidth = 0       // 人为设置的宽
                    const columnObj = {}     // 其它列是否有设置宽度
                    rowItem.forEach(item => {
                        if (item.width) {
                            // 有设置宽度，累加
                            countWidth += item.width
                        }else {
                            columnObj['column' + item.columnIdx] = true
                        }
                    })
                    // 累加当前选中列
                    countWidth += val
                    // 剩余可用宽度
                    const residueVal = 100 - parseFloat(countWidth)

                    // 提醒
                    if (residueVal < 0) {
                        this.$message.info('提醒：设置的行单元格宽度总量超过100%')
                    }
                    // 是否有需要设置的列宽
                    const otherItemLen = Object.keys(columnObj).length
                    if (otherItemLen) {
                        // 均匀宽度
                        const itemVal = residueVal / otherItemLen
                        // 赋值列的均匀宽度
                        this.viewport.forEach(item => {
                            if (columnObj['column' + item.columnIdx]) {
                                item.itemStyle.width  = itemVal + '%'
                                item.itemStyle.height = item.itemStyle.height ? item.itemStyle.height : this.style.height
                            }
                        })
                    }

                }    
            }
            // TODO 这可以判断行的
        },
        // 点击布局中的块
        onClickLayoutBox(item, index, keydown) {

            this.lastActive = index;
            if (keydown) {
                this.keyCtrl = true;
                setTimeout(() => {
                    this.keyCtrl = false;
                }, 200);
            }
            if (!keydown && this.keyCtrl) {
                return;
            }

            if (!keydown) {
                this.active = [index];
            }else if (!this.active.includes(index)) {
                this.active.push(index);
            }
            this.selectData.id     = item.id;
            
            this.selectData.group  = item.group;
            this.selectData.remark = item.remark;
            this.selectData.offset = item.offset;

            this.selectData.width = item.width;
            this.selectData.height = item.height;

            this.selectData.rowIdx = item.rowIdx;
            this.selectData.columnIdx = item.columnIdx;
        },
        onMenuLayoutBox(event) {
            if (event) {
                // const postition = {
                //     top: event.clientY,
                //     left: event.clientX
                // }
                // this.$refs.addLayoutContextmenu.show(postition)
                this.$refs.addLayoutContextmenu.handleReferenceContextmenu(event);
            }
        },
        // 点击显示到布局中关联
        onClickItemToLayout(item) {
            if (item.isHide) {
                return;
            }
            this.active.forEach(val => {
                this.viewport[val].value = item.value;
                this.viewport[val].angle = item.angle;
                this.viewport[val].type  = item.type;
                this.viewport[val].offset = 0
            });
        },
        // 点击行
        onDbclickRow(row) {
            this.selectItem.showText = row.text;
            // 设置选中项
            this.selectItem.name = row.text;
            this.selectItem.id   = row.id;
            this.selectItem.position = row.position;
            this.selectItem.isHide = row.isHide;
            // 设置选择显示行列
            this.row = row.options.layout.row;
            this.column = row.options.layout.column;

            this.num = row.options.layout.num;

            if (row.options.containerStyle) {
                this.containerStyle = row.options.containerStyle
            }

            this.onClickCreate(true);

            // 显示视图序列
            this.viewport = row.showLayout;
        },
        // 表格右键删除，布局配置
        onClickDel() {
            dbLayoutSet.then(e => {
                e.del(this.rightMenu.id).then(e => {
                    if (e.success) {
                        // 当前视图是选择的
                        if (this.selectItem.id === this.rightMenu.id) {
                            this.selectItem.id = '';
                            this.selectItem.showText = '';
                        };
                        this.tableData.splice(this.rightMenu.index, 1); 
                        this.$store.dispatch('saveAllConfig')
                        return;
                    }
                })
            });

            // dbLayoutSet.del(this.rightMenu.id, e => {
            //     if (e.success) {
            //         // 当前视图是选择的
            //         if (this.selectItem.id === this.rightMenu.id) {
            //             this.selectItem.id = '';
            //             this.selectItem.showText = '';
            //         };
            //         this.tableData.splice(this.rightMenu.index, 1); 
            //         return;
            //     }
            // })
        },
        // 点击改变状态
        // onClickChangeState(state) {
        //     // 已经隐藏又点隐藏 || 已经显示又点显示
        //     if (this.selectItem.isHide && state === true || !this.selectItem.isHide && state === false) {
        //         return;
        //     }
        //     this.selectItem.isHide = state;
        //     this.onClickUpdate();
        // },
        /* 把每一行的索引放进row */
        tableRowClassName({row, rowIndex}) {
            row.rowIndex = rowIndex;
        },
        rowContextMenu(row, column, event) {
            this.rightMenu.id = row.id;
            this.rightMenu.index = row.rowIndex;
			event.preventDefault();
			this.contextmenuShow(event);
        },
        contextmenuShow(e) {
			this.$refs.contextmenu.handleReferenceContextmenu(e);
        },
        closeDialog() {
            this.$store.state.triggerLayoutSet = false;
        },
        openDialog() {
            this.$store.state.triggerLayoutSet = true;
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent);
            })
        }
    },
    mounted() {
        this.getData();
    },
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog.is-fullscreen {
    width: 80%;
    height: 93%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    overflow: auto;
    .el-dialog__header{
        border-bottom: none;
    }
    .el-dialog__body {
        height: calc(100% - 75px);
        padding: 0px !important;
    }
}
.c-main{
    width: 100%;
    height: 100%;
    display: flex;
    .c-left{
        display: flex;
        flex: 1;
        overflow: hidden;
    }
    .c-right{
        width: 270px;
        background: #fbfbfb;
        padding: 0px 10px;
        border: 1px solid #eee;
        border-right: none;
        display: flex;
        flex-direction: column;
    }
}
.c-content{
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    width: 0px;
    position: relative;
    background-color: black;
    overflow: hidden;
}
.c-from{
    position: relative;
    padding: 6px 0px 6px 6px;
    li {
        padding-bottom: 6px;
        &:last-child{
            padding-bottom: 0px;
        }
        &.i-item{
            .i-button{
                position: absolute;
                right: 0px;
                top: 50px;
            }
            .number-input{
                width: 100px;
                ::v-deep input {
                    text-align: left;
                    text-indent: 10px;
                }
            }
        }
        > span{
            display: inline-block;
            width: 80px;
            padding-left: 6px;
        }
    }
}
.c-box{
    border: 1px solid #E4E7ED;
    > span{
        display: inline-block;
        width: 44px;
        height: 30px;
        line-height: 30px;
        text-align: center;
    }
    .c-box-content{
        display: flex;
        padding-left: 2px;
        > ul {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            height: 140px;
            justify-content: space-between;
            li{
                width: 44px;
                height: 44px;
                cursor: pointer;
                border: 2px solid #c0c4cc;
                border-radius: 2px;
                padding: 2px;
                &.i-disable{
                    opacity: 0.5;
                    cursor: no-drop;
                }
                i{
                    display: inline-block;
                    width: 100%;
                    height: 100%;
                    background-size: cover;
                }
                &:hover{
                    box-shadow: 1px 1px 2px 0px #c0c0c0;
                    border-color: #409eff;
                }
            }
        }
        > div {
            width: 62px;
            text-align: center;
            span{
                font-size: 13px;
                height: 46px;
                line-height: 46px;
                display: inline-block;
            }
        }
    }
}
.c-action{
    display: flex;
    justify-content: space-between;
    padding: 4px 0px 10px 12px;
}
.table-box{
    flex: 1;
    overflow: hidden;
}
.box{
    position: relative;
    border: 1px solid #008000;
    &.i-white{
        background: white;
        .c-note{
            color: black;
        }
    }
    .i-select{
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0px;
        &.active{
            border: 2px solid #ff4949;
        }
    }
    .c-note{
        position: absolute;
        top: 4px;
        left: 4px;
        color: white;
        p{
            padding-bottom: 4px;
            font-size: 13px;
        }
    }
    i {
        display: inline-block;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: contain;
    }
}
.c-title{
    padding-left: 15px;
    .c-info{
        font-size: 14px;
        font-weight: 700;
        padding-right: 30px;
        color: #2384d3;
        float: left;
    }
    .plan-action{
        float: left;
    }
}
.el-dropdown-link{
    cursor: pointer;
    margin-right: 20px;
    &:hover{
        color: #409eff;
    }
}
</style>