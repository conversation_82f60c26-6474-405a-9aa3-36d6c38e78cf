import Api from '$api'
import getConfigByStorageKey from '$library/utils/configStorage.js'
import Fun from '$library/utils/function'
export default {
	state: {
		visitedViews: [],
		seriesMap: new Map(),
		sPatientId: null,
		deviceTypeId: null,
		userId: null,
		token: null,
		seriesInfo: {},
		hideTools: false,
		disabledTool: false,
		// 鼠标事件
		mouseEvents: {
			mid: 'Pan',
			right: 'Zoom'
		},
		// 工具连续使用
		toolRepeatUse: false,
		toolSync: false,
		openSlectSeries: {
			dialogVisible: false,
			limitType: 1,       // 阅图，重建, 2 3D MPR
			title: '患者序列'
		},
		renderScroll: true,  // 用于渲染滚动条高度

        authCode: null,
        userInfo: {},
		contrastUId: new Set(),
		listenTool: {
			value: false,
			seriesId: null
		},
		listenCapture: {},
		modalityTagItems: {},
		triggerLayoutSet: null,
		synchronization: {
			start: false,
			position: false,
			zoomPan: false,
			window: false
		},
		readDefaultLayout: '',
		checkModality: '',      // 当前最后打开检查设备
		isRemark: false,        // 是否截图备注
		isReadShowRemark: true, // 阅图是否在图像上显示备注
		showRebuild: {},
		excludeDescriptions: [], // 排除的序列描述
		defaultColormap: 'hot',
		layoutFill: false,
	},
	mutations: {
		SET_OPENSLECTSERIES: (state, payload) => {
			const { dialogVisible, limitType } = payload
			state.openSlectSeries.dialogVisible = dialogVisible
			state.openSlectSeries.limitType = limitType || 1
		},
		SET_HIDETOOLS: (state, value) => {
			state.hideTools = value
		},
		ADD_VIEW: (state, view) => {
			if (state.visitedViews.some(v => v.path === view.path)) return;
			const obj = Object.assign({}, view, {
				title: view.meta.title || 'no-name'
			})
			obj.matched = [] // 奇怪，不清空会堆栈

			//   if (['ReadDcm', 'ViewSlice'].includes(view.name)){
			//     obj.affix = true
			//   }
			if (obj.name === 'Select') return;
			state.visitedViews.push(obj)
		},
		DEL_VIEW: (state, view) => {
			for (const [i, v] of state.visitedViews.entries()) {
				if (v.path === view.path) {
					state.visitedViews.splice(i, 1)
					break
				}
			}
		},
		SET_SERIES: (state, payload) => {
			state.seriesMap.set(payload.key, payload.value)
		},
		CLEAR_SERIES: (state) => {
			state.seriesMap.clear()
		},
		SET_CONTRASTUID: (state, value) => {
			state.contrastUId.add(value)
		},
		GET_MOUSEEVENTS: (state) => {
			// 从 storage 获取最新的配置
			const newEvents = getConfigByStorageKey('configs-mouse');
			if (!newEvents) return;
			state.mouseEvents.mid   = newEvents.mid;
			state.mouseEvents.right = newEvents.right;
		},
		GET_TOOLREPEATUSE: (state) => {
			// 从 storage 获取最新的配置
			const status = getConfigByStorageKey('configs-toolRepeatUse');
			state.toolRepeatUse   = status;
		}, 
		GET_TOOLSYNC: (state) => {
			const status = getConfigByStorageKey('configs-toolSync');
			state.toolSync   = status;
		}, 
		GET_INIT_PARAMS: (state, payload) => {
			if (!payload) {
				return;
			}
			const { sPatientId, userId, deviceTypeId, token, authCode } = payload
			if (sPatientId) state.sPatientId = sPatientId
			if (userId) state.userId = userId
			if (deviceTypeId) state.deviceTypeId = deviceTypeId
			if (token) state.token = token
            if (authCode) state.authCode = authCode
		},
		setUserTokenInfo: (state, payload) => {
			state.userInfo = payload
			state.token    = payload.token
			sessionStorage.setItem("authCode", state.authCode)
			sessionStorage.setItem("userInfo", JSON.stringify(payload))
			sessionStorage.setItem("token", payload.token)
		},
		removeUserTokenInfo: (state) => {
			state.userInfo = ''
			state.token    = ''
			sessionStorage.removeItem("userInfo")
		},
		setTagItems: (state, payload) => {
			state.modalityTagItems = payload;
		},
		setSynchronization: (state, value) => {
			if (typeof value === 'object') {
				state.synchronization = value;
			}else {
				state.synchronization[value] = !state.synchronization[value];
			}
		},
		setReferenceLines: (state, payload) => {
			if (!cornerstoneTools.store.state.hideReferenceLines) {
				cornerstoneTools.store.state.hideReferenceLines = {}
			}
			// 隐藏状态 true
			cornerstoneTools.store.state.hideReferenceLines[payload.tabId] = payload.hide
		},
		setReadDefaultLayout: (state, payload) => {
			state.readDefaultLayout = payload
		},
		setRemark: (state, payload) => {
			state.isRemark = payload
		},
		setReadShowRemark: (state, payload) => {
			state.isReadShowRemark = payload
		},
		setShowRebuild: (state, payload) => {
			state.showRebuild = payload
		},
		setLayoutFill: (state, payload) => {
			state.layoutFill = payload
		},
		setExcludeDescriptions: (state, payload) => {
			state.excludeDescriptions = payload
		}
	},
	actions: {
		addView({
			commit
		}, view) {
			commit('ADD_VIEW', view)
		},
		delView({
			commit,
			state
		}, view) {
			return new Promise(resolve => {
				commit('DEL_VIEW', view)
				resolve({
					visitedViews: [...state.visitedViews]
				})
			})
		},
		// 加载序列
		loadStudy({ commit, state }) {
			let params = {
				type: 0,
				caseId: state.userInfo.caseId
			}
			let imgUrl = "dicomweb://" + window.configs.imageServe + '/cdcloud/image/web/view/getFile';
			return new Promise((resolve, reject) => {
				Api.getStudy(params).then(res => {
					if (res.success) {
						// 清空序列
						commit('CLEAR_SERIES')
						// 一次检查有多个 study。study 有多个 series
						let checkModality = []
						// 检查
						res.data.forEach(_ => {
							// 患者基本信息
							const seriesInfo = {
								iStudyDate: _.iStudyDate,
								sAccessionNumber: _.sAccessionNumber,
								sPatientAge: _.sPatientAge,
								sPatientID: _.sPatientID,
								sPatientSex: _.sPatientSex,
								sPatientName: _.sPatientName,
								sPatientWeight: _.sPatientWeight,
								sStudyInstanceUID: _.sStudyInstanceUID,
								sStudyDescription: _.sStudyDescription
							}
							state.seriesInfo = seriesInfo
							let instanceList = new Map()
							// 序列
							_.seriesList.map(series => {
								// 单个图像
								let stack = []
								series.instanceList.map(image => {
									// isImgPatient 需要加上这个参数--后端分表
									stack.push(
										imgUrl + `?sopInstanceUid=${image.sSOPInstanceUID}&caseId=${state.userInfo.caseId}&studyDate=${_.iStudyDate}`
									)
								})
								if (series.instanceList.length) {
									instanceList.set(
										series.sSeriesInstanceUID, 
										{
											imageIds: stack,
											uid: series.sSeriesInstanceUID,
											dateTime: series.iSeriesDate,
											sModality: series.sModality,
											sSeriesDescription: series.sSeriesDescription,
											sSeriesInstanceUID: series.sSeriesInstanceUID,
											iInstanceCount: series.iInstanceCount,
											sSOPClassUID: series.instanceList[0].sSOPClassUID,
											sStudyInstanceUID: _.sStudyInstanceUID,
											seriesNumber: series.iSeriesNumber,
											imageType: series.sImageType.replace(/\\/g, '|'),

											// 信息放在这，会卡？
											data: {
												instanceList: series.instanceList,
												sImgAccessionNumber: _.sAccessionNumber,
												sImgStudyDate: _.iStudyDate,
												sImgPatientId: _.sPatientID
											}
										}
									)

									// checkModality 第一位放 PT,NM
									if (!Fun.isCaptrue(series.sSeriesDescription, series.sSeriesInstanceUID, false, series.sImageType)) {
										checkModality[ ['PT', 'NM'].includes(series.sModality) ? 0 : 1 ] = series.sModality
									}

								}
							})
							
							
							commit('SET_SERIES', {
								key: _.sStudyInstanceUID,
								value: {
									seriesInfo,
									instanceList
								}
							})

						});
						// 当前检查设备类型拼接
						state.checkModality = checkModality.join('')

						resolve(state.seriesMap)
					} else {
						reject({msg: res.msg})
					}
				}).catch(() => {
					reject({msg: '请求错误！'})
				})
			})
		},

	},
	modules: {}
}