<template>
    <div class="c-main">
        <div class="c-item-01">
            <el-button type="mini" @click="onClickMetchRebuild" icon="el-icon-edit-outline" >编 辑</el-button>
            <el-button type="mini" @click="onClickRefresh" icon="el-icon-refresh" >刷 新</el-button>
        </div>
        <div class="c-item-02">
            <el-scrollbar style="height: 100%" class="overflow-x-hide">
                <div class="table-item" v-for="(item, index) in showSeries" :key="item.sStudyInstanceUID">
                    <h4 class="table-title">
                        <p class="table-title--p">日期：{{ item.iStudyDate }}</p>
                        <p class="table-title--p">设备：{{ item.modality | strJoin }}</p>
                        <p class="table-title--p overflow">描述：{{ item.sStudyDescription }}</p>
                    </h4>
                    <i class="js-control-show el-icon-caret-top" @click="onClickShowHide(index, $event)"></i>
                    <el-table
                        :ref="'multipleTable' + index" border @selection-change="(selection) => { sliceSelection(selection, index) }" @row-click="(row, column, e) => onClickRow(row, column, e, index)"
                        :data="item.instanceList" tooltip-effect="dark" style="width: 100%">
                        <el-table-column type="index" label="序号" width="50" show-overflow-tooltip align="center"></el-table-column>
                        <el-table-column type="selection" width="55" align="center"></el-table-column>
                        <el-table-column prop="name" label="名称" width="100" align="center">
                            <template slot-scope="scope">
                                <p>{{ scope.row.name }}</p>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sModality" label="设备信息" width="120" align="center">
                            <template slot-scope="scope">
                                <p v-show="scope.row.pt.iInstanceCount">{{ scope.row.pt.sModality }}：{{ scope.row.pt.iInstanceCount }}</p>
                                <p v-show="scope.row.ct.iInstanceCount">{{ scope.row.ct.sModality }}：{{ scope.row.ct.iInstanceCount }}</p>
                            </template>
                        </el-table-column>
                        <el-table-column prop="sSeriesDescription" label="序列描述">
                            <template slot-scope="scope">
                                <p>{{ scope.row.pt.sSeriesDescription }}</p>
                                <p>{{ scope.row.ct.sSeriesDescription }}</p>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <p v-if="!showSeries.length" class="c-tip">无</p>
            </el-scrollbar>
        </div>
        <div class="c-item-03">
            <el-button type="small" @click="onClickRebuild" icon="el-icon-folder-opened" >重 建</el-button>
        </div>
        <!-- 重建匹配弹窗页 -->
        <component :is="lazy.LayerMatchRebuild" v-model="visible"></component>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
const LayerMatchRebuild = () => import('./layerMatchRebuild');
import event from '$src/event.js'
export default {
    props: {
        originalSeries: {
            type: [Array, Object],
            default: []
        },
        openVisible: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            visible: false,
            rebuildMateKey: 'configs-rebuild-mate',
            autoRebuildAboutKey: 'configs-rebuild-about',
            rebuildMate: [],
            autoRebuildAbout: {},
            showSeries: [],
            selectionCheck: {},
            lazy: { },
        }
    },
    filters: {
        strJoin: function(arr) {
            return Array.from(new Set(arr)).join('&')
        }
    },
    mounted() {
        event.$on('findAndOpenRebuild', this.findAndOpenRebuild)

        this.init()
    },
    beforeDestroy() {
        event.$off('findAndOpenRebuild', this.findAndOpenRebuild)
    },
    methods: {
        // 存储自动重建序列到 vuex 中
        storeAutoRebuild() {
            this.$store.commit('setAutoRebuilds', this.showSeries);
        },
        // 点击打开重建匹配编辑弹窗
        onClickMetchRebuild() {
            if (!this.lazy.LayerMatchRebuild) {
                this.lazy.LayerMatchRebuild = LayerMatchRebuild;
            }
            this.visible = true
        },
        init() {
            // 获取 storage 数据
            this.rebuildMate = getConfigByStorageKey(this.rebuildMateKey)
            this.autoRebuildAbout = getConfigByStorageKey(this.autoRebuildAboutKey)
            this.showSeries.splice(0)
            this.findAndOpenRebuild()
        },
        /**
         * 查询跟打开重建
         */
        findAndOpenRebuild() {
            // 查找重建
            this.findRebuild().then(() => {
                this.storeAutoRebuild()
                // 打开重建
                this.openRebuild();
                setTimeout(() => {
                    this.$forceUpdate()
                }, 0);
            })
        },
        // 查找重建，按照 key 匹配重建
        async findRebuild(key = '') {
            if (!key) {
                key = this.$store.state.seriesInfo.key
            }

            // 过滤不可重建
            const filterSeries = this.originalSeries.filter(original => original.key === key).map((item, index) => {
                // 清除选中
                this.$set(this.selectionCheck, 'multiple'+index, [])
                const instanceList = item.instanceList.filter(item => {
                    return !this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, true, item.imageType);
                })
                const res = Object.assign({}, item, {instanceList: instanceList});
                return res;
            })
            // TODO 优化 - 读取图像中的 图像类型，序列number tag 值  如果后端实现这个字段，就可以省略加载
            // await Promise.all(filterSeries.map(async item => {
            //     await Promise.all(item.instanceList.map(async instance => {
            //         const image = await cornerstone.loadImage(instance.imageId);
            //         instance.imageType = image.data.string('x00080008') || ''
            //         instance.seriesNumber = image.data.string('x00200011') || ''
            //     }))
            // }))
            const ct  = ['CT', 'MR']
            const pet = ['PT', 'PET', 'NM'] 

            const showSeries = []
            // 遍历条件
            this.rebuildMate.map(item => {
                // const { checkModality, imageDesc, mathSymbol, mathValue, seriesDesc, seriesNumber } = item.series1
                const series1 = this.$fun.deepClone(item.series1);
                const series2 = this.$fun.deepClone(item.series2);
                const differ  = item.differ;

                // ct 在第一位或者 ct在第二位
                const ctTwo = (ct.includes(item.series2.checkModality) && pet.includes(item.series1.checkModality));

                // 交换位置
                if (ctTwo) {
                    const temp = series1;
                    series1 = series2;
                    series2 = temp;
                }
                // 遍历检查
                filterSeries.map((study, studyIndex) => {
                    
                    if (!showSeries[studyIndex]) {
                        showSeries[studyIndex] = {}
                    }
                    showSeries[studyIndex].iStudyDate = study.iStudyDate;
                    showSeries[studyIndex].sStudyDescription = study.sStudyDescription;
                    showSeries[studyIndex].sStudyInstanceUID = study.sStudyInstanceUID;
                    
                    if (!showSeries[studyIndex].modality) {
                        showSeries[studyIndex].modality = [];
                    }

                    if (!showSeries[studyIndex].instanceList) {
                        showSeries[studyIndex].instanceList = [];
                    }

                    // 遍历检查序列
                    study.instanceList.find(series => {
                        
                        // 查找 ct、mr
                        if (!this.conditionMatch(series, series1)){
                            return false;
                        }
                    
                        // 通过已经找到（序列一）匹配

                        // 没有序列二
                        if (!series2 || !Object.keys(series2).length) {
                            const instanceItem = {
                                mergeUid: series.uid,
                                sStudyInstanceUID: study.sStudyInstanceUID,
                                name: item.customName || item.checkPart,
                                scheme: item.scheme,
                                sort: item.sort
                            }

                            if (pet.includes(item.series1.checkModality)) {
                                instanceItem.ct = {}
                                instanceItem.pt = series
                            }else {
                                instanceItem.ct = series
                                instanceItem.pt = {}
                            }
                            showSeries[studyIndex].instanceList.push(instanceItem)
                            showSeries[studyIndex].modality.push(series.sModality)
                            return false;
                        }else {
                            // （序列二）遍历查找相应配对 pt nm
                            const findPt = study.instanceList.find(twoSeries => {

                                if (!this.conditionMatch(twoSeries, series2)){
                                    return false;
                                }

                                // 找到如果还有数目差，序号差。在做判断是否符合
                                // 数目差
                                if (differ.number) {
                                    const count = Math.abs(series.iInstanceCount - twoSeries.iInstanceCount)
                                    if (Number(differ.number) > count) {
                                        return false;
                                    }
                                }

                                // 序号差
                                if (differ.series) {
                                    const count = Math.abs(series.seriesNumber - twoSeries.seriesNumber)
                                    if (Number(differ.series) > count) {
                                        return false;
                                    }
                                }
                                // 条件全部通过
                                return true
                            })

                            // （找不到配对）find 不等于 undefind 找到
                            if (findPt) {

                                // 过滤没有的
                                const mergeUid = series.uid + findPt.uid
                                const status = showSeries[studyIndex].instanceList.some(item => {
                                    return item.mergeUid === mergeUid
                                })
                                // 没有重复，就加入到重建显示中
                                if (!status) {
                                    showSeries[studyIndex].instanceList.push({
                                        ct: series,
                                        pt: findPt,
                                        mergeUid: series.uid + findPt.uid,
                                        sStudyInstanceUID: study.sStudyInstanceUID,
                                        name: item.customName || item.checkPart,
                                        scheme: item.scheme,
                                        sort: item.sort
                                    })
                                    showSeries[studyIndex].modality.push(series.sModality)
                                    showSeries[studyIndex].modality.push(findPt.sModality)
                                }
                            }
                        }

                    });
                })
            })
            const addSeries = showSeries.filter(item => {
                if (item.instanceList.length) {
                    item.instanceList = item.instanceList.sort((a,b) => a.sort - b.sort)
                    return true
                }
                return false
            })
            this.showSeries = this.showSeries.concat(addSeries)
        },
        conditionMatch(series, conditionSeries) {
            // if (series.sSeriesDescription == 'CT Delay') {
            //     console.log('stop')
            //     debugger
            // }
            const imageType = series.imageType.split('|')

            // 相同设备，相同序列描述，相同图像类型
            // imageType == conditionSeries.imageDesc
            let findStep =  series.sModality === conditionSeries.checkModality && 
                            series.sSeriesDescription == conditionSeries.seriesDesc
                            && (!conditionSeries.imageDesc || imageType.findIndex(item => conditionSeries.imageDesc.includes(item)) != -1)
          
            if (!findStep) {
                return false;
            }
            // 图像数目
            const num = Number(conditionSeries.mathValue)
            if (conditionSeries.mathSymbol) {
                findStep = series.iInstanceCount == num
            }else {
                findStep = series.iInstanceCount >= num
            }

            if (!findStep) {
                return false;
            }

            // 有序列数，就做判断
            if (conditionSeries.seriesNumber) {
                findStep = series.seriesNumber == conditionSeries.seriesNumber
            }

            if (!findStep) {
                return false;
            }

            return true;
        },
        // 打开匹配到的重建序列
        openRebuild() {
            // 进入的 module 是 0 或者没有跳过不自动重建
            let searchPatient = this.$fun.searchParse();
            const module = Number(searchPatient.module);

            if (isNaN(module) || !module) {
                return;
            }

            // 不自动重建
            // if (this.autoRebuildAbout.multipleNotAuto && this.showSeries.length >= this.autoRebuildAbout.number) {
            //     return;
            // }
            const num = Number(this.autoRebuildAbout.number);
            let curNumb = 0;

            for (let i = 0; i < this.showSeries.length; i++) {

                const instanceList = this.showSeries[i].instanceList;
                const seriesLen = instanceList.length;
                for (let j = 0; j < seriesLen; j++) {
                    const uids = {
                        ct: instanceList[j].ct.uid,
                        pet: instanceList[j].pt.uid,
                    }
                    event.$emit('onSelectUid', uids, 'ViewSlice', this.showSeries[i].sStudyInstanceUID, {name: instanceList[j].name})
                    curNumb += 1;
                    if (curNumb === num) { 
                        break;
                    };
                }
                if (curNumb === num) { 
                    break;
                };
            }
            // 有自动重建，打开弹窗，没有不打开弹窗
            this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: !this.showSeries.length })
            // 跳到第一个 tab
            this.$nextTick(() => {
                event.$emit('onGoFirst')
            })
        },

        // 相同代码...
        // 多选改变弹窗
        sliceSelection(val, index){
            this.selectionCheck['multiple'+index] = val
        },
        // 重建表格中点击行
        onClickRow(row, column, e, index){
            this.$refs['multipleTable'+index][0].toggleRowSelection(row);
        },
        // 点击显隐
        onClickShowHide(index, event) {
            try {
                const transform = event.target.style.transform
                const rotate = transform.replace(/[^0-9]/ig, '') === "90" ? "0" : "90"
                event.target.style.transform = 'rotate('+ rotate +'deg)'
                const dom = this.$refs['multipleTable' + index][0].$el.querySelector('.el-table__body-wrapper')
                dom.style.display = rotate == '90' ? 'none' : 'initial';
            } catch (error) {
                // error
            }

        },
        // 点击刷新
        onClickRefresh() {
            this.rebuildMate = getConfigByStorageKey(this.rebuildMateKey);
            this.autoRebuildAbout = getConfigByStorageKey(this.autoRebuildAboutKey);

            // 获取所有key
            let keys = []
            this.originalSeries.map(item => {
                keys.push(item.key)
            })
            keys = [...new Set(keys)]
            // 遍历 key 查询匹配
            this.showSeries = []
            keys.forEach(key => {
                this.findRebuild(key);
            })
            this.storeAutoRebuild()
            this.$message({message: '刷新成功！', type: 'info'});
        },
        // 点击重建
        onClickRebuild() {
            if (!Object.keys(this.selectionCheck).length) {
                this.$message({message: '请选择重建数据。', type: 'info'})
            }
            // 关闭弹窗
            this.$emit('update:openVisible', false)
            for (const key in this.selectionCheck) {
                if (Object.hasOwnProperty.call(this.selectionCheck, key)) {
                    const select = this.selectionCheck[key];

                    for (let index = 0; index < select.length; index++) {
                        const item = select[index];

                        const uids = {
                            ct: item.ct.uid,
                            pet: item.pt.uid,
                        }
                        event.$emit('onSelectUid', uids, 'ViewSlice', item.sStudyInstanceUID, {name: item.name})
                    }
                    
                }
            }
        }
    },
}
</script>
<style lang="scss" scoped>
.c-main{
    display: flex;
    flex-direction: column;
    height: 560px;
    padding: 10px;
    .c-item-01 {
        padding-bottom: 14px;
        display: flex;
        align-items: center;
        justify-content: end;
    }
    .c-item-02{
        flex: 1;
        overflow: auto;
        .c-tip {
            position: absolute;
            top: 40px;
            font-size: 18px;
            width: 100%;
            text-align: center;
            color: #c0c0c0;
        }
    }
    .c-item-03{
        text-align: right;
        padding-top: 10px;
    }
}


.dialog-footer{
    button {
        margin-right: 20px;
        &:last-child{
            margin-right: 5px;
        }
    }
}
.table-item{
    position: relative;
    background: #727f8e;
    padding-left: 30px;
}
.table-title{
    display: flex;
    height: 30px;
    line-height: 30px;
    color: white;
}
.table-title--p{
    min-width: 140px;
    &.overflow {
        width: 315px;
        overflow: auto;
    }
}
.js-control-show{
    font-size: 24px;
    color: white;
    position: absolute;
    left: 3px;
    top: 41px;
    cursor: pointer;
    transition: all 0.2s;
    &:hover{
        color: #fafafa;
    }

}
</style>