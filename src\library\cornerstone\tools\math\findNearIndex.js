import cornerstone from '$library/cornerstone/cornerstone'
/**
 * 查找最近的图像，返回最近图像的下标 findNearIndex
 * @param {*} arr 序列 图像 ids
 * @param {*} orientation x,y,z角
 * @param {*} patientPoint 患者点
 * @returns 
 */
 export default function findRenderIndex(arr, orientation, patientPoint) {

    let currentPoint = 0;
    let orientationIndex = 0;

    if (orientation === 'x') {
        // 矢状位置
        currentPoint = patientPoint.x;
        orientationIndex = 0;
    }else if (orientation === 'y') {
        // 冠状位
        currentPoint = patientPoint.y;
        orientationIndex = 1;
    }else {
        // 横截面
        currentPoint = patientPoint.z;
        orientationIndex = 2;
    }

    let minValue = null;
    let index = null;
    // 遍历当前操作的图像序列
    try {
        arr.some((_, idx) => {
            // 绝对值
            const differ = Math.abs(_getImageIppByString(_, orientationIndex) - currentPoint);
    
            // 差距越来越小，赋值最新差值
            if (differ < minValue || minValue === null){
                minValue = differ;
                index = idx;
            }else {
                // 差距值越来越大，直接退出
                return true;
            }
        })
    } catch (error) {
        console.warn(error)
    }
    return index;
}
// 获取 xyz ipp 值
/**
 * 
 * @param {*} imageId 图像 id
 * @param {*} index   xyz 0 = x, 1 = y, 2 = z
 */
function _getImageIppByString(imageId = '', index = 0){
    // 这个也能获取到 imagePositionPatient 位置
    if (imageId.includes('mpr')){
        // 一种获取方式 
        const arr = imageId.split(':');
        return arr[3].split(',').map(Number)[index];
    }
    cornerstone.loadAndCacheImage(imageId)
    const metaData = cornerstone.metaData.get('imagePlaneModule',imageId)
    return metaData.imagePositionPatient[index];
}