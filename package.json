{"name": "report-read-dcm", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-report": "vue-cli-service build --report"}, "dependencies": {"axios": "^0.27.2", "blueimp-md5": "^2.18.0", "core-js": "^3.6.5", "cornerstone-math": "^0.1.8", "date-fns": "^2.19.0", "dicom-parser": "^1.8.9", "echarts": "^5.3.2", "element-ui": "^2.15.1", "gl-matrix": "^3.3.0", "hammerjs": "^2.0.8", "html2canvas": "1.1.4", "lodash-es": "^4.17.21", "shader-loader": "^1.3.1", "v-contextmenu": "^2.9.0", "vtk.js": "^23.4.3", "vue": "^2.6.11", "vue-router": "^3.2.0", "vue-runtime-helpers": "^1.1.2", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "wslink": "^1.0.7"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-router": "^4.5.0", "@vue/cli-plugin-vuex": "^4.5.0", "@vue/cli-service": "^4.5.0", "compression-webpack-plugin": "^6.1.1", "javascript-obfuscator": "^4.0.2", "node-sass": "^4.12.0", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11", "webpack-obfuscator": "^2.6.0", "worker-loader": "^3.0.8"}}