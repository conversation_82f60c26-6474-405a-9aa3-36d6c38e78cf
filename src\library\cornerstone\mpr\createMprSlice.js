import { mat4, vec3 } from 'gl-matrix';
import vtkMatrixBuilder from 'vtk.js/Sources/Common/Core/MatrixBuilder';
import vtkImageReslice from 'vtk.js/Sources/Imaging/Core/ImageReslice';
import setAndGetMetaData from './mprMetadata/setBaseMetaData';

import vtkImageInterpolator from '$library/vtk/Viewport/vtkImageInterpolator.js';
// import { SlabMode } from 'vtk.js/Sources/Imaging/Core/ImageReslice/Constants';
// import fun from '$library/utils/function.js';
import { getClipPlaneXY } from './getClipPlaneXY.js'
const intpInstance = vtkImageInterpolator.newInstance()
/**
 *
 * @function createMprSlice
 * 重建执行
 * @param {Object} vtkVolume
 * @param {vtkImageData} vtkVolume.vtkImageData
 * @param {Vec3} vtkVolume.centerIpp
 * @param {Object} [options={}]
 * @param {String} [options.imageOrientationPatient]
 * @param {String} [options.imagePositionPatient]
 * 
 * @returns {Object} - {slice, metaData}
 */
export default function (vtkVolume, options = {}, oneImg) {
    const originMetaData = setAndGetMetaData(oneImg) || {}
    // 角度
    const iop = options.imageOrientationPatient || "1,0,0,0,1,0";
    // 原点
    const ipp = options.imagePositionPatient || "0,0,0"; // Top Left of slice
    // Find our position in ZED, and our reslice axes
    const iopArray = iop.split(',').map(parseFloat);
    // 行列向量
    const rowCosinesVec3 = vec3.fromValues(iopArray[0], iopArray[1], iopArray[2]);
    const colCosinesVec3 = vec3.fromValues(iopArray[3], iopArray[4], iopArray[5]);
    const ippVec3 = ipp === "center"
        ? vtkVolume.centerIpp
        : ipp.split(',').map(parseFloat)

    // 行列向量转换法线
    let zedCosinesVec3 = vec3.create()
    vec3.cross(zedCosinesVec3, rowCosinesVec3, colCosinesVec3);

    const vtkImageData = vtkVolume.vtkImageData;

    const [x0, y0, z0] = vtkImageData.getOrigin();                           // x,y,z 原点
    const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing();        //图像的像素间距
    const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent();   // 获取 x,y,z 范围

    // 由总偏移量 分别计算x y方向的偏移量
    const offsetParams = options.offset || '0,0,0' // 总偏移向量
    const paramsArray = offsetParams.split(',')
    const offset = vec3.fromValues(paramsArray[0], paramsArray[1], paramsArray[2])
    // 把偏移量分解到两个方向上，获取偏移值
    const offsetX = vec3.dot(offset, rowCosinesVec3) / vec3.length(rowCosinesVec3)  // 在X轴投影 = a与b的点乘 / b的模
    const offsetY = vec3.dot(offset, colCosinesVec3) / vec3.length(colCosinesVec3)  // 在Y轴投影
    const offsetZ = vec3.dot(offset, zedCosinesVec3) / vec3.length(zedCosinesVec3)
    const offsetOrientFlag = rowCosinesVec3[0] ? 1 : -1 // 用来修正偏移方向
    ippVec3[_byNormalFindAngleIdx(zedCosinesVec3)] += offsetZ * offsetOrientFlag // 找出代表切片位置的那个轴, 在这个轴上加上Z偏移值


    // const xStart = x0 + xSpacing * (xMax - xMin);  // x最大值(ipp 切片x最大位)
    // const yStart = y0 + ySpacing * (yMax - yMin);  // y最大值
    const zStart = z0 + zSpacing * (zMax - zMin);  // z最大值 Inverted z for vtk??

    const position = vec3.fromValues(  // 切片位置， 会根据zed轴方向判断取原先的x y z轴的值
        (zedCosinesVec3[0] * -1 * (ippVec3[0] - x0)) + x0,
        (zedCosinesVec3[1] * (ippVec3[1] - y0)) + y0,
        (zedCosinesVec3[2] * (ippVec3[2] - zStart)) + zStart);

    // Maths
    // TODO: MetaDataProvider to grab `volumeSpacing` and `volumeExtent` for a given volume?
    // 切割4x4矩阵
    let axes = _calculateRotationAxes(rowCosinesVec3, colCosinesVec3, position);
    // console.log(axes)
    // 设置 vtkImageReslice
    // const sliceOrien = fun.getNormal(iopArray, true)
    let interpolationMode = cornerstoneTools.store.state.interpolationMode
    // CT/MR 横截面 没有改过层间距的切图不用线性插值
    if ((originMetaData.generalSeriesModule.modality == 'CT' || originMetaData.generalSeriesModule.modality == 'MR') && iop == '1,0,0,0,1,0' && !vtkVolume.thickness) {
        interpolationMode = 0
    }
    const vtkImageResliceOpts =   {
        interpolator: intpInstance,
        interpolationMode: interpolationMode // 线性插值 注释代码变快
    }
    const imageReslice = vtkImageReslice.newInstance(vtkImageResliceOpts );
    imageReslice.setOutputSpacing([xSpacing, ySpacing, zSpacing]) // 设置间距,后端vtk绑定数据后会自动调整，vtk.js不会
    imageReslice.setOutputOrigin([offsetX, offsetY])

    // 这个可以改变层厚...做mip图
    // imageReslice.setSlabMode(1);
    // imageReslice.setSlabNumberOfSlices(100);

    const machineOffset = (originMetaData.machineOffset || []).map(i => i || 0)

    if (machineOffset[3]) {
        const { matrix } = vtkMatrixBuilder.buildFromDegree()
            .setMatrix(axes)
            .rotateX(machineOffset[3])
            .rotateY(machineOffset[4])
            .rotateZ(machineOffset[5] * -1)

        axes = matrix;
    }

    let imageX = 0, imageY = 0, imageZ = 0;

    if (options.imageClip === 'clip' || options.imageClip === 'clip-special') {

        const { clipXY: [xMinNew, xMaxNew, yMinNew, yMaxNew], clipIpp, slicesType,
            clipAllPixelSpacing: { diffInfo } } = getClipPlaneXY(iopArray, options.seriesNumber + '&' + options.clipKey)

        if (options.imageClip === 'clip-special') {
            axes[12] = ippVec3[0]
            axes[13] = ippVec3[1]
            axes[14] = ippVec3[2]
        }else {

            // axes[12] = -134.985
            // axes[13] = -116.702

            // axes[12] = -131.489
            // axes[13] = -122.926

            // // 设置患者点
            if (slicesType === 'axial') {
                axes[12] = clipIpp.x
                axes[13] = clipIpp.y
            }else if (slicesType === 'coronal') {
                axes[12] = clipIpp.x
                axes[14] = clipIpp.z
            }else if (slicesType === 'sagittal') {
                axes[13] = clipIpp.y
                axes[14] = clipIpp.z
            }
        }

        // // 设置图像大小
        const croppingPlane = [
            xMinNew !== undefined ? xMinNew : xMin,
            xMaxNew !== undefined ? xMaxNew : xMax,
            yMinNew !== undefined ? yMinNew : yMin,
            yMaxNew !== undefined ? yMaxNew : yMax, zMin, zMax]
        
        imageX = croppingPlane[1] - croppingPlane[0] - 1
        imageY = croppingPlane[3] - croppingPlane[2] - 1
        imageZ = croppingPlane[5] - croppingPlane[4]

        // console.log('原始大小：', vtkImageData.getExtent())
        // console.log('想要的大小：', imageX, imageY, imageZ)
        const extent = [xMin, Math.round(imageX), yMin, Math.round(imageY), zMin, Math.round(imageZ)]
        imageReslice.setOutputExtent(extent)
        

        // // 加上旋转值
        // const rowCosines = [0.99415, -0.09104, 0.05804]
        // const colCosines = [0.09573, 0.99187, -0.08385]
        // let wCrossProd = vec3.create()
        // vec3.cross(wCrossProd, rowCosines, colCosines);

        // const axes1 = mat4.fromValues(
        //     rowCosines[0], rowCosines[1], rowCosines[2], 0,
        //     colCosines[0], colCosines[1], colCosines[2], 0,
        //     wCrossProd[0], wCrossProd[1], wCrossProd[2], 0,
        //     0, 0, 0, 1
        // )
        // // console.log(JSON.parse(JSON.stringify(axes)))
        // // console.log(JSON.parse(JSON.stringify(axes1)))
        // mat4.multiply(axes, axes, axes1)
        // // console.log(JSON.parse(JSON.stringify(axes)))
    }


    imageReslice.setInputData(vtkImageData);                // 设置体数据
    imageReslice.setOutputDimensionality(2);                // 数据类型（2维）
    imageReslice.setBackgroundColor(255, 255, 255, 255);    // 背景颜色（黑色）
    imageReslice.setResliceAxes(axes);                      // 设置旋转轴（4x4矩阵）

    // axes[12] = -131.48899841308594
    // axes[13] = -122.9260025024414
    // imageReslice.update()
    // 程序最耗时就是获取切图数据
    // console.time('output')

    let outputSlice = imageReslice.getOutputData();
    // console.timeEnd('output')
    // 当前图像的ipp原点位置
    let ippXYZ = [axes[12], axes[13], axes[14]]
    // 切片位置
    let sliceLocation = ippXYZ[_byNormalFindAngleIdx(zedCosinesVec3)];


    if (options.imageClip === 'clip' || options.imageClip === 'clip-special') {

        const { clipAllPixelSpacing: { columnPixel, rowPixel, diffInfo } } = getClipPlaneXY(iopArray, options.seriesNumber + '&' + options.clipKey)
        // // 改变 PT/NM 的 物理距离
        const spacing = outputSlice.getSpacing();
        const dimensions = outputSlice.getDimensions()

        const newColumnSpacing = columnPixel / dimensions[0];
        const newRowSpacing    = rowPixel    / dimensions[1];
        outputSlice.setSpacing([newColumnSpacing ,newRowSpacing, spacing[2]]);

        // console.log('之前间距：', spacing)
        // console.log('修正间距：', [newColumnSpacing ,newRowSpacing, spacing[2]])
    }

    let dimensions = outputSlice.getDimensions();
    const spacing = outputSlice.getSpacing();

    // axes[0] = 0.99415
    // axes[1] = -0.09104
    // axes[2] = 0.05804
    // axes[4] = 0.09573
    // axes[5] = 0.99187
    // axes[6] = -0.08385



    const result = {
        slice: outputSlice,   // 切面对象（vtk）
        metaData: {           // cornerstone 使用的元数据
            imagePlaneModule: {
                imageOrientationPatient: [
                    axes[0], axes[1], axes[2],
                    axes[4], axes[5], axes[6]
                ],
                imagePositionPatient: [ippXYZ[0], ippXYZ[1], ippXYZ[2]],
                rowCosines: [axes[0], axes[1], axes[2]],
                columnCosines: [axes[4], axes[5], axes[6]],
                rowPixelSpacing: spacing[1],
                columnPixelSpacing: spacing[0],
                frameOfReferenceUID: "THIS-CAN-BE-ALMOST-ANYTHING",
                columns: dimensions[0],
                rows: dimensions[1],
                sliceLocation
            },
            ...originMetaData
        }
    }
    return result;
}


/**
 * Creates a 4x4 matrix that vtk can use as a "rotation matrix". The values
 * correspond to:
 * 
 * ux, uy, uz, 0
 * vx, vy, vz, 0
 * wx, wy, wz, 0
 * px, py, pz, 1
 * 
 * ux, uy, uz, vx, vy, vz - "ImageOrientationPatient"
 * w - cross_product(u,v)
 * px, py, pz - "ImagePositionPatient"
 * 
 * ImagePositionPatient: [60.**********, 170.**********, -32]
 * ImageOrientationPatient: [-1, 0, 0, 0, -1, 0]
 * RowCosines: [-1, 0, 0]
 * ColumnCosines: [0, -1, 0]
 * 
 * Reference: https://public.kitware.com/pipermail/vtkusers/2012-November/077297.html
 * Reference: http://nipy.org/nibabel/dicom/dicom_orientation.html
 *
 * @param {Float32Array} rowCosines
 * @param {Float32Array} colCosines
 * @param {Float32Array} ippArray
 * @returns {Mat4} - 4x4 Rotation Matrix
 */
function _calculateRotationAxes(rowCosines, colCosines, ippArray) {
    // 法线
    let wCrossProd = vec3.create()
    vec3.cross(wCrossProd, rowCosines, colCosines);
    const axes = mat4.fromValues(
        rowCosines[0], rowCosines[1], rowCosines[2], 0,  // 传进来的行向量
        colCosines[0], colCosines[1], colCosines[2], 0,  // 传进来的列向量
        wCrossProd[0], wCrossProd[1], wCrossProd[2], 0,  // 传进来的行列向量转换的法线
        ippArray[0], ippArray[1], ippArray[2], 1         // 计算出来的位置？
    )
    return axes;
}

// 通过法线，[0,0,0]。那个位置最大，就表示所属方向 x,y,z
// 返回下标  0(x),1(y),2(z)
function _byNormalFindAngleIdx(normal) {
    const absNormal = normal.map(item => Math.abs(item));
    let max = absNormal[0];
    let index = 0;
    for (let i = 1; i < absNormal.length; i++) {
        if (max < absNormal[i]) {
            max = absNormal[i];
            index = i;
        }
    }
    return index; // 0(x), 1(y), 2(z) 
}