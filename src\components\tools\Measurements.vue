<template>
    <div class="box">
        <el-scrollbar class="measurement-scrollbar">
            <ul style="overflow: hidden;height: auto;">
                <li @click="onClickActive(item, index)" class="box-item" v-for="(item, index) in list" :key="index">
                    <div class="item-left">
                        <!-- <span>{{index + 1}}</span> -->
                        <i class="iconfont" :class="item.icon"></i>
                    </div>
                    <div class="item-right">
                        <h6>{{item.title || '...'}}</h6>
                        <p>{{item.value}}</p>
                    </div>
                    <div class="item-action">
                        <span class="i-commit" @click.stop="onClickEdit(item, index)">编辑</span>
                        <span class="i-commit i-delete" @click.stop="onClickDel(index)">删除</span>
                    </div>
                </li>

                <div class="edit-popover" v-show="visible">
                    <h6>编辑标题</h6>
                    <el-input size="mini" v-model="editObj.value" ref="editInput"></el-input>
                    <div class="edit-action">
                        <span class="i-commit" @click="visible = false">取消</span>
                        <span class="i-commit" @click="onClickSave">保存</span>
                    </div>
                </div>
            </ul>
        </el-scrollbar>

        <span v-if="!list.length" class="c-tip">无</span>
    </div>
</template>
<script>
export default {
    props: {
        viewportElements: {
            type: [Array, HTMLCollection],
            default: () => { [] }
        },
        activeViewportIndex: {
            type: Number,
            default: 0
        },
        studyId: {
            default: '',
        },
    },
    data() {
        return {
            list: [],
            seriesId: null,
            visible: false,
            editObj: {
                value: '',
                index: 0,
            },
            patientId: '',
            timer: null
        }
    },
    watch: {
        studyId: {
            handler(later) {
                if (later != '') {
                    if (later instanceof Array) {
                        this.patientId = this.$store.state.seriesMap.get(later[0]).seriesInfo.key
                    }else {
                        this.patientId = this.$store.state.seriesMap.get(later).seriesInfo.key
                    }
                }
            },
            immediate: true
        },
        activeViewportIndex: {
            handler(){
                // 获取当前点击元素的信息
                this.getActiveSelectTool();
            },
            immediate: true
        },
        measurementLength: {
            handler() {
                this.$emit('measurementLength', this.measurementLength)
            },
            immediate: true
        },
        'listenTool.value': {
            handler() {
                if (this.listenTool.seriesId === this.seriesId) {
                    this.getActiveSelectTool()
                }
            }
        }
        
    },
    computed: {
        measurementLength() {
            return this.list.length
        },
        listenTool() {
            return this.$store.state.listenTool
        },
        toolSync() {
            return this.$store.state.toolSync;
        },
    },
    methods: {
        getActiveSelectTool() {
            this.$nextTick(() => {
                const el = this.getEnabledElement(this.activeViewportIndex);
                if (!el) {
                    this.list = []
                    this.seriesId = null
                    return;
                }
                const modality = cornerstone.getEnabledElement( el ).modality
                this.getLoadCurImage(el).then(seriesId => {
                    this.seriesId = seriesId
                    const list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
                    if (!this.toolSync && modality) { // 关闭/打开测量工具同步时
                        this.list = list.filter(item => {
                            if (item.toolData && typeof item.toolData.modality === 'string') {
                                return item.toolData.modality === modality
                            } else {
                                return true
                            }
                        })
                    } else {
                        this.list = list
                    }
                })
            })
        },
        async getLoadCurImage(el){
            const enabledElement = cornerstone.getEnabledElement( el );
            if (enabledElement) {
                const allStack = cornerstoneTools.getToolState(el, 'stack');
                if (allStack){
                    // const img = allStack.data[0].imageIds[allStack.data[0].currentImageIdIndex];
                    // await cornerstone.loadImage(img);
                    return allStack.data[0].state.seriesId;
                }
            }else {
                return false
            }
        },
        getStateStack() {
            const el = this.getEnabledElement(this.activeViewportIndex);
            if (!el) {
                return {};
            }
            const allStack = cornerstoneTools.getToolState(el, 'stack');
            if (allStack){
                return {el, stack: allStack.data[0]};
            }
            return {}
        },
        // 从视窗中通过下标获取 enabled 元素
		getEnabledElement(index){
            if (!this.viewportElements[index]) return;
			const el = this.viewportElements[index].getElementsByClassName('viewport-element')[0];
			if (!el) return;
            return el;
		},
        onClickEdit(item, index) {
            this.visible = true
            this.editObj.value = item.title
            this.editObj.index = index
            this.$nextTick(() => {
                this.$refs.editInput.focus()
            })
        },
        // 点击删除
        onClickDel(index) {
            const {el, stack} = this.getStateStack()
            const activeTool = this.list[index]
            // 移除原生工具状态
            cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(activeTool.imageId, activeTool.toolName, activeTool.toolData)
            // 移除在原生基础工具状态
            cornerstoneTools.imageIdStateManager.delToolState(stack.state.seriesId, activeTool.imageId, activeTool.toolName, activeTool.toolData.uuid)
            // 移除当前组件工具状态
            this.list.splice(index, 1)

            this.triggerDelToolEvent(activeTool)
            // 更新视图
            cornerstone.updateImage(el)
            // 更新当前组件
            this.$forceUpdate();
        },
        // 删除全部序列工具
        delAllTools(attr) {
            const {el, stack} = this.getStateStack()

            this.list.forEach(toolItem => {
                // 移除原生工具状态
                cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(toolItem.imageId, toolItem.toolName, toolItem.toolData)
                // 移除在原生基础工具状态
                cornerstoneTools.imageIdStateManager.delToolState(this.seriesId, toolItem.imageId, toolItem.toolName, toolItem.toolData.uuid)
            
                this.triggerDelToolEvent(toolItem)
            })

            this.list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(this.seriesId)
            if (!el) {
                return;
            }
            // 更新视图
            cornerstone.updateImage(el)
            // 更新当前组件
            this.$forceUpdate();
        },
        removeRebuildAllTool(petUid, ctUid, thickness, isAnomalyFuse) {
            cornerstoneTools.imageIdStateManager.removeRebuildAllTool(petUid, ctUid, thickness, isAnomalyFuse);
            
            
            this.list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(this.seriesId);
            this.$forceUpdate();
        },
        // 保存工具
        saveTools(attr) {
            // 保存当前
            if (attr === 'active') {
                const { stack } = this.getStateStack()
                this.saveToolData(this.patientId, stack.state.seriesId)
            }else {
                for (let index = 0; index < this.viewportElements.length; index++) {
                    const el = this.getEnabledElement(index);
                    if (!el){ 
                        continue; 
                    }

                    let allStack = cornerstoneTools.getToolState(el, 'stack');
                    if (!allStack || !allStack.data.length) { return }
                    const state = allStack.data[0].state;

                    this.saveToolData(state.patientId, state.seriesId)
                }
            }

        },
        saveToolData(patientId, seriesId) {
            cornerstoneTools.imageIdStateManager.saveToolData(patientId, seriesId, (res) => {

                clearTimeout(this.timer)
                this.timer = setTimeout(() => {
                    if (res && res.success) {
                        this.$message({
                            message: `保存成功`,
                            type: 'success',
                            duration: 2000
                        });
                        return
                    }
                    this.$message({
                        message: '保存失败',
                        type: 'error',
                        duration: 1500
                    });
                }, 200);
            })
        },
        onClickSave() {
            this.list[this.editObj.index].title = this.editObj.value
            const {stack} = this.getStateStack()
            const activeTool = this.list[this.editObj.index]
            cornerstoneTools.imageIdStateManager.updateToolStateName(
                stack.state.seriesId, 
                activeTool.imageId,
                activeTool.toolName,
                activeTool.toolData.uuid,
                activeTool.title)
            this.visible = false
        },
        onClickActive(item, index) {
            const {el, stack} = this.getStateStack()
            if (stack === false) {
                return;
            }
            if (item.activeIndex != null && item.activeIndex != undefined) {
                // 直接跳转
                return;
            }
            // 数组查找跳转
            const idx = stack.imageIds.indexOf(item.imageId)
            if (idx !== -1) {
                cornerstoneTools.scrollToIndex(el, idx)
            }
        },
        triggerDelToolEvent(activeTool) {
            const { el } = this.getStateStack()

            const eventType = cornerstoneTools.EVENTS.MEASUREMENT_REMOVED;
            const eventData = {
                listRemoved: true,      // 这个状态是为了，能触发其它组移除
                measurementData: activeTool.toolData,
                toolName: activeTool.toolName

            };

            cornerstoneTools.triggerEvent(el, eventType, eventData)
        }

    }
}
</script>
<style lang="scss" scoped>
.box{
    height: 212px;
    width: 100%;
    text-align: left;
    overflow: hidden;
    position: relative;
    display: flex;
}
.measurement-scrollbar{
    height: 100%;
    width: 100%;
    ::v-deep .el-scrollbar__wrap{
        overflow-x: hidden;
    }
}
.edit-popover{
    position: absolute;
    top: 0px;
    width: 100%;
    background: #f5f9fc;
    padding: 10px;
    box-shadow: 1px 1px 1px #eee;
    border: 1px solid #eee;
    h6{
        font-size: 16px;
        padding-bottom: 10px;
    }
    .edit-action{
        padding-top: 10px;
        text-align: right;
        span:last-child{
            margin-left: 4px;
        }
    }
}
.c-tip{
    position: absolute;
    left: calc(50% - 7px);
    top: 28%;
    color: #c0c0c0;
}
.box-item{
    position: relative;
    display: flex;
    height: 48px;
    margin: 4px 8px 4px 4px;
    background: #fff;
    padding: 2px 0px;
    cursor: pointer;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    box-shadow: 1px 1px 1px #eee;
    &:hover{
        background: #f5f9fc;
        .item-action{
            right: 0px;
            transition: all 0.3s;
        }
    }
    .item-left{
        width: 34px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        text-align: center;
        span {
            font-size: 13px;
        }
    }
    .item-right{
        flex: 1;
        display: flex;
        flex-direction: column;
        padding-left: 4px;
        border-left: 1px solid #dcdfe6;
        h6 {
            font-size: 13px;
        }
        p {
            flex: 1;
            padding-top: 5px;
            font-size: 12px;
            overflow: hidden;
        }
    }
    .item-action{
        display: flex;
        flex-direction: column;
        position: absolute;
        right: -48px;
        top: -1px;
        span {
            font-size: 13px;
            line-height: 22px;
            background: white;
            &:hover{
                background: #f5f7fa;
            }
        }
        .i-delete{
            border-top: none;
            padding-top: 1px;
        }
    }
}
</style>