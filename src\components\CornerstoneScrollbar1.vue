<template>
    <div class="scroll" :style="styleScroll" @dblclick="stopPropagate">
        <div class="scroll-holder">
            <input class="imageSlider" type="range" ref="inputRange" :style="style" min="0" step="1" :max="max"
                :value="curVal" @input="onChange" @mousedown="onMousedown" @keydown="onKeydown" @mouseup="onMouseup" />
        </div>
    </div>
</template>
<script>
import { throttle } from "lodash-es";

// 原生 input 修改出来的滚动条
export default {
    props: {
        curVal: {
            type: [Number, String],
            default: 0,
        },
        max: {
            type: [Number, String],
            default: 0,
        },
    },
    data() {
        return {
            height: "100px",
            styleScroll: {},
            start: true, // 点击慢慢滚动， false 点击直接到目标位置
        };
    },
    computed: {
        style() {
            return { width: this.height };
        },
        renderScroll() {
            return this.$store.state.renderScroll;
        },
    },
    watch: {
        renderScroll: {
            handler() {
                setTimeout(() => {
                    this.height = `${this.$parent.element.clientHeight}px`;
                    this.initStype();
                }, 0);
            },
            immediate: true,
        },
    },
    methods: {
        onChange: throttle(function (event) {
            const intValue = parseInt(event.target.value, 10);
            this.$emit("onInputCallback", intValue);
        }, 16.6),
        onKeydown(event) {
            event.preventDefault()
        },
        onMousedown(event) {
            if (!this.start) {
                return;
            }

            clearInterval(this.timer);

            let targetValue = this.getScrollValue(
                event.target.clientWidth,
                event.offsetX
            );

            const stepValue = (event.target.clientWidth - 24) / this.max
            const curOffset = stepValue * this.curVal

            if (event.offsetX - curOffset <= 24 && event.offsetX - curOffset >= 0) {
                return
            }
            event.preventDefault();
            
            this.timer = setInterval(() => {
                let value = this.curVal;

                if (this.curVal === targetValue) {
                    clearInterval(this.timer);
                    return
                }
                if (targetValue > this.curVal) {
                    value += 1;
                } else {
                    value -= 1;
                }
                this.$emit("onInputCallback", value);
            }, 41.66);
        },
        onMouseup() {
            clearInterval(this.timer);
        },
        // 阻止事件冒泡
        stopPropagate(e){
            e.stopPropagation()
        },
        getScrollValue(maxHeight, offsetX) {
            if (offsetX <= 15) {
                return 0;
            } else if (offsetX >= maxHeight - 15) {
                return this.max;
            } else {
                return Math.ceil(
                    (this.max * (offsetX < 50 ? offsetX - 14 : offsetX)) / maxHeight
                );
            }
        },
        // 判断滚动条在那一侧
        initStype() {
            let curDom = this.$parent.element.parentNode;

            let parentDom = curDom.offsetParent;

            if (!parentDom.getAttribute("class").includes("c-content")) {
                parentDom = parentDom.offsetParent;
            }

            if (!curDom.getAttribute("class").includes("i-active")) {
                curDom = curDom.offsetParent;
            }

            if (!parentDom) {
                return;
            }
            let parentWidth = parentDom.clientWidth;
            let curDomWidth = curDom.clientWidth + 3;
            let offsetLeft = curDom.offsetLeft;
            if (curDomWidth + offsetLeft >= parentWidth) {
                if (offsetLeft > 0) {
                    this.styleScroll = { left: "-18px" };
                } else {
                    this.styleScroll = { left: "initial", right: "0px" };
                }
            } else {
                this.styleScroll = { right: "-18px", left: "initial" };
            }
        },
    },
};
</script>
<style lang="scss" scoped>
input[type="range"] {
    margin: 0px;
    padding: 0px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
}

.scroll {
    height: 100%;
    padding: 0px;
    position: absolute;
    top: 0;
    left: -20000px;
    z-index: 4;
    overflow: hidden;
}

.scroll .scroll-holder {
    height: 100%;
    // margin-top: 5px;
    position: relative;
    width: 18px;
    background: #ebeef5;
}

.scroll .scroll-holder .imageSlider {
    height: 18px;
    left: 18px;
    padding: 0;
    position: absolute;
    top: 0;
    transform: rotate(90deg);
    transform-origin: top left;
    -webkit-appearance: none;
    background-color: #f0f0f0;
}

.scroll .scroll-holder .imageSlider:focus {
    outline: none;
}

.scroll .scroll-holder .imageSlider::-moz-focus-outer {
    border: none;
}

.scroll .scroll-holder .imageSlider::-webkit-slider-runnable-track {
    background-color: rgba(0, 0, 0, 0);
    border: none;
    cursor: pointer;
    height: 5px;
    z-index: 6;
}

.scroll .scroll-holder .imageSlider::-moz-range-track {
    background-color: rgba(0, 0, 0, 0);
    border: none;
    cursor: pointer;
    height: 2px;
    z-index: 6;
}

.scroll .scroll-holder .imageSlider::-ms-track {
    animate: 0.2s;
    background: transparent;
    border: none;
    border-width: 15px 0;
    color: rgba(0, 0, 0, 0);
    cursor: pointer;
    height: 12px;
    width: 100%;
}

.scroll .scroll-holder .imageSlider::-ms-fill-lower {
    background: rgba(0, 0, 0, 0);
}

.scroll .scroll-holder .imageSlider::-ms-fill-upper {
    background: rgba(0, 0, 0, 0);
}

.scroll .scroll-holder .imageSlider::-webkit-slider-thumb {
    -webkit-appearance: none !important;
    background-color: #cdcdcd;
    border: none;
    border-radius: 57px;
    cursor: -webkit-grab;
    height: 18px;
    margin-top: -7px;
    width: 24px;
    border-radius: 0px;
}

.scroll .scroll-holder .imageSlider::-webkit-slider-thumb:active {
    background-color: #a6a6a6;
    cursor: -webkit-grabbing;
}
.scroll .scroll-holder .imageSlider::-webkit-slider-thumb:hover {
    background-color: #a6a6a6;
}
.scroll .scroll-holder .imageSlider::-moz-range-thumb {
    background-color: #163239;
    border: none;
    border-radius: 57px;
    cursor: -moz-grab;
    height: 12px;
    width: 39px;
    z-index: 7;
}

.scroll .scroll-holder .imageSlider::-moz-range-thumb:active {
    background-color: #20a5d6;
    cursor: -moz-grabbing;
}

.scroll .scroll-holder .imageSlider::-ms-thumb {
    background-color: #163239;
    border: none;
    border-radius: 57px;
    cursor: ns-resize;
    height: 12px;
    width: 39px;
}

.scroll .scroll-holder .imageSlider::-ms-thumb:active {
    background-color: #20a5d6;
}

.scroll .scroll-holder .imageSlider::-ms-tooltip {
    display: none;
}

@media screen and (-ms-high-contrast: active),
(-ms-high-contrast: none) {
    .imageSlider {
        left: 50px;
    }
}
</style>
