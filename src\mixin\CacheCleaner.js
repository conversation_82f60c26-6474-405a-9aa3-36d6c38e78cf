/**
 * cornerstone缓存定时清理
 * @returns void
 */

import event from '$src/event.js'

let hasLoadingTask = false
let hasLoadingTaskTimer = null

const cornerstone = window.cornerstone
const loader = window.cornerstoneWADOImageLoader

const resetWebWorker = () => {
    const wadouri = loader.wadouri
    wadouri.fileManager.purge()
    loader.webWorkerManager.terminate()
    loader.webWorkerManager.initialize({
        maxWebWorkers: navigator.hardwareConcurrency || 1,
        startWebWorkersOnDemand: true, // true 需要时创建 Web, false = 初始化时全部创建。默认 false
        taskConfiguration: {
            decodeTask: {
                initializeCodecsOnStartup: false, // 初始化 JPEG2000 或 JPEG-LS 解码器
                strict: false,
            },
        },
    });
}

const cleanCache = (clearWado = false) => {
    if (!loader || !cornerstone) return

    if (clearWado) {
        const wadouri = loader.wadouri

        wadouri.fileManager.purge()
        wadouri.dataSetCacheManager.purge()
    }

    resetWebWorker()
    cornerstone.imageCache.purgeCache()
}

const timingCacheClean = () => {
    if (!loader || !cornerstone) return

    const webWorkerManager = loader.webWorkerManager
    const webWorkers = webWorkerManager.webWorkers || []
    const busyWorker = webWorkers.filter(item => item.status !== 'ready' || item.task)
    if (!busyWorker.length && !hasLoadingTask) { // 没有busyWorker不代表不在加载图片
        resetWebWorker()
    }
}

const restartLoadingTaskTimer = () => {
    const cleanWorkerCache = () => {
        hasLoadingTask = false
        resetWebWorker()
    }
    if (hasLoadingTask === true) {
        clearTimeout(hasLoadingTaskTimer)
        hasLoadingTaskTimer = setTimeout(cleanWorkerCache, 13000);
    } else {
        hasLoadingTask = true
        hasLoadingTaskTimer = setTimeout(cleanWorkerCache, 13000);
    }
}

const addTaskOriginVer = cornerstoneWADOImageLoader.webWorkerManager.addTask
cornerstoneWADOImageLoader.webWorkerManager.addTask = (a, b, c, d) => { // 注入计时器在cornerstoneWADOImageLoader的worker里
    restartLoadingTaskTimer()
    return addTaskOriginVer(a, b, c, d)
}

const checkMemory = () => {
    const m = performance.memory
    // 压缩的会超过，但是不崩溃？
    if (m.totalJSHeapSize / m.jsHeapSizeLimit > 0.9) {
        // window.opener = null // 防止连带崩溃 （没用
        cornerstone.imageCache.purgeCache() // 只能预防崩溃了
    }
}

export default {
    data() {
        return {
            CacheCleanerTimer: null,
            loadTabTask: 0,
            checkMemoryTimer: null
        }
    },
    watch: {
        loadTabTask: {
            handler() {
                // 如果 tab 都加载完成了，就清除
                if (this.loadTabTask <= 0) {
                    setTimeout(() => {
                        cleanCache()
                    }, 3000);
                }
            }
        }
    },
    methods: {
        // 更新加载 tab 任务数
        updateLoadTabTask(type = 'add') {
            this.loadTabTask += type === 'add' ? 1 : -1
        }
    },
    mounted() {
        cleanCache(true)
        // this.checkMemoryTimer = setInterval(checkMemory, 2000)
        event.$on('updateLoadTabTask', this.updateLoadTabTask)
    },
    beforeDestroy() {
        // clearInterval(this.checkMemoryTimer)
        event.$off('updateLoadTabTask', this.updateLoadTabTask)
    }
}