<template>
    <div style="height: 100%;display: flex;flex-direction: column;">
        <div class="c-content">
            <div class="item">
                <span>重建默认伪彩:</span>
                <div class="box">
                    <el-select v-model="currentConfigs.colormap" size="small" placeholder="请选择">
                        <el-option
                        v-for="item in colormapsList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                        <span style="float: left">{{ item.name }}</span>
                        <canvas style="float: right;margin: 7px 0px 0px 4px;" :ref="item.id" width="66px" height="20px"></canvas>
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="item">
                <span>PET 浓度值:</span>
                <div class="box">
                    <el-input-number v-model="currentConfigs.petU" :min="0.1" :max="100" label="" size="small"></el-input-number>
                </div>
            </div>
            <div class="item">
                <span>MIP自动放大:</span>
                <div>
                    <el-switch
                        v-model="currentConfigs.mipMagnify"
                        active-color="#13ce66"
                        inactive-color="#eeeeee"
                        :active-value="true"
                        :inactive-value="false">
                    </el-switch>
                </div>
            </div>
            <div class="item">
                <span>PT/MIP同步调窗:</span>
                <div>
                    <el-switch
                        v-model="syncMipWwwc"
                        active-color="#13ce66"
                        inactive-color="#eeeeee"
                        :active-value="true"
                        :inactive-value="false">
                    </el-switch>
                </div>
            </div>
            <div class="item">
                <div style="display: flex;align-items: center;margin-bottom: 20px;">
                    <span style="padding-right: 10px;">通过描述排除可重建:</span> <el-button type="primary" size="mini" @click="onClickAddExclude" plain>添加</el-button>
                </div>
                <div>
                    <div class="exclude-box">
                        <div v-for="(item, index) in excludeDescriptions" :key="index" style="margin-right: 10px;display: flex;align-items: center; margin-bottom: 10px;">
                            <el-input v-model="item.value" size="mini" placeholder="序列描述" style="width: 160px; margin-right: -1px;"></el-input>
                            <el-button size="mini" @click="onClickDelExclude(index)">删除</el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setShowRebuild',
    data() {
        return {
            currentConfigs: {},                 // 当前值
            colormapsList: [
                { id: 'ge_color', name: 'ge_color' },
                { id: 'hot', name: 'Hot' },
                { id: 'hot2', name: 'Hot2' },
                { id: 'darkHot', name: 'darkHot' },

                { id: 'perfusion', name: 'Perfusion' },
                { id: 'rainbow', name: 'Rainbow' },
                { id: 'x_rain', name: 'x_rain' },
                { id: 'rainbow_brain', name: 'rainbow_brain' },
                { id: 'x_hot', name: 'X_hot' },
                { id: 'x_brain', name: 'x_brain' },
                { id: 'copper', name: 'Copper' },
                { id: 'hotMetalBlue', name: 'Hot Metal Blue' },
                { id: 'pet20Step', name: 'PET 20 Step' },
                { id: 'spectral', name: 'Spectral' },

                { id: 'blues', name: 'Blues' },
                { id: 'cool', name: 'Cool' },
                { id: 'jet', name: 'Jet' },
            ],
            storageKey: 'configs-show-rebuild',

            storageKeyWwwc: 'configs-syncMipWwwc',
            syncMipWwwc: false,

            storageKeyExcludeDesc: 'configs-exclude-descriptions',
            excludeDescriptions: []
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.colormapsList.forEach(_ => {
                this.updateColorbar(_.id)
            })
        })
        this.currentConfigs = getConfigByStorageKey(this.storageKey);
        if (this.currentConfigs.petU == undefined) {
            this.currentConfigs.petU = 10
        }

        this.syncMipWwwc = getConfigByStorageKey(this.storageKeyWwwc);

        // 加载排除的序列描述
        const excludeDesc = getConfigByStorageKey(this.storageKeyExcludeDesc);
        if (Array.isArray(excludeDesc)) {
            this.excludeDescriptions = excludeDesc.map(item => {
                return {
                    value: item
                }
            });
        }
    },
    methods: {
        onClickSave(){
            localStorage.setItem(this.storageKeyWwwc, JSON.stringify(this.syncMipWwwc));

            // 保存排除的序列描述
            const excludeDescArray = this.excludeDescriptions.map(item => item.value);
            localStorage.setItem(this.storageKeyExcludeDesc, JSON.stringify(excludeDescArray));

            // 设置新的缓存
            localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfigs))

            // 更新
            this.$store.commit('setShowRebuild', this.currentConfigs);
            // 更新排除的序列描述
            this.$store.commit('setExcludeDescriptions', excludeDescArray);

            // 触发渲染
            // this.triggerUpdateView();
            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },

        // 添加排除的序列描述
        onClickAddExclude() {
            this.excludeDescriptions.push({
                value: ''
            });
        },

        // 移除排除的序列描述
        onClickDelExclude(index) {
            this.excludeDescriptions.splice(index, 1);
        },
        // 浏览器触发渲染 canvas
        triggerUpdateView(){
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent)
            })
        },
        // canvas 颜色条
        updateColorbar(colormapId) {
            let colormap = cornerstone.colors.getColormap(colormapId);
            const lookupTable = colormap.createLookupTable();
            const canvas = this.$refs[colormapId][0];
            const ctx = canvas.getContext('2d');
            const height = canvas.height;
            const width = canvas.width;
            const colorbar = ctx.createImageData(66, 20); // 宽高

            lookupTable.setTableRange(0, width);
            
            for(let col = 0; col < width; col++) {
                const color = lookupTable.mapValue(col);
    
                for(let row = 0; row < height; row++) {
                    const pixel = (col + row * width) * 4;
                    colorbar.data[pixel] = color[0];
                    colorbar.data[pixel+1] = color[1];
                    colorbar.data[pixel+2] = color[2];
                    colorbar.data[pixel+3] = color[3];
                }
            }
    
            ctx.putImageData(colorbar, 0, 0);
        },
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    flex: 1;
    padding-top: 10px;
    .item {
        margin-bottom: 40px;
        > span{
            font-size: 15px;
            display: block;
            padding-bottom: 20px;
        }
        .exclude-box {
            display: flex;
            flex-wrap: wrap;
        }
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>