import { getDbTable, dbInit } from '../db/dbInit';
import getConfigByStorageKey from './configStorage';

const localKeyList = [
    'configs-WL',
    'configs-tools',
    'configs-lengthUnit',
    'configs-mouse',
    'configs-toolRepeatUse',
    'configs-toolSync',
    'configs-PET',
    'configs-rebuild-about',
    'configs-rebuild-mate',
    'configs-dot',
    'configs-circleRoi',
    'configs-circleMeasure',
    'configs-modalityTagItems',
    'synchronization',
    'configs-crosshairs-show',
    'configs-crosshairs-type',
    'configs-mipCrosshairDisplay',
    'configs-read-default-layout',
    'configs-default-layout',
    'configs-rebuild-layout',
    'setting-remark',
    'setting-read-show-remark',
    'configs-show-rebuild',
    'configs-layoutFill',
    'configs-interpolationMode',
    'configs-imageSampleDistance',
    'configs-show-read-modality',
    'configs-rebuild-sync-zoom',
    'configs-show-shortcut-key',
    'configs-default-reset-mode',
    'configs-imageFilter2Mode',
    'configs-imageFilterSharpen',
    'configs-imageFilteWithoutCnS',
    'configs-imageClipClarity',
    'configs-syncMipWwwc',
    'configs-readDcmSeting',
    'configs-dragLockSync',
    'configs-scrollMouseWheelLoop',
    'configs-zoomToCenter', 
    'configs-textShadow', 
    'configs-toolsColor', 
	'configs-show-direction',
	'configs-mipClickLocation',
]


//  获取全部本地存储数据
export async function getAllStorageInJSON() {
    const localSto = {}
    localKeyList.forEach(key => {
        localSto[key] = getConfigByStorageKey(key)
    })
    // localStorage.getItem(key)
    const dbTable = getDbTable()
    const dbNameList = dbTable.map(i => i.storeName)
    const dbSto = {}
    for (let index = 0; index < dbNameList.length; index++) {
        const name = dbNameList[index];
        dbSto[name] = await getDbDataByName(name)
    }

    const result = {
        localSto,
        dbSto
    }

    return {
        data: result,
        json: JSON.stringify(result)
    }
}

//  读取存储数据
export async function setAllStorageInJSON(totalData) {
    const localSto = totalData.localSto
    if (localSto) {
        for (const key in localSto) {
            if (Object.hasOwnProperty.call(localSto, key)) {
                localStorage.setItem(key, JSON.stringify(localSto[key]));
            }
        }
    }

    const dbSto = totalData.dbSto
    let result = true

    if (dbSto) {
        const dbTable = getDbTable()
        const dbNameList = dbTable.map(i => i.storeName)
        for (const key in dbSto) {
            if (Object.hasOwnProperty.call(dbSto, key) && dbNameList.includes(key)) {
                result = await setDbDataByName(key, dbSto[key]) && result
            }
        }
    }

    return result
}

function getDbDataByName(storeName) {
    return new Promise((resolve) => {
        dbInit({ storeName }).then((e) => {
            e.getAll().then((e) => {
                if (e.success) {
                    // console.log(e.data) 
                    resolve(e.data)
                } else {
                    console.warn('获取db数据错误：e.getAll')
                }
            });
        });
    })
}

async function setDbDataByName(storeName, dataList = []) {
    const db = await dbInit({ storeName })
    // console.log(dataList)
    if (!db) {
        console.warn(' indexedDB错误：dbInit')
        return false
    }

    const isClear = await db.clear()

    if (!isClear) {
        console.warn(' indexedDB错误： clear')
        return false
    }

    let result = true

    if (Array.isArray(dataList)) {
        for (let index = 0; index < dataList.length; index++) {
            result = await db.put(dataList[index]) && result
        }

        return (result)
    }
    console.warn(' indexedDB错误： put')
    return false

}



