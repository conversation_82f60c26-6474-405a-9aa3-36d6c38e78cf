<template>
    <div class="action-area" ref="area">
        <div class="box-area" :style="areaStyle">
            <slot></slot>
        </div>
        <div v-if="showFooter" class="bottom-bar" :style="bottomBarStyle">
            <slot name="footer"></slot>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        isMuch: {
            type: Boolean,
            default: false
        },
        showFooter: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            NAME: 'AreaFit',
            areaStyle: {
				width: '100%',
				height: '100%'
			},
            bottomBarStyle: {
                height: '30px',
                width: '100%'
            }
        }
    },
    computed: {

    },
    beforeDestroy() {
		window.removeEventListener('resize', this.resizeScreen)
	},
    mounted() {
		window.addEventListener('resize', this.resizeScreen)
		this.resizeScreen();
    },
    methods: {
        resizeScreen(){
            if (!this.isMuch) {
                const el = this.$refs.area;
                const height = el.clientHeight;
                const width  = el.clientWidth;
                let minSize  = Math.min(width, height)

                this.areaStyle.width = minSize + 'px'
                this.areaStyle.height = minSize + 'px'
            }else {
                this.areaStyle.width = '100%'
                this.areaStyle.height = '100%'
            }
            this.$emit('resizeArea')
        },
    }
}
</script>
<style lang="scss" scoped>
.action-area{
	display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
	flex: 1;
	background: #D7DAE1;
	overflow: hidden;
	.box-area{
		display: flex;
		overflow: hidden;
        // border: 1px solid #067706;
	}
    .bottom-bar {
        position: relative;
        display: block;
        background: #547495;
        color: #fff;
        line-height: 30px;
        text-align: left;
        text-indent: 20px;
    }
}
</style>