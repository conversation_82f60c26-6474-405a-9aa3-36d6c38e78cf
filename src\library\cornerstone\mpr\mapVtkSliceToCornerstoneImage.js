// Inspiration: https://github.com/Kitware/vtk-js/blob/master/Sources/Filters/Cornerstone/ImageDataToCornerstoneImage/index.js
import VoiMapping from '$library/cornerstone/function/VoiMapping.js';
import customLocalMeta from '$library/cornerstone/mpr/store/customLocalMeta.js';

export default function (vtkSlice, imageId, imagePlaneModule) {
	// const [xMin, xMax, yMin, yMax] = vtkSlice.getExtent(); // 获取 x,y,z 范围
	// const imageHeight = yMax + 1;
	// console.log(imageHeight)
	const spacing = vtkSlice.getSpacing();
	const dimensions = vtkSlice.getDimensions();
	const scalars = vtkSlice.getPointData().getScalars();
	const dataRange = scalars.getRange(0);
	const rawData = scalars.getData();
	let pixelData = null;
	if (dimensions[2] === 1) {
		const scalarsData = scalars.getData();
		pixelData = !scalars.data ? scalarsData : scalars.data;
	} else {
		// TODO: There is no slice index property here?
		const offset =
			vtkSlice.sliceIndex * dimensions[0] * dimensions[1] * rawData.BYTES_PER_ELEMENT;

		pixelData = new macro.TYPED_ARRAYS[(scalars.getDataType())](
			rawData.buffer,
			offset,
			dimensions[0] * dimensions[1]
		);
	}
	// 补 y 轴高度
	// console.log(dimensions[1], imagePlaneModule.rows)
	dimensions[1] = imagePlaneModule.rows;
	// let dataAsInt16Array = new Int16Array(pixelData.length);
	// for(var i=0; i < pixelData.length; i++){
	//   dataAsInt16Array[i] = parseInt(pixelData[i] * 32767,10);
	// }
	// const probablyPixelData = obliqueSlice.getPointData().getArrays()[0].getData();
	// console.log(customLocalMeta)
	const modalityLutModule = cornerstone.metaData.get(
		'modalityLutModule',
		imageId
	);
	const { modality, seriesInstanceUID } = cornerstone.metaData.get(
		'generalSeriesModule',
		imageId
	);
	let imageInfo

	if (customLocalMeta.data[seriesInstanceUID]) {
		// 如果设置了 SUV 参数就用 SUV 参数值
		const uid = `mpr:${seriesInstanceUID}`
		customLocalMeta.setImageIdMetaData(uid)
		imageInfo = VoiMapping.getVoi(`mpr:${seriesInstanceUID}`, seriesInstanceUID)

	}else {
		imageInfo = VoiMapping.getVoi(imageId);
	}

	let intercept = modalityLutModule.rescaleIntercept || 0;
	let slope     = modalityLutModule.rescaleSlope || 1;

	if (modality === 'PT') {
		intercept = 0;
		slope = 1;
	}

	return {
		color: scalars.getNumberOfComponents() > 1,

		columnPixelSpacing: spacing[0],
		rows: dimensions[1],
		height: dimensions[1],

		//
		rowPixelSpacing: spacing[1],
		columns: dimensions[0],
		width: dimensions[0],

		//
		depth: dimensions[2],

		//
		intercept: intercept,
		invert: false,
		minPixelValue: dataRange[0], // 改变这个会影响成像，会形成一个范围长度 rgba 数组
		maxPixelValue: dataRange[1],

		//
		sizeInBytes: pixelData.length * pixelData.BYTES_PER_ELEMENT,
		slope: slope,

		//
		windowCenter: imageInfo.windowCenter || Math.round((dataRange[0] + dataRange[1]) / 2), // Math.round((dataRange[0] + dataRange[1]) / 2),
		windowWidth: imageInfo.windowWidth || dataRange[1] - dataRange[0], // dataRange[1] - dataRange[0],
		decodeTimeInMS: 0,

		getPixelData() {
			return pixelData;
		},
	};
}