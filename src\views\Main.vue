<template>
    <div class="xx-view" oncontextmenu="return false">
        <header class="xx-header c-header"> 
            <div style="flex: 1; overflow: hidden;">
                <div class="xx-tabs">
                    <div v-for="(item, index) in tabs" 
                    :key="item.id" 
                    :class="{'i-active': idValue === item.id}" 
                    class="tags-view-item" 
                    @click="onClickTab(item.id)"
                    :title="getTitle(item, true)">
                        <span class="text">{{ getTitle(item) }}</span>
                        <i class="el-icon-close" @click.prevent.stop="closeTab(index)"></i>
                    </div>
                </div>
            </div>
            <ConfigsSetView></ConfigsSetView>
        </header>
        <div v-for="item in tabs" :key="item.id" class="content-box" 
            :style="{
                'zIndex': idValue === item.id ? 2 : 1, 
                visibility: idValue === item.id ? 'visible' :  'hidden',}">
            <component :is="item.component" 
            :windowIsActive="idValue === item.id"
            :series="item.series" 
            :seriesId="item.seriesId" 
            :openIds="item.openIds"
            :tabId="item.id" 
            :reload="item.reload" 
            :viewportInof="item.viewportInof"
            :isScreenshotContrast="item.isScreenshotContrast"
            :isMuch="item.isMuch"></component>
        </div>
        <span v-if="!tabs.length" class="i-tip loader">L <i style="display: inline-block; width: 10px;"></i> ading</span>
    </div>
</template>
<script>
import ConfigsSetView from '$components/ConfigsSetView'
import ScrollPane from '$components/ScrollPane'

import ReadDcm from './ReadDcm'
import ViewSlice from './ViewSlice'
import ViewVtkFuseMpr from './ViewVtkFuseMpr'
import ViewVtkVR from './ViewVtkVR'
import ViewContrast from './ViewContrast'
import ViewMRLayout from './ViewMRLayout'
import Select from './Select'

import event from '$src/event.js'
import CacheCleaner from "$src/mixin/CacheCleaner.js";
// 多开关联
import broadcastManager from '$library/broadcast';

import getConfigByStorageKey from '$library/utils/configStorage.js'
import flexibility from '$library/utils/flexibility.js'
import { checkIfTurnGpuOn } from '$library/utils/checkGpu.js'
import { setAllStorageInJSON } from '$library/utils/readwriteStorage.js'

export default {
    name: 'Main',
    mixins: [CacheCleaner],
    components: {
        ConfigsSetView,
        ReadDcm,
        ViewSlice,
        ViewVtkFuseMpr,
        Select,
        ScrollPane,
        ViewContrast,
        ViewMRLayout,
        ViewVtkVR
    },
    data() {
        return {
            componentsList: ['ReadDcm', 'ViewSlice', 'ViewVtkFuseMpr', 'Select', 'ViewContrast', 'ViewMRLayout', 'ViewVtkVR'],   // tab 相应展示的tab组件
            componentsName: ['阅图', '重建', 'MPR', '选择', '对比', '拖拽重建', '体渲染'],                             // tab 显示名称
            tabs: [],                   // tabs 模块列表
            idValue: 1,                 // 打开模块id
            currentLoading: false,      // 是否在当前模块加载
            defaultModule: null,


            // 广播操作数据
            broadcastTimer: {},

        }
    },
    // filters: {
    //     customName: function(title, obj, isScreenshotContrast) {
    //         // 自动重建配置的名称
    //         if (obj && obj.name) {
    //             return obj.name;
    //         }
    //         if (isScreenshotContrast) {
    //             return '阅图对比';
    //         }
    //         // 显示默认名称
    //         return title;
    //     },
    // },
    computed: {
        listenCapture() {
            return this.$store.state.listenCapture
        }
    },
    watch: {
        $route: {
            handler: function(val){
                // 路由变化，关联打开的时候多了个 past 属性
                let searchPatient = this.$fun.searchParse();
                if (searchPatient.past) {
                    // 关联打开，同时打开多个“患者”
                    this.openNewReport(searchPatient)
                    return
                }

                const isCase = this.$store.state.sPatientId;   // 报告系统病例id
                const patientId = this.$store.state.patientId; // 无报告图像患者id

                if ((isCase && val.query.sPatientId !== isCase) || (patientId && val.query.patientId !== patientId)) {
                    // 不同的患者，直接刷新
                    window.location.reload(true)
                }else {
                    // TODO 不应该刷新，应该直接打开相应模块的阅图或重建
                    window.location.reload(true)
                }

            },
            // 深度观察监听
            deep: true
        },
        idValue() {
            // 如果截图了，那么遍历 tabs 看看当前进入的 tab 是不是截图 tab 是就从新加载
            // if (this.listenCapture) {
            //     for (let index = 0; index < this.tabs.length; index++) {
            //         const tab = this.tabs[index];
            //         if (tab.id == this.idValue && tab.isMuch && this.tabs[index].openIds === this.$store.state.seriesInfo.key) {
            //             // 加载新的图像
            //             // this.getNewCapture(index)
            //             this.tabs[index].reload = !this.tabs[index].reload;
            //             this.$store.state.listenCapture = false;
            //             break
            //         }
            //     }
            // }
            // 广播更新打开的 tab
            broadcastManager.setTabId(this.idValue);
        }
    },
    async mounted() {
        window.addEventListener('mousemove', this.handleMouseMove)
        // 读取接口，获取存在服务端的配置数据
        await this.getSetDataFromApi()
        
        // 加载配置...
        this.getInitConfigs();

        
        // 开启 emit 事件触发
        event.$on('updateCapture', this.updateCapture)  // 多屏更新截图
        event.$on('onSelectUid', this.onSelectUid)
        event.$on('currentShowSeries', this.setCurrentLoading)
        event.$on('setTabInfo', this.setTabInfo)
        event.$on('onGoFirst', this.onGoFirst)
        event.$on('setSynchronization', this.setSynchronization) // 多个浏览器tab同步关联传递的是否同步状态
        event.$on('openTabByOpenIds', this.openTabByOpenIds)
    
        // 开启键盘监听
        this.startKeyDown();

        // 获取vuex store数据
        this.$store.commit('GET_TOOLNEWIMAGEUSE')
        this.$store.commit('GET_TOOLREPEATUSE')
        this.$store.commit('GET_TOOLSYNC')
        this.$store.commit('GET_MIPCROSSHAIRDISPLAY')
        this.$store.commit('getRebuildSyncZoom')
        // 获取最新的鼠标右键滚轮事件值
        this.$store.commit('GET_MOUSEEVENTS')
        // 获取是否显示键盘快捷键
        this.$store.commit('getShowShortcutKey')
        // 获取是否显示打印缩略图
        this.$store.commit('getPrintThumb')

        // flex polyfill
        flexibility(document.querySelector('#app'));

        // 检查硬件加速开关
        // if (!checkIfTurnGpuOn()) {
        //     this.$message.warning('硬件加速未开启，系统功能受限，请检查浏览器设置')
        //     localStorage.setItem('configs-imageSampleDistance', '5');
        // }

        
        // 显示模块
        await this.showMudule();
        window.addEventListener('focus', () => {
            event.$emit('windowfocus')
        })
    },
    beforeDestroy() {
        // 销毁触发
        event.$off('updateCapture', this.updateCapture)
        event.$off('onSelectUid', this.onSelectUid)
        event.$off('currentShowSeries', this.setCurrentLoading)
        event.$off('setTabInfo', this.setTabInfo)
        event.$off('onGoFirst', this.onGoFirst)

        event.$off('setSynchronization', this.setSynchronization)
        event.$off('openTabByOpenIds', this.openTabByOpenIds)

        window.removeEventListener('mousemove', this.handleMouseMove)
    },
    methods: {
        handleMouseMove() {
            // 节流判断鼠标移动距离

            clearTimeout(this.broadcastTimer)
            this.broadcastTimer = setTimeout(() => {
                const current = this.tabs?.find(item => item.id === this.idValue)

                if (!current.openIds) {
                    return
                }
                broadcastManager.postMessage({
                    type: 'reportRelevance',
                    data: {
                        sId: current?.sId,
                        sName: current?.sPatientName,
                        iStudyDate: current?.iStudyDate,
                        sNuclearNum: current?.sNuclearNum,
                    }
                })
            }, 2000)
            
        },
        getTitle(item, desc = false) {
            const customName = (title, obj, isScreenshotContrast) => {
                // 自动重建配置的名称
                if (obj && obj.name) {
                    return obj.name;
                }
                if (isScreenshotContrast) {
                    return '阅图对比';
                }
                // 显示默认名称
                return title;
            }
            let str = ''
            if (desc) {
                str = `患者姓名：${item.sPatientName || ''}\n检查时间：${item.iStudyDate || ''}\n打开描述：${customName(item.title, item.viewportInof, item.isScreenshotContrast)}`
            }else {
                str = `${item.sPatientName || ''} ${item.iStudyDate || ''} ${customName(item.title, item.viewportInof, item.isScreenshotContrast)}`
            }
            return str
        },
        // 广播更新方法
        updateCapture(data) {
            this.$store.state.listenCapture = Object.assign({}, this.$store.state.listenCapture, {
                [data.key]: true
            })
        },
        async getSetDataFromApi() {
            try {
                const SetListResult = await this.$Api.apiSet.findList({
                    condition: {
                    },
                    page: {
                        pageSize: 999
                    }
                })
                let SetList
                if (SetListResult && SetListResult.success) {
                    SetList = SetListResult.data.records || SetListResult.data.recordList || []
                    this.$store.commit('setSystemSetList', SetList)
                }

                // 只有切换配置方案的时候才重置所有localStorage  (已弃用)
                // let needToSetStorage = +localStorage.getItem('needToSetStorage')
    
                let userLocalSetId = localStorage.getItem('configs-userSetId')

                if (!userLocalSetId && SetList[0]) {
                    userLocalSetId = SetList[0].sId

                    // 如果有这个用户创建的配置，优先使用
                    const MySet = SetList.find(item => item.sCreator === this.$store.state.userId)
                    if (MySet) {
                        userLocalSetId = MySet.sId
                    } else {
                        // 临床读取第一条，iIsClinic = 1 的 
                        const ClinicSet = SetList.find(item => item.iIsClinic == 1)
                        if (ClinicSet) {
                            userLocalSetId = ClinicSet.sId
                        }
                    }
                    localStorage.setItem('configs-userSetId', userLocalSetId)
                    // needToSetStorage = 1
                } 


                // if (needToSetStorage > 0) {
                    // console.log('needToSetStorage')

                    const ChosenSet = SetList.find(item => item.sId === userLocalSetId)
                    const ChosenSetJson = ChosenSet.sReadSetJson
                    let resultObj = {}
        
                    if (ChosenSetJson) {
                        try {
                            resultObj = JSON.parse(ChosenSetJson)
                        } catch (error) {
                            this.$message.warning('系统配置导入出错，将使用默认配置')
                            resultObj = null
                        }
                        if (resultObj && resultObj.localSto && resultObj.dbSto) {
                            const res = await setAllStorageInJSON(resultObj)
                            if (res) {
                                // this.$message.success('导入成功，刷新页面后生效！')
                            } else {
                                this.$message.warning('系统配置导入出错，将使用默认配置')
                            }
                        }
                    }

                    // localStorage.setItem('needToSetStorage', 0)

                // }
    
            } catch (error) {
                return true
            }
        },
        /**
         * 如果关闭默认显示定位线，就关闭它
         */
        // setDefaultCrosshairsLines(tabId) {
        //     const status = getConfigByStorageKey('configs-crosshairs-show');
        //     if (!status) {
        //         // 隐藏定位线
        //         this.$store.commit('setReferenceLines', { tabId, hide: true })
        //     }
        // },
        // 获取初始化配置
        getInitConfigs() {
            // 获取 tag 配置
            const modalityTagItems = getConfigByStorageKey('configs-modalityTagItems');
            this.$store.commit('setTagItems', modalityTagItems);
            // 多个浏览器 tab 同步启动信息
            const obj = getConfigByStorageKey('synchronization');
            this.$store.commit('setSynchronization', obj);

            // 阅图布局
            const layoutValue = getConfigByStorageKey('configs-read-default-layout');
            this.$store.commit('setReadDefaultLayout', layoutValue);

            // 是否截图时候弹出备注
            const isRemark = getConfigByStorageKey('setting-remark');
            this.$store.commit('setRemark', isRemark);

            const isReadShowRemark = getConfigByStorageKey('setting-read-show-remark');
            this.$store.commit('setReadShowRemark', isReadShowRemark);

            // 获取默认伪彩配置
            const showRebuild = getConfigByStorageKey('configs-show-rebuild');
            this.$store.commit('setShowRebuild', showRebuild);
            
            // 布局填充设置
            const layoutFill = getConfigByStorageKey('configs-layoutFill');
            this.$store.commit('setLayoutFill', layoutFill);

            // 方向信息
            const showDirection = getConfigByStorageKey('configs-show-direction');
            this.$store.state.showDirection = showDirection;

            // 工具预设
            // 字体实现使用的是 canvas fillText 方法
            const lengthUnit = getConfigByStorageKey('configs-lengthUnit');
            const configInfo = getConfigByStorageKey('configs-tools');
            const configPET = getConfigByStorageKey('configs-PET');
            const dotRadius = getConfigByStorageKey('configs-dot');
            const circleRoiDiameter = getConfigByStorageKey('configs-circleRoi');
            const crosshairsType = getConfigByStorageKey('configs-crosshairs-type');
            const interpolationMode = getConfigByStorageKey('configs-interpolationMode');
            const toolsColor = getConfigByStorageKey('configs-toolsColor');
            const measurementAccuracy = getConfigByStorageKey('configs-measurementAccuracy');

            cornerstoneTools.textStyle.setFont(`${configInfo.fontWeight} ${configInfo.fontSize}px ${configInfo.fontFamily}, Helvetica, Arial, sans-serif`);
            cornerstoneTools.textStyle.setPadding(configInfo.padding || 1);

            cornerstoneTools.toolStyle.setToolWidth(configInfo.toolWidth);
            cornerstoneTools.toolColors.setToolColor(configInfo.toolColor);
            cornerstoneTools.toolColors.setActiveColor(configInfo.toolColor);
            cornerstoneTools.toolColors.setFillColor(configInfo.toolColor);
            cornerstoneTools.toolColors.getColorIfActive = (toolData, toolClass) => {
                if (!toolClass || !toolClass.name) {
                    return configInfo.toolColor
                }
                return toolsColor[toolClass.name] || configInfo.toolColor
            }

            cornerstoneTools.store.state.circleRadius = dotRadius || 6;  // 点测量半径
            cornerstoneTools.store.state.touchProximity = 40;
            cornerstoneTools.store.state.handleRadius = 4;               // 操作点大小
            cornerstoneTools.store.state.reviseSUV = configPET.methodSUV;// SUV 公式选择
            cornerstoneTools.store.state.circleRoiDiameter = circleRoiDiameter;       // 圆直径
            cornerstoneTools.store.state.crosshairsType = crosshairsType;             // 定位线类型
            cornerstoneTools.store.state.interpolationMode = interpolationMode;       // 插值
            cornerstoneTools.store.state.lengthUnit = lengthUnit;        // 长度测量单位
            cornerstoneTools.store.state.measurementAccuracy = measurementAccuracy;        // 	图像测量值小数点后的位数 
            
            // cornerstoneTools.store.state.deleteIfHandleOutsideImage = true; // true 删除超出图像的绘制工具 - 有个问题，cornerstone 获取图像像素数据超过图像区域，是 undefined

            const originalToFixed = Number.prototype.toFixed;

            // 覆盖 toFixed 方法
            Number.prototype.toFixed = function(digits) {
                let d = 0
                if (isNaN(digits) || digits < 0) {
                    // throw new RangeError('Digits must be a non-negative number');
                } else {
                    d = digits
                    if (measurementAccuracy == '1') {
                        d = 1
                    }
                }

                let result = originalToFixed.call(this, d);

                return result;
            };

        },
        setSynchronization(data) {
            this.$store.commit('setSynchronization', data.info);
        },
        // 获取最新截图数据
        // getNewCapture(index) {
        //     this.$store.dispatch('loadStudy').then(() => {
        //         this.$message({
		// 			type: 'info',
		// 			message: '获取新截图',
        //             duration: 1500
		// 		})

        //         const allSeries = Array.from(this.$store.state.seriesMap.values())
        //         let allUids= []
        //         let curOpenSeriesId = []
        //         allSeries.forEach(item => {
        //             const instance = Array.from(item.instanceList.values())
        //             const captrue = instance.filter(item => {
        //                 return this.$fun.isCaptrue(item.sSeriesDescription, item.sSOPClassUID, false, item.imageType);
        //             });
        //             let uids = captrue.map(series => {
        //                 return series.uid
        //             })
        //             allUids = allUids.concat(uids)
        //             curOpenSeriesId.push(item.seriesInfo.sStudyInstanceUID)
        //         })

        //         if (allUids && allUids.length){
        //             this.tabs[index].series   = { uids: allUids }
        //             this.tabs[index].seriesId = curOpenSeriesId
        //             this.tabs[index].reload   = !this.tabs[index].reload
        //         }

        //     })
        // },

        // 设置当前页面加载(加载图像)
        setCurrentLoading(){
            this.currentLoading = true
        },
         // 获取默认显示那个布局
        getDefaultLayoutModule(checkModality) {
            // 默认布局列表
            const defaultLayout = getConfigByStorageKey('configs-default-layout');

            let selectModule = 1
            for (let index = 0; index < defaultLayout.length; index++) {
                const layout = defaultLayout[index];

                // 打开的设备类型区分使用那个模式
                if (layout.key == checkModality && layout.module != '') {
                    selectModule = layout.module
                    break
                }
            }
            return selectModule
        },
        // 显示模块， 通过 url 参数 module 0 阅图 1 重建  5 拖拽模式
        async showMudule(){
            this.$store.commit('addRequestList')

            let searchPatient = this.$fun.searchParse();
            const module = searchPatient.module;

            let idx = Number(module);

            await this.$store.dispatch('loadStudy').then(() => {
                if (idx === 1) {
                    // 重建进入，判断是否设置了打开其它重建模式
                    idx = this.getDefaultLayoutModule(this.$store.state.checkModality)
                }
            })

            this.defaultModule = idx;

            // 没有值，大于显示组件模块，小于组件模块
            if (isNaN(idx) || idx >= this.componentsList.length || idx < 0){
                idx = 0; // 阅图
            }

            // 第一次进入阅图
            let openIds = null;
            if (idx === 0) {
                // 报告系统患者
                if (searchPatient.sPatientId) {
                    openIds = searchPatient.sPatientId;
                }else {
                    // 没有写报告的患者
                    openIds = searchPatient.patientId + searchPatient.accessNo + searchPatient.studyDate;
                }
            }

            // url 是否有属性 pageId 有代表是多开进入
            if (searchPatient.pageId) {
                // 新页面中存储的打开信息，请求信息
                let arr = localStorage.getItem('open-new-page');

                if (!arr) {
                    arr = [];
                }else {
                    arr = JSON.parse(arr);
                }
                const findVal = arr.find(item => item.pageId == searchPatient.pageId );
                // 查找需要打开的序列信息
                if (findVal) {
                    // 附带了，请求参数列表
                    this.$store.state.requestList = findVal.requestList
                    // 多开打开，加载检查
                    this.$store.dispatch('loadStudy').then(() => {
                        this.setCurrentLoading();
                        this.onSelectUid(findVal.info.uids, findVal.info.component, findVal.info.seriesId);

                        // 设置浏览器 tab 显示名称
                        const tab = this.tabs[0];
                        if (tab) {
                            const oTitle = document.getElementsByTagName('title')[0];
                            oTitle.innerText = tab.sPatientName + ' ' + tab.iStudyDate + ' ' + tab.title;
                        }
                        
                    });
                }else {
                    // 没有查找到新浏览器tab打开图像信息。正常打开
                    this.pushTab(idx, 0, openIds);
                }
            }else {
                this.pushTab(idx, 0, openIds);
            }
        },
        pushTab(idx, showIndex = 0, openIds) {
            const id = this.$fun.onlyValue()

            // this.setDefaultCrosshairsLines(id)
            // push 模块
            this.tabs.push(
                {   
                    title: this.componentsName[idx], 
                    id, 
                    component: this.componentsList[idx],
                    reload: idx == 5 ? false : true, // 5 拖拽重建打开，不需要覆盖阅图。
                }
            )

            // 有 openIds 代表截图打开
            if (openIds) {
                const index = this.tabs.length - 1;
                this.tabs[index].openIds = openIds;
                this.tabs[index].isMuch  = true;
                this.tabs[index].reload  = false;
                this.tabs[index].viewportInof = {isOverlayVisible: false};

                const studys = Array.from(this.$store.state.seriesMap.values());
                const study = studys.find(item => item.seriesInfo && item.seriesInfo.key === openIds)
                if (study) {
                    this.tabs[index].iStudyDate = study.seriesInfo.iStudyDate;
                    this.tabs[index].sPatientName = study.seriesInfo.sPatientName;
                }
            }

            // 设置选中，打开模块
            this.idValue = this.tabs[showIndex].id;
        },
        /**
         * 通过 openIds 打开 tab，并且重新加载
         */
        openTabByOpenIds(openIds, reload) {
            const findIdx = this.tabs.findIndex(item => {
                return item.openIds === openIds
            })
            // 发现已打开 tab
            if (findIdx !== -1){
                // 直接跳到该 tabs
                this.idValue = this.tabs[findIdx].id;
                // 重载
                if (reload) {
                    this.tabs[findIdx].reload = !this.tabs[findIdx].reload
                }
                return;
            }else {
                // 删除多余的一个 tab 
                const findIdx = this.tabs.findIndex(item => item.reload);
                if (findIdx != -1) {
                    this.tabs.splice(findIdx, 1);
                    this.currentLoading = false;
                }
                this.pushTab(0, this.tabs.length, openIds);
            }
        },
        /**
         * 选中序列。打开 tabs
         * uids      图像序列的 uids 用于查找展示相应图像
         * component 需要打开相应的组件模块
         * seriesId  序列id，多次检查就会有多个
         * viewportInof 设置视图中的一些信息（不显示覆盖信息层）
         * reload  重新加载 tab
         * notJumpTab 不跳转
         * isScreenshotContrast 是否截图对比
         */
        onSelectUid(uids, component, seriesId, viewportInof = undefined, reload = false, notJumpTab = false, isScreenshotContrast = false){
            
            // MR布局
            if (component === 'ViewMRLayout') {
                const item = this.tabs.find(item => {
                    return item.component === 'ViewMRLayout'
                })
                if (item) {
                    // 已打开，直接跳转
                    this.onClickTab(item.id)
                }else {
                    // 当前 tab 中替换
                    if (this.currentLoading){
                        this.currentLoading = false
                        const idx = this.tabs.findIndex(item => {
                            return item.id == this.idValue
                        })
                        const id = this.$fun.onlyValue()

                        this.tabs.splice(idx, 1, 
                        {   
                            title: this.componentsName[5], 
                            id, 
                            component: this.componentsList[5]
                        })
                        this.idValue = id // 设置选择的值
                        return;
                    }
                    // 新打开一个 tab
                    this.pushTab(5, this.tabs.length) 
                }
                return;
            }

            const isMuchSduty = seriesId instanceof Array
            const seriesInfo = this.$store.state.seriesMap.get(isMuchSduty ? seriesId[0] : seriesId).seriesInfo
            const id = this.$fun.onlyValue();
            let uidStr = '';

            // 结构设计不合理？uids 有可能是 数组类型，对象类型，字符串类型
            if (uids.constructor === Array) {
                uidStr = uids.join();
            }else if (uids.constructor === Object) {
                uidStr = JSON.stringify(uids);
            }else {
                uidStr = uids;
            }
            // 加上当前模块名组成新的唯一值
            uidStr = component + uidStr;
            // 不存在该组件
            if (!this.componentsList.includes(component)){
                this.$message({
                    type: 'warning',
                    message: '不存在模块-error'
                })
                return;
            }
            this.clearTabSelect('Select')

            // 判断是否是已经打开了该序列，如果打开直接跳转到相应 tabs 只有 home 组件才这样
            const findIdx = this.tabs.findIndex(item => {
                // 如果当前模块是截屏图，且重载，阅图组件，直接当做截图刷新
                if (item.isMuch && reload === true && component === 'ReadDcm') {
                    return true
                }
                return item.openIds === uidStr
            })
            // 发现已打开 tab
            if (findIdx !== -1){
                // 直接跳到该 tabs
                this.idValue = this.tabs[findIdx].id;
                // 重载
                if (reload === true) {
                    this.tabs[findIdx].reload = !this.tabs[findIdx].reload
                }
                return;
            }
            const tab = {
                title: this.componentsName[this.componentsList.indexOf(component)],
                id,
                component,
                openIds: uidStr,
                seriesId: seriesId,
                series: { uids },
                iStudyDate: seriesInfo.iStudyDate,
                sPatientName: seriesInfo.sPatientName,
                sId: seriesInfo.key,
                sNuclearNum: seriesInfo.sAccessionNumber,
                reload: false,
                isMuch: false,
                isScreenshotContrast,
                viewportInof
            }
            // this.setDefaultCrosshairsLines(id)
            // 在当前模块更新
            if (this.currentLoading){
                this.currentLoading = false
                const idx = this.tabs.findIndex(item => {
                    return item.id == this.idValue
                })
                this.tabs.splice(idx, 1, tab)
            }else {
                // 打开新的 tabs 模块 
                this.tabs.push(tab)
            }
            // 不跳转 tab
            if (notJumpTab) {
                return;
            }
            this.idValue = id // 设置选择的值

        },
        // 点击 tab 切换
        onClickTab(id){
            this.idValue = id
        },
        // 跳到第一个
        onGoFirst() {
           const id = this.tabs[0] && this.tabs[0].id
           if (id) {
               this.idValue = id
           }
        },
        // 清除 tab
        clearTabSelect(clearTab){
            // 是否存在该组件
            if (this.componentsList.includes(clearTab)){
                const index = this.tabs.findIndex(item => item.component === clearTab)
                if (index != -1){
                    // 删除
                    this.tabs.splice(index, 1)
                }
            }
            const myEvent = new Event('resize');
            window.dispatchEvent(myEvent);
        },
        setTabInfo(tabId, info) {
            this.tabs.some((item, index) => {
                if (item.id === tabId){
                    this.$set(this.tabs[index], 'iStudyDate', info.iStudyDate)
                    this.$set(this.tabs[index], 'sPatientName', info.sPatientName)
                    return true
                }
            })
        },
        // 关闭当前 tab
        closeTab(index){
            this.tabs.splice(index, 1);
            const len = this.tabs.length - 1;
            // 一个tabs都没有了
            if (len === -1){
                // 打开选择弹窗模块
                this.tabs.push(
                    {   
                        title: this.componentsName[3], 
                        id: this.$fun.onlyValue(), 
                        component: this.componentsList[3]
                    }
                )
                this.idValue = this.tabs[0].id
                return;
            }
            this.idValue = index < len ? this.tabs[index].id : this.tabs[len].id
        },
        /**
         * 打开新报告需要处理的事情
         */
        openNewReport(params) {
            // 更新传递的参数
            this.$store.commit('GET_INIT_PARAMS', params)
            // 添加请求列表
            this.$store.commit('addRequestList')

            // 加载数据
            this.$store.dispatch('loadStudy').then(() => {
                // 打开弹窗,并刷新弹窗
                this.$store.commit('SET_OPENSLECTSERIES', { dialogVisible: true, refresh: true })
                setTimeout(() => {
                    const module = Number(params.module)
                    if (module === 1) {
                        // 打开重建匹配
                        event.$emit('findAndOpenRebuild')
                    }else {
                        // 打开阅图
                        let searchPatient = this.$fun.searchParse();
                        let openIds = '';
                        if (searchPatient.sPatientId) {
                            openIds = searchPatient.sPatientId;
                        }else {
                            // 没有写报告的患者
                            openIds = searchPatient.patientId + searchPatient.accessNo + searchPatient.studyDate;
                        }
                        this.pushTab(0, 0, openIds);

                        const id = this.tabs[this.tabs.length - 1].id;
                        if (id) {
                            this.idValue = id;
                        }
                    }
                }, 100);
            }).catch(err => {
                this.$message({
                    type: 'error',
                    message: err.msg,
                    duration: 2000
                })
            })
        },
        // 键盘监听
        startKeyDown(){
            window.onkeydown = (e) => {
                let e1 = e || window.event || arguments.callee.caller.arguments[0];

                // 当前位置是输入框、长文本输入框不做键盘监听
                if (!e1 || (e1.srcElement instanceof HTMLInputElement && e1.srcElement.getAttribute('type') != 'range') || e1.srcElement instanceof HTMLTextAreaElement) { 
                    return; 
                }
                // 112 ~ 118 不要默认行为
                if (e1.keyCode >= 112 && e1.keyCode <= 118) { e1.preventDefault(); }

                // 46 走进删除
                if (e1.keyCode === 46) {
                    event.$emit('onKeyDel', this.idValue);
                }else {
                    event.$emit('onKeyDown', e1, this.idValue);
                }
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.xx-view{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .xx-header{
        display: flex;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        text-align: left;
        background-color: #547495;
        box-shadow: 0 0px 2px 0px #333;

        .tags-view-item{
            position: relative;
            background: #547495;
            display: inline-block;
            padding: 0px 25px 0px 10px;
            color: white;
            // margin-right: 10px;
            cursor: pointer;
            border-right: 1px solid #eee;
            overflow: hidden;
            .text {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            &.i-active{
                background: #d7dae1;
                color: #303133;
                > i{
                    visibility: initial;
                }
                &:hover{
                    color: #142B4B;
                    text-decoration: underline;
                }
            }
            &:hover{
                text-decoration: underline;
                > i{
                    visibility: initial;
                }
            }
            &:last-child{
                margin-right: 0px;
            }
            > i{
                position: absolute;
                right: 7px;
                top: 9px;
                font-size: 15px;
                visibility: hidden;
                width: 14px;
                vertical-align: middle;
                cursor: pointer;
                overflow: hidden;
                transition: width .3s cubic-bezier(.645,.045,.355,1);
                &:hover{
                    border-radius: 50%;
                    background-color: #c0c4cc;
                    color: #fff;
                }
            }
        }

    }
}
.content-box{
    position: absolute;
    top: 32px;
    left: 0;
    width: 100%;
    height: calc(100% - 32px);
    overflow: hidden;
}
.xx-tabs{
    width: 100%;
    display: flex;
}
.i-tip{
    position: absolute;
    top: 50%;
    left: calc(50% - 95px);
    color: #dcdfe6;
}
.loader {
  display: inline-block;
  font-size: 48px;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #dcdfe6;
}
.loader::before {
  content: '';  
  position: absolute;
  left: 30px;
  bottom: 8px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 5px solid #dcdfe6;
  border-bottom-color: #339955;
  box-sizing: border-box;
  animation: rotation 0.6s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 
</style>