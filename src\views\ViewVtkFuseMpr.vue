<template>
	<div class="c-mpr-content pc-body" ref="pcBody">
		
		<AreaFit :isMuch="layoutFill">
		<section class="c-left" ref="center">
			<div v-if="modelType == 1" class="model-1">
				<div class="item-1">
					<View2D
					v-if="viewports[0]"
					:imageId="viewports[0].imageId"
					:modality="viewports[0].modality"
					:volumes="viewports[0].volumes"
					:onCreated="storeApi(0)"
					:orientation="viewports[0].orientation"
					/>
				</div>
				<div class="item-2">
					<div>
						<View2D
						v-if="viewports[1]"
						:imageId="viewports[1].imageId"
						:modality="viewports[1].modality"
						:volumes="viewports[1].volumes"
						:onCreated="storeApi(1)"
						:orientation="viewports[1].orientation"
						/>
					</div>
					<div>
						<View2D
						v-if="viewports[2]"
						:imageId="viewports[2].imageId"
						:modality="viewports[2].modality"
						:volumes="viewports[2].volumes"
						:onCreated="storeApi(2)"
						:orientation="viewports[2].orientation"
						/>
					</div>
				</div>
			</div>
			<div v-else-if="modelType == 2" class="model-2">
				<View2D
				v-for="(item, index) in viewports" :key="item.id"
				:imageId="item.imageId"
				:modality="item.modality"
				:volumes="item.volumes"
				:onCreated="storeApi(index)"
				:orientation="item.orientation"
				/>
			</div>
		</section>
		</AreaFit>
		<section class="c-right">
            <h6>工具</h6>
			<div class="c-item-03 i-tool">
				<div class="i-button" @click="toggleWL('Wwwc')" 
            		:class="[activeTool == 'Wwwc' ? 'i-active' : '']"><i class="iconfont icontiaochuang"></i><span>调窗</span>
				</div>
			 	<!-- <div class="i-button" @click="toggleToCrosshairs('crosshairs')" 
                    :class="[activeTool == 'crosshairs' ? 'i-active' : '']"><i class="iconfont iconmidpoint"></i><span>定位线</span>
				</div>

				</div> -->
				<div class="i-button" @click="toggleToProbe " 
                    :class="[activeTool == 'probe' ? 'i-active' : '']"><i class="iconfont iconct-value"></i><span>点测量</span>
				</div>
				<div class="i-button i-more" @click="toggleToCrosshairs('crosshairs')" :class="[activeTool == 'crosshairs' ? 'i-active' : '']">
                    <div>
						<i class="iconfont iconreset"></i><span>定位线</span>
                    </div>
                    <el-dropdown @command="toggleLineWay" :hide-on-click="false">
                            <span class="el-dropdown-link">
                                    <i class="el-icon-caret-bottom"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="rotation">{{ lineWay == '2' ? '单线' : '联动'}}旋转</el-dropdown-item>
                            </el-dropdown-menu>
                    </el-dropdown>
            	</div>

				<div class="i-button" @click="toggleReset('all')" ><i class="iconfont iconreset"></i><span>重置</span>
				</div>
			</div>

			<div class="c-item-03 c-slider-default i-bottom-line">
				<h6>投影密度</h6>
				<el-slider v-model="slabThickness" vertical height="160px" :step="1" :min="0.1" :max="100" @input="handlesetSlabThickness" :format-tooltip="formatTooltip"></el-slider>
			</div>

			<div class="c-item-03 i-tool">
				

				<el-popover
				placement="left"
				title="备注说明"
				width="200"
				trigger="click"
				v-model="visiblePopover">
					<el-input
						type="textarea" style="border-radius: 0px;"
						:rows="7" v-model="formRemark"
						placeholder="请输入内容" resize="none">
					</el-input>
					<el-button size="mini" type="primary" class="popover-button" @click="onClickRemarkSure">确定</el-button>

					<div slot="reference" class="i-button" @click="onClickSavePrintButton" >
						<i class="elment-ui-icon" :class="buttonAnimation.savePrint"></i>
						<span style="font-size: 12px;">保存截图</span>
					</div>
				</el-popover>
			</div>

		</section>
		<Percentage :visible="percentage.loading" :total="percentage.total" :loadCount="percentage.loadCount"/>
	</div>
</template>
<script>
import { View2D,
 	vtkSVGRotatableCrosshairsWidget,
	vtkInteractorStyleRotatableMPRCrosshairs,
	vtkSVGProbeWidget,
	vtkInteractorStyleProbe,
	vtkInteractorStyleMPRWindowLevel,
	invertVolume
} from "$library/vtk";

import Percentage from '$components/tools/Percentage'

import getVtkVolumeBySeriesUid from "$library/cornerstone/function/getVtkVolumeBySeriesUid.js";
import { loadFrameImage } from "$library/cornerstone/function/getImageIds.js";
import VoiMapping from '$library/cornerstone/function/VoiMapping.js';
// 存放体地方
import appState from '$library/cornerstone/mpr/store/appState.js';

import AreaFit from '$src/layout/AreaFit.vue';

import { throttle } from "lodash-es";

import Printscreen from "$src/mixin/Printscreen.js";

export default {
	name: "ViewVtkFuseMpr",
	mixins: [ Printscreen ],
	components: {
		View2D,
		Percentage,
		AreaFit
	},
    props: {
        series: {
			type: Object,
			default: () => {}
		},
		seriesId: {         // 这个应该是 studyUID
			type: String,
			default: ''
		},
    },
	data() {
		return {
			buttonAnimation: {
				savePrint: 'el-icon-picture',
			},
			formRemark: '',
			visiblePopover: false,
			modelType: 1, // 1 = 冠矢横，2 = 3x3
			rebuildStatus: 'fuse',  // 有二组就叫 fuse ，只有 ct 就叫 CT ，只有 pt 就叫 PT
			studyUID: '', // 
			ctUID: '',
			ptUID: '',
			ctVolumes: {},
			ptVolumes: {},
			fuseVolumes: {},
			slabThickness: 0.1,
			percentage: {
				loading: false,
				loadCount: 0,
				total: 0
			},
			viewports: [],
			apis: [null, null, null],
			groupApis: {},
			activeTool: 'crosshairs',
			timerApi: null,
			lineWay: '1'
		};
	},
	mounted(){
		this.lineWay = localStorage.getItem('configs-rotation-Line-Way') || '1';
		this.init();
	},
	computed: {
		layoutFill() {
            return this.$store.state.layoutFill;
        },
		isRemark() {
            return this.$store.state.isRemark
        }
	},
	methods: {
		onClickSavePrintButton() {
			// 加载中
            if (this.buttonAnimation.savePrint === 'el-icon-loading') {
                // 不填备注
                this.visiblePopover = true // 不知道为啥 element-ui true 就不弹窗了
                return
            }
            // 可填写备注
            if (this.isRemark) {
                this.formRemark = ''
                return
            }
            // 不填备注
            this.visiblePopover = true
			
			this.emitSavePrint()
			
		},
		// 点击备注确认
        onClickRemarkSure() {
            this.visiblePopover = false
            this.emitSavePrint()
        },
		emitSavePrint() {
			this.buttonAnimation.savePrint = 'el-icon-loading'
			this.$nextTick(() => {
				this.onClickSavePrint(() => {
                    this.buttonAnimation.savePrint = 'el-icon-picture'
                }, this.formRemark);
            })
		},
		init() {
			const uids = this.series && this.series.uids;
			if (!uids) return;

			this.studyUID  = this.seriesId;
			// 重建的序列 instanceUID
			this.ctUID = uids.ct;
			this.ptUID = uids.pet;

			if (this.ctUID && this.ptUID) {
				this.rebuildStatus = 'fuse';
			}else if(this.ptUID) {
				this.rebuildStatus = 'pt';
			}else {
				this.rebuildStatus = 'ct';
			}
			
			// 加载体
			this.loadData().then(res => {
				this.loadDataSuccess(res)
			})
		},
		loadDataSuccess(res) {
			if (res && res.code === 200) {
				this.apis = [null, null, null]; // 清除

				if (this.rebuildStatus === 'fuse') {
					this.modelType = 1;
					this.rebuildStatus = 'fuse';
					this.setModelData1(this.rebuildStatus);
				}else {
					// 不健全的融合重建
					this.modelType = 1;
					this.setModelData1(this.rebuildStatus);
				}

			}
		},
		setModelData1(type) {
			this.viewports.splice(0);

			this.viewports.push({
				id: type + '1',
				imageId: this[`${type}Volumes`].imageId,
				modality: this[`${type}Volumes`].modality,
				volumes: this[`${type}Volumes`].volumes,
				orientation: { sliceNormal: [0, 0, 1], viewUp: [0, -1, 0] }
			});
			this.viewports.push({
				id: type + '2',
				imageId: this[`${type}Volumes`].imageId,
				modality: this[`${type}Volumes`].modality,
				volumes: this[`${type}Volumes`].volumes,
				orientation: { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: type + '3',
				imageId: this[`${type}Volumes`].imageId,
				modality: this[`${type}Volumes`].modality,
				volumes: this[`${type}Volumes`].volumes,
				orientation: { sliceNormal: [-1, 0, 0], viewUp: [0, 0, 1] }
			});
		},
		setModelData2() {
			this.viewports.splice(0);

			this.viewports.push({
				id: '1',
				modality: this.ptVolumes.modality,
				volumes: this.ptVolumes.volumes,
				orientation: { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: '2',
				modality: this.ptVolumes.modality,
				volumes: this.ptVolumes.volumes,
				orientation: { sliceNormal: [1, 0, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: '3',
				modality: this.ptVolumes.modality,
				volumes: this.ptVolumes.volumes,
				orientation: { sliceNormal: [0, 0, 1], viewUp: [0, -1, 0] }
			});

			this.viewports.push({
				id: '4',
				modality: this.ctVolumes.modality,
				volumes: this.ctVolumes.volumes,
				orientation: { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: '5',
				modality: this.ctVolumes.modality,
				volumes: this.ctVolumes.volumes,
				orientation: { sliceNormal: [1, 0, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: '6',
				modality: this.ctVolumes.modality,
				volumes: this.ctVolumes.volumes,
				orientation: { sliceNormal: [0, 0, 1], viewUp: [0, -1, 0] }
			});

			this.viewports.push({
				id: '7',
				modality: this.fuseVolumes.modality,
				volumes: this.fuseVolumes.volumes,
				orientation: { sliceNormal: [0, 1, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: '8',
				modality: this.fuseVolumes.modality,
				volumes: this.fuseVolumes.volumes,
				orientation: { sliceNormal: [1, 0, 0], viewUp: [0, 0, 1] }
			});
			this.viewports.push({
				id: '9',
				modality: this.fuseVolumes.modality,
				volumes: this.fuseVolumes.volumes,
				orientation: { sliceNormal: [0, 0, 1], viewUp: [0, -1, 0] }
			});
		},
		/**
         * 初始体相应序列。如果有体返回空。没有体，返回需要下载的序列 id
         */
        setVolumesSeries(studyUID, seriesUID) {
            
            if (!studyUID && !seriesUID) {
                return []
            }

            // 没有这个体信息，设置空对象
            if (!appState[seriesUID]) {
                appState[seriesUID] = {}
            }

            // 没有体数据, 添加体数据相应的序列
            if (!appState[seriesUID].vtkVolumes) {
                const instanceList = this.$store.state.seriesMap.get(studyUID).instanceList
                const imageIds = instanceList.get(seriesUID).imageIds

                appState[seriesUID].series = imageIds

                return imageIds
            }

            return []
        },
		async setVolumesData() {
			// CT 体
			if (this.ctUID) {
				await getVtkVolumeBySeriesUid(this.ctUID, '', true).then(res => {
					const { volumes, modality, imageId } = res;

					this.ctVolumes.imageId = imageId;
					this.ctVolumes.volumes = volumes;
					this.ctVolumes.modality = modality;
				});
			}else {
				this.ctVolumes = {};
			}
			
			// PT 体
			if (this.ptUID) {
				await getVtkVolumeBySeriesUid(this.ptUID, '', true).then(res => {
					const { volumes, modality, imageId } = res;

					this.ptVolumes.imageId = imageId;
					this.ptVolumes.volumes = volumes;
					this.ptVolumes.modality = modality;
				});
			}else {
				this.ptVolumes = {};
			}
			// 融合
			if (this.ctUID && this.ptUID) {
				await getVtkVolumeBySeriesUid(this.ptUID, this.ctUID, true).then(res => {
					const { volumes, modality, imageId } = res;

					this.fuseVolumes.imageId = imageId;
					this.fuseVolumes.volumes = volumes;
					this.fuseVolumes.modality = modality;
				})
			}else {
				this.fuseVolumes = {};
			}

			return {
				code: 200,
				msg: 'ok'
			}
		},
		// 加载图像数据
		async loadData() {
			this.percentage.loading = true;
			this.percentage.loadCount = 0;
			this.percentage.total = 0;

			let ctPromises = [], ptPromises = [];
			let ctImageIds = [], ptImageIds = []

			// 是否有重建 CT
			if (this.ctUID) {
				ctImageIds = this.setVolumesSeries(this.studyUID, this.ctUID);

				await loadFrameImage(ctImageIds);

				this.percentage.total += ctImageIds.length;

				if (!window.configs.startLimitDownload) {
					ctPromises = ctImageIds.map((imageId) => {
						return cornerstone.loadAndCacheImage(imageId).then(() => {
							this.percentage.loadCount += 1
						});
					});
				}
			}

			// 是否有重建 PT
			if (this.ptUID) {
				ptImageIds = this.setVolumesSeries(this.studyUID, this.ptUID);

				await loadFrameImage(ptImageIds);

				this.percentage.total += ptImageIds.length;

				if (!window.configs.startLimitDownload) {
					ptPromises = ptImageIds.map((imageId) => {
						return cornerstone.loadAndCacheImage(imageId).then(() => {
							this.percentage.loadCount += 1;
						});
					});
				}	

			}

			if (window.configs.startLimitDownload) {	
				const requestType = 'prefetch';
				const preventCache = false;

				if (this.percentage.total === 0) {
					this.percentage.total = 1;
					this.percentage.loadCount = 1;
					setTimeout(() => {
						// 设置 volumes
						this.setVolumesData().then(res => {
							this.percentage.loading = false
							this.loadDataSuccess(res)
						})
					}, 100)
				}

				ctImageIds.concat(ptImageIds).forEach(imageId => {
					cornerstoneTools.requestPoolManager.addRequest(
						{},
						imageId,
						requestType,
						preventCache,
						() => {
							this.percentage.loadCount += 1
							if (this.percentage.loadCount >= this.percentage.total) {
								setTimeout(() => {
									// 设置 volumes
									this.setVolumesData().then(res => {
										this.percentage.loading = false
										this.loadDataSuccess(res)
									})
								}, 100)
							}
						},
						(imageId) => {
							console.log('下载错误，错误请求头携带获取最新图像..')
							// 不应该这样做
							this.percentage.loadCount += 1
							if (this.percentage.loadCount >= this.percentage.total) {
								setTimeout(() => {
									// 设置 volumes
									this.setVolumesData().then(res => {
										this.percentage.loading = false
										this.loadDataSuccess(res)
									})
								}, 100)
							}
						}
					);
				});
				cornerstoneTools.requestPoolManager.startGrabbing();
			}else {
				// 下载
				return new Promise(resolve => {
					Promise.all(ctPromises.concat(ptPromises)).then(() => {
						
						// 图已经加载过了，让进度走到 100%
						if (this.percentage.total === 0) {
							this.percentage.total = 1;
							this.percentage.loadCount = 1;
						}
						setTimeout(() => {
							// 设置 volumes
							this.setVolumesData().then(res => {
								this.percentage.loading = false;
								resolve(res);
							})
						}, 100);

					}).catch(err => {
						resolve({
							code: 201,
							msg: err
						})
					})
				})
			}

		},
		// 更新全部视图
		updateAllViewports(isInvert) {
			Object.keys(this.apis).forEach((viewportIndex) => {
				const api = this.apis[viewportIndex];
				if (!api) return
				api.genericRenderWindow.setBackground(isInvert ? [255, 255, 255] : [0, 0, 0]);
				api.genericRenderWindow.getRenderWindow().render();
			});
		},
		// 2d view 组件数据返回，用来保存2d组件信息
		storeApi(viewportIndex) {
			return (api) => {

				if (!this.groupApis[api.modality]) {
					this.groupApis[api.modality] = [];
				}
				this.groupApis[api.modality].push(api);

				this.apis[viewportIndex] = api;

				const apis = this.apis;

				// 获取渲染的窗口
				const renderWindow = api.genericRenderWindow.getRenderWindow();

				// 适应放大，不能放在下面
				this.$fun.fill2DView(api.genericRenderWindow, api.getSliceNormal());

				// 设置最大密度投影
				const mapper = api.volumes[0].getMapper();
				if (mapper.setBlendModeToMaximumIntensity) {
					mapper.setBlendModeToMaximumIntensity();
				}

				// 层厚
				api.setSlabThickness(this.slabThickness);


				if (['PET', 'PT', 'pet', 'NM', 'nm'].includes(api.modality)) {
					// invertVolume(api.volumes[0], this.updateAllViewports);
					api.changeInvert(true)
					api.changeMipColormap('hot')
				}
				
				// 渲染窗口
				renderWindow.render();

				clearTimeout(this.timerApi);
				this.timerApi = setTimeout(() => {

					const every = apis.every(item => item)

					if (every) {
						this.groupApis = Object.values(this.groupApis);
						// 开启定位线
						// 按照组区分
						this.groupApis.forEach((apis, groupIndex) => {
							// 组加组的工具
							apis.forEach((api, index) => {

								// 添加 svg 工具- 定位线名称 crosshairsWidget...用于控制显示等
								api.addSVGWidget(
									vtkSVGRotatableCrosshairsWidget.newInstance(),
									"rotatableCrosshairsWidget"
								);

								api.addSVGWidget(
									vtkSVGProbeWidget.newInstance(),
									"probeWidget"
								);

								// 操作功能
								const istyle = vtkInteractorStyleRotatableMPRCrosshairs.newInstance();
								api.setInteractorStyle({
									istyle,
									configuration: { apis, apiIndex: index },
								});

								api.svgWidgets.rotatableCrosshairsWidget.setApiIndex(index);
								api.svgWidgets.rotatableCrosshairsWidget.setApis(apis);
								api.svgWidgets.rotatableCrosshairsWidget.setGroupIndex(groupIndex);

								if (this.modelType == 2) {
									api.widgetManager.onModified(() => {
										this.triggerScaleSyanc(groupIndex, index);
									});
									api.svgWidgets.rotatableCrosshairsWidget.onModified(throttle((res) => {
										this.triggerCrosshairsSyanc(res);
									},30));
								}

							})
							apis[0].svgWidgets.rotatableCrosshairsWidget.resetCrosshairs(apis, 0);
						})
						
					}
				}, 100);
				
			}
		},
		// 缩放平移
		triggerScaleSyanc(sourceGroupIndex, sourceApiIndex) {
			// 当前触发源
			const api = this.groupApis[sourceGroupIndex][sourceApiIndex];
			const sourceCamera = api.genericRenderWindow.getRenderer().getActiveCamera();
			const sourceWorldPosition = api.get('cachedCrosshairWorldPosition');

			// window.sourceCamera = sourceCamera;
			// window.w = api.genericRenderWindow;

			const viewUp = sourceCamera.getViewUp();
			const viewPlaneNormal = sourceCamera.getViewPlaneNormal();
			const clippingRange = sourceCamera.getClippingRange();
			const position = sourceCamera.getPosition();
			const focalPoint = sourceCamera.getFocalPoint();
			const parallelScale = sourceCamera.getParallelScale();
			const viewAngle = sourceCamera.getViewAngle();


			this.groupApis.forEach((apis, groupIndex) => {
				// 不同组
				if (groupIndex != sourceGroupIndex) {
					// 不同组,相同位置(apiIndex);
					const targetApi = apis[sourceApiIndex];

					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setViewUp(viewUp);
					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setDirectionOfProjection(-viewPlaneNormal[0], -viewPlaneNormal[1], -viewPlaneNormal[2]);
					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setClippingRange(clippingRange);
					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setPosition(...position);
					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setFocalPoint(...focalPoint);
					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setParallelScale(parallelScale);
					targetApi.genericRenderWindow.getRenderer().getActiveCamera().setViewAngle(viewAngle);

					targetApi.svgWidgets.rotatableCrosshairsWidget.updateCrosshairForApi(targetApi);

					targetApi.genericRenderWindow.getRenderWindow().render();
					targetApi.svgWidgets.rotatableCrosshairsWidget.moveCrosshairs(sourceWorldPosition, apis);
				}
			})
		},
		triggerCrosshairsSyanc(sourceWidgets) {
			// console.log('123')
			if (sourceWidgets) {
				const sourceGroupIndex = sourceWidgets.getGroupIndex();
				const sourceApiIndex = sourceWidgets.getApiIndex();
				const sourceLines = sourceWidgets.getReferenceLines();

				if (sourceWidgets.getActionType() === 1 || sourceWidgets.getActionType() === 2) {
					// 拖拽十字线、拖拽单条线
					const api = this.groupApis[sourceGroupIndex][sourceApiIndex];

					// window.api = api;
					// window.sourceWidgets = sourceWidgets;

					const sourceWorldPosition = api.get('cachedCrosshairWorldPosition');

					this.groupApis.forEach((apis, groupIndex) => {
						if (groupIndex != sourceGroupIndex) {
							
							const targetApi = apis[sourceApiIndex];

							const targetWorldPosition = targetApi.get('cachedCrosshairWorldPosition');
						
							const rotatableCrosshairsWidget = apis[sourceApiIndex].svgWidgets.rotatableCrosshairsWidget;

							if (rotatableCrosshairsWidget && targetWorldPosition != sourceWorldPosition) {
								rotatableCrosshairsWidget.moveCrosshairs(sourceWorldPosition, apis);
							};
						}
					})
				}else if (sourceWidgets.getActionType() === 0) {
					// 旋转十字线
					const line = sourceLines.find(line => {
						if (line.active) {
							return line;
						}
					})
					if (line) {
						this.triggerScaleSyanc(sourceGroupIndex, line.apiIndex);
						// const targetApi = this.groupApis[sourceGroupIndex][line.apiIndex];
						// targetApi.svgWidgets.rotatableCrosshairsWidget.updateCrosshairForApi(targetApi);
					}
				}
				
			}
		},
		toggleReset(){
			const group = this.groupApis;
			group.forEach(apis => {
				apis.forEach((api, index) => {
					const renderWindow = api.genericRenderWindow.getRenderWindow();
					
					// 重置摄像头
					const renderer = api.genericRenderWindow.getRenderer();
					renderer.resetCamera();

					this.$fun.fill2DView(api.genericRenderWindow, api.getSliceNormal());

					// 重置窗宽窗位
					this.defaultWWc(api.volumes[0], api.imageId, renderWindow);

					// 重置方向
					api.resetOrientation();

					// 重置定位线
					// this.toggleToCrosshairs('crosshairs');
					api.svgWidgets.rotatableCrosshairsWidget.resetCrosshairs(apis, index);

					renderWindow.render();
				})
			})
		},
		async defaultWWc(actor, imageId, renderWindow) {
			if (!imageId) {
				return;
			}
			await cornerstone.loadAndCacheImage(imageId);

			let metaSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId);
    		const modality = metaSeriesModule.modality;

			let { windowWidth, windowCenter } = VoiMapping.getVoi(imageId);

			if (modality === 'CT') {
				windowWidth = 360;
        		windowCenter = 1060;
			}

			const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter);
			const rgbTransferFunction = actor.getProperty().getRGBTransferFunction(0);
			rgbTransferFunction.setMappingRange(lower, upper);

			renderWindow.render();

		},
		// 窗口窗位
		toggleWL(name) {
			this.activeTool = name
			const apis = this.apis;

			apis.forEach(api => {
				let istyle = vtkInteractorStyleMPRWindowLevel.newInstance();
				
				const callbacks = {
					setOnLevelsChanged: voi => {
                        const { windowWidth, windowCenter } = voi;
                        Object.keys(this.apis).forEach((viewportIndex) => {
                            const api = this.apis[viewportIndex];
                            api.updateVOI(windowWidth, windowCenter)
                            api.genericRenderWindow.getRenderWindow().render();
                        });
					}
				}
				
				api.setInteractorStyle({
					istyle,
					callbacks,
				});
			});
		},
		toggleLineWay() {
			this.lineWay = this.lineWay == '1' ? '2' : '1';
			localStorage.setItem('configs-rotation-Line-Way', this.lineWay);
		},
		// 定位线
		toggleToCrosshairs(name) {
            this.activeTool = name;

			const group = this.groupApis;
			group.forEach(apis => {
				apis.forEach((api, apiIndex) => {
					const { svgWidgetManager } = api;
					svgWidgetManager.render();
					let istyle = vtkInteractorStyleRotatableMPRCrosshairs.newInstance();
					// add istyle
					api.setInteractorStyle({
						istyle,
						configuration: { apis, apiIndex },
					});
				})
			})
		},
		toggleToProbe() {
			this.activeTool = 'probe';
            const apis = this.apis;
            apis.forEach((api, apiIndex) => {
                const { svgWidgetManager, svgWidgets } = api;
                svgWidgetManager.render();

                let istyle = vtkInteractorStyleProbe.newInstance();
                // // add istyle
                api.setInteractorStyle({
                	istyle,
                	configuration: { apis, apiIndex },
                }); 

            });

			// const group = this.groupApis;
			// group.forEach(apis => {
			// 	apis.forEach((api, apiIndex) => {
			// 		const { svgWidgetManager, svgWidgets } = api;
			// 		svgWidgetManager.render();

			// 		let istyle = vtkInteractorStyleProbe.newInstance();
			// 		// // add istyle
			// 		api.setInteractorStyle({
			// 			istyle,
			// 			configuration: { apis, apiIndex },
			// 		}); 
			// 	})
			// })
		},
		// 设置投影密度
		handlesetSlabThickness(valueInMM){
            this.apis.forEach(api => {
				if (!api) return
                const renderWindow = api.genericRenderWindow.getRenderWindow();

                api.setSlabThickness(valueInMM);
                renderWindow.render();
            });
		},
		formatTooltip(val) {
            return val + 'mm';
		}
	},
};
</script>
<style lang="scss" scoped>
.c-mpr-content {
	position: relative;
	height: 100%;
	display: flex;
	overflow: hidden;
	.c-left {
		position: relative;
		height: 100%;
		background: #eee;
		flex: 1;
		display: flex;
		background-color: black;
		> div{
			position: relative;
            // height: 100%;
            // width: 100%;
            display: flex;
            flex-wrap: wrap;
		}
		.model-1{
			position: relative;
			flex: 1;
			height: 100%;
			display: block;
			.item-1{
				position: relative;
				height: 100%;
				width: 50%;
				float: left;
				.vtkviewport {
					border: 1px solid #444;
					border-right: none;
				}
			}
			.item-2{
				position: relative;
				height: 100%;
				width: 50%;
				float: left;
				> div {
					height: 50%;
					box-sizing: border-box;
    				border: 1px solid #444;
				}
			}
		}
		.model-2{
			display: flex;
			> div{
				width: 33.3333%;
            	height: 33.3333%;
				box-sizing: border-box;
				border: 1px solid #444;
			}
		}
	}
	.c-right {
		width: 70px;
		height: 100%;
		background: white;
		.title{
			background: #eee;
			height: 30px;
			line-height: 30px;
			text-align: center;
		}
	}
}
h6{
	padding: 6px 0px;
	background: #6294B7;
	color: white;
}
.c-slider-default{
    padding-bottom: 10px;
    padding-top: 0px !important;
    .el-slider{
        padding-left: 16px;
		margin-top: 10px;
    }
}
.c-mpr-content .c-right .c-item-03 .i-button{
	margin-left: 0px;
	margin-bottom: 10px;
	.elment-ui-icon {
		display: block;
		height: 30px;
		line-height: 30px;
		font-size: 18px;
	}
}
.c-item-03{
	padding-top: 10px;
	padding-bottom: 10px;
	&.i-tool{
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 20px;
	}
}
.i-more{
	display: flex;
    align-items: center;
	.el-dropdown{
		position: absolute;
		right: 0px;
		color: #dcdfe6;
	}
}
.popover-button{
    width: 100%;
    margin-top: 6px;
    text-align: center;
}
</style>
