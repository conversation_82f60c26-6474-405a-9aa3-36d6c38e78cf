<template>
  <div class='vtkviewport'>
    <div ref='container' class='vtkviewport' />
  </div>
</template>

<script>
import vtkGenericRenderWindow from 'vtk.js/Sources/Rendering/Misc/GenericRenderWindow';
import vtkWidgetManager from 'vtk.js/Sources/Widgets/Core/WidgetManager';
// import vtkPaintFilter from 'vtk.js/Sources/Filters/General/PaintFilter';
// import vtkPaintWidget from 'vtk.js/Sources/Widgets/Widgets3D/PaintWidget';

import { ViewTypes } from 'vtk.js/Sources/Widgets/Core/WidgetManager/Constants';
import { createSub } from '../lib/createSub.js';
import createLabelPipeline from './createLabelPipeline';
import './viewport.css';

import 'vtk.js/Sources/Rendering/OpenGL/Profiles/All';
import 'vtk.js/Sources/Rendering/WebGPU/Profiles/All';

export default {
  name: 'view-3d',
  props: {
    volumes: { type: Array },
    actors: { type: Array },
    painting: { type: Boolean, default: false },
    paintFilterBackgroundImageData: { type: Object },
    paintFilterLabelMapImageData: { type: Object },
    onPaint: { type: Function },
    onPaintStart: { type: Function },
    onPaintEnd: { type: Function },
    sliceNormal: {
      type: Array,
      default() {
        return [0, 0, 1];
      },
    },
    dataDetails: { type: Object },
    onCreated: { type: Function },
    onDestroyed: { type: Function },
    labelmapRenderingOptions: {
      type: Object,
      default() {
        return { visible: true, renderOutline: false };
      },
    },
  },

  data() {
    return {
      container: {},
      subs: {
        interactor: createSub(),
        data: createSub(),
        labelmap: createSub(),
        paint: createSub(),
        paintStart: createSub(),
        paintEnd: createSub(),
      },
    };
  },

  beforeCreate() {},

  created() {
  },

  beforeMount() {},

  mounted() {
    this.genericRenderWindow = vtkGenericRenderWindow.newInstance({
      background: [0, 0, 0],
    });
    // this.genericRenderWindow = vtkGenericRenderWindow.newInstance();

    this.container = this.$refs.container;
    this.genericRenderWindow.setContainer(this.container);

    let widgets = [];
    let filters = [];
    let actors = [];
    let volumes = [];

    const radius = 5;
    const label = 1;

    this.renderer = this.genericRenderWindow.getRenderer();
    this.renderWindow = this.genericRenderWindow.getRenderWindow();

    this.widgetManager = vtkWidgetManager.newInstance();
    this.widgetManager.disablePicking();
    this.widgetManager.setRenderer(this.renderer);
    // this.paintWidget = vtkPaintWidget.newInstance();
    // this.paintWidget.setRadius(radius);
    // this.paintFilter = vtkPaintFilter.newInstance();
    // this.paintFilter.setLabel(label);
    // this.paintFilter.setRadius(radius);

    // trigger pipeline update
    // this.componentDidUpdate({});

    if (this.actors) {
      actors = actors.concat(this.actors);
    }

    if (this.labelmap && this.labelmap.actor) {
      actors = actors.concat(this.labelmap.actor);
    }

    if (this.volumes) {
      volumes = volumes.concat(this.volumes);
    }

    filters = [this.paintFilter];
    widgets = [this.paintWidget];

    // must be added AFTER the data volume is added so that this can be rendered in front
    if (this.labelmap && this.labelmap.actor) {
      this.renderer.addVolume(this.labelmap.actor);
    }

    if (this.volumes) {
      // this.isInited = true;
      this.updateVolumesForRendering(this.volumes);

      this.renderer.resetCamera();
      this.renderer.updateLightsGeometryToFollowCamera();

      // TODO: Not sure why this is necessary to force the initial draw
      this.genericRenderWindow.resize();
    }

    if (this.onCreated) {
      /**
       * Note: The contents of this Object are
       * considered part of the API contract
       * we make with consumers of this component.
       */
      const api = {
        _component: this, // Backdoor still open for now whilst the API isn't as mature as View2D.
        type: 'VIEW3D',
        genericRenderWindow: this.genericRenderWindow,
        widgetManager: this.widgetManager,
        container: this.container,
        widgets,
        filters,
        volumes,
        actors,
      };

      this.onCreated(api);
    }
  },

  beforeUpdate() {},

  updated() {},

  beforeDestroy() {
    Object.keys(this.subs).forEach((k) => {
      this.subs[k].unsubscribe();
    });

    if (this.onDestroyed) {
      this.onDestroyed();
    }

    this.genericRenderWindow.delete();
  },

  destroyed() {},

  methods: {
    setOrientation(sliceNormal, viewUp) {
      // console.log(sliceNormal, viewUp) // 设置选择角度？
      const renderWindow = this.genericRenderWindow.getRenderWindow();
      const currentIStyle = renderWindow.getInteractor().getInteractorStyle();

      // this.updateRotationRelativeToOrientation(sliceNormal);

      currentIStyle.setSliceOrientation(sliceNormal, viewUp);
    },
    updateVolumesForRendering(volumes) {
      if (volumes) {
        volumes.forEach((volume) => {
          if (!volume.isA('vtkVolume')) {
            console.warn('Data to <Vtk2D> is not vtkVolume data');
          }
        });

        if (volumes.length) {
            volumes.forEach(this.renderer.addVolume);
          } else {
            // TODO: Remove all volumes
          }
      }

      this.renderWindow.render();
    },
  },

  watch: {
    volumes(newVolumes, prevVolumes) {
      if (prevVolumes !== newVolumes) {
        newVolumes.forEach((volume) => {
          if (!volume.isA('vtkVolume')) {
            console.warn('Data to <Vtk2D> is not vtkVolume data');
          }
        });

        if (newVolumes.length) {
          newVolumes.forEach(this.renderer.addVolume);
        } else {
          // TODO: Remove all volumes
        }

        this.renderWindow.render();
      }
    },
    actors(newActors, prevActors) {
      if (prevActors !== newActors && newActors) {
        newActors.forEach((actor) => {
          if (!actor.isA('vtkActor')) {
            console.warn('Data to <Vtk2D> is not vtkActor data');
          }
        });

        if (newActors.length) {
          newActors.forEach(this.renderer.addActor);
        } else {
          // TODO: Remove all actors
        }

        this.renderWindow.render();
      }
    },
    paintFilterBackgroundImageData(newBackground, prevBackground) {
      if (!prevBackground && newBackground) {
        // re-render if data has updated
        this.subs.data.sub(
          newBackground.onModified(() => this.renderWindow.render())
        );
        this.paintFilter.setBackgroundImage(newBackground);
      } else if (prevBackground && !newBackground) {
        this.paintFilter.setBackgroundImage(null);
        this.subs.data.unsubscribe();
      }
    },
    paintFilterLabelMapImageData(newLabel, prevLabel) {
      if (prevLabel !== newLabel && newLabel) {
        this.subs.labelmap.unsubscribe();

        const labelmapImageData = newLabel;
        const labelmap = createLabelPipeline(
          this.paintFilterBackgroundImageData,
          labelmapImageData,
          this.labelmapRenderingOptions,
          false
        );

        this.labelmap = labelmap;

        labelmap.mapper.setInputConnection(this.paintFilter.getOutputPort());

        // You can update the labelmap externally just by calling modified()
        this.paintFilter.setLabelMap(labelmapImageData);
        this.subs.labelmap.sub(
          labelmapImageData.onModified(() => {
            labelmap.mapper.modified();

            this.renderWindow.render();
          })
        );
      }
    },
    painting(newPainting, prevPainting) {
      if (prevPainting !== newPainting) {
        if (newPainting) {
          console.time('turnOnPainting');
          this.viewWidget = this.widgetManager.addWidget(
            this.paintWidget,
            ViewTypes.VOLUME
          );
          this.subs.paintStart.sub(
            this.viewWidget.onStartInteractionEvent(() => {
              this.paintFilter.startStroke();
              this.paintFilter.addPoint(
                this.paintWidget.getWidgetState().getTrueOrigin()
              );
              if (this.onPaintStart) {
                this.onPaintStart();
              }
            })
          );
          this.subs.paint.sub(
            this.viewWidget.onInteractionEvent(() => {
              if (this.viewWidget.getPainting()) {
                this.paintFilter.addPoint(
                  this.paintWidget.getWidgetState().getTrueOrigin()
                );
                if (this.onPaint) {
                  this.onPaint();
                }
              }
            })
          );
          this.subs.paintEnd.sub(
            this.viewWidget.onEndInteractionEvent(() => {
              const strokeBufferPromise = this.paintFilter.endStroke();

              if (this.onPaintEnd) {
                strokeBufferPromise.then((strokeBuffer) => {
                  this.onPaintEnd(strokeBuffer);
                });
              }
            })
          );

          this.widgetManager.grabFocus(this.paintWidget);
          this.widgetManager.enablePicking();
          console.timeEnd('turnOnPainting');
        } else if (this.viewWidget) {
          console.time('turnOffPainting');
          this.widgetManager.releaseFocus();
          this.widgetManager.removeWidget(this.paintWidget);
          this.widgetManager.disablePicking();

          this.subs.paintStart.unsubscribe();
          this.subs.paint.unsubscribe();
          this.subs.paintEnd.unsubscribe();
          this.viewWidget = null;
          console.timeEnd('turnOffPainting');
        }
      }
    },
  },

  computed: {
    voi() {
      let voi = {
        windowCenter: 0,
        windowWidth: 0,
      };

      if (this.pipeline) {
        const actor = this.volumes[0];

        // Note: This controls window/level
        const rgbTransferFunction = actor
          .getProperty()
          .getRGBTransferFunction(0);
        const range = rgbTransferFunction.getMappingRange();
        const windowWidth = range[0] + range[1];
        const windowCenter = range[0] + windowWidth / 2;

        voi = {
          windowCenter,
          windowWidth,
        };
      }

      return voi;
    },

    zoom() {
      return 1.2;
    },
  },
};
</script>
