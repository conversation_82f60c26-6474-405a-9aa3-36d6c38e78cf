<template>
    <div saveimage="true" ref="root">
        <div :class="['i-left-center','i-overlay', textColor]" v-if="showDirection">{{ position.left }}</div>
        <div :class="['i-left-center', 'i-overlay', textColor]" id="i-scale" v-html="scaleHtml" ref="scale"></div>

        <div :class="['i-top-center','i-overlay', textColor]" v-if="showDirection">{{ position.top }}</div>
    </div>
</template>
<script>
import appState from '$library/cornerstone/mpr/store/appState.js';

export default {
    props: {
        imageId: {
            type: String,
            default: ''
        },
        detail: {
            type: Object,
            default: () => {}
        },
        scaleDetail: {
            type: Object,
            default: () => {}
        },
        invert: {
            type: Boolean,
            default: false
        },
        state: {
            type: Object,
            default: () => {}
        },
        
    },
    watch: {
        imageId(later){
            if(later != ''){
                this.renderOrientation()
            }
        },
        detail: {
            handler(){
                this.renderOrientation()
            },
            deep: true,
        },
        scaleDetail: {
            handler(){
                this.renderScale()
            },
            deep: true,
        }
    },
    data() {
        return {
            scaleHtml: '',
            position: {
                left: "",
                top: ""
            }
        }
    },
    computed: {
        textColor() {
            if (this.invert) return 'black'
            return 'white'
        },
        showDirection() {
            return this.$store.state.showDirection;
        }
    },
    mounted() {
        // 不加，隐藏在显示，会不见
        this.renderOrientation()
        if (Object.keys(this.scaleDetail).length) {
            this.renderScale();
        }
    },
    methods: {
        async renderOrientation(){
            let rowCosines, columnCosines
            if (this.imageId.includes('mpr')) {
                const iop = this.imageId.split(':')[2]?.split(',')?.map(Number)
                rowCosines = iop?.slice(0,3)
                columnCosines = iop?.slice(3)
            } else {
                const metaData = await cornerstone.metaData.get('imagePlaneModule', this.imageId) || {};
                rowCosines = metaData.rowCosines
                columnCosines = metaData.columnCosines
            }
            
            let position = this.$fun.getOrientationMarkers(
                rowCosines, 
                columnCosines, 
                this.detail.rotationDegrees, 
                this.detail.isFlippedVertically, 
                this.detail.isFlippedHorizontally)
            this.position = position
        },
        renderScale(){
            const scaleDom = this.$refs.root;
            const width = scaleDom.parentElement.clientWidth / 1.5;
            const horizonIntervalScale = (10.0 / this.scaleDetail.rowPixelSpacing) * this.scaleDetail.scale;

            if (horizonIntervalScale === Infinity || !this.scaleDetail.rowPixelSpacing){
                this.scaleHtml = '';
                return;
            }
            let str = '<ul>', len = 0;
            for (let index = 0; index < 10; index++) {
                if (horizonIntervalScale * (index + 1) > width){
                    break;
                }
                len += 1
                str += `<li style="height: ${horizonIntervalScale}px"></li>`
            }
            str += `</ul>`;
            // str += `</ul><span>${len}cm</span>`;
            this.scaleHtml = str;
        }
    }
}
</script>

<style lang="scss" scoped>
#i-scale {
    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 8px;
    right: auto;
    width: 4px;
    height: 100%;
    margin: auto;
    top: 0;
    bottom: 9px; 
    overflow: hidden;
}
</style>