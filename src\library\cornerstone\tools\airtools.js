import csTools from '$library/cornerstone/cornerstoneTools'
const BaseTool = csTools.importInternal("base/BaseTool");

/**
 * cornerstone Tools 空工具，用于清除定位线
 */
export default class AirtoolsTool extends BaseTool {
	constructor(props = {}) {
		const defaultProps = {
			name: "Airtools",
			supportedInteractionTypes: ["Mouse", "Touch"],
			defaultStrategy: "default",
		};
		super(props, defaultProps);
	}

	activeCallback(element) {
		// const node = this.getWidgetNode(element, this.instanceId);
		// node.innerHTML = ``;
	}

	// getWidgetNode(svgContainer, widgetId = 'mpr-crosshairs') {
	// 	// console.log(svgContainer)
	// 	let node = svgContainer.querySelector(`.${widgetId}`);
	// 	if (!node) {
	// 		node = document.createElement("g");
	// 		node.setAttribute("class", widgetId);
	// 		node.setAttribute(
	// 			"style",
	// 			"position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
	// 		);
	// 		svgContainer.appendChild(node);
	// 	}
	// 	return node;
	// }
}