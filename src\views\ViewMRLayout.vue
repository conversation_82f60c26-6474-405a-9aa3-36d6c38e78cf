<template>
    <div class="inner-pc-plan pc-body" ref="pcBody">
        <section class="c-body">
            <DragImageList
            ref="refDragImageList"
            @fuseData="fuseData"
            @dragSelect="dragSelect"
            @openSelect="openSelect"></DragImageList>
            <AreaFit :isMuch="layoutFill">
                <div ref="center" class="box-area">
                    <draggable
                    class="c-content"
                    v-model="viewports"
                    group="mr-layout"
                    :disabled="true"
                    :move="dragMove"
                    :style="gridLayoutStyle.containerStyle"
                    >
                        <template v-for="(item, index) in viewports">
                            <ViewMPRItem
                                ref="mprItem"
                                :propActiveTool.sync="activeTool"
                                :propIsOverlayVisible.sync="isOverlayVisible"
                                :propCrosshairsTool.sync="crosshairsTool"
                                :key="item.id" 
                                :index="index" 
                                :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style"
                                :imageInfo="item"
                                :id="item.id"
                                :tabId="tabId"
                                :activeSelect="windowIsActive && activeViewportIndex === index"
                                :fullScreen="fullScreenIndex === index"
                                :showAction="showAction === index"
                                :fuseLayers="fuseLayers"
                                @setNewlayout="setNewlayout(index, $event)"
                                @selectStudyId="selectStudyId"
                                @changeViewType="(type) => changeViewType(index, type)"
                                @getVtkVolumeInfo="getVtkVolumeInfo"
                                @vtkChangeCsIndex="vtkChangeCsIndex"
                                @setCrosshairs="activeCrosshairs"
                                @onClickFuse="onClickFuse"
                                @onContextShow="onContextShow"
                                @clearSelectTool="clearSelectTool"
						        @selectTool="selectTool"
                                @onToolRenderCompleted="onToolRenderCompleted"
                                @setViewportActive="setViewportActive(index, $event)"
                                @onDblclickViewport="onDblclickViewport(index)"
                                @onNewImage="onNewImage"
                                @renderCallBack="renderCallBack"
                                @setLayerFuse="setLayerFuse(index, $event)"
                                @emitApi="api => storeApi(api, index)">
                            </ViewMPRItem>
                        </template>
                    </draggable>

                </div>
            </AreaFit>

                <!-- 基本工具。旋转、点击工具、点击重置、点击反片事件 -->
            <BaseTools 
                ref="baseTools"
                toolType="slice"
                :contrast="false"
                :tabId="tabId"
                :seriesId="studyId"
                :activeTool.sync="activeTool"
                :crosshairsTool.sync="crosshairsTool.mipShow"
                :activeViewportIndex="activeViewportIndex"
                :viewportElements="viewportElements"
                :isOverlayVisible.sync="isOverlayVisible"
                :apis="apis"
                :clearImage="true"
                @onClearImage="onClearImage"
                @onInvert="toggleToInvert"
                @onReset="toggleToReset"
                @playClip="vtkPlayClip"
                @syncSizeRender="syncSizeRender"
                @onClickSavePrint="onClickSavePrint"
                @setCrosshairs="activeCrosshairs"
                @onClickBox="onClickBox">
                <div slot="header" class="c-tool-header">
                    <div class="c-layout">
                        <ul class="c-box c-box-01-right ">
                            <li v-for="item in layoutList" :key="item.id" :class="{ 'i-active': item.id === selectLayout }"
                                @click="onClickChangeLayout(item, true)">
                                <i v-if="item.imageType"
                                    :style="{ backgroundImage: 'url(' + require(`$assets/` + item.img) + ')' }"></i>
                                <i v-else :style="{ backgroundImage: 'url(' + item.img + ')' }"></i>
                            </li>
                            <p class="i-tip-set" v-if="!layoutList.length" @click="onClickLayoutSet">设置</p>
                        </ul>
                    </div>
                </div>
            </BaseTools>
        </section>

        <v-contextmenu ref="contextmenu" oncontextmenu="return false">
			<v-contextmenu-item @click="onClickImageSave(1)"><i class="el-icon-picture" style="padding-right:4px"></i>截屏另存...</v-contextmenu-item>
			<v-contextmenu-item @click="onClickImageSave(0)"><i class="el-icon-picture" style="padding-right:4px"></i>图像另存...</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onClickSaveLayout"><i class="el-icon-edit" style="padding-right:4px"></i>保存布局设置</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onClickArea"><i :class="[layoutFill ? 'el-icon-close' : 'el-icon-check']" style="padding-right:4px"></i>布局为正方形</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-submenu >
                <span slot="title"  @click.top="onClickLockSync('start')"><i :class="[lockSync.start ? 'el-icon-check' : 'el-icon-close']" style="padding-right:4px"></i>数据同步锁定</i></span>
                <v-contextmenu-item @click="onClickLockSync('scroll')"><i style="padding-right:4px" :class="[lockSync.scroll ? 'el-icon-check' : 'el-icon-close']"></i>翻页同步</i></i></v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickLockSync('windowColor')"><i style="padding-right:4px" :class="[lockSync.windowColor ? 'el-icon-check' : 'el-icon-close']"></i>窗宽窗位</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickLockSync('moveZoom')"><i style="padding-right:4px" :class="[lockSync.moveZoom ? 'el-icon-check' : 'el-icon-close']"></i>缩放&平移</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="onClickLockSync('convert')"><i style="padding-right:4px" :class="[lockSync.convert ? 'el-icon-check' : 'el-icon-close']"></i>旋转&翻转&反片</v-contextmenu-item>
            </v-contextmenu-submenu>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="toggleCoordinateMoving"><i :class="coordinateMenuText" style="padding-right:4px"></i>隐藏坐标微调</v-contextmenu-item>
            <v-contextmenu-item @click="toggleRotationMoving"><i :class="rotationMenuText" style="padding-right:4px"></i>隐藏图像微调</v-contextmenu-item>

		</v-contextmenu>
        <SaveImage :element="saveImage.element" v-model="saveImage.visible"></SaveImage>
        <SaveLayout 
        v-if="saveLayoutObj.visible"
        :dialogVisible.sync="saveLayoutObj.visible" 
        :layout="saveLayoutObj.layout" 
        :gridLayoutStyle="gridLayoutStyle" 
        :viewports="viewports"
        :screenshotDom="$refs.center"
        @refresh="refreshLayoutList"></SaveLayout>
    </div>
</template>
<script>
import SelectTool from "$src/mixin/SelectTool.js"
import RenderRele from "$src/mixin/RenderRele.js"
import VtkTool from '$src/mixin/VtkTool.js'
import Printscreen from "$src/mixin/Printscreen.js"


import { dbLayoutSet } from '$library/db';
import getConfigByStorageKey from '$library/utils/configStorage.js';
import { throttle } from 'lodash-es';
// import html2canvas from "html2canvas"
import draggable from 'vuedraggable'
import AreaFit from '$src/layout/AreaFit.vue'
import BaseTools from '$components/tools/BaseTools'
import DragImageList from '$components/tools/DragImageList'
import ViewMPRItem from '$components/ViewMPRItem'
import SaveImage from '$components/layer/SaveImage'
import SaveLayout from '$components/layer/SaveLayout'

export default {
    name: 'ViewMRLayout',
    mixins: [ RenderRele, SelectTool, VtkTool, Printscreen ],
    components: {
        AreaFit,
        BaseTools,
        DragImageList,
        draggable,
        ViewMPRItem,
        SaveImage,
        SaveLayout
    },
    props: {
        tabId: {
            default: 'none',
        },
        windowIsActive: {
            default: false,
        },
    },
    provide() {
        return {
            provideShowToolObj: this.showToolObj
        }
    },
    data() {
        return {
            viewports: [{}, {}, {}, {}],
            style: {
				width: '50%',
				height: '50%',
			},
            activeTool: 'Airtools',
            crosshairsTool: {
                mipShow: false
            },
            activeViewportIndex: -1,
            fullScreenIndex: -1,
            showAction: -1,              // 选中出现的工具列表
            viewportElements: [],
            isOverlayVisible: true,
            toolRenderLast: false,       // 用于控制，在工具渲染后，不在触发点选状态（直接赋值选中）
            toolThrottle: null,          // 工具渲染节流
            fuseLayers: [],              // 融合选择层
            isMuch: false,
            synchronizerCrosshairs: Object, // 十字线同步器
            timerSynchronizer: null,
            vtkAndCsRenderState: {
                curRender: 'none',
                timer: null
            }, // 谁在渲染，截断处理
            studyId: '',
            apis: [],
            showToolObj: {
                showCoordinateTools: false, // 显示坐标微调
                showRotationTools: false,   // 显示旋转微调
            },
            saveImage: {		// 保存图像
				visible: false,
				element: null,
                rightSelectElement: null,
			},
            layoutList: [],
            selectLayout: '2x2axial',
            gridLayoutStyle: {
                containerStyle: {},
                itemStyle: []
            },
            saveLayoutObj: {
                visible: false,
                layout: {}
            }
        }
    },
    computed: {
        layoutFill() {
            return this.$store.state.layoutFill;
        },
        coordinateMenuText() {
            return this.showToolObj.showCoordinateTools ? 'el-icon-close' : 'el-icon-check'
        },
        rotationMenuText() {
            return this.showToolObj.showRotationTools ? 'el-icon-close' : 'el-icon-check'
        },
    },
    watch: {
        'crosshairsTool.mipShow': {
            handler(later) {
                if (later) {
                    // 开启线条绘制
                    this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: false })
                    this.activeCrosshairs()
                    setTimeout(() => {
                        this.activeTool = 'Airtools';
                    }, 0);
                }else {
                    this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: true })
                    const tempTool = this.activeTool
                    this.activeTool = 'temp'
                    setTimeout(() => {
                        this.activeTool = tempTool
                    }, 0);
                }
            },
            immediate: true
        },
    },
    mounted() {
        this.crosshairsTool.mipShow = getConfigByStorageKey('configs-crosshairs-show')
        this.getLayout()
        cornerstoneTools.store.state.showReferenceLines = true
        this.initSynchronizerCrosshairs()
    },
    methods: {
        onClearImage() {
            const len = this.viewports.length

            this.viewports.splice(0) // 清空显示

            console.log(len)
            setTimeout(() => {
                for (let index = 0; index < len; index++) {
                    this.viewports.push({})
                    
                }
                this.activeViewportIndex = -1
                this.updateElement()
            }, 100);
        },
        // 向下添加格子显示展开图
        setNewlayout(index, obj) {
            const { list, studyUID } = obj

            if (list.length >= 2) {

                if (this.gridLayoutStyle.containerStyle.flexDirection === 'column') {
                    const current = this.gridLayoutStyle.itemStyle[index]
                    current.height = parseFloat(current.height) / 2 + '%'
                    this.gridLayoutStyle.itemStyle.splice(index + 1, 0, current)
                }else if (!this.gridLayoutStyle.containerStyle.flexDirection) {
                    this.style.height = parseFloat(this.style.height) / 2 + '%'
                }

                list.forEach((item, idx) => {
                    const info = {
                        desc: this.viewports[index].desc,
                        seriesUID: item,
                        studyUID: studyUID,
                        viewType: this.viewports[index].viewType,
                        unfold: idx
                    }
                    if (idx !== 0 && this.viewports[index+idx].seriesUID === item) {
                        // 添加的，如果添加的位置已经有一模一样的，就不在添加
                        return
                    }
                    this.viewports.splice(index + idx, idx == 0 ? 1 : 0, Object.assign({id: this.$fun.onlyValue()}, info))
                })
            }


        },
        refreshLayoutList(cb) {
            // 配置可添加布局
            dbLayoutSet.then(e => {
                // 3 拖拽重建
                e.getGroup(3).then(e => {
                    if (e.success) {
                        this.layoutList = e.data
                        
                        this.layoutList.sort((a, b) => {
                            return a.position - b.position;
                        }); 

                        cb && cb()
                    }
                })
            });  
        },
        getLayout() {
            this.refreshLayoutList(() => {
                this.getDefaultLayout()
            })
        },
        getDefaultLayout() {
            // const id = 29 // getConfigByStorageKey('configs-dragDefaultLayoutId')
            // if (!id) {
            //     return
            // }
            // const obj = this.layoutList.find(item => item.id == id)

            const obj = this.layoutList[0]
            
            if (obj) {
                this.onClickChangeLayout(obj, false, false)
            }
        },
        // 选择布局模式
        /**
         * options 布局参数
         * id      布局主键
         */
        onClickChangeLayout(obj, isPassive = false, clearGrid = false) {
            const { options, id, showLayout } = obj

            this.selectLayout = id
            // 特殊布局的 css 信息
            if (options.containerStyle) {
                this.gridLayoutStyle.containerStyle = this.$fun.deepClone(options.containerStyle);
            } else {
                this.gridLayoutStyle.containerStyle = {}
            }

            if (options.itemStyle && options.itemStyle.length) {
                this.gridLayoutStyle.itemStyle = this.$fun.deepClone(options.itemStyle);
            } else {
                this.gridLayoutStyle.itemStyle = [];
            }

            this.saveLayoutObj.layout = obj
            this.saveLayoutObj.grid = this.gridLayoutStyle


            // 改变布局
            this.onClickBox(options.layout, isPassive, clearGrid);

            // 设置布局数据
            this.setViewportByLayout(obj.customLayout)

        },
        setViewportByLayout(customLayout) {
            const layout = customLayout || []
            const imageList = this.$refs.refDragImageList.imageList || []

            // 遍历布局格子
            layout.forEach((item, index) => {
                if (!item) {
                    return
                }

                const unfold = item.unfold >= 0 ? item.unfold : undefined

                // 避免过多的展开 WDI 图
                if (unfold > 0) {
                    return
                }

                const series1 = {
                    modality: item.modality, 
                    seriesDesc: item.seriesDesc,
                    seriesNumber: item.seriesNumber,
                    viewType: item.viewType,
                    unfold
                }

                const series2 = {
                    modality: item.modality1, 
                    seriesDesc: item.seriesDesc1,
                    seriesNumber: item.seriesNumber1,
                    viewType: item.viewType,
                    unfold
                }

                // 遍历检查
                imageList.forEach((study, i) => {
                    // 遍历序列
                    study.instanceList.find(series => {
                        // 查找 ct、mr
                        if (!this.conditionMatch(series, series1)){
                            return false;
                        }

                        if (!series2.modality) {
                            const info = {
                                desc: series.sSeriesDescription,
                                seriesUID: series.sSeriesInstanceUID,
                                studyUID: series.sStudyInstanceUID,
                                viewType: item.viewType,
                                unfold
                            }
                            this.viewports.splice(index, 1, Object.assign({id: this.$fun.onlyValue()}, info))
                            return true;
                        
                        }else {
                            // 融合图像，需要查找第二个
                            const findPt = study.instanceList.find(twoSeries => {
                                if (!this.conditionMatch(twoSeries, series2)){
                                    return false
                                }
                                return true
                            })

                            if (findPt) {
                                const info = {
                                    seriesUID: series.sSeriesInstanceUID,
                                    studyUID: series.sStudyInstanceUID,
                                    viewType: item.viewType,
                                    unfold,
                                    desc: findPt.sSeriesDescription,
                                    seriesUID1: findPt.sSeriesInstanceUID,
                                    studyUID1: findPt.sStudyInstanceUID,
                                }
                                this.viewports.splice(index, 1, Object.assign({id: this.$fun.onlyValue()}, info))
                            }
                        }
                    })
                })
            })
        },
        conditionMatch(series, conditionSeries) {
            // 相同设备，相同序列描述，相同图像类型
            let findStep =  series.sModality === conditionSeries.modality && 
                            series.sSeriesDescription == conditionSeries.seriesDesc
            if (!findStep) {
                return false;
            }
            // 图像数目
            // const num = Number(conditionSeries.mathValue)
            // if (conditionSeries.mathSymbol) {
            //     findStep = series.iInstanceCount == num
            // }else {
            //     findStep = series.iInstanceCount >= num
            // }

            // if (!findStep) {
            //     return false;
            // }

            // 有序列数，就做判断
            if (conditionSeries.seriesNumber) {
                findStep = series.seriesNumber == conditionSeries.seriesNumber
            }

            if (!findStep) {
                return false;
            }

            return true;
        },
        // 点击打开布局设置弹窗
        onClickLayoutSet() {
            this.$store.state.triggerLayoutSet = true;
        },
        toggleCoordinateMoving() {
            this.showToolObj.showCoordinateTools = !this.showToolObj.showCoordinateTools
        },
        toggleRotationMoving() {
            this.showToolObj.showRotationTools = !this.showToolObj.showRotationTools
        },
        fuseData(data) {
            this.fuseLayers = data
        },
        /**
         * 保持布局位置
         * arr  原始数组
         * arrw 原数组宽
         * w 新的宽
         * h 新的高
         */
        copyTo(arr, arrW, w, h) { 
            return arr.reduce((ret, x, i) => ((i / arrW < h && i % arrW < w) && (ret[~~(i / arrW) * w + i % arrW] = x), ret), Array(w * h).fill({}))
        },
        /**
         * layout 布局信息
         * isPassive true 人为点击布局
         */
        onClickBox(layout, isPassive = false, clearGrid = true) {
            
            if (clearGrid) {
                this.gridLayoutStyle.containerStyle = {}
                this.gridLayoutStyle.itemStyle = []
                this.saveLayoutObj.layout = {}
            }

			const width = layout.column
			const height = layout.row
            const beforeW = Math.round(100 / parseInt(this.style.width))

			// 设置布局样式
 			this.style.width = 100 / width + '%'
        	this.style.height = 100 / height + '%'

            this.viewports = this.copyTo(this.viewports, beforeW, width, height)
            this.updateElement()

            // MIP 视图在调整布局后，不会适应窗宽大小
            this.$nextTick(() => {
                const myEvent = new Event('resize')
                window.dispatchEvent(myEvent)
            })
        },
        /**
         * 拖拽选中
         * idx  放置位置
         * info 拖拽图像信息
         */
        dragSelect(idx, info) {

            let newIdx = idx


            // 如果放入的是拆开格子，特殊处理，合并拆开格子。
            if (this.viewports[newIdx].unfold !== undefined) {
                // 拖入拆开图
                newIdx -= this.viewports[newIdx].seriesUID.split('-')[1]

                const currentViewport = this.viewports[newIdx].seriesUID.split('-')

                let popCount = 0
                for (let index = newIdx + 1; index < this.viewports.length; index++) {
                    const viewport = this.viewports[index];

                    const targetViewport = viewport.seriesUID.split('-')

                    if (currentViewport[0] === targetViewport[0] && targetViewport[1] >= 1) {
                        popCount += 1 
                    }
                    break;
                }
                if (popCount) {
                    this.viewports.splice(newIdx+1, popCount)
                    if (this.gridLayoutStyle.containerStyle.flexDirection === 'column') {
                        const current = this.gridLayoutStyle.itemStyle[newIdx]
                        this.gridLayoutStyle.itemStyle[newIdx].height = parseFloat(current.height) * (popCount+1) + '%'

                        this.gridLayoutStyle.itemStyle.splice(newIdx + 1, popCount)
                    }else if (!this.gridLayoutStyle.containerStyle.flexDirection) {
                        this.style.height = parseFloat(this.style.height) * (popCount+1) + '%'
                    }
                }
            }

            this.viewports.splice(newIdx, 1, Object.assign({id: this.$fun.onlyValue()}, info))
            this.activeViewportIndex = Number(newIdx)
            this.updateElement()
        },
        changeViewType(index, type) {
            this.viewports[index].viewType = type
            const activeViewportIndex = this.activeViewportIndex
            this.activeViewportIndex = -1
            setTimeout(() => {
                this.activeViewportIndex = activeViewportIndex
            }, 0);

        },
        /**
         * 序列点击打开
         * 步骤1、先配对
         * 步骤2、按照配对显示相应布局
         * checkboxList 选中的序列
         */
        openSelect(checkboxList){
            // 分组
            const findModality = ['PT', 'NM']
            const ptList    = checkboxList.filter( item => findModality.includes(item.modality.toLocaleUpperCase()) )
            const notPtList = checkboxList.filter( item => !findModality.includes(item.modality.toLocaleUpperCase()) )
            // 匹配项
            let metchItems = []
            let isFuse     = false

            if (ptList.length && notPtList.length) {
                // 有融合
                isFuse = true

                notPtList.forEach(item => {
                    const group = { 
                        studyUID: '',
                        seriesUID: '',
                        studyUID1: item.studyUID,
                        seriesUID1: item.seriesUID
                     };
                    const currentFirstDesc = item.desc ? item.desc.split(' ')[0] : ''
                    // TODO 不知道查找 PT 的逻辑条件是什么？ 应该是查找与 ct、mr 相符融合 PT 图像
                    const findPt = ptList.find(item => {
                        return currentFirstDesc == item.desc ? item.desc.split(' ')[0] : ''
                    })
                    
                    const layer1 = findPt || ptList[0]

                    group.studyUID = layer1.studyUID
                    group.seriesUID = layer1.seriesUID

                    metchItems.push(group);
                })

            }else {
                // 没有融合
                checkboxList.forEach(item => {
                    metchItems.push({
                        studyUID: item.studyUID,
                        seriesUID: item.seriesUID,
                        desc: item.desc,
                        viewType: 'cs'
                    })
                });
            }

            // 有一组显示2x2 ct、pt、fuse、mip 形式
            // 有二组图2x2 ct1、ct2、fuse1、fuse2
            // 有三组图3x2 ct1、ct2、ct3、fuse1、fuse2、fuse3
            const metchLen = metchItems.length
            this.viewports.splice(0) // 清空显示

            if (!isFuse) {
                // 一组
                // 设置布局
                this.openAutoLayout(metchLen)
                setTimeout(() => {
                    for (let index = 0; index < metchLen; index++) {
                        this.viewports.splice(index, 1, Object.assign({}, metchItems[index], {id: this.$fun.onlyValue()}))
                    }
                }, 100);
            }else {
                // 多组,最多4组
                const maxValue = 4
                const viewportNum = metchLen > maxValue ? maxValue : metchLen

                this.onClickBox({row: 2, column: viewportNum > 1 ? viewportNum : 2}, false)

                
                if (metchLen === 1) {
                    // 只有一组融合
                    const groupFuse = metchItems[0]

                    this.viewports.splice(0, 1, {
                        studyUID: groupFuse.studyUID1,seriesUID: groupFuse.seriesUID1,
                        desc: groupFuse.desc, viewType: 'cs', id: this.$fun.onlyValue()
                    })
                    this.viewports.splice(1, 1, {
                        studyUID: groupFuse.studyUID,seriesUID: groupFuse.seriesUID,
                        desc: groupFuse.desc, viewType: 'cs', id: this.$fun.onlyValue()
                    })
                    this.viewports.splice(2, 1, {
                        studyUID: groupFuse.studyUID, seriesUID: groupFuse.seriesUID,
                        studyUID1: groupFuse.studyUID1, seriesUID1: groupFuse.seriesUID1,
                        desc: groupFuse.desc, viewType: 'cs', id: this.$fun.onlyValue()
                    })
                    this.viewports.splice(3, 1, {
                        studyUID: groupFuse.studyUID, seriesUID: groupFuse.seriesUID,
                        // studyUID1: groupFuse.studyUID1, seriesUID1: groupFuse.seriesUID1,
                        desc: groupFuse.desc, viewType: 'vtk', id: this.$fun.onlyValue()
                    })
                }else {
                    for (let index = 0; index < viewportNum; index++) {
                        
                        // 一层
                        this.viewports.splice(index, 1, {
                            studyUID: metchItems[index].studyUID1,seriesUID: metchItems[index].seriesUID1,

                            desc: metchItems[index].desc,
                            viewType: 'cs',
                            id: this.$fun.onlyValue()
                        })

                        // 多层，融合
                        this.viewports.splice(index + viewportNum, 1, {
                            studyUID: metchItems[index].studyUID, seriesUID: metchItems[index].seriesUID,
                            studyUID1: metchItems[index].studyUID1, seriesUID1: metchItems[index].seriesUID1,

                            desc: metchItems[index].desc,
                            viewType: 'cs',
                            id: this.$fun.onlyValue()
                        })

                    }
                }
            }
            this.activeViewportIndex = 0
        },
        /**
		 * 单个item重建时用事件把有用的信息传回来 修改viewport数据
		 */
        getVtkVolumeInfo(id, volumeInfo) {
            const index = this.viewports.findIndex(item => item.id === id)
            if (index > -1) {
                this.$set(this.viewports[index], 'volumeInfo', volumeInfo)
            }
        },
        /**
		 * 根据打开的数量，匹配相应布局结构
		 */
		openAutoLayout(len) {
			const arr = [1, 4, 9, 16, 25, 36, 49, 64, 81, 100]
			let idx = arr.findIndex(item => {
				return len <= item;
			})
			idx += 1;
			const box = idx > 0 ? idx : 5;
			this.onClickBox({column: box, row: box}, false)
		},
        onClickArea() {
            this.$store.commit('setLayoutFill', !this.layoutFill)
            localStorage.setItem('configs-layoutFill', this.layoutFill)
            this.$nextTick(() => {
                const myEvent = new Event('resize')
                window.dispatchEvent(myEvent)
            })
        },
        onContextShow(e, hide = false) {
            if (hide) {
                this.$refs.contextmenu.hide();
                return;
            }
			this.$refs.contextmenu.handleReferenceContextmenu(e);
            setTimeout(() => {
                this.saveImage.rightSelectElement = this.viewportElements[this.activeViewportIndex];
            }, 100)
        },
        onClickImageSave(isScreen = 0) {
            if (isScreen) {
                this.saveImage.element = this.$refs.center;
                this.saveImage.visible = true;
            }else {
                const name = this.$store.state.seriesInfo.sPatientName;
                if (this.saveImage.rightSelectElement) {
                    cornerstoneTools.SaveAs(this.saveImage.rightSelectElement, name + '_' + new Date().toLocaleString() + '.jpg');
                }
            }
		},
        updateElement(){
			// 设置元素数量
			// 获取所有视图元素（dom）结构
            this.$nextTick(() => {
                try {
                    this.viewportElements = this.$refs['center'].getElementsByClassName('mpr-item')
                    // this.setFontByWidthAndHeight()
                    setTimeout(() => {
                        this.$refs.baseTools.onAllRender()
                        // 初始化定位线同步器
                        this.addSynchronizerCrosshairs()
                    }, 200);
                } catch (error) {
                    console.warn(error)
                }
            })
		},
        // 双击全屏
        onDblclickViewport(index) {
            if (this.activeTool === 'TextMarker') {
                return
            }
            // 全屏
            this.fullScreenIndex = this.fullScreenIndex === index ? -1 : index 

            // 全屏后，触发让 cornerstone 相应调整
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent);
                // this.setFontByWidthAndHeight(index);
            })

            // 同步其它视图
            setTimeout(() => {
                // const item = this.$refs.csViewport[index] || {}
                // if (item.state){
                //     item.syncCallBack('relative')
                // }
                // 执行 baseTools 组件中的方法
                this.$refs.baseTools.onAllRender()
            }, 100);
        },
        onClickFuse() {
            this.$refs.baseTools.onAllRender()
        },
        // 设置当前选中的视窗
		setViewportActive(index, button){
            this.activeViewportIndex = index

            if (this.toolRenderLast) {
                this.toolRenderLast = false
                this.showAction = this.activeViewportIndex
                return;
            }
            if (button !== 0) {
                this.showAction = index;
                return;
            }
            // 选中出现工具
			this.showAction = this.activeTool === 'Airtools' && !this.crosshairsTool.mipShow && this.showAction === index ? -1 : index
		},
        setLayerFuse(index, obj) {
            // const { studyUID, seriesUID } = obj
            // console.log(studyUID, seriesUID)

            const item = this.viewports[index]
            if (obj.fuse) {
                item.studyUID1 = item.studyUID
                item.seriesUID1 = item.seriesUID

                item.studyUID = obj.studyUID
                item.seriesUID = obj.seriesUID
            }else {
                item.studyUID = item.studyUID1
                item.seriesUID = item.seriesUID1

                delete item.studyUID1
                delete item.seriesUID1

            }

        //   this.viewports[index].layer = this.viewports[index].layer === 0 ? 1 : 0  
        },
        /**
         * 工具渲染处理
         * sourceElement 操作渲染的 dom 
         * sourceOrientation 操作渲染的 方向 x,y,z
         */
        onToolRenderCompleted(sourceElement, sourceOrientation, status){
            this.toolRenderLast = true
            if (status === 'removed') {
                this.toolRenderLast = false
            }
            clearTimeout(this.toolThrottle)
            this.toolThrottle = setTimeout(() => {
                const viewportElements = this.$refs['center'].getElementsByClassName('viewport-element')

                for (let index = 0; index < viewportElements.length; index++) {
                    const el = viewportElements[index]
                    if (el){
                        const orientation = el.getAttribute('orientation');
                        // 不同切面的退出
                        if (orientation !== sourceOrientation) return;
                        cornerstone.updateImage(el);
                    }
                }
            }, 500);
            // 更新；调用 baseTools 组件
			if (status === 'completed' || status === 'removed' || status === 'modifiedAndState'){
				this.$refs.baseTools.getNewToolList()
			}
        },
        // 显示区域禁止拖拽
        dragMove() {
            return false;
        },

        // 初始定位线同步器
        initSynchronizerCrosshairs() {
            // 先进行判断销毁
            if (Object.keys(this.synchronizerCrosshairs).length){
                this.synchronizerCrosshairs.destroy()
                this.synchronizerCrosshairs = Object
            }
            this.synchronizerCrosshairs = new cornerstoneTools.Synchronizer(
                'cornerstonenewimage',
                cornerstoneTools.updateImageSynchronizer
            )
        },
        // 添加定位线同步器元素
        addSynchronizerCrosshairs(sourceElement) {
            if (Object.keys(this.synchronizerCrosshairs).length){
                this.synchronizerCrosshairs.destroy()
            }

            const elements = this.$refs['center'].getElementsByClassName('viewport-element')
            if (!elements) {
                return
            }
            for (let index = 0; index < elements.length; index++) {
                const el = elements[index]
                
                if (!el) {
                    return
                }
                const enableElement = cornerstone.getEnabledElement(el)

                if (enableElement && enableElement.image && enableElement.image.imageId) {
                   this.synchronizerCrosshairs.add(el)
                    // 当前元素没有定位线工具，就加入定位线
                    if (!cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')) {
                       cornerstoneTools.addToolForElement(el, cornerstoneTools.NuclearCrosshairsTool, {
                           configuration: {
                               tabId: this.tabId,
                               renderCallBack: this.nuclearCrosshairsCallBack
                           }
                       })
                    }
                    clearTimeout(this.timerSynchronizer)
                    this.timerSynchronizer = setTimeout(() => {
                        if (this.crosshairsTool.mipShow){
                            this.activeCrosshairs('Active', sourceElement)
                        }else {
                            if (cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')) {
                                this.activeCrosshairs('Passive', sourceElement)
                            }
                        }
                    }, 100);
                }
            }
        },
        // 工具活跃  Passive  Active
        activeCrosshairs(mode = 'Active', sourceElement) {

            const elements = this.$refs['center'].getElementsByClassName('viewport-element')
            if (!elements) {
                return
            }
            for (let index = 0; index < elements.length; index++) {
                const el = elements[index]
                
                if (!el || !cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')) {
                    return
                }
                cornerstoneTools['setTool'+mode+'ForElement'](el, 'NuclearCrosshairs', {
                    mouseButtonMask: 1,
                    synchronizationContext: this.synchronizerCrosshairs,
                    renderNewPoint: sourceElement === el
                });
            }
        },
        syncSizeRender() {
            setTimeout(() => {
                const refMprItem  = this.$refs.mprItem
                const itemComponent = refMprItem[this.activeViewportIndex]

                if (itemComponent) {
                    const mprViewport = itemComponent.$refs.mprViewport
                    if (mprViewport) {
                        const csViewport = mprViewport.$refs.csViewport
                        csViewport.syncCallBack('relative')
                    }
                }
            }, 100);
        },
        selectStudyId(studyId) {
            this.studyId = studyId
        },
        // 接受子组件的vtk api，传给baseTool
        storeApi(api, index) {
            this.apis[index] = api;
        },
        onClickSaveLayout() {
            this.saveLayoutObj.visible = true
        },
        // VTK 和 CS 渲染状态节流控制
        setvtkAndCsRenderThrottle() {
            clearTimeout(this.vtkAndCsRenderState.timer);
            this.vtkAndCsRenderState.timer = setTimeout(() => {
                this.vtkAndCsRenderState.curRender = 'none';
            }, 200);
        },
        // 改变 MIP 定位线位置 - 当 cornerstone 视图改变时调用
        nuclearCrosshairsCallBack(el) {
            // 设置当前渲染为 cornerstone ，在vtk反向改变截断
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'cs') {
                this.vtkAndCsRenderState.curRender = 'cs'
                this.setvtkAndCsRenderThrottle()
            }

            if (this.vtkAndCsRenderState.curRender != 'vtk') {
                // 节流在停止后，在更新 MIP 定位线
                this.vtkCrosshairsthrottle(el)
            }
        },
        // 从 cornerstone 视图改变 VTK 定位线位置
        // csChangeVtkIndex(el) {
        //     console.log('csChangeVtkIndex called')
        //     const apis = this.apis;
        //     if (!apis || !apis.length) {
        //         return
        //     }

        //     // 获取当前图像的位置信息
        //     const ipp = getIppByPoint(el)
        //     console.log('Got IPP from cornerstone:', ipp)
        //     if (!ipp || !ipp.length) {
        //         return
        //     }

        //     // 更新所有 VTK 视图的定位线
        //     apis.forEach((api, apiIndex) => {
        //         if (api && api.svgWidgets && api.svgWidgets.crosshairsWidget) {
        //             console.log('Updating VTK crosshairs for api', apiIndex)
        //             api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, apis, apiIndex);
        //         }
        //     })
        // },
        // 节流函数 - 防止频繁更新 VTK 定位线
        vtkCrosshairsthrottle: throttle(function (el) {
            this.csChangeVtkIndex(el)
        }, 100),
    }
}
</script>
<style lang="scss" scoped>
.box-area{
    width: 100%;
    height: 100%;
    .c-content{
        display: flex;
        flex-wrap: wrap;
    }
}

.c-tool-header {
    min-height: 100px;
    .c-layout {
        display: flex;
        padding-left: 1px;
        border-bottom: 1px solid #d2d2d2;
        min-height: 100px;
    }

    .c-box {
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-around;
        width: 98px;
    }

    .c-box-01,
    .c-box-01-right {
        padding: 2px 0px 2px 0px;

        >li {
            float: left;
            width: 48px;
            height: 48px;
            line-height: 48px;
            /* border: 1px solid #d2d2d2; */
            margin-right: 0;
            margin-bottom: 0;
            cursor: pointer;
            transition: all 0.2s;
            background: #fff;
            border-radius: 4px;

            i {
                display: inline-block;
                width: 100%;
                height: 100%;
                border-radius: 2px;
                background-size: cover;
            }

            &:hover {
                box-shadow: 2px 2px 4px 0px #c0c0c0;
            }

            &.i-active {
                border-color: #6294B7;
                color: white;
            }
        }

    }

    .c-box-01-right {
        width: 148px;
        border-left: 1px solid #aaa;
    }
}
</style>