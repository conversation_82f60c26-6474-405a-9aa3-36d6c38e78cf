import fun from '$library/utils/function.js';

export default function toolCoordTransition(sourceElement, toolName, measurementData, targetElement, resetUuid = true) {
    const measurement = fun.deepClone(measurementData);

    const domDiffX = (targetElement.clientWidth - sourceElement.clientWidth) * targetElement.clientWidth / sourceElement.clientWidth
    const domDiffY = (targetElement.clientHeight - sourceElement.clientHeight) * targetElement.clientHeight / sourceElement.clientHeight

    if (resetUuid) {
        delete measurement.uuid;
    }
    if (toolName === 'FreehandRoi' || toolName === 'FreehandLength') { // 勾画
        // 点长度
        const lastIndex = measurement.handles.points.length - 1;
        // 遍历点
        measurement.handles.points.map((item, index) => {
            const coord = cornerstone.pixelToCanvas(sourceElement, { x: item.x, y: item.y })
            coord.x = coord.x + domDiffX
            coord.y = coord.y + domDiffY
            const { x, y } = cornerstone.canvasToPixel(targetElement, coord);

            item.x = x;
            item.y = y;
            // preIndex 上一个点 x,y
            const preIndex = index - 1;
            if (preIndex >= 0) {
                // lines 存储上一个的x,y
                measurement.handles.points[preIndex].lines[0].x = x;
                measurement.handles.points[preIndex].lines[0].y = y;

                // 最后一个，放第一个点的 x,y
                if (lastIndex === index && toolName === 'FreehandRoi') {
                    measurement.handles.points[index].lines[0].x = x;
                    measurement.handles.points[index].lines[0].y = y;
                }
            }
        })
        // 修改 text 位置
        const textBox = cornerstone.pixelToCanvas(sourceElement, measurement.handles.textBox)
        const pxTextBox = cornerstone.canvasToPixel(targetElement, textBox);
        measurement.handles.textBox.x = pxTextBox.x;
        measurement.handles.textBox.y = pxTextBox.y;


    } else if (toolName === 'LesionArea') {
        let tx, ty
        measurement.allTracePoints.forEach((item, index) => {
            const coord = cornerstone.pixelToCanvas(sourceElement, { x: item.x, y: item.y })
            coord.x = coord.x + domDiffX
            coord.y = coord.y + domDiffY
            const { x, y } = cornerstone.canvasToPixel(targetElement, coord);
            item.x = tx = x;
            item.y = ty = y;
        })
        measurement.lesionPoints.forEach((item, index) => {
            const coord = cornerstone.pixelToCanvas(sourceElement, { x: item.x, y: item.y })
            coord.x = coord.x + domDiffX
            coord.y = coord.y + domDiffY
            const { x, y } = cornerstone.canvasToPixel(targetElement, coord);
            item.x = tx = x;
            item.y = ty = y;
        })
        // 修改 text 位置  
        measurement.handles.textBox.x = tx + 20;
        measurement.handles.textBox.y = ty;
        measurement.handles.textBox.hasMoved = true;

        const points = measurement.allTracePoints

        const bounds = {
            left: points[0].x,
            right: points[0].x,
            bottom: points[0].y,
            top: points[0].x,
        };

        for (let i = 0; i < points.length; i++) {
            bounds.left = Math.min(bounds.left, points[i].x);
            bounds.right = Math.max(bounds.right, points[i].x);
            bounds.bottom = Math.min(bounds.bottom, points[i].y);
            bounds.top = Math.max(bounds.top, points[i].y);
        }

        measurement.polyBoundingBox = {
            left: bounds.left,
            top: bounds.bottom,
            width: Math.abs(bounds.right - bounds.left),
            height: Math.abs(bounds.top - bounds.bottom),
        };

    }
    else {
        // 箭头、直线、圆、椭圆、矩形
        // 转换成 canvas 坐标
        const end = cornerstone.pixelToCanvas(sourceElement, measurement.handles.end)
        const start = cornerstone.pixelToCanvas(sourceElement, measurement.handles.start)
        const textBox = cornerstone.pixelToCanvas(sourceElement, measurement.handles.textBox)

        start.x = start.x + domDiffX
        start.y = start.y + domDiffY
        end.x = end.x + domDiffX
        end.y = end.y + domDiffY
        textBox.x = textBox.x + domDiffX
        textBox.y = textBox.y + domDiffY

        const pxEnd = cornerstone.canvasToPixel(targetElement, end);
        const pxStart = cornerstone.canvasToPixel(targetElement, start);
        const pxTextBox = cornerstone.canvasToPixel(targetElement, textBox);

        measurement.handles.end.x = pxEnd.x;
        measurement.handles.end.y = pxEnd.y;

        measurement.handles.start.x = pxStart.x;
        measurement.handles.start.y = pxStart.y;

        measurement.handles.textBox.x = pxTextBox.x;
        measurement.handles.textBox.y = pxTextBox.y;

        // 角度测量多了个坐标点
        if (measurement.handles.middle) {
            const middle = cornerstone.pixelToCanvas(sourceElement, measurement.handles.middle)
            middle.x = middle.x + domDiffX
            middle.y = middle.y + domDiffY
            const pxMiddle = cornerstone.canvasToPixel(targetElement, middle)

            measurement.handles.middle.x = pxMiddle.x;
            measurement.handles.middle.y = pxMiddle.y;
        }
        // 中心点线
        if (measurement.handles.perpendicularStart) {
            const perpendicularStart = cornerstone.pixelToCanvas(sourceElement, measurement.handles.perpendicularStart)
            perpendicularStart.x = perpendicularStart.x + domDiffX
            perpendicularStart.y = perpendicularStart.y + domDiffY
            const transitionObj = cornerstone.canvasToPixel(targetElement, perpendicularStart)

            measurement.handles.perpendicularStart.x = transitionObj.x;
            measurement.handles.perpendicularStart.y = transitionObj.y;
        }
        // 中心点线
        if (measurement.handles.perpendicularEnd) {
            const perpendicularEnd = cornerstone.pixelToCanvas(sourceElement, measurement.handles.perpendicularEnd)
            perpendicularEnd.x = perpendicularEnd.x + domDiffX
            perpendicularEnd.y = perpendicularEnd.y + domDiffY
            const transitionObj = cornerstone.canvasToPixel(targetElement, perpendicularEnd)

            measurement.handles.perpendicularEnd.x = transitionObj.x;
            measurement.handles.perpendicularEnd.y = transitionObj.y;
        }
    }

    return measurement;
}

export function updateToolCoord(sourceMeasurement, targetmeasurement, toolName) {
    targetmeasurement.invalidated = true;
    if (toolName === 'FreehandRoi' || toolName === 'LesionArea' || toolName === 'FreehandLength') {
        // 勾画

    } else {
        // 箭头、直线、圆、椭圆、矩形

        // 移入点，取消选中状态
        sourceMeasurement.handles.start.active = false;
        sourceMeasurement.handles.end.active = false;
    }
    targetmeasurement.handles = sourceMeasurement.handles;
}

export function singlePointTrans(sourceElement, targetElement, item) {
    const domDiffX = (targetElement.clientWidth - sourceElement.clientWidth) * targetElement.clientWidth / sourceElement.clientWidth
    const domDiffY = (targetElement.clientHeight - sourceElement.clientHeight) * targetElement.clientHeight / sourceElement.clientHeight
    const coord = cornerstone.pixelToCanvas(sourceElement, { x: item.x, y: item.y })
    coord.x = coord.x + domDiffX
    coord.y = coord.y + domDiffY
    const { x, y } = cornerstone.canvasToPixel(targetElement, coord);
    return { x, y }
}

