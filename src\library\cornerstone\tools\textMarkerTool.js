import csTools from '$library/cornerstone/cornerstoneTools'
import getConfigByStorageKey from '$library/utils/configStorage.js'

const logger = console.log;

const { addToolState, removeToolState, getToolState, toolStyle, toolColors, getModule, store } = csTools;
const external = csTools.external;

const BaseAnnotationTool = csTools.importInternal('base/BaseAnnotationTool');
 
// Drawing
const getNewContext = csTools.importInternal('drawing/getNewContext');
const draw = csTools.importInternal('drawing/draw');
const setShadow = csTools.importInternal('drawing/setShadow');
const drawTextBox = csTools.importInternal('drawing/drawTextBox');   
const pointInsideBoundingBox = csTools.importInternal('util/pointInsideBoundingBox'); 
const triggerEvent = csTools.importInternal('util/triggerEvent');
const moveAnnotation = csTools.importInternal('manipulators/moveAnnotation');
const moveHandleNearImagePoint = csTools.importInternal('manipulators/moveHandleNearImagePoint');

const { freehandRoiCursor } = csTools.importInternal('tools/cursors');

/**
 * @public
 * @class TextMarkerTool
 * @memberof Tools.Annotation
 *
 * @classdesc Tool for annotating an image with text markers.
 * @extends Tools.Base.BaseAnnotationTool
 */
export default class TextMarkerTool extends BaseAnnotationTool {
  constructor(props = {}) {
    const defaultProps = {
      name: 'TextMarker',
      supportedInteractionTypes: ['Mouse', 'Touch'],
      configuration: {
        markers: [],
        current: '',
        ascending: true,
        loop: false,
      },
      svgCursor: freehandRoiCursor,
    };

    super(props, defaultProps);
    this.mouseClickCallback = this.createNewMark.bind(this);
    this.mouseUpCallback = this.createNewMark.bind(this);
  }

  createNewMeasurement(eventData) {

  }

  addNewMeasurement(evt) {

    evt.stopImmediatePropagation();
    evt.stopPropagation();
    evt.preventDefault();

    // const eventData = evt.detail;
    // const measurementData = this.createNewMark(eventData);
    // const element = evt.detail.element;

    // addToolState(element, this.name, measurementData);
  }

  handleSelectedCallback(evt, toolData, handle) {
    var interactionType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'mouse';

    const isCtrlKey = (evt.detail.event.ctrlKey)
    if (isCtrlKey) {
      const inputText = prompt('修改标注内容：', '')
      if (inputText) toolData.text = inputText
      external.cornerstone.updateImage(evt.detail.element);
      return
    }

    this._triggerSelect(evt.detail.element, toolData);

    moveHandleNearImagePoint(evt, this, toolData, handle, interactionType);
  }

  // toolSelectedCallback(evt, annotation) {
  //   var interactionType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'mouse';

  //   this._triggerSelect(evt.detail.element, annotation);
  //   console.log('an',evt)

  //   moveAnnotation(evt, this, annotation, interactionType);
  // }


  createNewMark(evt) {
    const eventData = evt.detail
    const config = this.configuration;
    const element = evt.detail.element;

    const inputText = prompt('请输入标注文字', config.current)
    if (!inputText) {
      const modifiedEventData = {
        toolName: this.name,
        element,
        measurementData: {}
      }
  
      triggerEvent(
        element,
        csTools.EVENTS.MEASUREMENT_COMPLETED,
        modifiedEventData
      );
  
      csTools.clearToolState(element, this.name);
      external.cornerstone.updateImage(element);
      return;
    }

    // Create the measurement data for this tool with the end handle activated
    const measurementData = {
      visible: true,
      active: true,
      text: inputText,
      color: 'black',
      handles: {
        end: {
          x: eventData.currentPoints.image.x,
          y: eventData.currentPoints.image.y,
          highlight: true,
          active: true,
          hasBoundingBox: true,
        },
      },
    };

    // Create a rectangle representing the image
    const imageRect = {
      left: 0,
      top: 0,
      width: eventData.image.width,
      height: eventData.image.height,
    };

    // Check if the current handle is outside the image,
    // If it is, prevent the handle creation
    if (
      !external.cornerstoneMath.point.insideRect(
        measurementData.handles.end,
        imageRect
      )
    ) {
      return;
    }

    // Update the current marker for the next marker
    let currentIndex = config.markers.indexOf(config.current);

    const increment = config.ascending ? 1 : -1;

    currentIndex += increment;

    if (currentIndex >= config.markers.length) {
      currentIndex = config.loop ? 0 : -1;
    } else if (currentIndex < 0) {
      currentIndex = config.loop ? config.markers.length : -1;
    }

    config.current = config.markers[currentIndex];


    addToolState(element, this.name, measurementData);

    const modifiedEventData = {
      toolName: this.name,
      element,
      measurementData
    }

    triggerEvent(
      element,
      csTools.EVENTS.MEASUREMENT_COMPLETED,
      modifiedEventData
    );

    csTools.clearToolState(element, this.name);
    external.cornerstone.updateImage(element);

  }

  pointNearTool(element, data, coords) {
    if (data.visible === false) {
      return false;
    }

    if (!data.handles.end.boundingBox) {
      return;
    }

    const distanceToPoint = external.cornerstoneMath.rect.distanceToPoint(
      data.handles.end.boundingBox,
      coords
    );
    const insideBoundingBox = pointInsideBoundingBox(data.handles.end, coords);

    return distanceToPoint < 10 || insideBoundingBox;
  }

  updateCachedStats() {
    // Implementing to satisfy BaseAnnotationTool
  }

  renderToolData(evt) {
    const eventData = evt.detail;
    const config = this.configuration;

    // If we have no toolData for this element, return immediately as there is nothing to do
    const toolData = getToolState(eventData.element, this.name);

    if (!toolData) {
      return;
    }

    // We have tool data for this element - iterate over each one and draw it
    const context = getNewContext(eventData.canvasContext.canvas);

    for (let i = 0; i < toolData.data.length; i++) {
      const data = toolData.data[i];

      if (data.visible === false) {
        continue;
      }

      const color = toolColors.getColorIfActive(data, this);

      draw(context, context => {
        setShadow(context, config);

        const textCoords = external.cornerstone.pixelToCanvas(
          eventData.element,
          data.handles.end
        );

        const options = {
          centering: {
            x: true,
            y: true,
          },
          select: data.select,
          toolName: 'text',
        };

        data.handles.end.boundingBox = drawTextBox(
          context,
          data.text,
          textCoords.x,
          textCoords.y - 10,
          color,
          options
        );
      });
    }
  }

  _changeText(evt) {
    const eventData = evt.detail;
    const { element, currentPoints } = eventData;
    let data;


    

    const config = this.configuration;
    const coords = currentPoints.canvas;
    const toolData = getToolState(element, this.name);

    // Now check to see if there is a handle we can move
    if (!toolData) {
      return;
    }



    for (let i = 0; i < toolData.data.length; i++) {
      data = toolData.data[i];
      if (this.pointNearTool(element, data, coords)) {
        data.active = true;
        external.cornerstone.updateImage(element);

        // Allow relabelling via a callback
        // this.changeTextCallback(data, eventData, doneChangingTextCallback);
        // doneChangingTextCallback(data, prompt('请输入标注文字', data.text), this.name, element);


        try {
          evt.stopImmediatePropagation();
          evt.preventDefault();
          evt.stopPropagation();
        } catch (error) {
          // Console.log(error)
        }

        return;
      }
    }
  }
}

 function doneChangingTextCallback(data, updatedText, cpName, element) {
  if (updatedText == null) {
    // updatedText = data.text;
    removeToolState(element, cpName, data);
  } else {
    data.text = updatedText;
  }


  data.active = false;
  external.cornerstone.updateImage(element);
}
