// 引入拖拽组件
import draggable from 'vuedraggable'
import { cloneDeep } from 'lodash-es';
import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import { dbDisplayPlan, defaultPlanData } from "$library/db";

import '$assets/ifont/iconfont.css'
import '$assets/ifont/iconfont.js'
import 'element-ui/lib/theme-chalk/index.css';

import "$assets/style/public.scss"
// 全局 cornerstone 初始化
import initCornerstone from '$library/cornerstone/init'
initCornerstone()

// 全局方法
import Fun from '$library/utils/function'
Vue.prototype.$fun = Fun

// 接口
import Api from '$api'
Vue.prototype.$Api = Api

// 右键
import contentmenu from 'v-contextmenu'
import 'v-contextmenu/dist/index.css'

Vue.use(contentmenu)
// 按需引入 element-ui
import Message from 'element-ui/lib/message.js'
import MessageBox from 'element-ui/lib/message-box.js'
import Notification from 'element-ui/lib/notification'

import Aside from 'element-ui/lib/aside'
import Container from 'element-ui/lib/container'
import Dropdown from 'element-ui/lib/dropdown.js'
import DropdownMenu from 'element-ui/lib/dropdown-menu.js'
import DropdownItem from 'element-ui/lib/dropdown-item.js'
import Slider from 'element-ui/lib/slider.js'
import Select from 'element-ui/lib/select.js'
import Option from 'element-ui/lib/option.js'
import Progress from 'element-ui/lib/progress.js'
import Dialog from 'element-ui/lib/dialog.js'
import ColorPicker from 'element-ui/lib/color-picker.js'
import InputNumber from 'element-ui/lib/input-number.js'
import Input from 'element-ui/lib/input.js'
import Autocomplete from 'element-ui/lib/autocomplete.js'
import Button from 'element-ui/lib/button.js'
import Switch from 'element-ui/lib/switch.js'

import CheckboxGroup from 'element-ui/lib/checkbox-group.js'
import Checkbox from 'element-ui/lib/checkbox.js'

import Table from 'element-ui/lib/table.js'
import TableColumn from 'element-ui/lib/table-column.js'

import RadioGroup from 'element-ui/lib/radio-group.js'
import Radio from 'element-ui/lib/radio.js'

import Popover from 'element-ui/lib/popover.js'

import Loading from 'element-ui/lib/loading.js'

import Scrollbar from 'element-ui/lib/scrollbar.js'

import Tabs from 'element-ui/lib/tabs.js'
import TabPane from 'element-ui/lib/tab-pane.js'

import Image from 'element-ui/lib/image.js'
import Icon from 'element-ui/lib/icon.js'

import DatePicker from 'element-ui/lib/date-picker.js'
import TimePicker from 'element-ui/lib/time-picker.js'

import Badge from 'element-ui/lib/badge.js'

Vue.use(Table)
Vue.use(TableColumn)

Vue.use(Button)
Vue.use(Aside)
Vue.use(Container)
Vue.use(Slider)
Vue.use(Dropdown)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Select)
Vue.use(Option)
Vue.use(Progress)
Vue.use(Dialog)

Vue.use(CheckboxGroup)
Vue.use(Checkbox)
Vue.use(ColorPicker)
Vue.use(InputNumber)
Vue.use(Input)
Vue.use(Autocomplete)
Vue.use(Switch)
Vue.use(RadioGroup)
Vue.use(Radio)

Vue.use(Popover)

Vue.use(Scrollbar)

Vue.use(Tabs)
Vue.use(TabPane)

Vue.use(DatePicker)
Vue.use(TimePicker)

Vue.use(Image)
Vue.use(Icon)

Vue.use(Loading)
Vue.use(Loading.directive)

Vue.use(Badge)

Vue.prototype.$loading = Loading
Vue.prototype.$message = Message
Vue.prototype.$MessageBox = MessageBox
Vue.prototype.$notify = Notification

Vue.use(draggable)

Vue.directive('number', {
	inserted: function (e) {
		const el = e.getElementsByTagName('input')[0];
		el.addEventListener('input', function () {
			el.value = el.value.replace(/[^\d]/g, "") //只能输入数字
		})
	}
})
Vue.directive('floorNumber', {
	inserted: function (e, binding) {
		const el = e.getElementsByTagName('input')[0];
		el.addEventListener('input', function () {

			let t = el.value.charAt(0);
			const precision = Number(binding.arg) || 2;
			const regexPattern = `([0-9]+\.[0-9]{${precision}})[0-9]*`;
			const regex = new RegExp(regexPattern);

			el.value = el.value.replace(".", "$#$")//把第一个字符'.'替换成'$#$'
				.replace(/\./g, "")//把其余的字符'.'替换为空
				.replace("$#$", ".")//把字符'$#$'替换回原来的'.'
				.replace(/[^\d.]/g, "")//只能输入数字和'.'
				.replace(/^\./g, "")//不能以'.'开头
				.replace(regex, "$1")//只保留2位小数  
			if (t == '-') {
				el.value = '-' + el.value;
			}
			if (el.value == '-0') {
				el.value = '0';
			}

		})
	}
})


Vue.config.productionTip = false

dbDisplayPlan.then((e) => {
	e.getGroup('displayPlan').then((e) => {
		if (e.success && e.data && e.data.length === 0) {
			// 如果数据库里没有数据，加入一个默认显示方案
			const defaultData = cloneDeep(defaultPlanData)
			dbDisplayPlan.then(e => {
				e.add(defaultData).then(() => {
					// 方案存储到vuex中
					store.commit('setSchemeRebuilds', [defaultData])
					console.log('加入默认显示方案')
				})
			});
		}else {
			// 方案存储到vuex中
			store.commit('setSchemeRebuilds', e.data)
		}
	});
});


// 设置浏览器顶部标签名
let oTitle = document.getElementsByTagName('title')[0];
if (window.configs.systemName) {
	oTitle.innerText = window.configs.systemName;
}

new Vue({
	router,
	store,
	render: h => h(App)
}).$mount('#app')
