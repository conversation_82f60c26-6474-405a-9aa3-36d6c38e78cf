import tryGetVtkVolumeForSeriesNumber from '$library/cornerstone/mpr/tryGetVtkVolumeForSeriesNumber.js';
import appState from '$library/cornerstone/mpr/store/appState.js';
/**
 * 获取新的图像 id 列表 一个角度的
 * @param {*} direction  角度（sliceType）切片类型
 * @param {*} ctData     vtk 中读取分析到的数量值，起始位置值
 * @param {*} thickness  切割的层厚
 * @param {*} ctUid  ctUid
 * @param {*} petUid  petUid
 * @param {*} clip       图像裁剪
 * @param {*} offsetParams       图像偏移\旋转值
 * @returns 
 */
export function getNewImageIds(direction, ctData, thickness, ctUid, petUid, clip = 'clip', offsetParams = '0,0,0,0,0,0'){
    let ctIds = [], ptIds = []
    let count = 0
    let start = 0
    const offsetParamsArr = offsetParams.split(',') // 前三个是pt偏移 后三个是ct偏移
    switch (direction) {
        case 'sagittal':
            count = ctData.xCount
            start = ctData.xStart
            for (let index = 0; index < count; index++) {
                // 0 CT、MR...，1 PT
                // TODO MR 应该是要有角度的
                const iop = '0,1,0,0,0,-1'

                if (ctUid) {
                    ctIds.push('mpr:'+ ctUid +':' + iop + ':' + start + ',0,0: : :' + offsetParamsArr.slice(3).join(','))
                    if (petUid) {
                        ptIds.push('mpr:'+ petUid +':' + iop + ':' + start + ',0,0:' + clip + ':' + ctUid + ':' + offsetParamsArr.slice(0,3).join(','))
                    }
                }else {
                    ptIds.push('mpr:'+ petUid +':' + iop + ':' + start + ',0,0: : :' + offsetParamsArr.slice(0,3).join(','))
                }
                start += thickness
            }
            break;
        case 'coronal':
            count = ctData.yCount
            start = ctData.yStart
            for (let index = 0; index < count; index++) {
                const iop = '1,0,0,0,0,-1'

                if (ctUid) {
                    ctIds.push('mpr:'+ ctUid +':' + iop + ':0,' + start + ',0: : :' + offsetParamsArr.slice(3).join(','))
                    if (petUid) {
                        ptIds.push('mpr:'+ petUid +':' + iop + ':0,' + start + ',0:' + clip + ':' + ctUid + ':' + offsetParamsArr.slice(0,3).join(','))
                    }
                }else {
                    ptIds.push('mpr:'+ petUid +':' + iop + ':0,' + start + ',0: : :' + offsetParamsArr.slice(0,3).join(','))
                }
                start += thickness
            }
            break;
        default:
            count = ctData.zCount
            start = ctData.zStart
            // 横切面
            // 起始位置开始，遍历添加张数 1,0,0,0,1,0
            for (let index = 0; index < count; index++) {
                // TODO 不知道为什么用一个稍微倾斜的角度(IOP)，最终出来的图像会缺少？位置不对？
                // ctIds.push('mpr:'+ ctUid +':'+ imageOrientationPatient.join() +':0,0,' + start + ':' + componentId)
                // ptIds.push('mpr:'+ petUid +':' + imageOrientationPatient.join() + ':0,0,' + start + ':' + clip + ':' + componentId)
                const iop = '1,0,0,0,1,0'

                if (ctUid) {
                    ctIds.unshift('mpr:'+ ctUid +':' + iop + ':0,0,' + start + ': : :' + offsetParamsArr.slice(3).join(','))
                    if (petUid) {
                        ptIds.unshift('mpr:'+ petUid +':' + iop + ':0,0,' + start + ':' + clip + ':' + ctUid + ':' + offsetParamsArr.slice(0,3).join(','))
                    }
                }else {
                    ptIds.unshift('mpr:'+ petUid +':' + iop + ':0,0,' + start + ': : :' + offsetParamsArr.slice(0,3).join(','))
                }
                start += thickness
            }
            break;
    }

    return [ctIds, ptIds];
}

/**
 * 获取容积中的x,y,z信息，有用最大切割数量，有用最大轴位值
 * @param {*} seriesIndex  序列下标 0 CT, 1 PT （appstate.js文件中序列）
 * @param {*} thickness    层厚
 */
export async function getVolumeSliceNumber(seriesIndex = 0, thickness = 3){
    const { vtkVolume } = await tryGetVtkVolumeForSeriesNumber(seriesIndex);
    const vtkImageData = vtkVolume.vtkImageData;
    const [x0, y0, z0] = vtkImageData.getOrigin();  // x,y,z 原点
    const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
    const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

    let xStart = x0 // + xSpacing * (xMax - xMin);  // x最大值(ipp 切片x最大位)
    let yStart = y0 // + ySpacing * (yMax - yMin);  // y最大值
    let zStart = z0 // + zSpacing * (zMax - zMin);  // z最大值

    // 算出有用的张数
    let xNmax = xSpacing * (xMin + xMax);
    let xCount = Math.round(xNmax / thickness);

    let yNmax = ySpacing * (yMin + yMax);
    let yCount = Math.round(yNmax / thickness);

    let zNmax = zSpacing * (zMin + zMax) + zSpacing;
    let zCount = Math.round(zNmax / thickness);

    return { xCount, yCount, zCount, xStart, yStart, zStart, 
        xNmax, yNmax, zNmax
    };
}


// 加载多帧
export async function loadFrameImage(imageIds) {
    if (imageIds.length === 1 && !imageIds[0].includes('?frame=')) {
        let url = imageIds[0].slice(9)
        await cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.load(url, cornerstoneWADOImageLoader.internal.xhrRequest).then((dataSet) => {
            let numFrames = dataSet.intString('x00280008');
            if (numFrames && numFrames > 1){
                imageIds.splice(0)
                for(let i=0; i < numFrames; i++) {
                    const imageId = 'dicomweb:' + url + "?frame=" + i;
                    imageIds.push(imageId);
                }
            }
        })
    }
    return imageIds;
}

export function storePatientPosition(patientPosition, ctUid, petUid, direction) {
    if (patientPosition === 'HFP' || patientPosition === 'FFP') {
        if (direction === 'sagittal') {
            const ctId = `mpr+${ctUid}+0,1,0,0,0,-1+ `
            cornerstone.recumbentPosition[ctId] = {hflip: true}
            if (petUid) {
                const petId = `mpr+${petUid}+0,1,0,0,0,-1+${ctUid}`
                cornerstone.recumbentPosition[petId] = {hflip: true}
            } 
        }else if (direction === 'coronal') {
            const ctId = `mpr+${ctUid}+1,0,0,0,0,-1+ `
            cornerstone.recumbentPosition[ctId] = {hflip: true}
            if (petUid) {
                const petId = `mpr+${petUid}+1,0,0,0,0,-1+${ctUid}`
                cornerstone.recumbentPosition[petId] = {hflip: true}
            }
        }else {
            const ctId = `mpr+${ctUid}+1,0,0,0,1,0+ `
            cornerstone.recumbentPosition[ctId] = { rotaion: 180 }
            if (petUid) {
                const petId = `mpr+${petUid}+1,0,0,0,1,0+${ctUid}`
                cornerstone.recumbentPosition[petId] = { rotaion: 180 }
            }
        }
    }
}