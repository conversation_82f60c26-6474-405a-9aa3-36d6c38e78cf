const databaseName = 'image';
const table = [
	{
		storeName: 'layoutSet',
		keypath: 'id',
		createIndex: [
			{name: 'templateType', options: {unique: false}}
		]
	},
	{
		storeName: 'screenImage',
		keypath: 'id',
		createIndex: [
			{name: 'groupId', options: {unique: false}}
		]
	},
	{
		storeName: 'lesionAssess',
		keypath: 'id',
		createIndex: [
			{name: 'patientKey', options: {unique: false}}
		]
	},
	{
		storeName: 'displayPlan',
		keypath: 'id',
		createIndex: [
			{name: 'planKey', options: {unique: false}}
		]
	}
];

export function getDbTable() {
	return table
}

export function dbInit(params) {
	return new Promise((resolve, reject) => {
		// 加了个病灶评估，版本改为 2，加个新表，就要改变版本
		// 加了个displayPlan，版本改为 3，加个新表，就要改变版本
		const connection = window.indexedDB.open(databaseName, params.version || 3);
		connection.onsuccess = function (event) {
			const db = event.target.result;
			const storeName = params.storeName;
			let defaultIndex = '';

			const findItem = table.find(item => {
				return item.storeName === storeName;
			})
			if (findItem && findItem.createIndex[0]) {
				defaultIndex = findItem.createIndex[0].name;
			}

			// 添加
			function add(item) {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readwrite')
						.objectStore(storeName)
						.add(item);
					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				})
			}

			function put(item) {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readwrite')
						.objectStore(storeName)
						.put(item);
					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				})
			}
			
			// 更新
			function update(item) {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readwrite')
						.objectStore(storeName)
						.put(item);
					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				})
			}
		
			// 删除 
			function del(id) {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readwrite')
						.objectStore(storeName)
						.delete(id);

					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				})
			}
		
			// 读取
			function read(id) {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName])
						.objectStore(storeName).getAll(id);

					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				})
			}
		
			// 获取组数据
			function getGroup(id, attr) {
				const indexAttr = typeof attr === 'string' ? attr : defaultIndex;
				
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readonly')
						.objectStore(storeName)
						.index(indexAttr)
						.getAll(id);
					
					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				});
			}

			function getAll() {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readonly')
						.objectStore(storeName)
						.getAll();
					
					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				});
			}

			function clear() {
				return new Promise((resolve, reject) => {
					const request = db.transaction([storeName], 'readwrite')
						.objectStore(storeName)
						.clear();

					request.onsuccess = function () {
						resolve({
							success: true,
							data: request.result || {}
						});
					};
					request.onerror = function (event) {
						reject({
							success: false,
							msg: event
						});
					}
				})
			}
			
			resolve({
				add,
				put,
				update,
				del,
				read,
				getGroup,
				getAll,
				clear
			});
		}
		// 未创建数据库先 onupgradeneeded 再到 onsuccess
		// 已经创建数据库不在执行，只有版本改变才会执行
		connection.onupgradeneeded = function (event) {
			const db = event.target.result;

			table.map(item => {
				if (!db.objectStoreNames.contains(item.storeName)) {

					let objectStore = db.createObjectStore(item.storeName, {
						autoIncrement: true,
						keyPath: item.keypath
					});
	
					item.createIndex.map(item => {
						objectStore.createIndex(item.name, item.name, item.options);
					})
				};
			})
		};
		connection.onerror = function (err) {
			reject(err);
		}
	})
}