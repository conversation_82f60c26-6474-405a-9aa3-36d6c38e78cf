export { default as setCrosshairs } from './setCrosshairs.vue'            // 设置定位线
export { default as setToolsColor } from './setToolsColor.vue'            // 设置工具颜色样式
export { default as setWindowCenter } from './setWindowCenter.vue'        // 预设值配置
export { default as setShowRebuild } from './setShowRebuild.vue'          // 显示方案（重建）
export { default as setShowRead } from './setShowRead.vue'                // 显示阅图设置


export { default as otherInfoSet } from './otherInfoSet.vue'                // 其它操作设置
export { default as setPET } from './setPET.vue'                            // PET 设置

export { default as setImageTag } from './setImageTag.vue'                  // 图像tag显示设置 

export { default as layerMatchRebuild } from './layerMatchRebuild.vue'
export { default as tabsRebuild } from './tabsRebuild.vue'


export { default as setDot } from './setDot.vue'                            // 点测量，半径值
export { default as setCircleRoi } from './setCircleRoi.vue'                // 圆测量
export { default as setCircleMeasure } from './setCircleMeasure.vue'        // 体测量（画圆）


export { default as defaultLayout } from './defaultLayout.vue'   // 重建默认模式
export { default as defaultLayoutModule } from './defaultLayoutModule.vue'   // 重建默认模式

export { default as setImageSharpness } from './setImageSharpness.vue'      // 设置图像质量

