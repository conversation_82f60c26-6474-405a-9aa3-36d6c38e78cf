import cornerstone from "$library/cornerstone/cornerstone";

export default function getPatientWeightAndCorrectedDose(imageId, seriesUID) {
    const seriesModule = cornerstone.metaData.get("generalSeriesModule", imageId);

    const patientStudyModule = cornerstone.metaData.get(
        "patientStudyModule",
        imageId
    );

    if (!patientStudyModule) {
        throw new Error("patientStudyModule metadata is required");
    }

    const patientWeight = patientStudyModule.patientWeight; // In kg

    // if (!patientWeight) {
    //   throw new Error(
    //     'patientWeight must be present in patientStudyModule for modality PT'
    //   );
    // }
    const petSequenceModule = cornerstone.metaData.get(
        "petIsotopeModule",
        imageId
    );

    if (!petSequenceModule) {
        throw new Error("petSequenceModule metadata is required");
    }

    const geModule = cornerstone.metaData.get("geModule", imageId);

    if (!geModule) {
        throw new Error("petSequenceModule metadata is required");
    }
    // TODO:
    // - Update this to match the SUV logic provided here:
    //   https://github.com/salimkanoun/fijiPlugins/blob/master/Pet_Ct_Viewer/src/SUVDialog.java
    // - Test with PET datasets from various providers to ensure SUV is correct
    const radiopharmaceuticalInfo = petSequenceModule.radiopharmaceuticalInfo;
    const startTime = radiopharmaceuticalInfo.startTime;
    const totalDose = radiopharmaceuticalInfo.radionuclideTotalDose;
    const halfLife = radiopharmaceuticalInfo.radionuclideHalfLife;
    const seriesDate = radiopharmaceuticalInfo.seriesDate;
    const seriesTime = radiopharmaceuticalInfo.seriesTime;
    const studyDate = radiopharmaceuticalInfo.studyDate;
    let decayFactor = radiopharmaceuticalInfo.decayFactor || 1;

    const injectionstartTime = radiopharmaceuticalInfo.radiopharmaceuticalStartTime || {};
    const seriesAcquisitionTime = seriesModule.seriesTime;

    const manufacturer = String(
        radiopharmaceuticalInfo.manufacturer
    ).toUpperCase();

    let correctedDose = 0.0;

    const getDateStmp = (strtmp) => {
        let result = 0;
        try {
            let year = strtmp.substring(0, 4);
            let month = strtmp.substring(4, 6);
            let day = strtmp.substring(6, 8);
            let hour = strtmp.substring(8, 10);
            let minute = strtmp.substring(10, 12);
            let second = strtmp.substring(12, 14);
            result = +new Date(year, month - 1, day, hour, minute, second).getTime(); // 初始时，日期
        } catch (error) {
            console.log("getDateStmp err:" + strtmp);
        }
        return result;
    };

    if (!totalDose && manufacturer === 'GE MEDICAL SYSTEMS') {
        // 没有总剂量的时候，GE设备处理
        let measuredTracerActivityDate;
        let administreredDateTime;

        measuredTracerActivityDate = geModule.measuredTracerActivityDate;
        administreredDateTime = geModule.administreredDateTime;

        if (!measuredTracerActivityDate || !administreredDateTime) {
            if (seriesUID) {
                let localStoreItem = localStorage.getItem("setting-suv-params");
                localStoreItem = !localStoreItem ? {} : JSON.parse(localStoreItem);
                const targetItem = localStoreItem[seriesUID];
                measuredTracerActivityDate = targetItem.measuredTracerActivityDate;
                administreredDateTime = targetItem.administreredDateTime;
            }
        }
        // 初始时，日期
        let stm1 = getDateStmp(measuredTracerActivityDate);

        // 注射时，日期
        let stm2 = getDateStmp(administreredDateTime);

        // 注射时间 - 初始时间。结果秒数
        let diff = (stm2 - stm1) / 1000;

        // 减去注射前衰减的剂量
        // 损耗时间 * Math.log(0.5) === 负损耗时间 * Math.log(2)
        correctedDose =
            geModule.measuredTracerActivityDose *
            1000000.0 *
            Math.exp((diff / halfLife) * Math.log(0.5));

        // 减去针筒残留剂量？ 未知
        correctedDose -=
            (geModule.postInjectionMeasuredDose * 1000000.0) /
            Math.exp(((0 * 1.0) / halfLife) * Math.log(0.5));

        // 图像采集开始时间，日期
        let strtmp = geModule.acquisitionDate + geModule.acquisitionTime; // 图像采集开始时间，日期
        let stm3 = getDateStmp(strtmp); // 初始时，日期

        // 采集时间 - 注射时间  = 已过秒数
        const acquisitionDiff = (stm3 - stm2) / 1000;

        // 扫描时实际剂量，注射到扫描时损耗
        correctedDose =
            correctedDose * Math.exp((acquisitionDiff / halfLife) * Math.log(0.5));
        // 衰变因子
        correctedDose *= geModule.decayFactor;

    }else if (manufacturer === "UIH") {
         // 联影图像
        let acquisitionDateUIH =
            radiopharmaceuticalInfo.acquisitionDate || studyDate;
        let acquisitionTimeUIH = seriesTime
            ? seriesTime
            : radiopharmaceuticalInfo.acquisitionTime;
        let strtmp1UIH = getDateStmp(acquisitionDateUIH + startTime);
        let strtmp2UIH = getDateStmp(acquisitionDateUIH + acquisitionTimeUIH);
        var durationInSecondsUIH = (strtmp2UIH - strtmp1UIH) / 1000;
        correctedDose =
            totalDose * Math.pow(2.0, -(durationInSecondsUIH / halfLife));
        decayFactor = 1;
    }else {
        // 旧
        const acquisitionTimeInSeconds =
            _fracToDec(seriesAcquisitionTime.fractionalSeconds || 0) +
            seriesAcquisitionTime.seconds +
            seriesAcquisitionTime.minutes * 60 +
            seriesAcquisitionTime.hours * 60 * 60;
        const injectionStartTimeInSeconds =
            _fracToDec(injectionstartTime.fractionalSeconds || 0) +
            injectionstartTime.seconds +
            injectionstartTime.minutes * 60 +
            injectionstartTime.hours * 60 * 60;
        const durationInSeconds =
            acquisitionTimeInSeconds - injectionStartTimeInSeconds;
        correctedDose =
            totalDose * Math.exp((-durationInSeconds * Math.log(2)) / halfLife);
    }


    return { patientWeight, correctedDose, decayFactor, radiopharmaceuticalInfo };
}

/**
 * Returns a decimal value given a fractional value.
 * @private
 * @method
 * @name _fracToDec
 *
 * @param  {number} fractionalValue The value to convert.
 * @returns {number}                 The value converted to decimal.
 */
function _fracToDec(fractionalValue) {
    return parseFloat(`.${fractionalValue}`);
}
