/**
 * 重建
 */
import CornerstoneViewport from '$components/CornerstoneViewport.vue'
import BaseTools from '$components/tools/BaseTools'
import AreaFit from '$src/layout/AreaFit.vue'

import appState from '$library/cornerstone/mpr/store/appState.js';

import getIppByPoint from '$library/cornerstone/tools/math/getIppByPoint.js';

import { getNewImageIds, getVolumeSliceNumber } from '$library/cornerstone/function/getImageIds.js';
import toolCoordTransition, { updateToolCoord } from '$library/cornerstone/function/toolCoordTransition.js';
import { debounce, throttle } from 'lodash-es';

import getConfigByStorageKey from '$library/utils/configStorage.js';

export default {
    components: {
        CornerstoneViewport,
        BaseTools,
        AreaFit
    },
    props: {
        series: {               // 图像序列 uid 集合
			type: Object,
			default: () => {}
		},
		seriesId: {             // 存的是检查的 uid
			type: String,
			default: ''
		},
        tabId: {                // 当前打开 tab id
            default: 'none',
        }
    },
    data() {
        return {
            styleFont: 'overlay-10',     // 响应字体变化
            originSeries: [],            // 存储的原始序列
            viewports: [],               // 视图渲染数据
            viewportElements: [],        // 视图元素-dom
            activeViewportIndex: 0,      // 当前选中视图
            showAction: -1,              // 工具显示
            fullScreenIndex: -1,         // 用于判断全屏
            activeTool: 'Airtools',      // 当前工具 Airtools
            isOverlayVisible: true,      // 覆盖信息显示
            toolRenderLast: false,       // 用于控制，在工具渲染后，不在触发点选状态（直接赋值选中）
            toolThrottle: null,          // 工具渲染节流
            scrollThrottle: false,       // 滚动节流
            ignoreFiredEvents: false,    // 渲染新图像忽略事件
            firedEvents: null,           // 忽略事件时，用户操作的元素
            style: {
				width: '100%',
				height: '100%',
			},

            allModalityIndex: {},        // ct\mr pt\nm 融合,不同角度第一个位置
            allowAngel: 'all',           // 允许角度，all 全部。x, y, z 角度
            // componentId: '',             // 当前模块 id 唯一值
            customLayout: [],            // 自定义布局按钮
            selectLayout: '2x2axial',    // 当前选中的布局

            groupUid: [],                // 每一组重建 uid
            loadShowCount: 0,            // 已经加载显示出来的图像数量


            layoutMatrix: {              // 布局矩阵信息用于改变布局时控制放入视图
                column: 2,               // 原始改变布局列数量
                originalIdx: [],         // 原始改变布局 customLayout 值
                matrix10: [],            // 基于 original 存储10x10布局 customLayout 的值
                isReload: true,          // 是否获取新的 matrix10 存储值
                curColumn: 2,            // 当前布局列
                curRow: 2,               // 当前布局行
            },

            renderAbsoluteThrottle: {    // 渲染节流 - 调窗、旋转、翻转、反片
                renderEvents: false,      
                renderElement: null
            },

            lockSync: {
                start: true,           // 启动锁
                windowColor: true,      // 伪彩、窗宽窗位同步
                convert: true,          // 旋转，翻转，反片
                moreZoom: true,         // 移动、缩放
                scroll:  true,          // 翻页
            },
            lookScrollLayout: {},        // 对比时，点击锁定同步
            synchronizerCrosshairs: {},  // 多组定位线同步器
            // showReferenceLines: false,   // 定位线显示隐藏
            vtkAndCsRenderState: {
                curRender: 'none',
                timer: null
            }, // 谁在渲染，截断处理
            crosshairsTool: {
                mipShow: true
            },
            gridLayoutStyle: {
                containerStyle: {},
                itemStyle: []
            },
            timerAddCrosshairs: null, // 添加定位线清除句柄
            timerSetCrosshairs: null
        }
    },
    mounted() {
        // this.componentId = this.$fun.onlyValue()
        // this.setReferenceLines(this.showReferenceLines)
    },
    watch: {
        // 监听工具改变
        activeTool: {
            handler(later){
                if (later === 'Wwwc') {
                    this.vtkToggleWL('Wwwc')
                }else if (later === 'vtkInteractorStyleMPRSlice') {
                    this.vtkToggleToMPRSlice('vtkInteractorStyleMPRSlice')
                }else if (['DragProbe', 'TextMarker', 'ArrowAnnotate'].includes(later)) {
                    // this.vtkToggleToProbe('DragProbe')
                    this.vtkToggleToSomeMarks(later)
                }else {
                    this.vtkToggleToCrosshairs('NuclearCrosshairs')
                }
            }
        },
        'crosshairsTool.mipShow': {
            handler(later) {
                if (later) {
                    // 开启线条绘制
                    this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: false })
                    this.setCrosshairs()
                    setTimeout(() => {
                        this.activeTool = 'Airtools';
                    }, 0);

                }else {
                    this.$store.commit('setReferenceLines', { tabId: this.tabId, hide: true })
                    const tempTool = this.activeTool
                    this.activeTool = 'temp'
                    setTimeout(() => {
                        this.activeTool = tempTool
                    }, 0);
                }

                const showMipCross = !this.$store.state.hideReferenceLines[this.tabId] // 定位线按钮是否开启
                // 控制定位线显示与否
                this.apis.forEach(api => {
                    const { svgWidgetManager, svgWidgets } = api;
                    this.vtkAndCsRenderState.curRender = 'cs'
                    this.$nextTick(() => {
                        svgWidgets.crosshairsWidget.setDisplay(showMipCross)
                        svgWidgetManager.render()
                        this.setvtkAndCsRenderThrottle()
                    })
                })

                // 在改变定位线时候，要更新图像(开启关闭定位线更新)
                this.viewportMap(el => {
                    if (el){
                        const isImage = cornerstone.getImage(el)
                        if (isImage) {
                            cornerstone.updateImage(el)
                        }
                    }
                })

                if (later) {
                    this.vtkToggleToCrosshairs('NuclearCrosshairs')
                }

            },
        },
    },
    computed: {
        showCsViewportLen(){
            // 用 cornerstone 显示的视图数
            return this.viewports.filter(item => { return item.sModality }).length;
        },
        customLayoutShow(){
            return this.customLayout.filter(_ => {
                return !_.hidden
            })
        },
    },
    methods: {
        // setReferenceLines(status) {
        //     cornerstoneTools.store.state.showReferenceLines = status
        //     this.viewportMap(el => {
        //         if (el){
        //             const isImage = cornerstone.getImage(el)
        //             if (isImage) {
        //                 cornerstone.updateImage(el)
        //             }
        //         }
        //     })
        // },
        // 设置字体通过宽高
        setFontByWidthAndHeight(index = -1) {
            this.$store.state.renderScroll = !this.$store.state.renderScroll
            try {
                const dom = this.getEnabledElement(index === -1 ? 0 : index)
                if (!dom) {
                    return
                }
                const width = dom.clientWidth
                if (width >= 320) {
                    this.styleFont = 'overlay-10'
                }else if (width >= 240) {
                    this.styleFont = 'overlay-08'
                }else if (width >= 210) {
                    this.styleFont = 'overlay-07'
                }else if (width >= 160) {
                    this.styleFont = 'overlay-06'
                }else if (width >= 130) {
                    this.styleFont = 'overlay-05'
                }else if (width >= 105) {
                    this.styleFont = 'overlay-04'
                }else if (width >= 75) {
                    this.styleFont = 'overlay-03'
                }else if (width >= 50) {
                    this.styleFont = 'overlay-02'
                }else {
                    this.styleFont = 'overlay-01'
                }
            } catch (error) {
                // console.log(error)
            }
		},
        onClickFullScreen() {
            this.onDblclickViewport(this.saveImage.rightSelectElement, this.activeViewportIndex)
        },
        // 双击全屏
        onDblclickViewport(el,index) {
            if (this.activeTool === 'TextMarker') return;
            // 全屏
            this.fullScreenIndex = this.fullScreenIndex === index ? -1 : index 

            // 全屏后，触发让 cornerstone 相应调整
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent);
                this.setFontByWidthAndHeight(index);
                
            })

            // 同步其它视图
            setTimeout(() => {

                if (el) {
                    let offsetVal = 0;
                    for (let i = 0; i < index; i++) {
                        const viewport = this.viewports[i];
                        if (viewport.vtk) {
                            offsetVal += 1;
                        }
                    }
                    const item = this.$refs.csViewport[index - offsetVal] || {}
                    if (item.state) {
                        item.syncCallBack('relative')
                        // 需要手动同步页数
                        // console.log(item.imageIdIndex)
                        const allStack = cornerstoneTools.getToolState(el, 'stack')
                        if (!allStack || !allStack.data.length) { return null }
                        const state = allStack.data[0].state
                        this.updateAllModalityIndex(item.imageIdIndex,
                            state.layoutId, item.element)

                        this.setCurrentViewportIndex()

                    }                    
                }

                // 执行 baseTools 组件中的方法
                this.$refs.baseTools.onAllRender()
            }, 200);
        },
        // 设置当前选中的视窗
		setViewportActive(index, button){
            this.activeViewportIndex = index
            this.coordinateToolsIndex = this.activeViewportIndex
            this.rotationToolsIndex = this.activeViewportIndex
            if (this.toolRenderLast) {
                this.toolRenderLast = false
                this.showAction = this.activeViewportIndex
                return;
            }
            if (button !== 0) {
                this.showAction = index;
                return;
            }
            // 选中出现工具
			this.showAction = this.activeTool === 'Airtools' && !this.crosshairsTool.mipShow && this.showAction === index ? -1 : index
		},
        // 从视窗中通过下标获取 enabled 元素
		getEnabledElement(index){
            const viewportEle = this.viewportElements[index].getElementsByClassName('viewport-element')
            if (!viewportEle){
                return false
            }
			return viewportEle[0];
		},
		// 元素视图遍历
		viewportMap(callBack){
			for (let index = 0; index < this.viewportElements.length; index++) {
                const dom = this.getEnabledElement(index)
                if (!dom) {
                    continue
                }
				callBack && callBack(dom, index)
			}
		},
        // 切换滚动序列位置
        async syncScrollToIndex(el, index){
            this.ignoreFiredEvents = true;
            cornerstoneTools.scrollToIndex(el, index);
            setTimeout(() => {
                this.ignoreFiredEvents = false;
            }, 0);
        },
        // 设置元素图像缩放
        setElementImageScale(){
            this.$refs.csViewport.map(item => {
                // TODO 因为 sModality 写死的
                if (item.state && (item.state.sModality === 'CT' || item.state.sModality === 'MR')){
                    item.syncCallBack('relative')
                }
            })
            const tempTool = this.activeTool
            
            this.activeTool = 'temp'
            setTimeout(() => {
                this.activeTool = tempTool
            }, 100);
            // 执行 baseTools 组件中的方法
            this.$refs.baseTools.onAllRender()
        },
        // 设置十字线同步器
        setSynchronizerCrosshairs(isNull = false) {
            // 先进行判断销毁
            if (Object.keys(this.synchronizerCrosshairs).length) {
                this.originSeries.forEach((item, index) => {
                    const key = index + ''
                    this.synchronizerCrosshairs[key].destroy()
                    this.synchronizerCrosshairs[key] = Object
                    if (isNull) {
                        this.synchronizerCrosshairs[key] = null
                        return
                    }
                })
            }
            this.originSeries.forEach((item, index) => {
                const key = index + ''
                this.$set(this.synchronizerCrosshairs, key, new cornerstoneTools.Synchronizer(
                    'cornerstonenewimage',
                    cornerstoneTools.updateImageSynchronizer
                ))
            })
        },
        initAddCrosshairs() {
            // 已经有同步器，清除原先同步器
            for (const key in this.synchronizerCrosshairs) {
                if (Object.hasOwnProperty.call(this.synchronizerCrosshairs, key)) {
                    const obj = this.synchronizerCrosshairs[key];
                    if (Object.keys(obj).length){
                        obj.destroy()
                    }
                }
            }

            this.viewportElements = this.$refs['center'].getElementsByClassName('viewportWrapper');

            this.viewportMap((el) => {
                if (!el) { return }
                // 循环中获取视窗中的信息
                const key = this.getGroup(el)
                if (key === null) { return }
                this.synchronizerCrosshairs[key].add(el)
                cornerstoneTools.addToolForElement(el, cornerstoneTools.NuclearCrosshairsTool, { configuration: {  tabId: this.tabId, renderCallBack: this.nuclearCrosshairsCallBack } })
            })
            
            // 开启定位线
            if (this.crosshairsTool.mipShow) {
                this.setCrosshairs('Active');
            }else {
                this.setCrosshairs('Passive');
            }
            this.resetViewport();
        },
        // 改变 MIP 定位线位置
        nuclearCrosshairsCallBack(el) {
            // 设置当前渲染为 cornerstone ，在vtk反向改变截断
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'cs') {
                this.vtkAndCsRenderState.curRender = 'cs'
                this.setvtkAndCsRenderThrottle()
            }
            if (this.vtkAndCsRenderState.curRender != 'vtk') {
                // 节流在停止后，在更新 MIP 定位线
                this.vtkCrosshairsthrottle(el)
            }
        },
        vtkCrosshairsthrottle: throttle(function (el) {
            const allStack = cornerstoneTools.getToolState(el, 'stack');
            if (!allStack || !allStack.data.length) { return }
            const state = allStack.data[0].state

            this.csChangeVtkIndex1(el, state.layoutId.split(',')[0])
        }, 100),

        csChangeVtkIndex1(el, group) {
            const ipp = getIppByPoint(el)
            
            this.apis.forEach((api, apiIndex) => {
                if (!api) return
                if (group !== undefined && api.group !== group) return
                api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, [api], 0);
            })
        },
        /**
         * 设置添加定位线同步器
         */
        setAddSynchronizerCrosshairs() {
            // 已经有同步器，清除原先同步器
            // for (const key in this.synchronizerCrosshairs) {
            //     if (Object.hasOwnProperty.call(this.synchronizerCrosshairs, key)) {
            //         const obj = this.synchronizerCrosshairs[key];
            //         if (Object.keys(obj).length){
            //             obj.destroy()
            //         }
            //     }
            // }
            // this.viewportMap((el) => {
            //     if (!el) { return }
            //     // 循环中获取视窗中的信息
            //     const key = this.getGroup(el)
            //     if (key === null) { return }
            //     this.synchronizerCrosshairs[key].add(el)
            //     cornerstoneTools.addToolForElement(el, cornerstoneTools.NuclearCrosshairsTool, { configuration: {  tabId: this.tabId } })
            // })
        },
        // 设置开启定位线
        setCrosshairs(mode = 'Active') {
            clearTimeout(this.timerSetCrosshairs);
            this.timerSetCrosshairs = setTimeout(() => {
                this.viewportMap(el => {

                    if (el && cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')){
                        const key = this.getGroup(el)
                        if (key === null) { return }
    
                        cornerstoneTools['setTool'+mode+'ForElement'](el, 'NuclearCrosshairs', {
                            mouseButtonMask: 1,
                            synchronizationContext: this.synchronizerCrosshairs[key],
                        });
                    }

                })
            }, 200)
        },
        getGroup(el) {
            const allStack = cornerstoneTools.getToolState(el, 'stack')
            if (!allStack || !allStack.data.length) { return null }
            const state = allStack.data[0].state

            const key = state.layoutId.split(',')[0]
            if (key === undefined || key === null) {
                return null
            }
            return key
        },
        /**
         * 工具渲染处理
         * sourceElement 操作渲染的 dom 
         * sourceOrientation 操作渲染的 方向 x,y,z
         */
        onToolRenderCompleted(sourceElement, sourceOrientation, status){
            this.toolRenderLast = true
            if (status === 'removed') {
                this.toolRenderLast = false
            }
            clearTimeout(this.toolThrottle)
            this.toolThrottle = setTimeout(() => {
                this.viewportMap((el) => {
                    const isImage = cornerstone.getImage(el)
                    if (isImage) {
                        // if (sourceElement === el) return;
                        // 获取当前显示的 dom 切面
                        const orientation = el.getAttribute('orientation');
                        // 不同切面的退出
                        if (orientation !== sourceOrientation) return;
                        cornerstone.updateImage(el);
                    }

                })
            }, 500);
                // 添加
			if (status === 'completed' || status === 'removed' || status === 'modifiedAndState'){
				this.$refs.baseTools.getNewToolList()
			}
        },
        /**
         * 渲染回调
         * 渲染的原视窗 viewport 中的数据信息
         * 图像信息
         * 需要同步类型 
         */
        renderCallBack(sourceViewport, clayData, type){
            if (!clayData.modality) {
                return
            }
            if (!sourceViewport.width) {
                return;
            };
            // 最小化、切换其它 tab 页后在回到当前页面。该参数没有读取到信息。现在是x,y 没有
            if (isNaN(sourceViewport.x) || isNaN(sourceViewport.y)){
                // 进入下一轮循环在次执行
                setTimeout(() => {
                    this.setElementImageScale();
                }, 200);
                return;
            };
            // 同步操作-分为相对，绝对。
            if (type === 'absolute'){ 
                // 绝对，赋值一样的信息 (窗宽窗位，反片，旋转翻转)
                this.renderAbsolute(sourceViewport, clayData)
                // this.mriColorRender(sourceViewport, clayData)
            } else {
                // 相对 按照比例规模赋值 (平移，缩放)
                this.renderRelative(sourceViewport, clayData)
            }
        },
        /**
         * 绝对渲染-实时触发         -- 反片，翻转，旋转，窗宽窗位
         * @param {*} sourceViewport  当前操作视图 viewport 信息
         * @param {*} clayData        当前操作视图附件信息 （角度、设备类型、布局 id）
         */
        renderAbsolute(sourceViewport, clayData){
            
            const sourceModality    = clayData.modality          // 当前设备类型
            // const sourceOrientation = clayData.orientation    // 当前角度
            const layoutId          = clayData.layoutId          // 当前布局的 id
            const ww = sourceViewport.windowWidth                // 当前视图窗宽窗位
            const wc = sourceViewport.windowCenter
            const [sourceGroup, sourceOrientation] = layoutId.split(',')

            // 节流操作，避免多次执行
            if (this.renderAbsoluteThrottle.renderEvents && this.renderAbsoluteThrottle.renderElement !== clayData.el){
                return;
            }
            this.renderAbsoluteThrottle.renderElement = clayData.el;
            this.renderAbsoluteThrottle.renderEvents = true;

            // 同步翻转，旋转
            const syncConvert = (this.lockSync.start && this.lockSync.convert) || false
            // 窗宽窗位，伪彩
            const syncWindowColor = (this.lockSync.start && this.lockSync.windowColor) || false
            // start 同步渲染逻辑
            this.viewportMap((el) => {
                if (!el) { return }
                if (clayData.el === el){ return }

                // 循环中获取视窗中的信息
                const allStack = cornerstoneTools.getToolState(el, 'stack')
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state
                const [targetGroup, targetOrientation] = state.layoutId.split(',')
                const targetModality = state.sModality
                const viewport = cornerstone.getViewport(el)
                const layers   = cornerstone.getLayers(el)
                // 同角度同组。或者 -- 同角度不同组，但是开启了同步锁
                // 同步翻转，旋转
                if (sourceOrientation === targetOrientation && (sourceGroup === targetGroup || syncConvert)) {
                    if (viewport.hflip !== sourceViewport.hflip ||
                        viewport.vflip !== sourceViewport.vflip ||
                        viewport.rotation !== sourceViewport.rotation)
                    {
                        viewport.hflip = sourceViewport.hflip;
                        viewport.vflip = sourceViewport.vflip;
                        viewport.rotation = sourceViewport.rotation;
                        cornerstone.setViewport(el, viewport);
                    } 
                }
                
                // 非融合，非mip图同步反片
                // 满足同设备,同组或开启同步锁 
                if (targetModality === sourceModality && (sourceGroup === targetGroup || syncConvert) && !layers.length){
                    // 原(用户操作的视图)是等于1(非融合的) 不一样，赋值一样
                    if (cornerstone.getLayers(clayData.el).length == 0 && viewport.invert !== sourceViewport.invert) {
                        viewport.invert = sourceViewport.invert;
                        cornerstone.setViewport(el, viewport);
                    }
                }

                // 是融合 同组或开启同步时同步伪彩
                const isLayer = cornerstone.getLayers(clayData.el).length;
                if (layers.length && isLayer && clayData.modality == 'PT' && (sourceGroup === targetGroup || syncWindowColor)) {

                    let colormap = 'hot';
                    if (typeof sourceViewport.colormap === 'string') {
                        colormap = sourceViewport.colormap;
                    }else if (sourceViewport.colormap && sourceViewport.colormap.getId()){
                        colormap = sourceViewport.colormap.getId();
                    }
                    if (viewport.colormap !== colormap) {
                        viewport.colormap = colormap;
                        cornerstone.setViewport(el, viewport);
                    }
                }
                

                // 多层图像，遍历设置层中一样设备,同组||不同组但开启同步锁的窗宽窗位
                layers.forEach(_ => {
                    const layer = cornerstone.getLayer(el, _.layerId);
                    const modality = layer.image.data.string('x00080060') || '';
                    const viewport = layer.viewport;

                    if (sourceModality.toLocaleUpperCase() === modality.toLocaleUpperCase() && (sourceGroup === targetGroup || syncWindowColor)){
                        if (viewport.voi.windowWidth  === ww && 
                            viewport.voi.windowCenter === wc ){
                            return;
                        }
                        viewport.voi.windowWidth = ww;
                        viewport.voi.windowCenter = wc;
                        cornerstone.updateImage(el)
                    }
                });

                // 相同设备,同组||不同组但开启同步锁的窗宽窗位
                if (sourceModality.toLocaleUpperCase() === targetModality.toLocaleUpperCase() && (sourceGroup === targetGroup || syncWindowColor)){

                    if (viewport.voi.windowWidth  === ww && 
                        viewport.voi.windowCenter === wc ){
                        return;
                    }
                    viewport.voi.windowWidth = ww;
                    viewport.voi.windowCenter = wc;

                    cornerstone.setViewport(el, viewport);
                }
            })

            // end   同步渲染逻辑

            // 最后关闭节流
            setTimeout(() => {
                this.renderAbsoluteThrottle.renderEvents = false;
            }, 0);
        },
        // 点击保存序列工具
        onMeasurements(funName) {
            if (funName === 'flush') {
                this.$refs.baseTools.getNewToolList()
            } else if (funName === 'singleDel') {
                const element = this.getEnabledElement(this.activeViewportIndex)
                this.$refs.baseTools.removeSinglePageTool(element)
            } else if (funName === 'save') {
                this.$refs.baseTools.onclickSaveTool('active')
            } else {
                this.clearMark(this.saveImage.rightSelectElement)
            }
        },
        clearMark(_element) {
            const element = _element || this.getEnabledElement(this.activeViewportIndex)
            if (!element) {
                return
            }
            const stack = cornerstoneTools.getToolState(element, 'stack');
            if (stack && stack.data && stack.data[0]) {
                // const state = stack.data[0].state;
                // const [ targetGroup ] = state.layoutId.split(',')                
                // const { noPtId, ptId, thickness } = this.groupUid[targetGroup]
                this.groupUid.forEach(item => {
                    const { noPtId, ptId, thickness, isAnomalyFuse } = item
                    this.$refs.baseTools.removeAllTabTool(ptId, noPtId, thickness, isAnomalyFuse);
                })

                setTimeout(() => {
                    for (let index = 0; index < this.viewportElements.length; index++) {
                        const el = this.getEnabledElement(index)
                        if (!el) {
                            continue
                        }
                        const isImage = cornerstone.getImage(el)
                        if (isImage) {
                            cornerstone.updateImage(el)
                        }
                    }
                }, 0);
            }
        },
        // 触发绘制其它组同张工具
        triggerToolGroupRender(data) {
            // console.log(data)
            const [sourceGroup, sourceOrientation] = data.layoutId.split(',')
            const sourceModality = data.sModality
            const sourceImageIdIndex = data.imageIdIndex

            // 文本不同步
            if (data.toolName === 'TextMarker') {
                return;
            }
            // 遍历视图
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }

                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state;
                const imageIdIndex = allStack.data[0].currentImageIdIndex;
                const [targetGroup, targetOrientation] = state.layoutId.split(',')
                const targetModality = state.sModality

                // 判断，相同组，不同设备，相同方向，相同位置
                if (sourceGroup === targetGroup &&
                    sourceModality !== targetModality &&
                    sourceOrientation === targetOrientation &&
                    sourceImageIdIndex === imageIdIndex
                ) {
                    
                    if (data.status === 'add') {
                        const seriesId = state.seriesId
                        const imageId = cornerstone.getImage(el).imageId
                        // 坐标转换
                        const measurement = toolCoordTransition(data.element, data.toolName, data.measurementData, el)
                        // 添加
                        cornerstoneTools.addToolState(el, data.toolName, measurement);
                        cornerstoneTools.imageIdStateManager.addToolState(this.seriesId, seriesId, imageId, data.toolName, measurement)
                        cornerstoneTools.imageIdStateManager.triggerRender()

                        // PT绘制工具完成后，融合会出现工具会出现触碰效果，等待 0.5 秒 渲染清除触碰工具效果
                        this.renderUpdateImage();
                    } else if (data.status === 'removed') {
                        const seriesId = state.seriesId
                        const list = cornerstoneTools.imageIdStateManager.getTransitionSeriesTool(seriesId)
                        list.forEach(toolItem => {
                            if (!toolItem.toolData) return
                            // 是否有相同组，需要移除的
                            if (toolItem.toolData.connectId === data.measurementData.connectId) {
                                // 移除原生工具状态
                                cornerstoneTools.globalImageIdSpecificToolStateManager.removeImageIdToolState(toolItem.imageId, toolItem.toolName, toolItem.toolData)
                                // 移除在原生基础工具状态
                                cornerstoneTools.imageIdStateManager.delToolState(seriesId, toolItem.imageId, toolItem.toolName, toolItem.toolData.uuid)
                            }
                        })

                        cornerstoneTools.triggerEvent(el, cornerstoneTools.EVENTS.MEASUREMENT_REMOVED, {});

                        (() => {
                            for (let index = 0; index < this.viewportElements.length; index++) {
                                const el = this.getEnabledElement(index)
                                if (!el) {
                                    continue
                                }
                                const isImage = cornerstone.getImage(el)
                                if (isImage) {
                                    cornerstone.updateImage(el)
                                }
                            }
                        })()
                    } else {
                        // 坐标转换
                        const toolList = cornerstoneTools.globalImageIdSpecificToolStateManager.get(el, data.toolName);
                        if (!toolList) {
                            return;
                        }
                        const isMeasurement = toolList.data.find(item => {
                            return item.connectId === data.measurementData.connectId;
                        })
                        if (isMeasurement) {
                            // 坐标转换
                            const measurement = toolCoordTransition(data.element, data.toolName, data.measurementData, el, false)
                            updateToolCoord(measurement, isMeasurement, data.toolName)
                        }
                    }
                }
            }
        },
        renderUpdateImage: debounce(function () {
            for (let index = 0; index < this.viewportElements.length; index++) {
                const el = this.getEnabledElement(index)
                if (!el) {
                    continue
                }
                const isImage = cornerstone.getImage(el)
                if (isImage) {
                    cornerstone.updateImage(el)
                }
            }
        }, 200),
        /**
         * 相对渲染，在操作完成后触发 -- 平移、缩放
         * @param {*} sourceViewport   当前操作视图 viewport 信息
         * @param {*} clayData         当前操作视图附件信息 （角度、设备类型、布局 id）
         */
        renderRelative(sourceViewport, clayData){
            const layoutId = clayData.layoutId          // 当前布局的 id
            const [sourceGroup, sourceOrientation] = layoutId.split(',')

            // start 同步渲染逻辑

            // 平移缩放
            const syncMoreZoom = (this.lockSync.start && this.lockSync.moreZoom) || false

            this.viewportMap((el) => {
                if (!el) { return }
                if (clayData.el === el){ return }

                const allStack = cornerstoneTools.getToolState(el, 'stack')
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state
                const [targetGroup, targetOrientation] = state.layoutId.split(',')
                const viewport = cornerstone.getViewport(el);

                const scaleX = sourceViewport.columnPixelSpacing / viewport.displayedArea.columnPixelSpacing
                const scaleY = sourceViewport.rowPixelSpacing / viewport.displayedArea.rowPixelSpacing
                const scaleRatio = sourceViewport.width >= sourceViewport.height ? scaleX : scaleY

                if (sourceOrientation === targetOrientation && (sourceGroup === targetGroup || syncMoreZoom)) {
                    if (viewport.translation.x !== sourceViewport.x * scaleX ||
                        viewport.translation.y !== sourceViewport.y * scaleY ||
                        viewport.scale         !== sourceViewport.scale / scaleRatio)
                    {    
                        viewport.translation.x = sourceViewport.x * scaleX;
                        viewport.translation.y = sourceViewport.y * scaleY;
                        viewport.scale         = sourceViewport.scale / scaleRatio;
                        cornerstone.setViewport(el, viewport);
                    }

                    setTimeout(() => {
                        cornerstoneTools.getToolForElement(el, 'NuclearCrosshairs')._getInitialPoint(el)
                    }, 0);

                }
            })

            // end   同步渲染逻辑

        },
        setvtkAndCsRenderThrottle() {
            clearTimeout(this.vtkAndCsRenderState.timer);
            this.vtkAndCsRenderState.timer = setTimeout(() => {
                this.vtkAndCsRenderState.curRender = 'none';
            }, 200);
        },
        // 相同切片同步滚动
        onNewImage(obj){
            // 初始渲染，添加定位线
            if (obj.initRenderImage) {
                clearTimeout(this.timerAddCrosshairs);
                this.timerAddCrosshairs = setTimeout(() => {
                    this.initAddCrosshairs();
                }, 100);
            }

            // 设置当前渲染为 cornerstone ，在vtk反向改变截断
            if (this.vtkAndCsRenderState.curRender == 'none' || this.vtkAndCsRenderState.curRender == 'cs') {
                this.vtkAndCsRenderState.curRender = 'cs'
                this.setvtkAndCsRenderThrottle()
            }

            

            // 不执行事件
            if (this.ignoreFiredEvents && this.firedEvents !== obj.el) {
                return;
            };
            // 节流处理
            this.firedEvents = obj.el;
            if (this.scrollThrottle){
                this.ignoreFiredEvents = true;
                setTimeout(() => {
                    this.scrollThrottle = false;
                    this.ignoreFiredEvents = false;
                }, 0);
                return;
            }
            // start 同步滚动逻辑
            const [sourceGroup, sourceOrientation, sourceType] = obj.layoutId.split(',')

            this.updateAllModalityIndex(obj.currentImageIdIndex, obj.layoutId, obj.el)

            let changeIndex = Object.assign({}, this.allModalityIndex)
            
            // 同步锁定滚动
            const syncScroll = (this.lockSync.start && this.lockSync.scroll) || false
            const isVtkCall = this.vtkAndCsRenderState.curRender == 'vtk'
 
            if (syncScroll && !isVtkCall) {
                const curLookVal = this.lookScrollLayout[obj.layoutId]
                const curFirstVal = changeIndex[obj.layoutId]
                if (curLookVal !== undefined && curFirstVal !== undefined) {
                    // 滚动后同组信息第一个所在位置值，减锁定时所在位置值
                    const offset = curFirstVal - curLookVal
                    // 遍历第一位所在位置值，做修改
                    for (const key in changeIndex) {
                        if (Object.hasOwnProperty.call(changeIndex, key)) {
                            // 跟当前id不一样
                            if (key !== obj.layoutId) {
                                const [itemGroup, itemOrientation, itemType] = key.split(',')
                                // 同角度，同类型（图像融合，pt，ct这种类型），确保有锁定时保存的值
                                if (itemOrientation === sourceOrientation && this.lookScrollLayout[key] !== undefined) {
                                    // 变化值
                                    let changeVal = offset + this.lookScrollLayout[key]
                                    // changeVal 这个会小于0，或者大于超过序列数，可能要修改限制
                                    changeIndex[key] = changeVal
                                }
                            }
                        }
                    }
                }
            }

            this.viewportMap(el => {

                // if (obj.el === el) return;
                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const state = allStack.data[0].state
                const maxLen = allStack.data[0].imageIds.length - 1
                const [targetGroup, targetOrientation] = state.layoutId.split(',')

                // 不同切面不做处理
                if (targetOrientation !== sourceOrientation) { return }

                // 不同组,未开启同步
                if (targetGroup !== sourceGroup && !syncScroll) { return }
  
                const index = changeIndex[state.layoutId]
                if (index === undefined || index === null) { return }

                // 拖拽 vtk 改变 cornerstone view 时候记录下改变后的位置
                if (targetGroup !== sourceGroup && syncScroll && !isVtkCall) {
                    this.allModalityIndex[state.layoutId] = index;
                }

                const goIndex = index < 0 ? 0 : index > maxLen ? maxLen : index
                // this.syncScrollToIndex(el, goIndex)
                cornerstoneTools.scrollToIndex(el, goIndex);

                changeIndex[state.layoutId] += 1
            })

            // 在拖拽 vtk ，拖拽 cornerstone 定位线后要改变锁定位置
            this.initScrollLook()

            // end   同步滚动逻辑结束

            

            // 改变 MIP 定位线位置
            const allStack = cornerstoneTools.getToolState(obj.el, 'stack');
            if (!allStack || !allStack.data.length) { return }
            const state = allStack.data[0].state
            
            if (this.vtkAndCsRenderState.curRender == 'vtk') { // 点击vtk改变cs时，不要让cs再改变vtk 
                this.setvtkAndCsRenderThrottle()
            } else { 
                this.csChangeVtkIndex(obj.el, state.layoutId.split(',')[0])
            }

        },
        csChangeVtkIndex(el, group) {
            const ipp = getIppByPoint(el)
            
            // const syncScroll = (this.lockSync.start && this.lockSync.scroll) || false // 同步锁定滚动
            this.apis.forEach((api, apiIndex) => {
                if (!api) return
                if (group !== undefined && api.group !== group) return
                api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, [api], 0);
                // if (syncScroll) {
                //     api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, [api], 0);
                // } else {
                //     if (group !== undefined && api.group !== group) return
                //     api.svgWidgets.crosshairsWidget.moveCrosshairs(ipp, [api], 0);
                // }
            })
        },
        // 更新序列下标
        updateAllModalityIndex(curIndex, layoutId, el){
            // 设置改变值
            let preNumber = 0
            let stopPreNumber = 0
            // 获取当前滚动时，相同序列前面有多少个
            this.viewportMap(e => {
                if (e === el){
                    stopPreNumber = preNumber
                    return
                }
                let allStack = cornerstoneTools.getToolState(e, 'stack');
                if (allStack && allStack.data.length && allStack.data[0].state.layoutId === layoutId){
                    preNumber += 1;
                }
            })

            // for (let index = 0; index < this.viewportElements.length; index++) {
            //     if (this.getEnabledElement(index) === el){
            //         break;
            //     }
            //     let allStack = cornerstoneTools.getToolState(this.getEnabledElement(index), 'stack');
            //     if (allStack && allStack.data.length && allStack.data[0].state.layoutId === layoutId){
            //         preNumber += 1;
            //     }
            // }
            const [targetGroup, targetOrientation] = layoutId.split(',')
            let oneIndex = curIndex - stopPreNumber;
            // 更新视图同类型序列第一位值
            for (const key in this.allModalityIndex) {
                if (key.includes(`${targetGroup},${targetOrientation}`) && !key.includes('_data')){
                    this.allModalityIndex[key] = oneIndex
                }
            }
        },
        
        /**
         * 初始化默认第一个序列显示位置
         * @param {*} reset             重置第一个的位置值
         * @param {*} isResetMatrix     重置布局矩阵
         * @param {*} param2            {claerGroup 清除固定组第一个位置, group 组}
         */
        setInitModalityIndex() {
            // if (reset) {
            //     this.allModalityIndex = {}
            // }
            // // 清除固定组的第一位值
            // if (claerGroup.isClear) {
            //     for (const key in this.allModalityIndex) {
            //         if (Object.hasOwnProperty.call(this.allModalityIndex, key)) {
            //             const [ layoutGroup ] = key.split(',')
            //             if (layoutGroup == claerGroup.group) {
            //                 delete this.allModalityIndex[key]
            //             }
            //         }
            //     }
            // }
            // let originMatrixIdx = []
            // // 遍历视图中的信息，设置默认值
            // this.viewports.map(item => {
            //     originMatrixIdx.push(item.layoutId)
            //     if (!this.allModalityIndex[item.layoutId] && item.imageIds) {
            //         const midIndex = Math.floor(item.imageIds.length / 2) - 1
            //         this.allModalityIndex[item.layoutId] = midIndex
            //     }
            // })

            this.originSeries.forEach((item, index) => {
                
                let yIdx = Math.max(Math.floor(item.y.one.length / 2) - 1, 0)
                let xIdx = Math.max(Math.floor(item.x.one.length / 2) - 1, 0)
                let zIdx = Math.max(Math.floor(item.z.one.length / 2) - 1, 0)

                this.allModalityIndex[`${index},y,one`] = yIdx
                this.allModalityIndex[`${index},y,two`] = yIdx
                this.allModalityIndex[`${index},y,fuse`] = yIdx

                this.allModalityIndex[`${index},x,one`] = xIdx
                this.allModalityIndex[`${index},x,two`] = xIdx
                this.allModalityIndex[`${index},x,fuse`] = xIdx

                this.allModalityIndex[`${index},z,one`] = zIdx
                this.allModalityIndex[`${index},z,two`] = zIdx
                this.allModalityIndex[`${index},z,fuse`] = zIdx 
            })


            // 布局矩阵
            // this.layoutMatrix.isReload = isResetMatrix

            // this.layoutMatrix.column = Math.round(100 / parseInt(this.style.width))
            // this.layoutMatrix.originalIdx = originMatrixIdx

            setTimeout(() => {
                this.initScrollLook()
            }, 0);

        },

        /**
         * 获取 vtk 体中的信息（x,y,z能切数量,x,y,z起始位置）
         * @param {*} seriesId   序列 uid
         * @param {*} thickness  切割 层厚
         * @returns 
         */
        // async _getVtkVolume(seriesId = 0, thickness = 2.5){
        //     console.log(seriesId)
        //     const vtkImageData = appState[seriesId].vtkVolumes.vtkImageData;
        //     const [x0, y0, z0] = vtkImageData.getOrigin();  // x,y,z 原点
        //     const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
        //     const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

        //     let xStart = x0 + xSpacing * (xMax - xMin);  // x最大值(ipp 切片x最大位)
        //     let yStart = y0 + ySpacing * (yMax - yMin);  // y最大值
        //     let zStart = z0 + zSpacing * (zMax - zMin);  // z最大值

        //     // 算出有用的张数
        //     let xNmax = xSpacing * (xMin + xMax)
        //     let xCount = Math.round(xNmax / thickness);

        //     let yNmax = ySpacing * (yMin + yMax)
        //     let yCount = Math.round(yNmax / thickness);

        //     let zNmax = zSpacing * (zMin + zMax)
        //     let zCount = Math.round(zNmax / thickness);
        //     return { xCount, yCount, zCount, xStart, yStart, zStart}
        // },
        /**
         * 通过数量计算层厚
         * @param {*} seriesId   序列 uid
         * @param {*} count      数量
         * @param {*} angle      当前图像角度
         * @returns 
         */
        _getThicknessByCount(seriesId = 0, count, angle){
            const vtkImageData = appState[seriesId].vtkVolumes.vtkImageData;
            const [xSpacing, ySpacing, zSpacing] = vtkImageData.getSpacing(); //图像的像素间距
            const [xMin, xMax, yMin, yMax, zMin, zMax] = vtkImageData.getExtent(); // 获取 x,y,z 范围

            // 算出有用的张数
            let xNmax = xSpacing * (xMin + xMax)
            let yNmax = ySpacing * (yMin + yMax)
            let zNmax = zSpacing * (zMin + zMax)
            let thickness = 2.5
            switch (angle) {
                case 'sagittal':
                    thickness = xNmax / count;
                    break;
                case 'coronal':
                    thickness = yNmax / count;
                    break;
                case 'axial':
                    thickness = zNmax / count;
                    break;
            }
            return thickness
        },
        
        getNewImageIds,
        getVolumeSliceNumber,


        // ===================== BaseTools 组件方法 ==========================
        // 点击改变序列，通过角度||设备
        /**
         * 点击改变序列，通过角度||设备
         * @param {*} type   orientation || modality
         * @param {*} value  x|y|z ||  1 pet，0 ct、mr，2 融合
         */
        onClickChangeSeries(type, value){
            this.activeViewportIndex = -1;
            this.showAction = -1;
            if (type === 'orientation') {
                this.updateLayoutModalityViewport()
                // 点击角度
                this.viewports.forEach(viewport => {
                    // vtk 不需要变化
                    if (viewport.vtk) {
                        return
                    }
                    // 一样的不需要变化
                    if (viewport.orientation === value) {
                        return
                    }
                    const [group, , imageType] = viewport.layoutId.split(',')
                    
                    if (!this.originSeries[group]) {
                        return
                    }

                    const originSeriesItem = this.originSeries[group][value]
                    if (originSeriesItem) {
                        viewport.imageIds = originSeriesItem[imageType === 'fuse' ? 'two' : imageType].slice(0)
                        viewport.orientation = value
                        viewport.layoutId = `${group},${value},${imageType}`
                        viewport.seriesId= `mpr:${this.groupUid[group].ptId}&&${this.groupUid[group].noPtId}${this.groupUid[group].thickness}${value}`

                        // 存在多层（融合的）
                        if (imageType === 'fuse') {
                            viewport.layoutId = `${group},${value},fuse`
                            viewport.layerIds = originSeriesItem.one.slice(0)
                        }
                        viewport.uid = this.$fun.onlyValue()
                    }
                })
            }else {
                this.viewports.forEach(viewport => {
                    if (viewport.vtk) {
                        return
                    }
                    const [ group, angle, type ] = viewport.layoutId.split(',')
                    const typeString = ['one', 'two', 'fuse']
                    
                    // 相同设备
                    if (typeString[value] == type) {
                        return
                    }

                    if (!this.originSeries[group]) {
                        return
                    }

                    const originSeriesItem = this.originSeries[group][angle]
                    
                    viewport.layoutId = `${group},${angle},${typeString[value]}`
                    
                    if (viewport.layerIds) {
                        delete viewport.layerIds
                        // 更新id
                        viewport.uid = this.$fun.onlyValue();
                    };
                    switch (typeString[value]) {
                        case 'one':
                            // 第一层图像
                            viewport.imageIds = originSeriesItem.one.slice(0)
                            viewport.sModality = 'CT'
                            break;
                        case 'two':
                            // 第二层图像
                            viewport.imageIds = originSeriesItem.two.slice(0)
                            viewport.sModality = 'PT'
                            break;
                        case 'fuse':
                            // 第一，第二叠加图像
                            // 赋值融合对应序列
                            viewport.imageIds = originSeriesItem.two.slice(0)
                            viewport.layerIds = originSeriesItem.one.slice(0)
                            viewport.sModality = 'PT'
                            break;
                    }
                    viewport.uid = this.$fun.onlyValue()
                })
            }
            // 更新渲染
            this.$forceUpdate();
        },
        /**
         * 点击布局
         * @param {obj} layout    行列数
         * @param {*} isPassive   是否是人为触发
         */
        onClickBox(layout, isPassive = false){
            if (isPassive) {
                this.activeViewportIndex = -1;
                this.showAction = -1;
            }

            if (isPassive && Object.keys(this.gridLayoutStyle.containerStyle).length) {
                this.$message.info('该布局下无法修改，请切换其它布局！在做修改');
                return;
            }

            let width = 1;
            let height = 1;
            if (!layout.num) {
                width = layout.column;
                height = layout.row;
            }

			// 设置布局样式
 			this.style.width = 100 / width + '%'
        	this.style.height = 100 / height + '%'

            // 记录当前布局行列
            this.layoutMatrix.curColumn = width;
            this.layoutMatrix.curRow = height;

			// 窗口数量
			const num = !layout.num ? width * height : layout.num;
			// 改变布局后跟原来相差值
			const diffVal = Math.abs(num - this.viewports.length);

			if (this.viewports.length < num){
				// 视窗变多了，push
				for (let index = 0; index < diffVal; index++) {
					this.viewports.push({})
				}
			}else {
				// 视窗变少了，pop
				for (let index = 0; index < diffVal; index++) {
					this.viewports.pop()
				}
			}
            // 被动，人为操作
            if (isPassive){
                // 是否加载最新的 10x10结构
                if (this.layoutMatrix.isReload){
                    this.layoutMatrix.matrix10 = this.setMatrix10(this.layoutMatrix.originalIdx, this.layoutMatrix.column)
                    this.layoutMatrix.isReload = false
                }
                // 切换品布局，渲染
                this.$nextTick(() => {
                    const myEvent = new Event('resize');
                    window.dispatchEvent(myEvent);
                })
                if (!this.layoutMatrix.matrix10.length) return;
                const layoutIdx = this.getViewMatrixIndex(this.layoutMatrix.matrix10, height, width)
                this.changeLayoutPushView(layoutIdx)
            }

            // 改变布局需要更新什么？

        },
        // 改变 viewport 数据 (显示图像)
        setViewportByLayout(layout){
            layout.forEach((item, index) => {
                const [group, angle, type] = item.split(',')
                this.viewports[index] = {}
                this.viewports[index].orientation = angle
                this.viewports[index].group       = group
                this.viewports[index].uid = this.$fun.onlyValue()
                this.viewports[index].layoutId    = item

                if (this.groupUid[group] !== undefined) {
                    this.viewports[index].seriesId= `mpr:${this.groupUid[group].ptId}&&${this.groupUid[group].noPtId}${this.groupUid[group].thickness}${angle}`
                }else {
                    this.viewports[index].seriesId= 'mpr:null'
                }
                
                if (!this.originSeries[group]) {
                    return;
                }
                switch (type) {
                    case 'one':
                        // 第一层图像
                        this.viewports[index].imageIds = this.originSeries[group][angle].one.slice(0)
                        this.viewports[index].sModality = 'CT'
                        break;
                    case 'two':
                        // 第二层图像
                        this.viewports[index].imageIds = this.originSeries[group][angle].two.slice(0)
                        this.viewports[index].sModality = 'PT'
                        break;
                    case 'fuse':
                        // 第一，第二叠加图像
                        // 赋值融合对应序列
                        this.viewports[index].imageIds = this.originSeries[group][angle].two.slice(0)
                        this.viewports[index].layerIds = this.originSeries[group][angle].one.slice(0)
                        this.viewports[index].sModality = 'PT'
                        break;
                    case 'mipTwo':
                        // PT/NM MIP 图
                        this.setViewportVtkMIP(7, group, index);
                        break;
                    case 'mipOne':
                        // CT/MR MIP 图
                        this.setViewportVtkMIP(6, group, index);
                        break;
                    case 'mipFuse':
                        // 多层 MIP 图
                        this.setViewportVtkMIP(11, group, index);
                        break;
                    default:
                        break;
                }

            });
        },
        // 重置 视图
        resetViewport(){
            this.scrollThrottle = true;
            setTimeout(() => {
                this.viewportElements = this.$refs['center'].getElementsByClassName('viewportWrapper');
                
                this.setCurrentViewportIndex((el) => {
                    setTimeout(() => {
                        try {
                            // let obj = cornerstone.getEnabledElement(el)             // 获取开启的元素
                            // let viewport = cornerstone.getViewport(el);             // 获取视图
                            // if (!viewport) return;
                            // viewport.voi.windowWidth = obj.image.windowWidth;
                            // viewport.voi.windowCenter = obj.image.windowCenter;
                            // cornerstone.setViewport(el, viewport);

                            // 已加载显示全部 cornerstone 视图，做的逻辑处理
                            this.loadShowCount += 1;
                            if (this.loadShowCount === this.showCsViewportLen){
                                this.loadShowCount = 0;
                                this.setElementImageScale();
                            }
                        } catch (error) {
                            console.info(error);
                        }
                    }, 0);
                })
            }, 100);
        },
        // 设置滚动视图到相应位置
        async setCurrentViewportIndex(callBack){
            let changeIndex = Object.assign({}, this.allModalityIndex)
            this.viewportMap(el => {
                if (!el) { return }
                let allStack = cornerstoneTools.getToolState(el, 'stack')
                if (!allStack) { return }

                const key = allStack.data[0].state.layoutId
                const index = changeIndex[key]
                if (index === undefined || index === null){ return }

                if (allStack.data[0].imageIds.length === index) {
                    return;
                }

                this.awaitScrollToIndex(el, index < 0 ? 0 : index)
                changeIndex[key] += 1

                callBack && callBack(el)
            })
        },
        // 滚动
        async awaitScrollToIndex(el, index){
            this.scrollThrottle = true
            await cornerstoneTools.scrollToIndex(el, index)
        },
        /**
         * 按照原始矩阵下标，绘制出10X10矩阵
         * original 按照这个数组为矩阵基础放大
         * n        列数量
         */
         setMatrix10(original, n){
            if (!original || !original.length || !n){
                return [];
            }

            let matrix10 = []
            let len = original.length;
            // let n = 4;
            let lineNum = len % n === 0 ? len / n : Math.floor( (len / n) + 1 );

            // 转为二维数组
            for (let i = 0; i < lineNum; i++) {
                let row = original.slice(i*n, i*n+n);
                // 填充一行中的列，一行补充到10个
                let fillLen = Math.ceil(10 / n);
                for (let j = 0; j < fillLen; j++) {
                    row = row.concat(row)
                }
                // 切掉多余的
                row = row.splice(0, 10)
                matrix10.push(row)
            }
            
            // 填充行
            let fillLen = Math.ceil(10 / matrix10.length)
            for (let i = 0; i < fillLen; i++) {
                matrix10 = matrix10.concat(matrix10)
            }
            matrix10 = matrix10.splice(0, 10)
            return matrix10;
        },
        /**
         * 获取显示的矩阵（布局）
         */
        getViewMatrixIndex(matrix, x, y){
            let res = []
            for (let i = 0; i < x; i++) {
                for (let j = 0; j < y; j++) {
                    res.push(matrix[i][j])
                }
            }
            return res;
        },
        initScrollLook() {
            this.lookScrollLayout = {}
            this.viewportMap(el => {
                let allStack = cornerstoneTools.getToolState(el, 'stack');
                if (!allStack || !allStack.data.length) { return }
                const curIdx = allStack.data[0].currentImageIdIndex
                const state  = allStack.data[0].state

                if (this.lookScrollLayout[state.layoutId] === undefined){
                    this.lookScrollLayout[state.layoutId] = curIdx
                }
            })
        },
        // 工具点击了重置size，同步其它缩放大小渲染
        syncSizeRender(){
            setTimeout(() => {
                // 当前选中的视图
                const curViewport = this.viewports[this.activeViewportIndex];
                // 当前视图的唯一值
                const uid = curViewport && curViewport.uid
                // 遍历组件
                this.$refs.csViewport.map(item => {
                    // 组件与选中的视图一样
                    if (item.state && (item.state.uid === uid)){
                        // 触发改组件的方法
                        item.syncCallBack('relative')
                    }
                })
            }, 20);
        },
        // 保存更新布局 viewport
        updateLayoutModalityViewport() {
            this.viewports.forEach((item, index) => {
                if (item.vtk) {
                    // 暂存vtk图像窗宽
                    const vtkApi = this.apis[index]
                    if (!vtkApi) return
                    const wwwc = vtkApi.getVoi()
                    const key = `mip_${item.layoutId}_data`;
                    if (!this.allModalityIndex[key]) {
                        this.allModalityIndex[key] = {}
                    }
                    this.allModalityIndex[key].windowCenter = wwwc.windowCenter
                    this.allModalityIndex[key].windowWidth = wwwc.windowWidth
                    // data.parallelScale = vtkApi.genericRenderWindow.getRenderer().getActiveCamera().getParallelScale()
                    return
                }
                if (item.sModality) {
                    let modality = item.sModality;
                    let group = item.group;

                    const sourceElement = this.getEnabledElement(index);
                    if (!sourceElement) {
                        return;
                    }
                    const enabledElement = cornerstone.getEnabledElement(sourceElement);

                    const oldCanvasWidth = enabledElement.canvas.width;
                    const oldCanvasHeight = enabledElement.canvas.height;

                    if (item.layerIds) {
                        const layers = cornerstone.getLayers(sourceElement);
                        layers.forEach((layer, index) => {
                            this.saveViewportState(group, index === 0 ? 'PT' : 'CT', layer.viewport, 'fuseColor', 
                            { 
                                orientation: item.orientation, 
                                firstImageId: item.imageIds[0], 
                                oldCanvasWidth, 
                                oldCanvasHeight 
                            });
                        })
                    } else {
                        this.saveViewportState(group, modality, enabledElement.viewport, undefined, 
                            { 
                                orientation: item.orientation,
                                firstImageId: item.imageIds[0], 
                                oldCanvasWidth,
                                oldCanvasHeight
                            });
                    }
                }
            })
        },
        // 是否应该在 CT 同步 PT 的位置，PT 同步 CT 的位置
        // ？？？
        async saveViewportState(group, modality, viewport, isFue, moreParams) {
            if (!viewport) {
                return;
            }
            const { orientation, firstImageId, oldCanvasWidth, oldCanvasHeight } = moreParams
            await cornerstone.loadAndCacheImage(firstImageId);

            ['x', 'y', 'z'].forEach(itemOrientation => {
                const key = `${group}${modality}${itemOrientation}Viewport`;

                let { windowWidth, windowCenter } = viewport.voi

                if (!this.allModalityIndex[key]) {
                    this.allModalityIndex[key] = {}
                }
                this.allModalityIndex[key].windowWidth = windowWidth;
                this.allModalityIndex[key].windowCenter = windowCenter;

                if (itemOrientation == orientation) {
                    this.allModalityIndex[key].hflip = viewport.hflip;
                    this.allModalityIndex[key].vflip = viewport.vflip;
                    // this.allModalityIndex[key].invert = viewport.invert; // 反片有问题 TODO 
                    this.allModalityIndex[key].rotation = viewport.rotation;
                    this.allModalityIndex[key].scale = viewport.scale;
                    this.allModalityIndex[key].x = viewport.translation.x;
                    this.allModalityIndex[key].y = viewport.translation.y;

                    this.allModalityIndex[key].oldCanvasWidth = oldCanvasWidth;
                    this.allModalityIndex[key].oldCanvasHeight = oldCanvasHeight;
                }

                if (isFue) {
                    if (!this.allModalityIndex[key + 'Fuse']) {
                        this.allModalityIndex[key + 'Fuse'] = {}
                    }
                    if (this.allModalityIndex[key + 'Fuse']) {
                        this.allModalityIndex[key + 'Fuse'].colormap = viewport.colormap;
                    }
                } else {
                    this.allModalityIndex[key].colormap = viewport.colormap;
                }
            })
        }
    },
}