import macro from 'vtk.js/Sources/macro';
import vtkCompositeCameraManipulator from 'vtk.js/Sources/Interaction/Manipulators/CompositeCameraManipulator';
import vtkCompositeMouseManipulator from 'vtk.js/Sources/Interaction/Manipulators/CompositeMouseManipulator';
import vtkCoordinate from 'vtk.js/Sources/Rendering/Core/Coordinate';

// ----------------------------------------------------------------------------
// vtkInteractorStyleRotateManipulator methods 滚轮旋转工具
// ----------------------------------------------------------------------------

function vtkInteractorStyleRotateManipulator(publicAPI, model) {
	// Set our className
	model.classHierarchy.push('vtkInteractorStyleRotateManipulator');

	publicAPI.onScroll = (interactor, renderer, delta) => {

		const viewport = model.publicAPI.getViewport();

		// 旋转
		const value = delta > 0 ? 8 : -8
		viewport.rotateRelative(0, value);
		const {
			apis,
			apiIndex
		} = model.model;

		apis.forEach((api, index) => {
			if (apiIndex === index) {
				return
			}
			if (!api) {
				return
			}
			const renderWindow = api.genericRenderWindow.getRenderWindow()
			renderWindow.getInteractor().getInteractorStyle().getViewport().rotateRelative(0, value)
            renderWindow.render()
		})
		const api = apis[apiIndex];
		// 如果当前选择有定位线就触发定位线移动
		if (
			!api.svgWidgets.crosshairsWidget &&
			!api.svgWidgets.rotatableCrosshairsWidget
		) {
			return;
		}

		// //const renderer = api.genericRenderWindow.getRenderer();
		// let cachedCrosshairWorldPosition = api.get('cachedCrosshairWorldPosition');

		// if (cachedCrosshairWorldPosition === undefined) {
		// 	return;
		// }

		// const wPos = vtkCoordinate.newInstance();
		// wPos.setCoordinateSystemToWorld();
		// wPos.setValue(...cachedCrosshairWorldPosition);

		// const doubleDisplayPosition = wPos.getComputedDoubleDisplayValue(renderer);

		// const dPos = vtkCoordinate.newInstance();
		// dPos.setCoordinateSystemToDisplay();

		// dPos.setValue(doubleDisplayPosition[0], doubleDisplayPosition[1], 0);
		// let worldPos = dPos.getComputedWorldValue(renderer);

		// const camera = renderer.getActiveCamera();
		// const directionOfProjection = camera.getDirectionOfProjection();
		// const halfSlabThickness = api.getSlabThickness() / 2;

		// // Add half of the slab thickness to the world position, such that we select
		// //The center of the slice.

		// for (let i = 0; i < worldPos.length; i++) {
		// 	worldPos[i] += halfSlabThickness * directionOfProjection[i];
		// }

		// if (api.svgWidgets.crosshairsWidget) {
		// 	api.svgWidgets.crosshairsWidget.moveCrosshairs(worldPos, apis, apiIndex);
		// }
		// if (api.svgWidgets.rotatableCrosshairsWidget) {
		// 	api.svgWidgets.rotatableCrosshairsWidget.moveCrosshairs(
		// 		worldPos,
		// 		apis,
		// 		apiIndex
		// 	);
		// }

	};
}

// ----------------------------------------------------------------------------
// Object factory
// ----------------------------------------------------------------------------

const DEFAULT_VALUES = {
	zoomScale: 0.0,
};

// ----------------------------------------------------------------------------

export function extend(publicAPI, model, initialValues = {}) {
	Object.assign(model, DEFAULT_VALUES, initialValues);

	// Inheritance
	macro.obj(publicAPI, model);
	vtkCompositeMouseManipulator.extend(publicAPI, model, initialValues);
	vtkCompositeCameraManipulator.extend(publicAPI, model, initialValues);

	// Object specific methods
	vtkInteractorStyleRotateManipulator(publicAPI, model);
}

// ----------------------------------------------------------------------------

export const newInstance = macro.newInstance(
	extend,
	'vtkInteractorStyleRotateManipulator'
);

// ----------------------------------------------------------------------------

export default {
	newInstance,
	extend
};