<template>
    <el-dialog
        append-to-body
        title="图像另存"
        :visible.sync="innerVisible"
        :close-on-click-modal="false"
        @close="closeDialog"
        @open="openDialog"
        width="680px"
        custom-class="my-dialog">
        <div class="c-main">
            <div class="header">
                <!-- <div>
                    <span>图像尺寸</span>
                    <el-select v-model="form.imageLevel" @change="loadImage" size="mini" style="width: 110px" placeholder="">
                        <el-option
                        v-for="item in options.imageLevel"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                </div> -->
                <div>
                    <span>图片类型</span>
                    <el-select v-model="form.imageType" @change="loadImage" size="mini" style="width: 110px" placeholder="">
                        <el-option
                        v-for="item in options.imageType"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <span>图像名称</span>
                    <el-input size="mini" style="width: 110px"
                        v-model="form.imageName">
                    </el-input>
                </div>
                <div>
                    <el-checkbox v-model="form.showInfo" @change="loadImage">显示图像信息</el-checkbox>
                </div>
            </div>
            <div class="content" v-loading="loading">
                <!-- <img :src="dataURL"/> -->
                <el-image :src="dataURL" fit="cover" :preview-src-list="[dataURL]" :z-index="9999"></el-image>
            </div>
            <div class="footer">
                <el-button type="small" @click="onClickDown" icon="el-icon-download">下 载</el-button>
                <el-button type="small" @click="innerVisible = false" icon="el-icon-close" >关 闭</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import html2canvas from "html2canvas"
import ModeDialog from "$src/mixin/ModeDialog.js";
export default {
    mixins: [ ModeDialog ],
    props: {
        element: {
            type: [Array, HTMLCollection, HTMLDivElement],
            default: () => { [] }
        }
    },
    data() {
        return {
            loading: false,
            dataURL: '',
            form: {
                imageType: 'png',
                imageLevel: 1,
                imageName: 'image',
                showInfo: false
            },
            options: {
                imageType: [
                    {value: 'png', label: 'PNG'},
                    {value: 'jpeg', label: 'JPG'},
                ],
                imageLevel: [
                    {value: 1, label: '1倍'},
                    {value: 2, label: '2倍'},
                    {value: 3, label: '3倍'},
                    {value: 4, label: '4倍'},
                ]
            },
            cloneDomId: '',
        }
    },
    methods: {
        openDialog() {
            this.$nextTick(() => {
                this.loadImage();
            })
        },
        closeDialog() {
            this.dataURL = '';
            const dom = document.querySelector('#' + this.cloneDomId)
            dom && dom.remove()
        },
        loadImage() {
            if (this.cloneDomId) {
                const dom = document.querySelector('#' + this.cloneDomId)
                dom && dom.remove()
            }

            this.loading = true;
            setTimeout(() => {

                const dom = this.element 
            
                const cloneDom = dom.cloneNode(true)
                this.cloneDomId = 'clonedom' + +new Date()
                cloneDom.id = this.cloneDomId
                const w = dom.clientWidth
                const h = dom.clientHeight
                const minSize = Math.min(w, h)
                const domStyle = dom.getAttribute('style') || ''
                cloneDom.style = domStyle + `width:${minSize}px; height:${minSize}px; flex: 0 0 auto; z-index: 99999; position: absolute;top:19999px;left: 19999px;`


                cloneDom.querySelectorAll('[noprint=true]').forEach(e => e.remove())
                cloneDom.querySelectorAll('.layer-tools').forEach(e => e.remove())
                cloneDom.querySelectorAll('.i-print-select').forEach(e => e.remove())
                cloneDom.querySelectorAll('.iconcelianggongju1').forEach(e => e.remove())

                dom.parentNode.appendChild(cloneDom)


                const viewportList = dom.querySelectorAll('.viewportWrapper')
                const cloneViewportList = cloneDom.querySelectorAll('.viewportWrapper')
                viewportList.forEach((viewportEle, index) => {
                    const canvasEle = viewportEle.querySelector('canvas')
                    const cloneWrapper = cloneViewportList[index]
                    const cloneCav = cloneWrapper.querySelector('canvas')
                    if(!canvasEle || !cloneCav || !cloneWrapper) return
                    const cloneCavText = cloneCav.getContext('2d')
                    const viewportEleClass = viewportEle.getAttribute('class')

                    let widthDiff = cloneWrapper.clientWidth - canvasEle.width
                    let heightDiff = cloneWrapper.clientHeight - canvasEle.height
    
                    let remakeOffsetStl = `left: ${widthDiff / 2}px; top: ${heightDiff / 2}px; `
                    const remakeStyle = `width:${canvasEle.clientWidth}px; 
                        height:${canvasEle.clientHeight}px; ${remakeOffsetStl}`

                    if (/vtkviewport/.test(viewportEleClass)) {

                        widthDiff = cloneWrapper.clientWidth - canvasEle.clientWidth
                        heightDiff = cloneWrapper.clientHeight - canvasEle.clientHeight
                        remakeOffsetStl = `left: ${widthDiff / 2}px; top: ${heightDiff / 2}px; `

                        const vtkSvgStyle = `width:${canvasEle.clientWidth}px; 
                        height:${canvasEle.clientHeight}px; ${remakeOffsetStl}`

                        const svgDom = cloneWrapper.querySelector('svg[id^=vtkSVG]') 
                        if (svgDom) {
                            const svgStyle = svgDom.getAttribute('style') || ''
                            svgDom.style = svgStyle + vtkSvgStyle
                        }
                        cloneCav.style = vtkSvgStyle + 'position: relative; '
                    } else {
                        const crossDom = cloneWrapper.querySelector('.mpr-crosshairs')
                        if (crossDom) {
                            const Style = crossDom.getAttribute('style') || ''
                            crossDom.style = Style + remakeStyle
                        }
                        cloneCav.style = remakeStyle + 'position: relative; '
                    }

                    cloneCavText.drawImage(canvasEle, 0, 0)
                })

                const params = {
                    scale: 1,
                }

                params.ignoreElements = (element) => {
                    if (element.className === 'iconfont iconai69' ||                  // 打印标记图标
                        (!this.form.showInfo && element.getAttribute('saveimage'))    // 图像上覆盖的信息（患者、方位、标尺）) 
                        ) {
                        return true
                    }
                }

                html2canvas(cloneDom, params).then(canvas => {
                    this.dataURL = canvas.toDataURL("image/" + this.form.imageType, 1);
                    this.loading = false;
                }).catch(() => {
                    this.loading = false;
                })
            }, 100);
        },
        onClickDown() {
            this.$fun.getCanvasScreenshot(this.dataURL, (src) => {
                let a = document.createElement('a')
                a.download = this.form.imageName;
                a.href = src;
                document.body.append(a);
                a.click();
                a.remove()
            }, 1024, 1024, this.form.imageType);
        }
    }
}
</script>

<style lang="scss" scoped>
.header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    > div{
        span{
            padding-right: 10px;
        }
    }
}
.content{
    width: 600px;
    height: 602px;
    margin: 0 auto;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0px 0px 2px 0px #eee;
    text-align: center;
    overflow: auto;
}
.footer{
    text-align: right;
    padding-top: 10px;
}
</style>