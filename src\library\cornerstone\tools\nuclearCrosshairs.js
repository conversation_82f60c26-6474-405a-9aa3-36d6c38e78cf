import csTools from '$library/cornerstone/cornerstoneTools'
const BaseTool = csTools.importInternal("base/BaseTool");
const { crosshairsCursor } = csTools.importInternal("tools/cursors");
const { addToolState, getToolState, clearToolState, setToolOptions, loadHandlerManager } = csTools;
const convertToVector3 = csTools.import('util/convertToVector3')
import { vec3 } from 'gl-matrix';
import { throttle, debounce } from 'lodash-es';

// const triggerEvent = csTools.import('util/triggerEvent')

import crosshairsWidget from './crosshairsWidget'
import getConfigByStorageKey from '$library/utils/configStorage.js'


import findNearIndex from './math/findNearIndex.js';

export default class NuclearCrosshairs extends BaseTool {
    constructor(props = {}) {
        const defaultProps = {
            name: 'NuclearCrosshairs',
            supportedInteractionTypes: ['Mouse', 'Touch'],
            svgCursor: crosshairsCursor,
            configuration: {
                tabId: null,
                renderCallBack: () => {}
            }
        };

        super(props, defaultProps)
        this.instanceId = "mpr-crosshairs"
        this.timer = null
        this.showCrosshairs = true

        this.mouseDownCallback = this.throttleChooseLocation.bind(this);
        this.mouseDragCallback = this.throttleChooseLocation.bind(this);
        this.mouseClickCallback = this.throttleChooseLocation.bind(this);
        this.mouseUpCallback = this.throttleChooseLocation.bind(this);
        this.touchDragCallback = this.throttleChooseLocation.bind(this);
        this.postTouchStartCallback = this.throttleChooseLocation.bind(this);
        this.touchEndCallback = this.throttleChooseLocation.bind(this);
        this.newImageCallback = this._newImage.bind(this);

        this.getInitialPoint = this._getInitialPoint.bind(this);

        this.configsCrosshairsType = getConfigByStorageKey('configs-crosshairs-type');

    }
    /**
     * 获取当前 元素 的svg工具
     * @param {*} svgContainer 
     * @returns 
     */
    getWidgetNode(svgContainer) {
        let node = svgContainer.querySelector(`.${this.instanceId}`);
        if (!node) {
            node = document.createElement("g");
            node.setAttribute("class", this.instanceId);
            node.setAttribute(
                "style",
                "position: absolute; top: 0; left: 0; width: 100%; height: 100%;pointer-events: none;"
            );
            svgContainer.appendChild(node);
        }
        return node;
    }

    /**
     * 
     * @param {*} element 设置定位线元素
     * @param { x: Number, y: Number } point 定位xy值
     */
    setNodeCrosshairs(element, { x, y }) {
        const node = this.getWidgetNode(element)
        node.dataset.x = x
        node.dataset.y = y
    }

    /**
     * 
     * @param {*} element 定位线元素
     * @returns { x: Number, y: Number } 返回定位线 xy 值 
     */
    getNodeCrosshairs(element) {
        const node = this.getWidgetNode(element)
        if (!node.dataset.x || !node.dataset.y) {
            return null
        }
        return { x: node.dataset.x, y: node.dataset.y }
    }

    throttleChooseLocation(evt) {
        throttle(this._chooseLocation.bind(this, evt), 1000 / 60)(evt) // fps = 60
    }

    _chooseLocation(evt) {
        const eventData = evt.detail;
        const { element, image } = eventData;

        if (eventData.buttons === 0 && eventData.event.button !== 0) {
            return;
        }

        const sourceStackToolDataSource = getToolState(element, 'stack');
        if (sourceStackToolDataSource === undefined) {
            return;
        }
        const sourceStackData = sourceStackToolDataSource.data[0];
        const sourceOrientation = sourceStackData.state.orientation

        // 获取工具同步状态 
        const toolData = getToolState(element, this.name);
        if (!toolData) {
            return;
        }
        // 当前操作元素，cornerstone enabled 信息
        const sourceElement = element;

        // 获取操作元素的图像 id
        const sourceImageId = image.imageId;
        // 当前图像平面
        const sourceImagePlane = csTools.external.cornerstone.metaData.get(
            'imagePlaneModule',
            sourceImageId
        );
        if (!sourceImagePlane) {
            return;
        }
        // 当前操作点（图像坐标）
        const sourceImagePoint = eventData.currentPoints.image;

        const sourceOffsetParams = sourceImageId.split(':')[6] || '0,0,0' // 微调偏移参数

        // 转换为患者点（患者坐标系） 
        const patientPoint = this.imagePointToPatientPoint(
            sourceImagePoint,
            sourceImagePlane,
            sourceOffsetParams
        );
        const sourceImageIpp = vec3.fromValues(patientPoint.x, patientPoint.y, patientPoint.z)

        this.setNodeCrosshairs(element, sourceImagePoint)
        // this.drawLines(element, sourceImagePoint.x, sourceImagePoint.y)
        // const enEle = csTools.external.cornerstone.getEnabledElement(element)
        // csTools.external.cornerstone.displayImage(element, enEle.image, enEle.viewport)

        // 当前拖拽元素方向，存储起来
        csTools.store.state.chooseOrientation = sourceOrientation

        // 时间到，清除当前拖拽方向
        clearTimeout(csTools.store.state.chooseTimer)
        csTools.store.state.chooseTimer = setTimeout(() => {
            csTools.store.state.chooseOrientation = null
        }, 200);


        // 需要遍历改变的 x,y,z 角
        let allAngle = ['x', 'y', 'z']
        // 获取 synchronizer 同步器
        const syncContext = toolData.data[0].synchronizationContext;

        syncContext.patientPoint = patientPoint

        this.drawLinesInElement(element)
        requestAnimationFrame(() => {
            // 同步器所有元素
            const enabledElements = syncContext.getSourceElements();
            // 遍历同步器元素
            enabledElements.forEach( async(targetElement) => {
                // 与操作项相同
                if (targetElement === sourceElement) {
                    return;
                }

                const stackToolDataSource = getToolState(targetElement, 'stack');
                if (stackToolDataSource === undefined) {
                    return;
                }

                const stackData = stackToolDataSource.data[0];
                const orientation = stackData.state.orientation
                // const modality = stackData.state.sModality

                const targetAngleIdx = allAngle.indexOf(orientation)

                let targetImage
                try {
                    targetImage = cornerstone.getImage(targetElement);
                } catch (ex) {
                    console.warn('target image is not enabled??')
                    return
                }

                let targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId)

                // 不知道为什么，MR 非横截面图，没有 imagePlane 信息
                if (!targetImagePlane) {
                    await csTools.external.cornerstone.loadAndCacheImage(targetImage.imageId)
                    targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId)
                }
                // 不同角度，且相同角度只变化第一个
                if (sourceOrientation !== orientation && targetAngleIdx != -1) {
                    allAngle.splice(targetAngleIdx, 1)
                    // 遍历序列中的全部图像。在元素的堆栈中找到离所选位置最近的图像平面
                    const index = findNearIndex(stackData.imageIds, orientation, patientPoint)
                    // 滚动到对应位置
                    csTools.scrollToIndex(targetElement, index)
                }

                if (sourceOrientation == orientation) {
                    const crossPoint = this.projectPatientPointToImagePlane(sourceImageIpp, targetImagePlane)
                    // 存储最新点位置
                    this.setNodeCrosshairs(targetElement, { x: crossPoint.x, y: crossPoint.y })
    
                    // this.drawLines(targetElement, crossPoint.x, crossPoint.y)
                    window.requestAnimationFrame(() => {
                        this.drawLinesInElement(targetElement)
                    })
    
                }
            });
            // 回调
            this.configuration.renderCallBack(element)
        })
    }
    // 开启回调
    activeCallback(element, { mouseButtonMask, synchronizationContext, renderNewPoint }) {
        requestAnimationFrame(() => {

            // 设置工具配置（鼠标操作项）
            setToolOptions(this.name, element, { mouseButtonMask });

            // Clear any currently existing toolData
            clearToolState(element, this.name);
            // 添加工具同步状态
            addToolState(element, this.name, {
                synchronizationContext,
            });
            this.setFirstImage(synchronizationContext)
            // 不存在像素点，创建初始点
            if (!this.getNodeCrosshairs(element)) {
                this._getInitialPoint(element)
            } else if (renderNewPoint) {

                this.updateCrosshairs(element, synchronizationContext)
            }
        })

    }
    // 被动激活回调
    passiveCallback(element, { synchronizationContext, renderNewPoint }) {
        requestAnimationFrame(() => {

            const toolData = getToolState(element, this.name);
            if (!toolData || !toolData.data[0]) {
                addToolState(element, this.name, {
                    synchronizationContext,
                });
                this.setFirstImage(synchronizationContext)
            }

            // 不存在像素点，创建初始点
            if (!this.getNodeCrosshairs(element)) {
                const width = parseInt(element.clientWidth / 2, 10);
                const height = parseInt(element.clientHeight / 2, 10);
                const point = csTools.external.cornerstone.canvasToPixel(element, { x: width, y: height })

                this.setNodeCrosshairs(element, point)
                this.setSyncContextPatientPoint(element, point)
                // this.drawLines(element, point.x, point.y)
                this.drawLinesInElement(element)
            } else if (renderNewPoint) {


                this.updateCrosshairs(element, synchronizationContext)

            }
        })

    }
    // 标记设置第一个
    setFirstImage(synchronizationContext) {
        if (!synchronizationContext) {
            return;
        }
        const enabledElements = synchronizationContext.getSourceElements()

        let allType = [
            'CTx', 'CTy', 'CTz',
            'PTx', 'PTy', 'PTz',
            'FUSEx', 'FUSEy', 'FUSEz',
        ]

        for (let index = 0; index < enabledElements.length; index++) {
            const element = enabledElements[index];
            const toolData = getToolState(element, this.name);
            if (!toolData || !toolData.data[0]) {
                return;
            }
            const stackData = getToolState(element, 'stack');

            const type = stackData.data[0].state.viewportType

            const findTypeIdx = allType.indexOf(type)

            if (findTypeIdx !== -1) {
                allType.splice(findTypeIdx, 1)
                toolData.data[0].first = true
            }

        }
    }
    // 更新图像
    forceImageUpdate(element) {
        const enabledElement = csTools.external.cornerstone.getEnabledElement(
            element
        );

        if (enabledElement.image) {
            csTools.external.cornerstone.updateImage(element);
        }
    }
    // 图像变化时将十字线坐标更新到每一个视图
    _newImage(evt) {
        const { element, image } = evt.detail

        const toolData = getToolState(element, this.name);
        if (!toolData) {
            return;
        }
        // 同类型只在第一个视窗做更新处理
        if (!toolData.data[0] || !toolData.data[0].first) {
            return;
        }
        const sourceElement = element;
        const syncContext = toolData.data[0].synchronizationContext;

        const syncPatientPoint = syncContext.patientPoint
        if (!syncPatientPoint) {
            return;
        }
        const syncImageIpp = vec3.fromValues(syncPatientPoint.x, syncPatientPoint.y, syncPatientPoint.z)

        // 从患者点再转换成平面点 配合position变化后再转成患者点
        const targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', image.imageId)

        const point = this.projectPatientPointToImagePlane(syncImageIpp, targetImagePlane) // 患者点在这个图象平面内的点 

        const sourceOffsetParams = image.imageId.split(':')[6] || '0,0,0' // 微调偏移参数
        const newPatientPoint = this.imagePointToPatientPoint(
            point,
            targetImagePlane,
            sourceOffsetParams
        );
        const newSourceImageIpp = vec3.fromValues(newPatientPoint.x, newPatientPoint.y, newPatientPoint.z)

        // 同步器所有元素
        const enabledElements = syncContext.getSourceElements();
        enabledElements.forEach( async(targetElement) => {
            // 与操作项相同
            if (targetElement === sourceElement) {
                return;
            }


            let targetImage = cornerstone.getImage(targetElement);
            if (!targetImage) {
                return
            }

            const stackToolDataSource = getToolState(targetElement, 'stack');
            if (stackToolDataSource === undefined) {
                return;
            }

            const stackData = stackToolDataSource.data[0];
            const orientation = stackData.state.orientation

            // 有方向，方向值一样，不在处理
            if (csTools.store.state.chooseOrientation && csTools.store.state.chooseOrientation == orientation) {
                return;
            }


            let imagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId)
            // 不知道为什么，MR 非横截面图，没有 imagePlane 信息
            if (!imagePlane) {
                await csTools.external.cornerstone.loadAndCacheImage(targetImage.imageId)
                imagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId)
            }

            const crossPoint = this.projectPatientPointToImagePlane(newSourceImageIpp, imagePlane)

            // 存储最新点位置
            this.setNodeCrosshairs(targetElement, { x: crossPoint.x, y: crossPoint.y })

            this.setSyncContextPatientPoint(targetElement, crossPoint)

            // 渲染后，更新其它图的定位线
            window.requestAnimationFrame(() => {
                this.drawLinesInElement(targetElement)
            })
            // 回调
            this.configuration.renderCallBack(element)
        });
    }
    renderToolData(evt) {
        const element = evt.detail.element
        this.drawLinesInElement(element) 
    }
    drawLinesInElement(element) {
        const xy = this.getNodeCrosshairs(element)
        if (!xy) return
        this.drawLines(element, xy.x, xy.y)
    }
    // 绘制线条
    drawLines(element, px, py) {
        // 是否隐藏绘制定位线
        const hideReferenceLines = csTools.store.state.hideReferenceLines
        if (hideReferenceLines && hideReferenceLines[this.configuration.tabId]) {
            let node = element.querySelector(`.${this.instanceId}`);
            // 有定位线，进入删除定位线
            if (node && node.childNodes.length) {
                node.innerHTML = ''
            }
            return
        }
        const node = this.getWidgetNode(element);

        // 获取 canvas 的大小
        const width = parseInt(element.clientWidth, 10);
        const height = parseInt(element.clientHeight, 10);

        // 像素点转为 canvas 点，因为是要绘制到 svg上面的
        const {
            x,
            y
        } = cornerstone.pixelToCanvas(element, {
            x: px,
            y: py
        });

        let storeCrosshairsType = 'large';
        let type = 'CT';

        // 获取当前设备类型
        const sourceStack = cornerstoneTools.getToolState(element, 'stack');
        if (sourceStack !== undefined && sourceStack.data) {
            // 融合
            if (sourceStack.data[1]) {
                type = 'FUSE';
            }else {
                type = sourceStack.data[0].state.sModality;
            }
            storeCrosshairsType = cornerstoneTools.store.state.crosshairsType[type];
        }

        // 绘制线条
        crosshairsWidget(node, width, height, x, y, this.showCrosshairs, storeCrosshairsType, 
            this.configsCrosshairsType.toolColor);
    }
    // Tools 内置的一个算法 
    projectPatientPointToImagePlane(patientPoint, imagePlane) {
        const rowCosines = imagePlane.rowCosines;
        const columnCosines = imagePlane.columnCosines;
        const imagePositionPatient = imagePlane.imagePositionPatient;
        const rowCosinesVec3 = vec3.fromValues(...rowCosines);
        const colCosinesVec3 = vec3.fromValues(...columnCosines);
        const ippVec3 = vec3.fromValues(...imagePositionPatient);

        const point = vec3.create();
        vec3.sub(point, patientPoint, ippVec3);

        let x = vec3.dot(rowCosinesVec3, point) / imagePlane.columnPixelSpacing;
        let y = vec3.dot(colCosinesVec3, point) / imagePlane.rowPixelSpacing;

        return { x, y };
    }

    imagePointToPatientPoint(imagePoint, imagePlane, offsetParams = '0,0,0') {
        const rowCosines = convertToVector3(imagePlane.rowCosines); // { x, y, z }
        const columnCosines = convertToVector3(imagePlane.columnCosines);
        const imagePositionPatient = imagePlane.imagePositionPatient.slice();
        const x = rowCosines.clone().multiplyScalar(imagePoint.x);
        x.multiplyScalar(imagePlane.columnPixelSpacing);
        const y = columnCosines.clone().multiplyScalar(imagePoint.y);
        y.multiplyScalar(imagePlane.rowPixelSpacing);
        const patientPoint = x.add(y);

        // 坐标偏移
        const rowCosinesVec3 = imagePlane.rowCosines;
        const colCosinesVec3 = imagePlane.columnCosines;
        const zedCosinesVec3 = vec3.create()
        vec3.cross(zedCosinesVec3, rowCosinesVec3, colCosinesVec3);
        const paramsArray = offsetParams.split(',')
        const offset = vec3.fromValues(paramsArray[0], paramsArray[1], paramsArray[2])
        const offsetZ = vec3.dot(offset, zedCosinesVec3) / vec3.length(zedCosinesVec3)

        const offsetOrientFlag = rowCosinesVec3[0] ? 1 : -1 // 用来修正偏移方向
        imagePositionPatient[_byNormalFindAngleIdx(zedCosinesVec3)] -= offsetZ * offsetOrientFlag // 修正偏移方向

        const imagePositionPatientObj = convertToVector3(imagePositionPatient); // { x, y, z }

        patientPoint.add(imagePositionPatientObj);
        return patientPoint;
    }

    setSyncContextPatientPoint(element, sourceImagePoint) {
        const image = cornerstone.getImage(element);
        const sourceImageId = image.imageId;

        const sourceOffsetParams = sourceImageId.split(':')[6] || '0,0,0'; // 微调偏移参数

        const sourceImagePlane = csTools.external.cornerstone.metaData.get(
            'imagePlaneModule',
            sourceImageId
        );

        if (!sourceImagePlane) {
            return;
        }

        // 转换为患者点（患者坐标系） 
        const patientPoint = this.imagePointToPatientPoint(
            sourceImagePoint,
            sourceImagePlane,
            sourceOffsetParams
        );

        const toolData = getToolState(element, this.name);
        if (!toolData || !toolData.data[0]) {
            return;
        }

        const syncContext = toolData.data[0].synchronizationContext;
        syncContext.patientPoint = patientPoint;

        return patientPoint
    }

    updateCrosshairs(element, synchronizationContext) {
        const elements = synchronizationContext.getSourceElements()

        for (let index = 0; index < elements.length; index++) {

            const sourceElement = elements[index]
            if (sourceElement != element) {

                // 二件事，改变定位线、改变图像
                const toolData = getToolState(sourceElement, this.name);
                if (!toolData || !toolData.data[0]) {
                    continue;
                }

                const syncContext = toolData.data[0].synchronizationContext;
                const patientPoint = syncContext.patientPoint;

                const sourceImageIpp = vec3.fromValues(patientPoint.x, patientPoint.y, patientPoint.z)


                const stackData = getToolState(element, 'stack');
                if (!stackData || !stackData.data[0]) {
                    continue;
                }

                const index = findNearIndex(stackData.data[0].imageIds, stackData.data[0].state.orientation, patientPoint)
                // 滚动到对应位置
                csTools.scrollToIndex(element, index)

                setTimeout(() => {
                    const targetImage = cornerstone.getImage(element)
                    const targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId)
                    const crossPoint = this.projectPatientPointToImagePlane(sourceImageIpp, targetImagePlane)

                    // 存储最新点位置
                    this.setNodeCrosshairs(element, { x: crossPoint.x, y: crossPoint.y })

                    this.setSyncContextPatientPoint(element, crossPoint)
                    // this.drawLines(element, crossPoint.x, crossPoint.y)

                    this.drawLinesInElement(element)
                }, 0);
                return
            }
        }
    }

    // 获取初始点位置
    _getInitialPoint(element) {
        const toolData = getToolState(element, this.name);
        if (!toolData || !toolData.data[0]) {
            return;
        }
        const targetElement = element;
        const syncContext = toolData.data[0].synchronizationContext;
        const syncPatientPoint = syncContext.patientPoint
        if (!syncPatientPoint) {

            const width = parseInt(element.clientWidth / 2, 10);
            const height = parseInt(element.clientHeight / 2, 10);
            const point = csTools.external.cornerstone.canvasToPixel(element, { x: width, y: height })
            this.setNodeCrosshairs(element, point)
            this.setSyncContextPatientPoint(element, point)
            // this.drawLines(element, point.x, point.y)
            this.drawLinesInElement(element)
            return;
        }
        const syncImageIpp = vec3.fromValues(syncPatientPoint.x, syncPatientPoint.y, syncPatientPoint.z)
        let targetImage = cornerstone.getImage(targetElement);
        if (!targetImage) {
            return
        }
        const targetImagePlane = csTools.external.cornerstone.metaData.get('imagePlaneModule', targetImage.imageId)
        const point = this.projectPatientPointToImagePlane(syncImageIpp, targetImagePlane) // 患者点在这个图象平面内的点 
        this.setNodeCrosshairs(element, point)
        this.drawLinesInElement(element) // 拖拽布局默认使用定位线的时候。拖第二个不会渲染定位线的问题
    }
}


// 通过法线，[0,0,0]。那个位置最大，就表示所属方向 x,y,z
// 返回下标  0(x),1(y),2(z)
function _byNormalFindAngleIdx(normal) {
    const absNormal = normal.map(item => Math.abs(item));
    let max = absNormal[0];
    let index = 0;
    for (let i = 1; i < absNormal.length; i++) {
        if (max < absNormal[i]) {
            max = absNormal[i];
            index = i;
        }
    }
    return index; // 0(x), 1(y), 2(z) 
}
