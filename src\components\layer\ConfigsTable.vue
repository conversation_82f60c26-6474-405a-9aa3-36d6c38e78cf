<template>
    <el-dialog
        append-to-body
        :title="title"
        :visible.sync="innerVisible"
        :close-on-click-modal="false"
        width="720px"
        custom-class="my-dialog">
        <div style="height: 500px;padding: 20px">
            <el-table
                ref="modalityList" border v-loading="loading"
                :data="systemSetList" style="width: 100%" height="100%">
                <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
                <el-table-column prop="sReadSetName" label="配置名称" align="center"></el-table-column>
                <el-table-column prop="dCreateDate" label="创建时间" width="140" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.dCreateDate.slice(0, 10) }}
                    </template>
                </el-table-column>
                <el-table-column prop="iIsClinic" label="临床配置" width="90" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.iIsClinic ? '是' : '' }}
                    </template>
                </el-table-column>
                <el-table-column
                    label="操作"
                    align="center"
                    width="100">
                    <template slot-scope="scope">
                        <el-button size="mini" plain style="padding: 4px;" :disabled="!!scope.row.iIsClinic" @click="onClickClinic(scope.row)">临床</el-button>
                        <el-button size="mini" plain style="padding: 4px;" @click="onClickDel(scope.$index, scope.row)" :disabled="scope.row.sId == systemSetId" ><i class="el-icon-delete"></i></el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-dialog>
</template>
<script>
import ModeDialog from "$src/mixin/ModeDialog.js"
export default {
    mixins: [ModeDialog],
    props: {
        systemSetId: ''
    },
    data() {
        return {
            title: '服务器配置方案',
            loading: false
        }
    },
    computed: {
        systemSetList() {
            return this.$store.state.systemSetList
        },
    },
    methods: {
        onClickDel(index, row) {
            this.loading = true
            this.$Api.apiSet.delOne({sId: row.sId}).then(res => {
                if (res.success) {
                    this.$store.state.systemSetList.splice(index, 1)
                }
            }).finally(() => {
                this.loading = false
            })
        },
        onClickClinic(row) {
            this.loading = true
            this.$store.state.systemSetList.forEach(item => {
                if (item.iIsClinic) {
                    const params = {
                        sId: item.sId,
                        sReadSetName: item.sReadSetName,
                        sReadSetJson: item.sReadSetJson,
                        iIsEnable: item.iIsEnable,
                        sLoginUserId: item.sLoginUserId,
                        iIsClinic: 0
                    }
                    this.$Api.apiSet.editOne(params).then(res => {
                        item.iIsClinic = 0
                    })
                }
            });
            const params = {
                sId: row.sId,
                sReadSetName: row.sReadSetName,
                sReadSetJson: row.sReadSetJson,
                iIsEnable: row.iIsEnable,
                sLoginUserId: row.sLoginUserId,
                iIsClinic: 1
            }
            this.$Api.apiSet.editOne(params).finally(() => {
                this.loading = false
                row.iIsClinic = 1
            })
        }
    }
}
</script>