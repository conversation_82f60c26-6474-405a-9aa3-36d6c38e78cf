.c-tool-header{
    .c-layout{
        display: flex;
        padding-left: 1px;
        border-bottom: 1px solid #d2d2d2;
    }
    .c-box{
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-around;
        width: 98px;
    }
    .c-box-01 ,.c-box-01-right{
        padding: 2px 0px 2px 0px;
        > li {
            float: left;
            width: 48px;
            height: 48px;
            line-height: 48px;
            /* border: 1px solid #d2d2d2; */
            margin-right: 0;
            margin-bottom: 0;
            cursor: pointer;
            transition: all 0.2s;
            background: #fff;
            border-radius: 4px;
            i {
                display: inline-block;
                width: 100%;
                height: 100%;
                border-radius: 2px;
                background-size: cover;
            }
            &:hover{
                box-shadow: 2px 2px 4px 0px #c0c0c0;
            }
            &.i-active{
                border-color: #6294B7;
                color: white;
            }
        }
        
    }
    .c-box-01-right {
        width: 148px;
        border-left: 1px solid #aaa;
    }
}