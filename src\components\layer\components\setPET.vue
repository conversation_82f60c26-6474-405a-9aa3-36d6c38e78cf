<template>
    <div style="height: 100%;display: flex;flex-direction: column;">
        <ul class="c-content">
            <li class="item">
                <h6>SUV 校正因子</h6>
                <el-radio-group class="radio-group" v-model="currentConfigs.methodSUV">
                    <el-radio v-for="item in methodSUVList" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
                </el-radio-group>
            </li>
            <!-- <li class="item">
                <h6>重建默认窗宽窗位</h6>
            </li> -->
        </ul>
        <footer>
            <i class="c-tip">*仅保存在本地，长期使用请点下方保存至服务器</i>
            <span class="i-commit" @click="onClickSave">保存</span>
        </footer>
    </div>
</template>
<script>
import getConfigByStorageKey from '$library/utils/configStorage.js'
export default {
    name: 'setPET',
    data() {
        return {
            currentConfigs: {},                 // 当前值
            storageKey: 'configs-PET',
            methodSUVList: [
                { value: 1, name: 'SUV Body Weight (默认)' },
                { value: 2, name: 'SUV Lean Body Mass (LBM)' },
                { value: 3, name: 'SUV Body Surface Area (BSA)' },
                // { value: 4, name: 'Body Mass Index' }
            ]
        }
    },
    mounted() {
        this.currentConfigs = getConfigByStorageKey(this.storageKey);
    },
    methods: {
        onClickSave() {
            // 设置新的缓存
            localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfigs))

            cornerstoneTools.store.state.reviseSUV = this.currentConfigs.methodSUV
            // 触发渲染
            this.triggerUpdateView();
            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        },
        // 浏览器触发渲染 canvas
        triggerUpdateView(){
            const toolState = cornerstoneTools.globalImageIdSpecificToolStateManager.toolState
            for (const key in toolState) {
                if (Object.hasOwnProperty.call(toolState, key)) {
                    const image = toolState[key];
                    
                    for (const key in image) {
                        if (Object.hasOwnProperty.call(image, key)) {
                            const roi = image[key];
                            
                            for (let index = 0; index < roi.data.length; index++) {
                                const tool = roi.data[index];
                                tool.invalidated = true
                            }

                        }
                    }

                }
            }
            this.$nextTick(() => {
                const myEvent = new Event('resize');
                window.dispatchEvent(myEvent)
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content{
    flex: 1;
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .item{
        position: relative;
        width: 48%;
        height: 49%;
        padding: 10px 10px 10px 20px;
        border: 1px dashed #ccc;
        box-shadow: 0px 1px 2px 1px #ccc;
        background: white;
        >h6{
            font-size: 14px;
            position: absolute;
            top: -10px;
            left: 10px;
            background: white;
            font-weight: bold;
        }
    }
}
.radio-group{
    padding-top: 14px;
    label{
        display: block;
        padding-bottom: 14px;
        ::v-deep .el-radio__label{
            font-size: 13px;
        }
    }
}
footer{
    height: 40px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>