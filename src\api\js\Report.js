import request from '../request'
import qs from 'qs'
import apiSet from './Set'
const baseUrl = window.configs.protocol + '//' + window.configs.imageServe
const reportUrl = window.configs.protocol + '//' + window.configs.reportServe
const authUrl = reportUrl.replace('report', 'auth')
export default {
    apiSet,
    getStudy(params){
        return request({
            url: baseUrl + '/web/view/study/getlistByPatientId', // '/web/view/study/getlistByPatientId',
            method: 'POST',
            params
        })
    },
    getNoCaseStudy(params){
        return request({
            url: baseUrl + '/web/view/study/queryImageWithoutCase', // '/web/view/study/getlistByPatientId',
            method: 'POST',
            params
        })
    },
    findAllShotInstance(params) {
        return request({
            url: baseUrl + '/web/view/findAllShotInstance',
            method: 'POST',
            params
        })
    },
    findAllShotInstanceWithoutCase(params) {
        return request({
            url: baseUrl + '/web/view/findAllShotInstanceWithoutCase',
            method: 'POST',
            params
        })
    },
    queryPastPatients(data){
        return request({
            url: reportUrl + '/past/patient/info/queryPastPatients',
            method: 'POST',
            data
        })
    },
    findPatient(params){
        return request({
            url: reportUrl + '/report/find/patients/row',
            method: 'POST',
            params
        })
    },
    // printPDF(data){
    //     return request({
    //         url: baseUrl + '/web/view/createWebPrintFile',
    //         method: 'POST',
    //         data,
    //         timeout: 1000 * 180
    //     })
    // },
    // printReportPdf(data){
    //     return request({
    //         url: reportUrl + '/img/print/imgprintpdf',
    //         method: 'POST',
    //         data,
    //         timeout: 1000 * 180
    //     })
    // },
    
    // 获取模板方式
    getListOfWorkStation(params){
        return request({
            url: reportUrl + '/print/workStationPrintShow/listOfWorkStation',
            method: 'POST',
            data: qs.stringify(params)
        })
    },
    // 获取所有打印模板
    getTemplate(data){
        return request({
            url: reportUrl + '/modulePrint/getTemplateOfPrintClassify', // '/template/find/page', '/modulePrint/find/findModulePrintDisplay'
            method: 'POST',
            data
        })
    },
    // 改变打印参数
    saveWorkStationPrint(data) {
        return request({
            url: reportUrl + '/printing/workStationPrint/save',
            method: 'POST',
            data
        })
    },
    getPrinterNames() {
        return request({
            url: window.configs.apricotAssist + '/printer/get/names',
            method: 'POST'
        })
    },
    // 静默打印
    silentPrint(params) {
        return request({
            url: window.configs.apricotAssist + '/download/file/print',
            method: 'POST',
            data: qs.stringify(params)
        })
    },

    // 打开 word
    wordPreview(params) {
        return request({
            url: window.configs.apricotAssist + '/download/file/preview',
            method: 'POST',
            data: qs.stringify(params)
        })
    },

    // 创建图像打印
    createImagePrint(data) {
        return request({
            url: reportUrl + '/printFile/print',
            method: 'POST',
            data
        })
    },
    queryClientIp(data) {
        return request({
            url: reportUrl + '/system/queryClientIp',
            method: 'POST',
            data
        })
    },
    reconstructionSave(data) {
        return request({
            url: reportUrl + '/reconstruction/save',
            method: 'POST',
            data
        })
    },
    saveScreen(data, url){
        return request({
            url: window.configs.protocol + '//' + url + '/image/web/view/saveScreenshot',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        }) 
    },
    delScreen(data){
        return request({
            url: baseUrl + '/web/view/del/screenshots',
            method: 'POST',
            data
        })
    },
    saveMeasure(data){
        return request({
            url: baseUrl + '/web/view/save/measure',
            method: 'POST',
            data: data
        }) 
    },
    findMeasure(data) {
        return request({
            url: baseUrl + '/web/view/find/measure',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        }) 
    },
    findScreenByGroupId(data){
        return request({
            url: baseUrl + '/web/view/find/screenshots',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        }) 
    },
    getUserInfo(data) {
        const reportUrl = window.configs.protocol + '//' + window.configs.reportServe.split('/')[0]
        return request({
            url: reportUrl + '/auth/login/user/find/id',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        })
    },
    editInstanceRemark(data) {
        return request({
            url: baseUrl + '/web/view/edit/instanceRemark',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        })
    },
    editSignGraph(data) {
        return request({
            url: baseUrl + '/web/view/sign/graph',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        })
    },
    updateInstanceOrder(data) {
        return request({
            url: baseUrl + '/web/view/updateInstanceOrder',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        })
    },
    loginHeartbeat(params) {
        return request({
            url: authUrl + '/login/heartbeat',
            method: 'post',
            params
        })
    },
    getKV(data) {
        return request({
            url: reportUrl + '/frontend/getKV',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        })
    },
    putKV(data) {
        return request({
            url: reportUrl + '/frontend/putKV',
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: qs.stringify(data)
        })
    },
      
}