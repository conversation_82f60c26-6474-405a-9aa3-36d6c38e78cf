<template>
    <el-dialog append-to-body :title="title + '(版本号:' + checkUpdateString + ')'" :visible.sync="visible" @close="closeDialog" :close-on-click-modal="false"
        width="720px" custom-class="my-dialog config-set-dialog">
        <div class="c-main">
            <div class="c-left">
                <ul>
                    <li v-for="list in menuList" :key="list.id">
                        <template v-if="list.children">
                            <p class="menu-title">{{ list.name }}</p>
                            <ul class="inner-ul">
                                <li v-for="item in list.children" :key="item.id"
                                    :class="{ 'i-avtive': item.id === activeTab }" @click="onClickMenu(item.id)">{{
                                            item.name
                                    }}</li>
                            </ul>
                        </template>
                        <template v-else>
                            <p class="menu-title menu-link" :class="{ 'i-avtive': list.id === activeTab }"
                                @click="onClickMenu(list.id, true)">{{ list.name }}</p>
                        </template>
                    </li>
                </ul>
            </div>
            <div class="c-right">
                <keep-alive>
                    <component :is="activeTab" @close="visible = false" v-model="innerVisible" @closeDialog="closeInnerDialog"></component>
                </keep-alive>
            </div>
            <div class="bottom-bar">

                <div class="left">
                    <div class="text">
                        当前配置方案
                    </div>
                    <div class="inline-box" >  
                        <el-select v-model="systemSetId" @change="onSystemSetChange" size="small" style="width: 120px" >
                            <el-option
                            v-for="item in systemSetList"
                            :key="item.sId"
                            :label="item.sReadSetName"
                            :value="item.sId">
                            </el-option>
                        </el-select>
                    </div>
                    <el-button type="mini" @click="onClickSave">保存至服务器</el-button> 
                    <el-button type="mini" @click="onClickSaveAs">配置另存</el-button> 
                    <el-button type="mini" @click="visibleConfigsTable = true">更多</el-button>

                </div>
                <div class="right">
                    <el-button type="mini" @click="onClickExport">导出配置</el-button>
                    <el-button type="mini" @click="onClickImport">导入配置</el-button>
                    <input type="file" ref="fileInput" @change="onFileInputChange" style="display: none;" />

                </div>
            </div>
            <ConfigsTable v-model="visibleConfigsTable" :systemSetId="systemSetId"></ConfigsTable>
        </div>
    </el-dialog>
</template>
<script>
import {
    setShowRead,
    setCrosshairs, setToolsColor, setWindowCenter, setShowRebuild, otherInfoSet, setPET, setDot, setCircleRoi, setImageTag, setCircleMeasure, setImageSharpness
} from './components'
import { getAllStorageInJSON, setAllStorageInJSON } from '$library/utils/readwriteStorage.js'
// import DisplayPlanSet from '$components/layer/DisplayPlanSet'
const DisplayPlanSet = () => import('$components/layer/DisplayPlanSet')
import ConfigsTable from './ConfigsTable.vue'

import checkUpdateString from '/public/checkUpdate.json'


export default {
    props: {
        title: {
            type: String,
            default: "系统设置",
        },
        dialogVisible: {
            type: Boolean,
            default: false
        },
    },
    components: {
        setCrosshairs,
        setToolsColor,
        setWindowCenter,
        setShowRebuild,
        otherInfoSet,
        setPET,
        setDot,
        setImageTag,
        setCircleRoi,
        setCircleMeasure,
        setImageSharpness,
        DisplayPlanSet,
        setShowRead,
        ConfigsTable
    },
    data() {
        return {
            systemSetId: '',
            visible: false,
            innerVisible: false,
            loading: false,
            activeTab: 'setCrosshairs',
            preId: '',
            visibleConfigsTable: false,
            menuList: [
                {
                    id: 'showSet',
                    name: '显示设置',
                    children: [
                        {
                            id: 'setCrosshairs',
                            name: '定位线显示设置',
                        },
                        {
                            id: 'setToolsColor',
                            name: '工具样式设置',
                        },
                        {
                            id: 'setWindowCenter',
                            name: '窗宽窗位预设',
                        },
                        {
                            id: 'setShowRead',
                            name: '阅图设置',
                        },
                        {
                            id: 'setShowRebuild',
                            name: '重建设置',
                        },
                        {
                            id: 'DisplayPlanSet',
                            name: '显示方案（重建）',
                            openDialog: true
                        }
                    ]
                },
                {
                    id: 'otherSet',
                    name: '其它设置',
                    children: [
                        {
                            id: 'otherInfoSet',
                            name: '工具操作设置'
                        },
                        {
                            id: 'setPET',
                            name: 'PET-SUV设置'
                        },
                        {
                            id: 'setImageSharpness',
                            name: '图像质量'
                        }
                    ]
                },
                {
                    id: 'selectItemSet',
                    name: '选择项设置',
                    children: [
                        {
                            id: 'setDot',
                            name: '点测量'
                        },
                        {
                            id: 'setCircleRoi',
                            name: '圆测量'
                        },
                        {
                            id: 'setCircleMeasure',
                            name: '体测量'
                        }
                    ]
                },
                {
                    id: 'setImageTag',
                    name: '图像信息',
                },
                {
                    id: 'shortcutInfo',
                    name: '快捷键说明',
                }
            ]
        }
    },
    computed: {
        systemSetList() {
            return this.$store.state.systemSetList
        },
        userId() {
            return this.$store.state.userId
        },
        checkUpdateString() {
            const str = checkUpdateString || +new Date()
            return new Date(str).toLocaleString()
        }
    },
    watch: {
        dialogVisible: {
            handler() {
                this.visible = this.dialogVisible;
            },
            immediate: true
        }
    },
    methods: {
        async onClickSave() {
            const sId = this.systemSetId
            if (!sId) {
                this.$message.info('该配置数据未创建，请先【另存为】')
                return
            }
            const itemNow = this.systemSetList.find(item => item.sId === sId) 
            if(!itemNow) return

            const name = itemNow.sReadSetName

            const storage = await getAllStorageInJSON()
            const json = storage.json
            if(!json) return

            const params = {
                sId,
                sReadSetName: name,
                sReadSetJson: json,
                iIsEnable: 1,
                sLoginUserId: this.userId || '00000000'
            }

            const loading = this.$loading.service({
				target: '.config-set-dialog'
			})

            let requestResult
            try {
                requestResult = await this.$Api.apiSet.editOne(params) 
            } catch (error) {
                this.$message.error('请求出错')
                
            }
            if (requestResult) {
                if (requestResult.success) {
                    this.$message.success('配置保存成功！')
                }
            } else {
                this.$message.error('请求出错')
            }

            loading.close()
        },
        async onClickSaveAs() {
            const name = prompt('请输入配置名称')
            if(!name) return
            
            const storage = await getAllStorageInJSON()
            const json = storage.json
            if(!json) return

            const params = {
                sReadSetName: name,
                sReadSetJson: json,
                iIsEnable: 1,
                sLoginUserId: this.userId || '00000000'
            }

            const loading = this.$loading.service({
				target: '.config-set-dialog'
			})

            let requestResult
            try {
                requestResult = await this.$Api.apiSet.addOne(params) 
            } catch (error) {
                this.$message.error('请求出错')
                
            }
            if (requestResult) {
                if (requestResult.success) {
                    this.$message.success('配置另存成功！')

                    const SetListResult = await this.$Api.apiSet.findList({
                        condition: {
                        },
                        page: {
                            pageSize: 999
                        }
                    })
                    let SetList
                    if (SetListResult && SetListResult.success) {
                        SetList = SetListResult.data.records || SetListResult.data.recordList || []
                        this.$store.commit('setSystemSetList', SetList)
                    }
                }
            } else {
                this.$message.error('配置另存请求出错')
            }

            loading.close()

        },
        onSystemSetChange(val) {
            const name = this.systemSetList.find(item => item.sId === val).sReadSetName
            const confirmRes  = confirm('确定要切换到【' + name + '】吗？切换将会刷新页面')
            if (confirmRes) {
                localStorage.setItem('configs-userSetId', val)
                localStorage.setItem('needToSetStorage', 1)
                window.location.reload()
            } else {
                this.systemSetId = localStorage.getItem('configs-userSetId') || ''
            }
        },
        onClickExport() {
            getAllStorageInJSON().then((res) => {
                const json = res.json
                const fileBlob = new Blob([json], { type: 'text/json' });
                const linkEl = document.createElement('a')
                linkEl.href = URL.createObjectURL(fileBlob);
                linkEl.download = '图像系统配置_' + new Date().toLocaleString() + '.json';
                linkEl.click();
            })

        },
        onClickImport() {
            this.$refs.fileInput.click()
        },
        onFileInputChange(e) {
            const files = e.target.files
            const file = files[0]
            e.target.value = ''
            if (!file) return

            const fileReader = new FileReader();
            fileReader.onload = (event) => {
                const result = event.target.result
                let resultObj = {}
                try {
                    resultObj = JSON.parse(result)
                } catch (error) {
                    this.$message.error('导入出错！')
                    return
                }
                setAllStorageInJSON(resultObj).then((res) => {
                    if (res) {
                        this.$message.success('导入成功，刷新页面后生效！')
                    } else {
                        this.$message.error('导入出错！')
                    }
                })
            }

            fileReader.readAsText(file)

        },

        // 点击目录显示
        onClickMenu(id, toPre) {
            this.preId = this.activeTab;
            this.innerVisible = true;

            if (id === 'shortcutInfo') {
                this.$fun.triggerShortcutVisible.call(this)
            }else {
                this.activeTab = id;
            }

            if (toPre) {
                this.$nextTick(() => {
                    this.activeTab = this.preId;
                })
            }

        },
        closeDialog() {
            this.$emit('update:dialogVisible', false);
        },
        closeInnerDialog() {
            this.innerVisible = false
            this.$nextTick(() => {
                this.activeTab = this.preId
            })
        }
    },
    mounted() {
        this.systemSetId = localStorage.getItem('configs-userSetId') || ''
    }
}
</script>
<style lang="scss" scoped>
.c-main {
    display: flex;
    flex-direction: row;
    height: 620px;
    font-size: 15px;
    color: #666;
    padding-bottom: 42px;

    .c-left {
        width: 180px;
        margin-right: 20px;
        border-right: 1px solid #ccc;
        overflow: auto;

        .menu-title {
            padding-bottom: 14px;

            &.i-avtive {
                color: #6294b7;
            }

            &.menu-link:hover {
                cursor: pointer;
                text-decoration: underline;
            }
        }

        .inner-ul {
            padding-bottom: 4px;

            >li {
                padding-bottom: 14px;
                text-indent: 2em;
                cursor: pointer;

                &.i-avtive {
                    color: #6294b7;
                }

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }

    .c-right {
        position: relative;
        flex: 1;
        overflow: auto;


    }

    .bottom-bar {
        position: absolute;
        width: 100%;
        border-top: 1px solid #ccc;
        bottom: 0;
        left: 0;
        padding: 7px 5px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .left {
            display: flex;
            flex-direction: row;
            align-items: center;
            .text {
                display: inline-block;
                font-size: 14px;
                margin: 0 10px;
            }
            .inline-box {
                display: inline-block;
                margin: 0 10px 0 0;

            }
        }
        .right {
            display: flex;
            flex-direction: row;
            align-items: center;
        }
    }
}
</style>