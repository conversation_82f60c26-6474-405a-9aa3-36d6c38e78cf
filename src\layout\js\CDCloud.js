import Api from '$api'
import store from '$src/store'
import Fun from '$library/utils/function'
import { MessageBox } from 'element-ui'
export default class CDCloud {
    static getInstance() {
        if (!CDCloud.instance) {
            CDCloud.instance = new CDCloud();
        }
        return CDCloud.instance;
    }
    constructor() {
        this.project = window.configs.project || { token_expired: 401 }
        this.title = '信息验证中'
        this.visible = false
        this.storeParams()
    }
    
    getUserInfo() {
        return new Promise((resolve, reject) => {
            // 请求获取信息
            Api.authToken({authCode: store.state.authCode}).then(res => {
                if (res.success) {
                    store.commit('setUserTokenInfo', res.data)
                    resolve(true)
                }else {
                    resolve(false)
                }
            }).catch(() => {
                resolve(false)
            })
        })

    }
    storeParams() {
        let searchUrl = window.location.hash.split('?', 2)
        const urlObj = Fun.searchParse()
        if (searchUrl && searchUrl.length === 2) {
            // 存储 url 参数
            sessionStorage.setItem('urlParams', searchUrl[1])
            sessionStorage.setItem('urlObj', JSON.stringify(urlObj))
            // sessionStorage.setItem('token', urlObj.token)
            // 情况 url 参数
            // window.location.href = searchUrl[0]
            store.commit('GET_INIT_PARAMS', urlObj)
        } else {
            // 如果是没参数的时候
            const token = sessionStorage.getItem('token')
            // 是否存储了token
            if (token) {
                // 存储到 store
                const urlObj = JSON.parse(sessionStorage.getItem('urlObj'))
                store.commit('GET_INIT_PARAMS', urlObj)
            }
        }
    }
    authorization() {
        const userInfo = sessionStorage.getItem('userInfo')
        const authCode = sessionStorage.getItem('authCode')
        // 请求的权限值跟存储的权限值不一样，或者不存在缓存的用户信息
        // 再次请求获取新的权限
        if (authCode !== store.state.authCode || !userInfo) {
            this.getUserInfo().then(res => {
                this.visible = res
                if (res) {
                    this.setImageRequestInfo()
                }else {
                    this.title = '验证失败!'
                }
            }).catch(() => {
                this.visible = false
                this.title = '验证失败!'
            })
        }else {
            const info = JSON.parse(userInfo)
            store.state.token    = info.token
            store.state.userInfo = info
            this.setImageRequestInfo()
            this.visible = true
        }
    }
    setImageRequestInfo() {
        const token = sessionStorage.getItem('token')
        const beforeProcessing = this.beforeProcessing.bind(this)
        cornerstoneWADOImageLoader.configure(
            {
                beforeSend(xhr) {
                    xhr.setRequestHeader('token', token)
                },
                beforeProcessing,
                imageCreated() {},
                strict: false,
                useWebWorkers: true,
                decodeConfig: {
                    usePDFJS: false,
                },
            }
        )
    }

    beforeProcessing(xhr) {
        if (xhr.response.byteLength < 200) {
            let blob = new Blob([xhr.response]); 

            let reader = new FileReader();
            reader.readAsText(blob, 'utf-8');
            reader.onload = () => {
                if (reader.result[0] === '{') {
                    const json = JSON.parse(reader.result)
                    if (json.code === this.project.token_expired) {
                        MessageBox.alert(json.msg, '提示', {
                            confirmButtonText: '确定',
                            type: 'warning',
                            callback: () => {
                                store.commit('removeUserTokenInfo')
                                window.location.href = window.location.origin + window.location.pathname + '#/error';
                            }
                        });
                    }
                }
            }
        }


        return Promise.resolve(xhr.response);
    }
}