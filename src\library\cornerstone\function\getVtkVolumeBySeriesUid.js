import { throttle } from 'lodash-es'
import tryGetVtkVolumeForSeriesNumber from "$library/cornerstone/mpr/tryGetVtkVolumeForSeriesNumber.js";
import VoiMapping from "$library/cornerstone/function/VoiMapping.js";
import getConfigByStorageKey from '$library/utils/configStorage.js'

import vtkVolume from "vtk.js/Sources/Rendering/Core/Volume";
import vtkVolumeMapper from "vtk.js/Sources/Rendering/Core/VolumeMapper";
import vtkColorTransferFunction from "vtk.js/Sources/Rendering/Core/ColorTransferFunction";
import vtkPiecewiseFunction from "vtk.js/Sources/Common/DataModel/PiecewiseFunction";

export default async function getVtkVolumeBySeriesUid(
    layer1series,
    layer2series,
    defaultActor = false
) {
    if (!layer1series) {
        return false;
    }
    let layer1volume, layer2volume, imageId, volumes;

    // 2d 非 MIP 设置
    if (defaultActor) {
        if (layer1series && layer2series) {
            layer1volume = await getDefaultVolume(layer1series, 'color-pt-1'); // 默认第一位为'PT'
            layer2volume = await getDefaultVolume(layer2series, 'color-ct-1'); // 默认第二位为'CT'
            volumes = [layer1volume.vol, layer2volume.vol];
            imageId = layer1volume.imageId;

            return {
                volumes,
                imageId,
                modality: 'fuse',
                imageIdsGroup: [
                    layer1volume.imageIds,
                    layer2volume.imageIds
                ]
            };
        } else {
            // 单个序列
            layer1volume = await getDefaultVolume(layer1series);
            volumes = [layer1volume.vol];
            imageId = layer1volume.imageId;

            let metaSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId);
            const modality = metaSeriesModule.modality;

            return {
                volumes,
                imageId,
                modality,
                imageIdsGroup: [
                    layer1volume.imageIds
                ]
            };
        }
    }

    if (layer1series && layer2series) { // 传两个序列id时 返回融合图像
        layer1volume = await getSingleVolumeBySeriesUid(layer1series, "color-pt-1"); // 默认第一位为'PT'
        layer2volume = await getSingleVolumeBySeriesUid(layer2series, "color-ct-1"); // 默认第二位为'CT'
        volumes = [layer1volume.vol, layer2volume.vol];
        imageId = layer1volume.imageId;

        return {
            volumes,
            imageId,
            imageIdsGroup: [
                layer1volume.imageIds,
                layer2volume.imageIds
            ]
        };
    } else {
        // 单个序列
        layer1volume = await getSingleVolumeBySeriesUid(layer1series);
        volumes = [layer1volume.vol];
        imageId = layer1volume.imageId;
        return {
            volumes,
            imageId,
            imageIdsGroup: [
                layer1volume.imageIds
            ]
        };
    }
}

// vtk MPR 
async function getDefaultVolume(seriesUID, colorType) {
    if (!seriesUID) {
        return false;
    }
    const {
        vtkVolume: vtkVolumeObj,
        imageIds,
    } = await tryGetVtkVolumeForSeriesNumber(seriesUID);
    const imageData = vtkVolumeObj.vtkImageData;
    const range = imageData
        .getPointData()
        .getScalars()
        .getRange();
    const imageId = imageIds[0];
    await cornerstone.loadAndCacheImage(imageId);
    // let meta = cornerstone.metaData.get('imagePlaneModule', imageId)
    let metaSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId)
    const sModality = metaSeriesModule.modality

    let { windowWidth, windowCenter } = VoiMapping.getVoi(imageId);

    // 不知道窗宽窗位应该要什么？
    if (sModality == 'CT') {
        windowWidth = 360
        windowCenter = 1060
    }

    const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter);

    const vol = vtkVolume.newInstance();
    const mapper = vtkVolumeMapper.newInstance();
    const property = vol.getProperty();
    vol.setMapper(mapper);
    vol.setProperty(property);

    mapper.setInputData(imageData);
    mapper.setBlendModeToMaximumIntensity();
    mapper.setMaximumSamplesPerRay(8000);
    mapper.setSampleDistance(0.5);
    mapper.setAutoAdjustSampleDistances(false)

    const opacityFun = vtkPiecewiseFunction.newInstance();
    const colorFun = vtkColorTransferFunction.newInstance();

    colorFun.addRGBPoint(0, 0, 0, 0);
    colorFun.addRGBPoint(1, 1, 1, 1);

    if (colorType === "color-ct-1") {
        opacityFun.addPoint(0, 0);
        opacityFun.addPoint(range[1], 1);

        property.setGradientOpacityMinimumValue(0, 2.8);
        property.setGradientOpacityMinimumOpacity(0, 3.4);
        property.setGradientOpacityMaximumValue(0, 2.8);
        property.setGradientOpacityMaximumOpacity(0, 8.8);

    } else if (colorType === "color-pt-1") {

        colorFun.addRGBPoint(0, 0, 0, 0);
        colorFun.addRGBPoint(150 / 256, 0.85, 0, 0);
        colorFun.addRGBPoint(1, 0.85, 0.85, 0);

        volumeDisplayFn('pt-fuse', { range, mapper, opacityFun, colorFun, property, imageData })

    } else if (sModality === 'MR' || sModality === 'CT') { // 单个序列

        opacityFun.addPoint(0, 0);

        opacityFun.addPoint(range[1], 1);

        property.setGradientOpacityMinimumValue(0, 200);
        property.setGradientOpacityMinimumOpacity(0, 100);
        property.setGradientOpacityMaximumValue(0, 200);
        property.setGradientOpacityMaximumOpacity(0, 100);

    } else if (sModality === 'PT') { // 单个序列 
        volumeDisplayFn('pt-single-series', { range, mapper, opacityFun, colorFun, property, imageData })
        // volumeDisplayFn('pt-fuse', {range, mapper, opacityFun, colorFun, property, imageData}) 
    } else {
        // if (sModality === 'NM') // NM类型 跟 PT基本一致
        opacityFun.addPoint(0, 0);
        opacityFun.addPoint(20, 0.5);
        opacityFun.addPoint(200, 0.1);
        opacityFun.addPoint(3000, 0.7);
        // opacityFun.addPoint(12000, 0.99); // 肿瘤范围 大概5000 - 30000
        opacityFun.addPoint(32767, 1);

        property.setGradientOpacityMinimumValue(0, 0);
        property.setGradientOpacityMinimumOpacity(0, 0);
        property.setGradientOpacityMaximumValue(0, 1);
        property.setGradientOpacityMaximumOpacity(0, 5);  // 配合采样距离调整倍数 
    }

    colorFun.setRange(range[0], range[1]);

    colorFun.setMappingRange(lower, upper);

    property.setRGBTransferFunction(0, colorFun);
    property.setScalarOpacity(0, opacityFun);
    property.setInterpolationTypeToLinear();
    property.setUseGradientOpacity(0, true);

    return {
        vol,
        imageId,
        imageIds
    };
}

// vtk MIP
async function getSingleVolumeBySeriesUid(seriesUID, colorType) {

    // return await getDefaultVolume(seriesUID, colorType)

    if (!seriesUID) {
        return false;
    }
    const {
        vtkVolume: vtkVolumeObj,
        imageIds,
    } = await tryGetVtkVolumeForSeriesNumber(seriesUID);
    const imageData = vtkVolumeObj.vtkImageData;
    const range = imageData
        .getPointData()
        .getScalars()
        .getRange();
    const imageId = imageIds[0];
    await cornerstone.loadAndCacheImage(imageId);
    // let meta = cornerstone.metaData.get('imagePlaneModule', imageId)
    let metaSeriesModule = cornerstone.metaData.get('generalSeriesModule', imageId)
    const sModality = metaSeriesModule.modality

    const vol = vtkVolume.newInstance();
    const mapper = vtkVolumeMapper.newInstance();
    
    mapper.setInputData(imageData);
    vol.setMapper(mapper);

    const spacing = imageData.getSpacing()

    // Set the sample distance to half the mean length of one side. This is where the divide by 6 comes from.
    // https://github.com/Kitware/VTK/blob/6b559c65bb90614fb02eb6d1b9e3f0fca3fe4b0b/Rendering/VolumeOpenGL2/vtkSmartVolumeMapper.cxx#L344
    const sampleDistance = (spacing[0] + spacing[1] + spacing[2]) / 6 // 采样距离 取三个方向像素间隔的平均值的一半

    const sampleDistanceConfig = getConfigByStorageKey('configs-imageSampleDistance') // 1, 3, 5
    const SDparam = sampleDistanceConfig * 0.4 + 0.6 // 采样距离的调整系数

    // NM 图只设置映射范围，采样
    if (sModality === 'NM') {
        const rgbTransferFunction = vol
        .getProperty()
        .getRGBTransferFunction(0);
        rgbTransferFunction.setMappingRange(...range);

        mapper.setSampleDistance(sampleDistance * SDparam);

        return {
            vol,
            imageId,
            imageIds
        };
    }

    const { windowWidth, windowCenter } = VoiMapping.getVoi(imageId, seriesUID);
    const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter);

    const property = vol.getProperty();
    vol.setProperty(property);

    mapper.setBlendModeToMaximumIntensity();
    mapper.setMaximumSamplesPerRay(20000);
    mapper.setSampleDistance(sampleDistance * SDparam); // 1 , 1.8 , 2.6   * sampleDistance = 0.8 ~ 2.5

    // console.log(new Date(), sampleDistance)
    mapper.setAutoAdjustSampleDistances(false)
    mapper.setImageSampleDistance(sampleDistance * SDparam);

    const opacityFun = vtkPiecewiseFunction.newInstance();
    const colorFun = vtkColorTransferFunction.newInstance();

    // let preset = vtkColorMaps.getPresetByName('Grayscale');
    // colorFun.applyColorMap(preset);

    colorFun.addRGBPoint(0, 0, 0, 0);
    colorFun.addRGBPoint(1, 1, 1, 1);

    if (colorType === "color-ct-1") {

        // property.setScalarOpacityUnitDistance(0, 2);
        // opacityFun.addPoint(1200, 0); 
        // opacityFun.addPoint(4800, 1);
        
        if (sModality === 'MR') {
            
            opacityFun.addPoint(0, 0);    
            opacityFun.addPoint(400, 0.58);

            mapper.setSampleDistance(sampleDistance * SDparam * 1.5);
            
            property.setGradientOpacityMinimumValue(0, 0);
            property.setGradientOpacityMinimumOpacity(0, 0);
            property.setGradientOpacityMaximumValue(0, 1);
            property.setGradientOpacityMaximumOpacity(0, 3.1 * 1 / SDparam);

        } else {
            mapper.setSampleDistance(Math.max(sampleDistance * SDparam * 1.6, 1.5));
            
            opacityFun.addPoint(0, 0);
            opacityFun.addPoint(1300, 0); // 器械挡板 1250以下
    
            opacityFun.addPoint(range[1], 0.4); // 骨头范围 1220 - 2900  牙 >2900
    
            property.setGradientOpacityMinimumValue(0, 0);
            property.setGradientOpacityMinimumOpacity(0, 0);
            property.setGradientOpacityMaximumValue(0, 1);
            property.setGradientOpacityMaximumOpacity(0, 3.1 * 1 / SDparam);
        }

    } else if (colorType === "color-pt-1") {

        colorFun.addRGBPoint(0, 0, 0, 0);
        colorFun.addRGBPoint(150 / 256, 0.85, 0, 0);
        colorFun.addRGBPoint(1, 0.85, 0.85, 0);
        mapper.setSampleDistance(sampleDistance * SDparam * 3);

        if (sModality === 'NM') {

            opacityFun.addPoint(0, 0);   
            opacityFun.addPoint(32767, 1);


            property.setGradientOpacityMinimumValue(0, 0);
            property.setGradientOpacityMinimumOpacity(0, 0);
            property.setGradientOpacityMaximumValue(0, 1);
            // property.setGradientOpacityMaximumOpacity(0, 5);  // 配合采样距离调整倍数 
            property.setGradientOpacityMaximumOpacity(0, 750 * 1 / SDparam);
            
        } else {
            volumeDisplayFn('pt-fuse', { range, mapper, opacityFun, colorFun, property, imageData })
        }


    } else if (sModality === 'MR') { // 单个序列

        opacityFun.addPoint(0, 0);    
        opacityFun.addPoint(400, 0.58);

        property.setGradientOpacityMinimumValue(0, 0);
        property.setGradientOpacityMinimumOpacity(0, 0);
        property.setGradientOpacityMaximumValue(0, 1);
        property.setGradientOpacityMaximumOpacity(0, 3.1 * 1 / SDparam);

        mapper.setSampleDistance(Math.max(sampleDistance * SDparam * 1.6, 1.5)); // 1 , 1.8 , 2.6   * sampleDistance = 0.8 ~ 2.5


    } else if (sModality === 'CT') { // 单个序列

        opacityFun.addPoint(0, 0);
        opacityFun.addPoint(1300, 0);  // 器械挡板 1250以下

        opacityFun.addPoint(range[1], 0.4); // 骨头范围 1220 - 2900  牙 >2900

        property.setGradientOpacityMinimumValue(0, 0);
        property.setGradientOpacityMinimumOpacity(0, 0);
        property.setGradientOpacityMaximumValue(0, 1);
        property.setGradientOpacityMaximumOpacity(0, 3.61 * 1 / SDparam);

        mapper.setSampleDistance(Math.max(sampleDistance * SDparam * 1.6, 1.5));

    } else if (sModality === 'PT') { // 单个序列 
        volumeDisplayFn('pt-single-series', { range, mapper, opacityFun, colorFun, property, imageData })
        // volumeDisplayFn('pt-fuse', {range, mapper, opacityFun, colorFun, property, imageData}) 
        mapper.setSampleDistance(sampleDistance);

    } else if (sModality === 'NM') {

        const rangeWidth = range[1] - range[0]

        opacityFun.addPoint(0, 0);   
        opacityFun.addPoint(2.5 * rangeWidth, 1);


        property.setGradientOpacityMinimumValue(0, 0);
        property.setGradientOpacityMinimumOpacity(0, 0);
        property.setGradientOpacityMaximumValue(0, 1);
        property.setGradientOpacityMaximumOpacity(0, rangeWidth * 0.25 / SDparam); // 配合采样距离调整倍数 

        mapper.setSampleDistance(sampleDistance);
        
    } else {

        opacityFun.addPoint(0, 0);
        opacityFun.addPoint(20, 0.5);
        opacityFun.addPoint(200, 0.1);
        opacityFun.addPoint(3000, 0.7);
        // opacityFun.addPoint(12000, 0.99); // 肿瘤范围 大概5000 - 30000
        opacityFun.addPoint(32767, 1);

        property.setGradientOpacityMinimumValue(0, 0);
        property.setGradientOpacityMinimumOpacity(0, 0);
        property.setGradientOpacityMaximumValue(0, 1);
        property.setGradientOpacityMaximumOpacity(0, 5);  // 配合采样距离调整倍数 
    }

    colorFun.setRange(range[0], range[1]);

    colorFun.setMappingRange(lower, upper);

    property.setRGBTransferFunction(0, colorFun);
    property.setScalarOpacity(0, opacityFun);
    property.setInterpolationTypeToLinear();
    
    property.setUseGradientOpacity(0, true);

    return {
        vol,
        imageId,
        imageIds
    };
}


function volumeDisplayFn(type, {range, mapper, opacityFun, colorFun, property, imageData}) {
    switch (type) {
        case 'pt-fuse': 
        case 'pt-single-series':
            opacityFun.addPoint(range[0], 1);
            opacityFun.addPoint(range[1], 1);

            property.setGradientOpacityMinimumValue(0, 0);
            property.setGradientOpacityMinimumOpacity(0, 0);
            property.setGradientOpacityMaximumValue(0, 1);
            property.setGradientOpacityMaximumOpacity(0, 1); 
            break
        case 'pt-test':
            break
        default:
            break;
    }
}