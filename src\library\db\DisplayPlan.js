import { dbInit } from './dbInit';

const defaultPlanData = {
    name: '默认',
    planKey: 'displayPlan',
    pt: {
        w: '',
        l: '',
        colormap: '',
        invert: 0,
        mip: {
            w: '',
            l: '',
            colormap: '',
            invert: 0,
        }
    },
    ct: {
        w: '',
        l: '',
        colormap: '',
        invert: 0,
        mip: {
            w: '',
            l: '',
            colormap: '',
            invert: 0,
        }
    },
    fuse: {
        w: '',
        l: '',
        overlayW: '',
        overlayL: '',
        colormap: '',
        invert: 0,
        mip: {
            w: '',
            l: '',
            overlayW: '',
            overlayL: '',
            colormap: '',
            invert: 0,
        }
    },

}

async function newDisplayPlan() {
    return await dbInit({ storeName: 'displayPlan' })
}

const dbDisplayPlan = newDisplayPlan();
export { dbDisplayPlan , defaultPlanData }; 