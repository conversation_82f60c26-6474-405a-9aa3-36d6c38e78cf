<template>
    <div class="coordinate-tools" @dblclick="stopPropagate">
        <template v-if="!showInput">
            <div class="coordinate-tools-flex" ref="ulBox">
                <div>
                    <div class="coordinate-tools-item-box"></div>
                    <div
                        class="coordinate-tools-item-box"
                        :title="yName + '（' + nameMap[yName] + '+）方向切图调整'"
                        @click="onClickArrow('left')"
                    >
                        <div class="item-btn">
                            {{ yName }}+
                            <!-- <i class="el-icon-caret-left"></i> -->
                        </div>
                    </div>
                    <div class="coordinate-tools-item-box" title="切换输入方式" @click="toggleShowInput">
                        <div class="item-btn">
                            <i class="iconfont icontop-bottom"></i>
                        </div>
                    </div>
                </div>
                <div>
                    <div
                        class="coordinate-tools-item-box"
                        :title="xName + '（' + nameMap[xName] + '+）方向切图调整'"
                        @click="onClickArrow('up')"
                    >
                        <div class="item-btn">
                            {{ xName }}+
                            <!-- <i class="el-icon-caret-top"></i> -->
                        </div>
                    </div>
                    <div
                        class="coordinate-tools-item-box"
                        title="重置切面调整"
                        @click="onClickArrow('reset')"
                    >
                        <div class="item-btn">
                            <i class="reset-icon"></i>
                        </div>
                    </div>
                    <div
                        class="coordinate-tools-item-box"
                        :title="yName + '（' + nameMap[xName] + '-）方向切图调整'"
                        @click="onClickArrow('down')"
                    >
                        <div class="item-btn">
                            {{ xName }}-
                        </div>
                    </div>
                </div>
                <div>
                    <div class="coordinate-tools-item-box"></div>
                    <div
                        class="coordinate-tools-item-box"
                        :title="yName + '（' + nameMap[yName] + '-）方向切图调整'"
                        @click="onClickArrow('right')"
                    >
                        <div class="item-btn">
                            {{ yName }}-
                            <!-- <i class="el-icon-caret-right"></i> -->
                        </div>
                    </div>
                    <div class="coordinate-tools-item-box"></div>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="coordinate-tools-container">
                <div class="line">
                    <span class="label">{{ xInputName }}</span>
                    <span class="input">
                        <el-input-number
                            v-model.number="xValue"
                            @keyup.enter.native="onEnterOffset"
                            class="coordinate-tools-input"
                            controls-position="right"
                            size="mini"
                        ></el-input-number>
                    </span>
                </div>
                <div class="line">
                    <span class="label">{{ yInputName }}</span>
                    <span class="input">
                        <el-input-number
                            v-model.number="yValue"
                            @keyup.enter.native="onEnterOffset"
                            class="coordinate-tools-input"
                            controls-position="right"
                            size="mini"
                        ></el-input-number>
                    </span>
                </div>
                <div class="line">
                    <div class="coordinate-tools-item-box">
                        <div class="item-btn" @click="toggleShowInput">
                            <i class="iconfont icontop-bottom"></i>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
import { debounce } from 'lodash-es'

export default {
    props: {
        offsetX: {
            type: Number,
            default: 0
        },
        offsetY: {
            type: Number,
            default: 0
        },
        disabled: {
            type: Boolean
        },
        orientation: {
            type: String,
            default: 'z'
        }
    },
    data() {
        return {
            showInput: false,
            xValue: 0,
            yValue: 0,
            nameMap: { '冠': 'Y', '矢': 'X', '横': 'Z' },
            xName: '',
            yName: '',
            xInputName: '',
            yInputName: ''
        }
    },
    computed: {
    },
    watch: {
        offsetX: {
            handler(val) {
                this.xValue = val
            },
            immediate: true
        },
        offsetY: {
            handler(val) {
                this.yValue = val
            },
            immediate: true
        },
        orientation: {
            handler(val) {
                if (val.toUpperCase() === 'Z') {
                    this.xName = '冠'
                    this.yName = '矢'
                    this.xInputName = '矢'
                    this.yInputName = '冠'
                }else if (val.toUpperCase() === 'X') {
                    this.xName = '横'
                    this.yName = '冠'

                    this.xInputName = '冠'
                    this.yInputName = '横'
                }else {
                    this.xName = '横'
                    this.yName = '矢'

                    this.xInputName = '矢'
                    this.yInputName = '横'
                }
            },
            immediate: true
        }
    },
    methods: {
        // 输入框 回车
        onEnterOffset() {
            if(this.disabled) return
            this.$emit('onOffsetChange', { offsetX: this.xValue, offsetY: this.yValue})
        },
        // 阻止事件冒泡
        stopPropagate(e) {
            e.stopPropagation()
        },
        onClickArrow: debounce(function (type = 'up') {
            if(this.disabled) return

            let _offsetX = this.offsetX
            let _offsetY = this.offsetY
            switch (type) {
                case 'up':
                    _offsetY += 1
                    break
                case 'down':
                    _offsetY -= 1

                    break
                case 'left':
                    _offsetX += 1

                    break
                case 'right':
                    _offsetX -= 1

                    break
                case 'reset':
                    _offsetX = 0
                    _offsetY = 0
                    this.$emit('resetOffset')
                    return


            }
            // this.$emit('update:offsetX',  _offsetX )
            // this.$emit('update:offsetY',  _offsetY )

            this.$emit('onOffsetChange', { offsetX: _offsetX, offsetY: _offsetY })
        }, 100),
        toggleShowInput() {
            this.showInput = !this.showInput
        }
    },
    mounted() {
        this.xValue = this.offsetX
        this.yValue = this.offsetY

    }
}
</script>
<style lang="scss" scoped>
.coordinate-tools {
    position: absolute;
    width: 84px;
    height: 84px;
    background: #efefef;
    bottom: 2px;
    left: 2px;
    z-index: 4;
    transition: none;

    .coordinate-tools-container {
        position: relative;
        width: 100%;
        .el-input-number--mini {
            width: 66px;
            text-align: initial;
        }
        .line {
            .label {
                display: inline-block;
                width: 14px;
                color: #0a0a06;
            }
            .input {
                display: inline-block;
                width: 66px;
            }
        }
    }
    .coordinate-tools-flex {
        position: absolute;
        display: flex;
        justify-content: space-between;
        min-width: 100%;
    }
    .coordinate-tools-item-box {
        width: 28px;
        height: 28px;
        .item-btn {
            width: 28px;
            height: 28px;
            line-height: 26px;
            background: #f5f9fc;
            border: 1px solid #d0d2d1;
            color: #142b4b;
            border-radius: 4px;
            cursor: pointer;
            .reset-icon {
                position: relative;
                width: 15px;
                height: 15px;
                font-size: 26px;
                margin: 6px auto;
                background: #111;
                border-radius: 50%;
                &:active {
                    filter: invert(50%);
                }
            }
            &:hover {
                background-color: #d1dbe7;
            }
            &.coordinate-tools--type1 {
                width: 40px;
            }
            > i {
                display: inline-block;
                width: 100%;
                height: 100%;
                font-size: 26px;
                &:active {
                    filter: invert(50%);
                }
            }
        }
    }
    .coordinate-tools-input {
        height: 28px;
        width: 42px;
        ::v-deep input {
            position: relative;
            top: 1px;
            width: 38px;
            height: 26px;
            border-color: #d0d2d1;
            font-size: 14px;
            text-align: center;
            border-radius: 4px !important;
        }
    }
}
</style>