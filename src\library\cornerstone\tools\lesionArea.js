import getConfigByStorageKey from '$library/utils/configStorage.js';

import csTools from '$library/cornerstone/cornerstoneTools'
import eventBus from '$src/event.js'

const getHandleNearImagePoint = csTools.importInternal('manipulators/getHandleNearImagePoint');

const logger = console;

const { addToolState, clearToolState, getToolState, toolStyle, toolColors, getModule, store } = csTools;
const external = csTools.external;

const BaseAnnotationTool = csTools.importInternal('base/BaseAnnotationTool');

// Drawing
const getNewContext = csTools.importInternal('drawing/getNewContext');
const draw = csTools.importInternal('drawing/draw');
const drawJoinedLines = csTools.importInternal('drawing/drawJoinedLines');
const drawHandles = csTools.importInternal('drawing/drawHandles');
const drawLinkedTextBox = csTools.importInternal('drawing/drawLinkedTextBox');
const drawEllipse = csTools.importInternal('drawing/drawEllipse');
const fillBox = csTools.importInternal('drawing/fillBox');

// Util
const { clipToBox } = csTools.importInternal('util/clipToBox');
const freehandUtils = csTools.importInternal('util/freehandUtils');
const calculateSUV = csTools.importInternal('util/calculateSUV');
const { calculateEllipseStatistics, pointInEllipse } = csTools.importInternal('util/ellipseUtils');

const moveHandleNearImagePoint = csTools.importInternal('manipulators/moveHandleNearImagePoint');

const getROITextBoxCoords = csTools.importInternal('util/getROITextBoxCoords');
const numbersWithCommas = csTools.importInternal('util/numbersWithCommas');
const pointInsideBoundingBox = csTools.importInternal('util/pointInsideBoundingBox');
const throttle = csTools.importInternal('util/throttle');
const getPixelSpacing = csTools.importInternal('util/getPixelSpacing');
const triggerEvent = csTools.importInternal('util/triggerEvent');
const { state } = store;
const { freehandRoiCursor } = csTools.importInternal('tools/cursors');

const EVENTS = csTools.EVENTS


const {
  insertOrDelete,
  freehandArea,
  calculateFreehandStatistics,
  freehandIntersect,
  FreehandHandleData,
} = freehandUtils;

/**
 * @public
 * @class LesionAreaTool
 * @memberof Tools.Annotation
 * @classdesc Tool for drawing arbitrary polygonal regions of interest, and
 * measuring the statistics of the enclosed pixels.
 * @extends Tools.Base.BaseAnnotationTool
 */
export default class LesionAreaTool extends BaseAnnotationTool {
  constructor(props = {}) {
    const defaultProps = {
      name: 'LesionArea',
      supportedInteractionTypes: ['Mouse', 'Touch'],
      svgCursor: freehandRoiCursor,
      configuration: {
        drawHandles: true,
        renderDashed: false,
        hideHandlesIfMoving: true,
      },
      // configuration: {
      //   drawHandles: true,
      //   hideHandlesIfMoving: false,
      //   renderDashed: false,
      // },
    };

    super(props, defaultProps);
    this.circleRoiDiameter =
      state.circleRoiDiameter || this.configuration.circleRoiDiameter;
    this.throttledUpdateCachedStats = throttle(this.updateCachedStats, 110);
    this.localConfigData = getConfigByStorageKey('configs-circleMeasure');

  }

  createNewMeasurement(eventData) {
    const goodEventData =
      eventData && eventData.currentPoints && eventData.currentPoints.image;

    if (!goodEventData) {
      logger.error(
        `required eventData not supplied to tool ${this.name}'s createNewMeasurement`
      );

      return;
    }

    const measurementData = {
      visible: true,
      active: true,
      select: false,
      invalidated: true,
      color: undefined,
      handles: {
        points: [],
      },
    };

    measurementData.handles.textBox = {
      active: false,
      hasMoved: false,
      movesIndependently: true,
      drawnIndependently: true,
      allowedOutsideImage: true,
      hasBoundingBox: true,
    };

    return measurementData;
  }

  pointNearTool(element, data, coords, interactionType) {
    const hasStartAndEndHandles =
      data && data.handles && data.handles.start && data.handles.end;

    const getDistance = external.cornerstoneMath.point.distance;

    if (!hasStartAndEndHandles) {
      logger.warn(
        `invalid parameters supplied to tool ${this.name}'s pointNearTool`
      );
    }

    if (!hasStartAndEndHandles || data.visible === false) {
      this.customMouseUpTrigger = false

      return false;
    }

    const handleNearImagePoint = getHandleNearImagePoint(
      element,
      data.handles,
      coords,
      4
    );

    if (handleNearImagePoint) {
      this.customMouseUpTrigger = true

      return true;
    }

    const distance = interactionType === 'mouse' ? 15 : 26;
    const startCanvas = external.cornerstone.pixelToCanvas(
      element,
      data.handles.start
    );
    const endCanvas = external.cornerstone.pixelToCanvas(
      element,
      data.handles.end
    );


    const minorEllipse = {
      left: Math.min(startCanvas.x, endCanvas.x) + distance / 2,
      top: Math.min(startCanvas.y, endCanvas.y) + distance / 2,
      width: Math.abs(startCanvas.x - endCanvas.x) - distance,
      height: Math.abs(startCanvas.y - endCanvas.y) - distance,
    };

    const majorEllipse = {
      left: Math.min(startCanvas.x, endCanvas.x) - distance / 2,
      top: Math.min(startCanvas.y, endCanvas.y) - distance / 2,
      width: Math.abs(startCanvas.x - endCanvas.x) + distance,
      height: Math.abs(startCanvas.y - endCanvas.y) + distance,
    };

    const pointInMinorEllipse = pointInEllipse(minorEllipse, coords);
    const pointInMajorEllipse = pointInEllipse(majorEllipse, coords);

    if (pointInMajorEllipse && !pointInMinorEllipse) {
      this.customMouseUpTrigger = true

      return true;
    }

    // const rect = {
    //   left: Math.min(startCanvas.x, endCanvas.x),
    //   top: Math.min(startCanvas.y, endCanvas.y),
    //   width: Math.abs(startCanvas.x - endCanvas.x),
    //   height: Math.abs(startCanvas.y - endCanvas.y),
    // };

    // const distanceToPoint = external.cornerstoneMath.rect.distanceToPoint(
    //   rect,
    //   coords
    // );

    // return distanceToPoint < distance;

    this.customMouseUpTrigger = false



  }

  updateCachedStats(image, element, data) {

    data.invalidated = false;
  }

  renderToolData(evt) {
    const eventData = evt.detail;

    // If we have no toolState for this element, return immediately as there is nothing to do
    const toolState = getToolState(evt.currentTarget, this.name);

    if (!toolState) {
      return;
    }

    const getDistance = external.cornerstoneMath.point.distance;
    const { image, element, canvasContext } = eventData;

    const newContext = getNewContext(canvasContext.canvas);
    const { rowPixelSpacing, colPixelSpacing } = getPixelSpacing(image);

    // Meta
    const seriesModule =
      external.cornerstone.metaData.get('generalSeriesModule', image.imageId) ||
      {};

    // Pixel Spacing
    const hasPixelSpacing = rowPixelSpacing && colPixelSpacing;


    const config = this.configuration;
    const modality = seriesModule ? seriesModule.modality : null;


    // We have tool data for this element - iterate over each one and draw it
    const context = getNewContext(eventData.canvasContext.canvas);
    const lineWidth = toolStyle.getToolWidth();
    const { handleRadius,
      drawHandlesOnHover,
      hideHandlesIfMoving,
      renderDashed } = config;
    const lineDash = getModule('globalConfiguration').configuration.lineDash;

    draw(newContext, context => {
      // If we have tool data for this element, iterate over each set and draw it
      for (let i = 0; i < toolState.data.length; i++) {
        const data = toolState.data[i];

        if (!data || data.visible === false) {
          continue;
        }

        let moSuffix = '';
        if (['CT', 'MR'].includes(modality)) {
          moSuffix = 'HU';
        }
        data.unit = moSuffix;
        // Configure
        const color = toolColors.getColorIfActive(data);
        const handleOptions = {
          color,
          handleRadius,
          drawHandlesIfActive: drawHandlesOnHover,
          hideHandlesIfMoving,
          isActive: data.active,
          select: data.select,
        };


        if (data.allTracePoints.length) { 
            const points = data.allTracePoints
            const options = { color, lineWidth: 2, lineDash: [4, 2] };
            drawJoinedLines(context, element, points[0], points, options,
              'pixel');

            drawJoinedLines(
              context,
              element,
              points[points.length - 1],
              [points[0]],
              options,
              'pixel'
            ); 

        }
        // console.log(modality)
        if (!['CT', 'MR'].includes(modality)) {

          let startCanvas = external.cornerstone.pixelToCanvas(
            element,
            data.handles.start
          );

          const endCanvas = external.cornerstone.pixelToCanvas(
            element,
            data.handles.end
          );

          const drwcOptions = { color, lineWidth: 2.5 };


          // Draw Circle
          drawEllipse(
            context,
            element,
            startCanvas,
            endCanvas,
            drwcOptions,
            'canvas',
            0.0
          );

          drawHandles(context, eventData, data.handles, handleOptions);

        }



        // Update textbox stats
        if (data.invalidated === true) {
          if (data.cachedStats) {
            this.throttledUpdateCachedStats(image, element, data);
          } else {
            this.updateCachedStats(image, element, data);
          }
        }

        if (!data.handles.textBox.hasMoved) {
          var defaultCoords = getROITextBoxCoords(eventData.viewport, data.handles, element);
          Object.assign(data.handles.textBox, defaultCoords);
        }

        // if (!data.handles.textBox.hasMoved) {
        //   data.handles.textBox.x =
        //     data.polyBoundingBox.left + data.polyBoundingBox.width + 6;
        //   data.handles.textBox.y =
        //     data.polyBoundingBox.top + data.polyBoundingBox.height / 2;
        // }

        const text = textBoxText.call(this, data, element);
        text.push(`MTV=${data.volume} cm${String.fromCharCode(179)}`) //  cm³
        if (data.TLG) {
          text.push(`TLG=${data.TLG} cm${String.fromCharCode(179)}`)
        }

        drawLinkedTextBox(
          context,
          element,
          data.handles.textBox,
          text,
          [data.lesionPoints[0]],
          () => [data.lesionPoints[0]],
          color,
          lineWidth,
          0,
          true,
          data.select
        );





      }
    });
    if (!this.isInitedMouseUpCallback) {

      this.initMouseUpCallback(evt)
      this.isInitedMouseUpCallback = 1
    }

    function textBoxText(data, element) {
      let { totalSUVmax = 0, totalSUVmin = 0, totalSUVavg = 0,
        totalHUmax = 0, totalHUmin = 0, totalHUavg = 0, totalSUVpeak = 0,
        area, theDiameter, maxZdiff, maxLengthX, maxLengthY } = data;
      const textLines = [];
      const layoutId = element.getAttribute('layoutId')

      const suvUnit = csTools.getSUVUnit();

      const pushSUVtext = () => {
        if (totalSUVmax !== 0) textLines.push(`${suvUnit} Max=${numbersWithCommas(+totalSUVmax.toFixed(2))}`);
        if (totalSUVavg !== 0) textLines.push(`${suvUnit} Avg=${numbersWithCommas(+totalSUVavg.toFixed(2))}`);
        if (totalSUVmin !== 0) textLines.push(`${suvUnit} Min=${numbersWithCommas(+totalSUVmin.toFixed(2))}`);
        if (totalSUVpeak !== 0) textLines.push(`${suvUnit} Peak=${numbersWithCommas(+totalSUVpeak.toFixed(2))}`);
      }

      const pushHUtext = () => {
        if (totalHUmax !== 0) textLines.push(`HU Max=${numbersWithCommas(+totalHUmax.toFixed(2))}`);
        if (totalHUavg !== 0) textLines.push(`HU Avg=${numbersWithCommas(+totalHUavg.toFixed(2))}`);
        if (totalHUmin !== 0) textLines.push(`HU Min=${numbersWithCommas(+totalHUmin.toFixed(2))}`);
      }

      // 只对融合图像处理
      if (layoutId == 8 || layoutId == 9 || layoutId == 10) {
        pushSUVtext()
        if (this.localConfigData.isShowHU) {
          pushHUtext()
        }
      } else {
        if (['CT', 'MR'].includes(modality)) {
          pushHUtext()
        } else {
          pushSUVtext()
        }
      }

      if (area) {
        let suffix = ` cm${String.fromCharCode(178)}`;

        // if (!image.rowPixelSpacing || !image.columnPixelSpacing) {
        //   suffix = ` pixels${String.fromCharCode(178)}`;
        // }

        // area 面积单位cm2
        // const areaText = `Area=${numbersWithCommas(area.toFixed(2))}${suffix}`;

        // textLines.push(areaText);
      }

      textLines.push(`Diameter=${(theDiameter.toFixed(2))} cm`);
      // textLines.push(`Diameter=${numbersWithCommas(maxZdiff.toFixed(2))}`);
      textLines.push(`Size=${((maxLengthX).toFixed(2))}*${((maxLengthY).toFixed(2))}*${(maxZdiff.toFixed(2))} cm`);
      return textLines;
    }

  }
  initMouseUpCallback(evt) {
    const eventData = evt.detail;

    // If we have no toolState for this element, return immediately as there is nothing to do
    const toolState = getToolState(evt.currentTarget, this.name);

    if (!toolState) {
      return;
    }



    const { image, element, canvasContext } = eventData;
    element.addEventListener('mouseup', this.customMouseUpCB.bind(this))
    element.addEventListener('mousedown', this.customMouseDownCB.bind(this))
    this.customMouseUpTrigger = true

  }
  customMouseUpCB(evt) {
    if (this.customMouseDownClientX == evt.clientX
    &&  this.customMouseDownClientY == evt.clientY) return
    const eventData = evt.detail;

    const toolState = getToolState(evt.currentTarget, this.name);

    if (!toolState) {
      return;
    }
    let selectedData = null
    for (let i = 0; i < toolState.data.length; i++) {
      const data = toolState.data[i];
      if (data.select && !data.handles.textBox.active) {
        selectedData = { ...data }
      }
    }
    if (!selectedData) return
    if (!this.customMouseUpTrigger) return



    selectedData.startImgIndex = null
    // console.log(eventData)
    const showedSUVthresVal = selectedData.showedSUVthresType > 1 ? selectedData.showedSUVthresVal :
      `${selectedData.showedSUVthresVal}%`
    eventBus.$emit('changeLesionArea', selectedData, showedSUVthresVal)
    this.customMouseUpTrigger = false
  }
  customMouseDownCB(evt) {
    this.customMouseDownClientX = evt.clientX
    this.customMouseDownClientY = evt.clientY
  }
}




