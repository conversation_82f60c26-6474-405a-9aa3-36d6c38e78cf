import dicomParser from 'dicom-parser';
import cornerstone from '$library/cornerstone/cornerstone';
import cornerstoneWADOImageLoader from "$library/cornerstone/cornerstoneWADOImageLoader";
import cornerstoneWebImageLoader from "$library/cornerstone/cornerstoneWebImageLoader.min.js";
// import * as cornerstoneWADOImageLoader from "../../../public/js/cornerstoneWADOImageLoader.js";
// import * as dicomParser from "../../../public/js/dicomParser.js";
import cornerstoneMath from 'cornerstone-math';
import cornerstoneTools from '$library/cornerstone/cornerstoneTools';
import Hammer from 'hammerjs';

// cornerstone 7004 (enabledElement.options.colormap) 没有 options 属性，报错，改为(enabledElement.options && enabledElement.options.colormap)
// cornerstoneWADOImageLoader 多帧没有图像iop，ipp 6758
// vtk 修改 preserveDrawingBuffer = true 不改 html2canvas 无法截图
// vtk 17.10.1 ~ 18.0.1 。可以用 17.10.1，用 18.0.1 图像不显示，不报错
// MPR加载器
import mprImageLoader from '$library/cornerstone/mprImageLoader.js'
import mprMetaDataProvider from '$library/cornerstone/mpr/mprMetadata/mprMetaDataProvider.js';
import getPatientWeightAndCorrectedDose from '$library/vtk/lib/data/getPatientWeightAndCorrectedDose.js';

// 自定义工具
// import mprCrosshairs from './tools/mprCrosshairs.js'
import nuclearCrosshairs from './tools/nuclearCrosshairs.js'
import ellipticalMeasure from './tools/ellipticalMeasure.js'
import lesionArea from './tools/lesionArea.js'
import airtools from './tools/airtools.js'
import freehandRoi from './tools/freehandRoi.js'
import eraserTool from './tools/eraserTool.js'
import rectangleCrop from './tools/rectangleCrop.js'
import textMarkerTool from './tools/textMarkerTool.js'
import freehandLength from './tools/freehandLength.js'
import wwwcTool from './tools/wwwcTool.js'
import zoomTool from './tools/zoomTool.js'


import { imageIdStateManager } from '$library/cornerstone/measurementManager'
import getPeakValue from '$library/cornerstone/function/getPeakValue'

import ImageFilter from '$library/utils/ImageFilter.js'
// 获取旋转偏移key
function getOffsetRotationKey(imageId) {
	if (imageId.includes('mpr')) {
		// 重建图
		const [scheme, seriesNumber, imageOrientationPatient, , , fuseSeriesNumber ] = imageId.split(':');
		return `${scheme}+${seriesNumber}+${imageOrientationPatient}+${fuseSeriesNumber}`; 
	}else {
		// 非重建
		const instanceStr = imageId.split('=')[1]
		return `dicomweb+${instanceStr.slice(0, instanceStr.lastIndexOf('.'))}`
	}
}

export default function initCornerstone() {
	// 工具扩展
	cornerstoneTools.external.cornerstone = cornerstone;
	cornerstoneTools.external.Hammer = Hammer;
	cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
	cornerstoneTools.external.getPatientWeightAndCorrectedDose = getPatientWeightAndCorrectedDose;

	// 内置未开放工具   // tools 内置的方法导入
	cornerstoneTools.scrollToIndex = cornerstoneTools.import('util/scrollToIndex')
	cornerstoneTools.triggerEvent = cornerstoneTools.import('util/triggerEvent')
	// 注册工具

	// cornerstoneTools.register('module', 'mprCrosshairs', mprCrosshairs);
	cornerstoneTools.register('module', 'nuclearCrosshairs', nuclearCrosshairs);
	cornerstoneTools.register('module', 'airtools', airtools);
	cornerstoneTools.register('module', 'ellipticalMeasure', ellipticalMeasure);
	cornerstoneTools.register('module', 'lesionArea', lesionArea);
	cornerstoneTools.register('module', 'freehandRoi', freehandRoi);
	cornerstoneTools.register('module', 'eraserTool', eraserTool);
	cornerstoneTools.register('module', 'rectangleCrop', rectangleCrop);
	cornerstoneTools.register('module', 'textMarkerTool', textMarkerTool);
	cornerstoneTools.register('module', 'freehandLength', freehandLength);
	cornerstoneTools.register('module', 'wwwcTool', wwwcTool);
	cornerstoneTools.register('module', 'zoomTool', zoomTool);
	


	// cornerstoneTools.MprCrosshairsTool = cornerstoneTools.getModule('mprCrosshairs')
	cornerstoneTools.NuclearCrosshairsTool = cornerstoneTools.getModule('nuclearCrosshairs')
	cornerstoneTools.AirtoolsTool = cornerstoneTools.getModule('airtools')
	cornerstoneTools.EllipticalMeasureTool = cornerstoneTools.getModule('ellipticalMeasure')
	cornerstoneTools.LesionAreaTool = cornerstoneTools.getModule('lesionArea')
	cornerstoneTools.FreehandRoiTool = cornerstoneTools.getModule('freehandRoi')
	cornerstoneTools.EraserTool = cornerstoneTools.getModule('eraserTool')
	cornerstoneTools.RectangleCropTool = cornerstoneTools.getModule('rectangleCrop')
	cornerstoneTools.TextMarkerTool = cornerstoneTools.getModule('textMarkerTool')
	cornerstoneTools.FreehandLengthTool = cornerstoneTools.getModule('freehandLength')
	cornerstoneTools.WwwcTool = cornerstoneTools.getModule('wwwcTool')
	cornerstoneTools.ZoomTool = cornerstoneTools.getModule('zoomTool')

	// 获取测量单位
	cornerstoneTools.getSUVUnit = () => {
		if (cornerstoneTools.store.state.reviseSUV === 2) {
		  return 'SUV-lbm';
		} else if (cornerstoneTools.store.state.reviseSUV === 3) {
		  return 'SUV-bsa';
		}
		return 'SUV';
	}
	
	cornerstoneTools.imageIdStateManager = imageIdStateManager
	cornerstoneTools.getPeakValue        = getPeakValue // 获取峰值

    cornerstone.recumbentPosition = {}       // 卧体位
	cornerstone.offsetRotationMap = {}       // 初始化存储旋转微调值的地方
	cornerstone.translationMap = {}
	cornerstone.getOffsetRotationKey = getOffsetRotationKey  // 获取旋转微调id值

	// 工具
	cornerstoneTools.init({ // 初始工具
		showSVGCursors: false, // 开启工具鼠标光标风格
		globalToolSyncEnabled: true,
	});


	cornerstoneWebImageLoader.external.cornerstone = cornerstone;

	// 图像加载，加载配置
	cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
	cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
	cornerstoneWADOImageLoader.webWorkerManager.initialize({
		maxWebWorkers: Math.min(navigator.hardwareConcurrency || 1, 12),
		startWebWorkersOnDemand: true, // true 需要时创建 Web, false = 初始化时全部创建。默认 false
		taskConfiguration: {
			decodeTask: {
				initializeCodecsOnStartup: false, // 初始化 JPEG2000 或 JPEG-LS 解码器
				// usePDFJS: false,
				strict: false,
			},
		},
	});
	cornerstone.imageCache.setMaximumSizeBytes(1024 * 1024 * 1024 * 1.25);  // 最大缓存 超出时会自动回收一部分

	// 全局化
	window.cornerstone = cornerstone;
	window.cornerstoneTools = cornerstoneTools;
	window.dicomParser = dicomParser;
	window.cornerstoneWADOImageLoader = cornerstoneWADOImageLoader
	window.ImageFilter = ImageFilter

	// 引入 MPR 切片加载器
	cornerstone.registerImageLoader('mpr', mprImageLoader);
	cornerstone.metaData.addProvider(mprMetaDataProvider);

	cornerstone.events.addEventListener(
		cornerstone.EVENTS.IMAGE_LOADED,
		function(e) {
			const imageId = e.detail.image.imageId
            cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.unload(imageId)
            cornerstoneWADOImageLoader.wadouri.fileManager.purge()
		}
	);
}