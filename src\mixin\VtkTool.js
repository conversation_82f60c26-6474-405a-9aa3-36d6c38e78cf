import VoiMapping from '$library/cornerstone/function/VoiMapping.js';
import { invertVolume } from "$library/vtk";

export default {
    methods: {
        toggleToReset(status = 'all', claerIdx) {
            switch (status) {
                case 'rotation':
                    this.vtkResetRotation(claerIdx)
                    break;
                case 'size':
                    this.vtkResetSize(claerIdx)
                    break;
                case 'wwwc':
                    this.vtkResetWwwc(claerIdx)
                    break;
                default:
                    // 全部
                    this.vtkResetRotation(claerIdx)
                    this.vtkResetSize(claerIdx)
                    this.vtkResetWwwc(claerIdx)
                    // const api = this.apis[this.activeViewportIndex];
                    // api.volumes[0].getProperty().getRGBTransferFunction(0)
                    // window.api = api
                    break;
            }
        },
        getVtkSelect(claerIdx) {
            if (this.$options.name === 'ViewMRLayout') {
                // ViewMRLayout 组件
                const itemComponent = this.$refs.mprItem.find(item => {
                    return item.activeSelect 
                })
    
                if (itemComponent) {
                    const vtkViewport = itemComponent.$refs.vtkViewport
                    if (vtkViewport) {
                        return vtkViewport.api
                    }
                }
            }else if (this.$options.name === 'ViewSlice' || this.$options.name === 'ViewContrast') {
                // ViewSlice 组件
                return this.apis[claerIdx != undefined ? claerIdx : this.activeViewportIndex]
            }

            return false
        },
        // 还原旋转方向
		vtkResetRotation(claerIdx){
			const api = this.getVtkSelect(claerIdx);
            if (!api) return;
            const renderWindow = api.genericRenderWindow.getRenderWindow();
            api.resetOrientation()
            renderWindow.render();
		},
        // 还原缩放大小
		vtkResetSize(claerIdx){
            const api = this.getVtkSelect(claerIdx);
            if (!api) return;
            this.$fun.fill2DView(api.genericRenderWindow, api.getSliceNormal(), api.newZPosition);
            if (api.svgWidgets.crosshairsWidget) {
                api.svgWidgets.crosshairsWidget.updateCrosshairForApi(api);
            }
		},
        // 重置窗宽窗位
        vtkResetWwwc(claerIdx){
            const api = this.getVtkSelect(claerIdx);
            if (!api) return;
            const rgbTransferFunction = api.volumes[0].getProperty().getRGBTransferFunction(0)
            const range = api.volumes[0].getMapper().getInputData().getPointData().getScalars().getRange()

            rgbTransferFunction.setRange(range[0], range[1]);

            cornerstone.loadAndCacheImage(api.imageId).then(() => {
                const renderWindow = api.genericRenderWindow.getRenderWindow();
                const { windowWidth, windowCenter } = VoiMapping.getVoi(api.imageId);
                const { lower, upper } = VoiMapping.toLowHighRange(windowWidth, windowCenter)
                rgbTransferFunction.setMappingRange(lower, upper);

                renderWindow.render();
            });
        },
        // vtk 反片
		toggleToInvert() {

            const api = this.getVtkSelect();
            if (!api) return;

            api.changeInvert(!api.getInvert())
		},
        // 播放
        vtkPlayClip(status, speed, callBack) {

            // 获取 vtk 的 api
            const api = this.getVtkSelect();
            if (!api) return;

            const rotate = 8;
            const ms = 1000 / speed;
            if(!api || !Object.keys(api).length){
                return;
            }
            const renderWindow = api.genericRenderWindow.getRenderWindow();
            const viewport     = renderWindow.getInteractor().getInteractorStyle().getViewport();
            if (status === 'prev'){
                // 上
                viewport.rotateRelative(0, rotate);
                renderWindow.render();
            }else if (status === 'next'){
                // 下
                viewport.rotateRelative(0, -rotate);
                renderWindow.render();
            }else if (status === 2){
                // 需要播放
                clearInterval(api.intervalId) // 清除播放
                api.intervalId = setInterval(() => {
                    viewport.rotateRelative(0, -rotate);
                    renderWindow.render();
                }, ms);
            }else if (status === 1){
                // 需要暂停
                clearInterval(api.intervalId)
                api.intervalId = null;
            }else {
                // 执行改变播放图标状态
                callBack && callBack(api.intervalId ? 2 : 1)
            }

        },
    }
}