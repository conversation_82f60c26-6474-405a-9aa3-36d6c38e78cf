<template>
    <div class="inner-pc-plan pc-body" ref="pcBody">
        <section class="c-body">
            <AreaFit :class="styleFont" @resizeArea="setFontByWidthAndHeight" :isMuch="layoutFill">
                <div ref="center" class="c-content" :style="gridLayoutStyle.containerStyle">
                    <template v-for="(item, index) in viewports">
                        <CornerstoneViewport
                            :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style"
                            v-if="!item.vtk" innnerClass="crosshairs-parent" ref="csViewport"
                            :key="item.uid"
                            :contrast="true"
                            :state="viewportState(item)"
                            :imageIds="item.imageIds"
                            :layerIds="item.layerIds"
                            :class="{'i-active': activeViewportIndex === index, 'xx-full-screen': fullScreenIndex === index}"
                            :activeTool.sync="activeTool"
                            :crosshairsTool.sync="crosshairsTool.mipShow"
                            :isOverlayVisible.sync="isOverlayVisible"
                            toolType="slice"
                            :activeSelect="activeViewportIndex === index"
                            :showAction="showAction === index"
                            :showCoordinateTools="showCoordinateTools && coordinateToolsIndex === index"
                            :showRotationTools="showRotationTools && rotationToolsIndex === index"
                            :isviewmprcornerstone="true"
                            @onDblclickViewport="onDblclickViewport($event, index)"
                            @onToolRenderCompleted="onToolRenderCompleted"
                            @renderCallBack="renderCallBack"
                            @onNewImage="onNewImage"
                            @triggerToolGroupRender="triggerToolGroupRender"
                            @clearSelectTool="clearSelectTool"
                            @setViewportActive="setViewportActive(index, $event)"
                            @setCrosshairs="setCrosshairs"
                            @onMouseRight="onMouseRight">
                            <!-- <svg slot="inner" class="crosshairs" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" version="1.1" xmlns="http://www.w3.org/2000/svg"></svg> -->
                        </CornerstoneViewport>

                        <View2D v-else-if="item.vtk && item.volumes" 
                        :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style"
                        class="vtk-viewport"
                        :key="item.uid + index"
                        :class="{'i-active': activeViewportIndex === index, 'xx-full-screen': fullScreenIndex === index }" 
                        @setViewportActive="setViewportActive(index)"
                        @refresh='refreshView2D(item)'
                        :onCreated='storeApi(index)'
                        :modality="item.modality"
                        :layoutData="allModalityIndex"
                        :volumes="item.volumes"
                        :contrast="true"
                        :layoutId="item.layoutId"
                        :group="item.group"
                        :imageId="item.imageId"
                        :lock="lockSync.start"
                        :scrollRotate="scrollRotate"
                        :orientation="item.orientation"
                        :clipZ="item.vtkClipZ"
                        :isOverlay="false"
                        :windowIsActive="windowIsActive">
                            <ul noprint="true" class="c-action">
                                <li @click="onClickRotate(0, index)" title="正面">F</li>
                                <li @click="onClickRotate(180, index)" title="背面">B</li>
                                <li @click="onClickRotate(271, index)" title="左侧">L</li>
                                <li @click="onClickRotate(91, index)" title="右侧">R</li>
                                <li @click="onDblclickViewport(index)" title="全屏"> <i class="el-icon-full-screen"></i> </li>
                                <li @click="clearVtkWindowMark(index)" title="清除标注"> <i class="el-icon-delete"></i>
                                </li>
                            </ul>
                        </View2D>
                        <div v-else :style="gridLayoutStyle.itemStyle[index] ? gridLayoutStyle.itemStyle[index] : style" class="vtk-viewport viewportWrapper"></div>
                    </template>
                </div>
            </AreaFit>
            <BaseTools 
                ref="baseTools"
                toolType="slice"
                :contrast="true"
                :tabId="tabId"
                :allowAngel="allowAngel"
                :activeTool.sync="activeTool"
                :crosshairsTool.sync="crosshairsTool.mipShow"
                :activeViewportIndex="activeViewportIndex"
                :viewportElements="viewportElements"
                :isOverlayVisible.sync="isOverlayVisible"
                :lockSync="lockSync"
                :groupId="groupId"
                :apis="apis"
                @clearMark="onMeasurements('del')"
                @screenToRebuild="screenToRebuild"
                @playClip="vtkPlayClip"
                @setLockSync="setLockSync"
                @onChangeImg="onChangeImg"
                @onClickBox="onClickBox"
                @onReset="toggleToReset"
                @syncSizeRender="syncSizeRender"
                @onClickSavePrint="onClickSavePrint"
                @onInvert="toggleToInvert"
                @setCrosshairs="setCrosshairs"
                @onClickChangeSeries="onClickChangeSeries"
                @showLesionChart="showLesionChart">
                <div slot="header" class="c-tool-header">
                    <ul class="c-box c-box-01">
                        <li v-for="(item, index) in customLayoutShow" 
                            :key="item.id" 
                            :class="{'i-active': item.id === selectLayout}" 
                            @click="onClickChangeLayout(item.options, item.id, true)">
                            <i v-if="item.imageType" :style="{backgroundImage: 'url('+require(`$assets/`+item.img)+')'}"></i>
                            <i v-else :style="{backgroundImage: 'url('+item.img+')'}"></i>
                        </li>
                    </ul>
                </div>
            </BaseTools>
        </section>
        <v-contextmenu ref="contextmenu" oncontextmenu="return false">
            <v-contextmenu-item @click="onClickFullScreen"><i class="el-icon-full-screen"
                    style="padding-right:4px"></i>{{ fullScreenIndex === -1 ? '打开' : '退出' }}全屏</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onClickImageSave(1)"><i class="el-icon-picture" style="padding-right:4px"></i>截屏另存...</v-contextmenu-item>
            <v-contextmenu-item @click="onClickImageSave(0)"><i class="el-icon-picture" style="padding-right:4px"></i>图像另存...</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-item @click="onMeasurements('del')"><i class="el-icon-delete"
                    style="padding-right:4px"></i>清除标注</v-contextmenu-item>
            <v-contextmenu-item @click="onMeasurements('singleDel')"><i class="el-icon-delete" style="padding-right:4px"></i>清除单页标注</v-contextmenu-item>
            <v-contextmenu-item divider></v-contextmenu-item>
            <v-contextmenu-submenu>
                <span slot="title"  @click.top="setLockSync('start')"><i :class="[lockSync.start ? 'el-icon-check' : 'el-icon-close']"></i> 数据锁定</span>
                <v-contextmenu-item @click="setLockSync('scroll')"><i :class="[lockSync.scroll ? 'el-icon-check' : 'el-icon-close']"></i> 翻页同步</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="setLockSync('windowColor')"><i :class="[lockSync.windowColor ? 'el-icon-check' : 'el-icon-close']"></i> 色彩同步</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="setLockSync('moreZoom')"><i :class="[lockSync.moreZoom ? 'el-icon-check' : 'el-icon-close']"></i> 缩放平移同步</v-contextmenu-item>
                <v-contextmenu-item divider></v-contextmenu-item>
                <v-contextmenu-item @click="setLockSync('convert')"><i :class="[lockSync.convert ? 'el-icon-check' : 'el-icon-close']"></i> 旋转翻转同步</v-contextmenu-item>
             </v-contextmenu-submenu>
             <v-contextmenu-item divider></v-contextmenu-item> 
                <v-contextmenu-item @click="showRotationTools = !showRotationTools" title="调整图像融合对不准（位置微调）"><i :class="rotationMenuText"
                    style="padding-right:4px"></i>隐藏坐标微调</v-contextmenu-item> 
             <v-contextmenu-item divider></v-contextmenu-item>
             <v-contextmenu-item @click="lesionSave"><i class="el-icon-edit-outline" style="padding-right:4px"></i>保存病灶数据</v-contextmenu-item>
             <v-contextmenu-item @click="showLesionChart"><i class="iconfont iconth" style="padding-right:4px"></i>病灶评估</v-contextmenu-item>
        </v-contextmenu>

        <SaveImage :element="saveImage.element" v-model="saveImage.visible"></SaveImage>

        <SaveLesion v-model="saveLesion.visible" :data="saveLesion.lesionList"></SaveLesion>
        <LesionChartDialog
         :visible="LesionChartDialogVisible"
         @close="() => { LesionChartDialogVisible = false }"
        />
    </div>
</template>
<script>
// 引入混入方法
import Rebuild from "$src/mixin/Rebuild.js";
import VtkView from "$src/mixin/VtkView.js";
import VtkTool from '$src/mixin/VtkTool.js';
import SelectTool from "$src/mixin/SelectTool.js";
import Printscreen from "$src/mixin/Printscreen.js";
import LesionAssess from "$src/mixin/LesionAssess.js";

import appState from '$library/cornerstone/mpr/store/appState.js';
import SaveImage from '$components/layer/SaveImage';
import SaveLesion from '$components/layer/SaveLesion';
import LesionChartDialog from '$components/LesionChartDialog'
import { dbLayoutSet } from '$library/db';

export default {
    name: 'ViewContrast',
    mixins: [ Rebuild, VtkView, VtkTool, SelectTool, Printscreen, LesionAssess ],
    components: {
        SaveImage,
        SaveLesion,
        LesionChartDialog
    },
    props: {
        windowIsActive: {
            default: false,
        }
    },
    data() {
        return {
            saveImage: {		// 保存图像
				visible: false,
				element: null,
                rightSelectElement: null
			},
            saveLesion: {
                visible: false,
                lesionList: []
            },
            percentage: {         // 进度条数据
                loading: false,
                total: 0,
                loadCount: 0,
                message: '下载'
            },
            ctImageIds: [],      // 在这对比中，用于保存截图获取设备类型
            LesionChartDialogVisible: false,
            showCoordinateTools: false,
            showRotationTools: false,
            coordinateToolsIndex: -1,
            rotationToolsIndex: -1,
        }
    },
    computed: {
        groupId() {
            // 加个 contrast 区分重建对比
            return 'contrast' + this.groupUid.map(item => item.ptId + '&' + item.noPtId).join()
        },
        layoutFill() {
            return this.$store.state.layoutFill;
        },
        rotationMenuText() {
            return this.showRotationTools && this.rotationToolsIndex === this.activeViewportIndex ? 'el-icon-close' : 'el-icon-check'
        },
    },
    mounted() {
        // console.log('重建对比')
        // console.log(this.series.uids)
        // console.log(this.seriesId)
        // console.log(appState)
        this.setLayoutButton()
        this.loadData()
    },
    methods: {
        viewportState(item) {
            return {
                uid: item.uid, 
                sModality: item.sModality, 
                orientation: item.orientation, 
                layoutId: item.layoutId, 
                lock: this.lockSync.start,
                seriesId: item.seriesId,
                ctUid: (this.groupUid[item.group] || {}).noPtId || '',
                petUid: (this.groupUid[item.group] || {}).ptId || '',
                imgIndex: this.allModalityIndex[item.layoutId] || 0,
                thickness: (this.groupUid[item.group] || {}).thickness || 2.5,
                layoutId: String(item.layoutId),
                patientPosition: (this.groupUid[item.group] || {}).patientPosition?.name || '',
                defaultViewport: {
                    current: this.allModalityIndex[item.group + item.sModality + item.orientation + 'Viewport'] || {},
                    fuse: this.allModalityIndex[`${item.group}${item.sModality}${item.orientation}ViewportFuse`], // 不需要默认对象
                    layer: this.allModalityIndex[`${item.group}CT${item.orientation}Viewport`], // 不需要默认对象
                },
            }
        },
        setLayoutButton() {
            this.customLayout = []
            this.selectLayout = '2x2axial',
            this.customLayout.push(
            {
                img: 'images/contrast/2x2_1.png', text: '2x2_1', id: '2x2_1', imageType: 'loca',
                options: {
                    layout: { row: 2, column: 2 },
                    selectValue: [
                        '0,z,one', '0,x,mipTwo',
                        '1,z,one', '1,x,mipTwo',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/2x2_2.png', text: '2x2_2', id: '2x2_2', imageType: 'loca',
                options: {
                    layout: { row: 2, column: 2 },
                    selectValue: [
                        '0,z,fuse', '0,x,mipTwo',
                        '1,z,fuse', '1,x,mipTwo',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/2x3_1.png', text: '2x3_1', id: '2x3_1', imageType: 'loca',
                options: {
                    layout: { row: 2, column: 3 },
                    selectValue: [
                        '0,z,fuse', '0,x,fuse', '0,x,mipTwo',
                        '1,z,fuse', '1,x,fuse', '1,x,mipTwo',
                    ]
                }
            })
            
            this.customLayout.push(
            {
                img: 'images/contrast/2x3_2.png', text: '2x3_2', id: '2x3_2', imageType: 'loca',
                options: {
                    layout: { row: 2, column: 3 },
                    selectValue: [
                        '0,x,fuse', '0,y,fuse', '0,z,fuse',
                        '1,x,fuse', '1,y,fuse', '1,z,fuse',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/3x2_1.png', text: '3x2_1', id: '3x2_1', imageType: 'loca',
                options: {
                    layout: { row: 3, column: 2 },
                    selectValue: [
                        '0,z,one', '0,x,mipTwo',
                        '1,z,one', '1,x,mipTwo',
                        '2,z,one', '2,x,mipTwo',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/3x2_2.png', text: '3x2_2', id: '3x2_2', imageType: 'loca',
                options: {
                    layout: { row: 3, column: 2 },
                    selectValue: [
                        '0,z,fuse', '0,x,mipTwo',
                        '1,z,fuse', '1,x,mipTwo',
                        '2,z,fuse', '2,x,mipTwo',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/3x3_1.png', text: '3x3_1', id: '3x3_1', imageType: 'loca',
                options: {
                    layout: { row: 3, column: 3 },
                    selectValue: [
                        '0,z,fuse', '0,x,fuse', '0,x,mipTwo',
                        '1,z,fuse', '1,x,fuse', '1,x,mipTwo',
                        '2,z,fuse', '2,x,fuse', '2,x,mipTwo',
                    ]
                }
            })
            
            this.customLayout.push(
            {
                img: 'images/contrast/3x3_2.png', text: '3x3_2', id: '3x3_2', imageType: 'loca',
                options: {
                    layout: { row: 3, column: 3 },
                    selectValue: [
                        '0,x,fuse', '0,y,fuse', '0,z,fuse',
                        '1,x,fuse', '1,y,fuse', '1,z,fuse',
                        '2,x,fuse', '2,y,fuse', '2,z,fuse',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/2x3_3.png', text: '2x3-CT-FUSE-MIP', id: '2x3_3', imageType: 'loca',
                options: {
                    layout: { row: 2, column: 3 },
                    selectValue: [
                        '0,z,one', '0,z,fuse', '0,x,mipTwo',
                        '1,z,one', '1,z,fuse', '1,x,mipTwo',
                    ]
                }
            })

            this.customLayout.push(
            {
                img: 'images/contrast/3x3_3.png', text: '3x3-CT-FUSE-MIP', id: '3x3_3', imageType: 'loca',
                options: {
                    layout: { row: 3, column: 3 },
                    selectValue: [
                        '0,z,one', '0,z,fuse', '0,x,mipTwo',
                        '1,z,one', '1,z,fuse', '1,x,mipTwo',
                        '2,z,one', '2,z,fuse', '2,x,mipTwo',
                    ]
                }
            })

            //  自定义配置布局
            dbLayoutSet.then(e => {
                // 2 对比
                e.getGroup(2).then(e => {
                    if (e.success) {
                        const list = e.data.sort((a, b) => {
                            return a.position - b.position;
                        });
                        list.forEach(item => {
                            const selectValue = item.showLayout.map(_ => {
                                return `${_.group},${_.angle},${_.type}`
                            })
                            this.customLayout.push({
                                img: item.img,
                                text: item.text,
                                id: item.id,
                                options: {
                                    containerStyle: item.options.containerStyle,
                                    itemStyle: item.options.itemStyle,
                                    layout: item.options.layout,
                                    selectValue
                                }
                            })
                        })
                    }
                })
            });




        },
        /**
         * 点击固定布局
         */
        onClickChangeLayout(options, id, isPassive = false){
            if (isPassive) {
                this.activeViewportIndex = -1;
                this.showAction = -1;
                this.updateLayoutModalityViewport()
            }
            if (!this.originSeries.length) return
            if (id === this.selectLayout) {
                // 点击同样的布局时 不用切换
                return
            } 
            this.selectLayout = id
            
            // 清除 vtk 信息
            this.apis.splice(0)
            this.isCrosshairsCallback = []

            // 特殊布局的 css 信息
            if (options.containerStyle) {
                this.gridLayoutStyle.containerStyle = options.containerStyle;
            }else {
                this.gridLayoutStyle.containerStyle = {}
            }

            if (options.itemStyle && options.itemStyle.length) {
                this.gridLayoutStyle.itemStyle = options.itemStyle;
            }else {
                this.gridLayoutStyle.itemStyle = [];
            }

            this.onClickBox(options.layout)
            this.setViewportByLayout(options.selectValue)
            // this.setInitModalityIndex(false)
            this.resetViewport()
        },
        // 改变棋盘布局添入相应图像
        changeLayoutPushView(layouts){
            if (!this.originSeries.length) return
            this.apis.splice(0)
            // 设置布局数据
            this.setViewportByLayout(layouts);
            // 重置
            this.resetViewport();
            
        },
        loadData() {
            if (!this.series.uids){
                return
            }
            this.percentage.loadCount = 0
            this.percentage.loading = true
            
            let allImgeId = []
            this.series.uids.forEach(itemIds => {
                const [noPtId, ptId] = itemIds.split(',')
                const orignNoPtInstance = appState[noPtId].series
                let orignPtInstance     = ptId ? appState[ptId].series : []
                // 需要加载的图像
                let loadNoPtImageIds = [], loadPtImageIds = [];

                // 存一个 ids
                this.ctImageIds[0] = orignNoPtInstance[0]

                if (!appState[noPtId].vtkVolumes) {
                    // 没有体
                    loadNoPtImageIds = orignNoPtInstance;
                }else {
                    // 有体数据
                    loadNoPtImageIds = orignNoPtInstance.slice(0, 2);
                }
                if (ptId && !appState[ptId].vtkVolumes) {
                    if (orignPtInstance.length === 1) {
                        loadPtImageIds = this.loadFrameImage(orignPtInstance);
                    }
                }
                allImgeId = allImgeId.concat(loadNoPtImageIds, loadPtImageIds);

                this.percentage.total += allImgeId.length;
            });
            // 统一下载
            Promise.all(this.loadPromise(allImgeId)).then(() => {
                this.buildImage().then(() => {
                    

                    this.checkLayoutData()
                    this.setSynchronizerCrosshairs()
                    this.setDefaultViewport()
                    
                    // 调用改变布局
                    if (this.customLayoutShow[0]) {
                        // 改变视图网格
                        this.onClickBox(this.customLayoutShow[0].options.layout)
                        // 改变视图的数据
                        this.setViewportByLayout(this.customLayoutShow[0].options.selectValue)
                        
                        // 设置当前布局初始化位置值
                        this.setInitModalityIndex()

                        // 重置
                        this.resetViewport()
                    }
                })
            })
        },
        checkLayoutData() {
            const map = {
                "sagittal": 'x',
                "coronal": 'y',
                "axial": 'z'
            }
            // console.log(this.originSeries)
            const orientationType = this.originSeries.map(i => map[i.sliceType])
            const notZseries = this.originSeries.map((i, index) => { i.index = index; return i })
            .filter(i => i.sliceType !== 'axial')
            const notZseriesMap = {}
            notZseries.forEach(item => {
                notZseriesMap[item.index] = item
            })
            this.customLayoutShow.forEach((layout, layoutIdx) => {
                const selectValues = layout.options.selectValue
                const layoutWithZ = selectValues.forEach((str, idx) => {
                    const splitArr = str.split(',')
                    const index = splitArr[0]
                    const orien = splitArr[1]
                    const layoutType = splitArr[2] // one fuse mipone
                    if (notZseriesMap[index] && orien === 'z' && !layoutType.includes('mip')) {
                        selectValues[idx] = str.replace(orien, map[notZseriesMap[index].sliceType])
                    }
                    // return orien === 'z' && !layoutType.includes('mip')
                }) 
                
            })

        },
        setDefaultViewport() {
            this.originSeries.forEach((item, index) => {
                this.allModalityIndex[`${index}CTxViewport`] = {  };
                this.allModalityIndex[`${index}CTyViewport`] = {  };
                this.allModalityIndex[`${index}CTzViewport`] = {  };

                this.allModalityIndex[`${index}PTxViewport`] = {  };
                this.allModalityIndex[`${index}PTyViewport`] = {  };
                this.allModalityIndex[`${index}PTzViewport`] = {  };

                this.allModalityIndex[`${index}PTxViewportFuse`] = {  };
                this.allModalityIndex[`${index}PTyViewportFuse`] = {  };
                this.allModalityIndex[`${index}PTzViewportFuse`] = {  };

                this.allModalityIndex[`${index}PTmipViewport`] = {  };
                this.allModalityIndex[`${index}CTmipViewport`] = {  };
            })
        },
        // 加载单个图像，返回 promise
        loadPromise(imageIds) {
            return imageIds.map((imageId => {
                return cornerstone.loadAndCacheImage(imageId).then(() => {
                    this.percentage.loadCount += 1
                })
            }))
        },
        // 加载多帧
        loadFrameImage(instanceIds) {
            let url = instanceIds[0].slice(9)
            let imageIds = []
            cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.load(url, cornerstoneWADOImageLoader.internal.xhrRequest).then((dataSet) => {
                let numFrames = dataSet.intString('x00280008');
                if (numFrames){
                    for(let i=0; i < numFrames; i++) {
                        var imageId = 'dicomweb:' + url + "?frame=" + i;
                        imageIds.push(imageId);
                    }
                }
            })
            return imageIds
        },
        // 获取一组切片图像id
        async getSliceImageIds(noPtId, ptId) {
            const imageStoreInfo = appState[noPtId]

            let series0Meta = cornerstone.metaData.get('imagePlaneModule', imageStoreInfo.series[0])
            let series1Meta = cornerstone.metaData.get('imagePlaneModule', imageStoreInfo.series[1])

            const patientPosition = cornerstone.metaData.get('patientPosition', imageStoreInfo.series[0]) || {};
            // 图像偏移所处角度
            const sliceType = this.$fun.getNormal(series0Meta.imageOrientationPatient, true)
            // 层厚
            let thickness = Math.abs( series0Meta.sliceLocation - series1Meta.sliceLocation ) || 2.5
            thickness = Number(thickness.toFixed(2));

            let originSeries = {}
            let isAnomalyFuse = false
            if (sliceType !== 'axial' || this.$fun.isItalic(series0Meta.imageOrientationPatient)) {
                // 原图斜切
                // 斜切的图，noPtId 是不需要重建的
                const noPtImageIds = appState[noPtId].series
                let   ptImageIds   = []
                noPtImageIds.map(imageId => {
                    const metaData = cornerstone.metaData.get('imagePlaneModule', imageId);
                    ptImageIds.push('mpr:'+ ptId +':' + metaData.imageOrientationPatient.join(',') + ':' + metaData.imagePositionPatient.join(',') + ':clip-special:' + noPtId)
                })
                
                originSeries.x = { one: [], two: [] }
                originSeries.y = { one: [], two: [] }
                originSeries.z = { one: [], two: [] }

                const idx = ['sagittal', 'coronal', 'axial'].indexOf(sliceType)
                const angle = ['x','y','z'][idx]
                originSeries[angle] = { one: noPtImageIds.slice(0), two: ptImageIds }
                
                // originSeries.x = { one: noPtImageIds.slice(0), two: ptImageIds }
                // originSeries.y = { one: noPtImageIds.slice(0), two: ptImageIds }
                // originSeries.z = { one: noPtImageIds.slice(0), two: ptImageIds }
                isAnomalyFuse = true
            }else {
                // 原图正切(横截面)
                const res = await this.getVolumeSliceNumber(noPtId, thickness)
                let [oneX, twoXPT] = this.getNewImageIds('sagittal', res, thickness, noPtId, ptId)
                let [oneY, twoYPT] = this.getNewImageIds('coronal', res, thickness, noPtId, ptId)
                let [oneZ, twoZPT] = this.getNewImageIds('axial', res, thickness, noPtId, ptId)
                originSeries.x = { one: oneX, two: twoXPT }
                originSeries.y = { one: oneY, two: twoYPT }
                originSeries.z = { one: oneZ, two: twoZPT }
            }

            originSeries.sliceType = sliceType

            return { originSeries, thickness, sliceType, isAnomalyFuse, patientPosition}

        },
        // 改变层厚，数量 == 原理上是改变图像了
        async onChangeImg(value, key, el){
            let count = Number(value);
            const allStack = cornerstoneTools.getToolState(el, 'stack')
            if (!allStack || !allStack.data.length) { return }
            const state = allStack.data[0].state
            const [targetGroup, targetOrientation] = state.layoutId.split(',')
            const itemIds = this.series.uids[targetGroup]
            const [noPtId, ptId] = itemIds.split(',')
            let originSeries = {}
            let thickness = 2.5
            if (key === 'amount'){ 
                const angle = targetOrientation == 'x' ? 'sagittal' : targetOrientation == 'y' ? 'coronal' : 'axial'
                thickness = this._getThicknessByCount(noPtId, count, angle)
            }else {
                thickness = count;
            }
            this.getVolumeSliceNumber(noPtId, thickness).then(res => {
                let [oneX, twoXPT] = this.getNewImageIds('sagittal', res, thickness, noPtId, ptId)
                let [oneY, twoYPT] = this.getNewImageIds('coronal', res, thickness, noPtId, ptId)
                let [oneZ, twoZPT] = this.getNewImageIds('axial', res, thickness, noPtId, ptId)
                originSeries.x = { one: oneX, two: twoXPT }
                originSeries.y = { one: oneY, two: twoYPT }
                originSeries.z = { one: oneZ, two: twoZPT }

                // 清除缓存
                const itemSeries = this.originSeries[targetGroup]        
                for (const key in itemSeries) {
                    if (Object.hasOwnProperty.call(itemSeries, key)) {
                        const itemAngel = itemSeries[key]
                        for (const key in itemAngel) {
                            if (Object.hasOwnProperty.call(itemAngel, key)) {
                                const series = itemAngel[key];
                                if (series && series.imageIds){
                                    series.imageIds.map(img => {
                                        // 存在缓存，清除缓存
                                        if (cornerstone.imageCache.getImageLoadObject(img)){
                                            cornerstone.imageCache.removeImageLoadObject(img)
                                        }
                                    })
                                }
                            }
                        }
                    }
                }
                
                // 替换原来的数据
                this.originSeries.splice(targetGroup, 1, originSeries)

                // 更新显示的图像 ids
                this.viewports.forEach(viewport => {
                    // 跳出本次循环
                    if (viewport.vtk || viewport.group != targetGroup) return;

                    // 更新层厚
                    if (this.groupUid[viewport.group] !== undefined) {
                        this.groupUid[viewport.group].thickness = thickness
                        viewport.seriesId = `mpr:${this.groupUid[viewport.group].ptId}&&${this.groupUid[viewport.group].noPtId}${this.groupUid[viewport.group].thickness}${viewport.orientation}`
                    }

                    const modality = viewport.sModality === 'CT' ? 'one' : 'two'
                    const layerModality = modality == 'one' ? 'two' : 'one'
                    const obj = this.originSeries[viewport.group][viewport.orientation][modality]
                    if (obj){
                        viewport.imageIds = obj.slice(0)
                        // 存在第二层，序列
                        if (viewport.layerIds){
                            // 与第二层相反设备类型，同角度
                            const layerObj = this.originSeries[viewport.group][viewport.orientation][layerModality]
                            if (layerObj){
                                viewport.layerIds = layerObj.slice(0)
                            }
                        }
                        viewport.patientPosition = this.originSeries[viewport.group].patientPosition;
                        // 更新id
                        viewport.uid = this.$fun.onlyValue();
                    }
                });
                // 更新渲染
                this.$forceUpdate();

                this.setInitModalityIndex()
                this.$nextTick(() => {
                    this.resetViewport()
                })

            })

        },
        // 建立新图像id
        async buildImage() {
            this.originSeries = []
            this.groupUid     = []
            return new Promise((resolve, reject) => {
                Promise.all(this.series.uids.map(async (itemIds) => {
                    // 一组图像 id
                    const [noPtId, ptId] = itemIds.split(',')
                    const {originSeries, thickness, sliceType, isAnomalyFuse, patientPosition} = await this.getSliceImageIds(noPtId, ptId)

                    this.originSeries.push(originSeries)
                    this.groupUid.push({noPtId, ptId, thickness, sliceType, isAnomalyFuse, patientPosition})
                    return ''
                })).then(() => {
                    resolve({ originSeries: this.originSeries })
                })
            })
        },

        // 修改同步锁属性状态值
        setLockSync(attribute) {
            this.lockSync[attribute] = !this.lockSync[attribute] 

            // 点击的是开启或者滚动同步
            if (attribute === 'start' || attribute === 'scroll') {
                this.initScrollLook()
            }
            if (attribute === 'start') {
                this.$refs.contextmenu.hide();
            }
        },
        // 右键
		onMouseRight(e, hide = false) {
            if (hide) {
                this.$refs.contextmenu.hide();
                return;
            }
			this.$refs.contextmenu.handleReferenceContextmenu(e);
            setTimeout(() => {
                this.saveImage.rightSelectElement = this.getEnabledElement(this.activeViewportIndex);
            }, 100)
		},
        onClickImageSave(isScreen = 0) {
            if (isScreen) {
                this.saveImage.element = this.$refs.center;
                this.saveImage.visible = true;
            }else {
                const name = this.$store.state.seriesInfo.sPatientName;
                cornerstoneTools.SaveAs(this.saveImage.rightSelectElement, name + '_' + new Date().toLocaleString() + '.jpg');
            }
		},
        showLesionChart() {
            this.LesionChartDialogVisible = true
        },
        clearVtkWindowMark() {
            const api = this.apis[this.activeViewportIndex]
            if (api) {
                api.svgWidgets.markWidget.clearPointStorage()
                api.svgWidgets.probeWidget.clearPointStorage()
                api.svgWidgets.arrowWidget.clearPointStorage()
            }
        }
    },
}

</script>
<style lang="scss" scoped>
.c-content{
	display: flex;
    flex-wrap: wrap;
	> div{
		position: relative;
	}
    .vtk-viewport{
        &:hover{
            .c-action{
                display: block;
            }
        }
    }
    .c-action{
        display: none;
        position: absolute;
        top: 44%;
        right: 2px;
        margin-top: -64px;
        height: 128px;
        z-index: 1;
        li{
            width: 30px;
            height: 30px;
            line-height: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            margin-bottom: 2px;
            &:hover{
                background: #eee;
            }
            i{
                font-size: 18px;
            }
        }
    }
}
.c-tool-header{
    .c-box{
        display: flex;
        flex-wrap: wrap;
        width: 100%;
    }
    .c-box-01{
        padding: 8px 0px 4px 8px;
        border-bottom: 1px solid #d2d2d2;
        > li {
            width: 54px;
            height: 54px;
            line-height: 56px;
            border: 1px solid #d2d2d2;
            margin-right: 4px;
            margin-bottom: 4px;
            cursor: pointer;
            transition: all 0.2s;
            background: #fff;
            border-radius: 4px;
            i {
                display: inline-block;
                width: 100%;
                height: 100%;
                border-radius: 2px;
                background-size: cover;
            }
            &:hover{
                box-shadow: 2px 2px 4px 0px #c0c0c0;
            }
            &.i-active{
                border-color: #6294B7;
                color: white;
            }
        }
    }
}
</style>