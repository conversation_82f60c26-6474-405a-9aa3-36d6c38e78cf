const path = require('path')
const CompressionWebpackPlugin = require('compression-webpack-plugin');
var WebpackObfuscator = require('webpack-obfuscator');
function resolve(sPath) {
	return path.resolve(__dirname, sPath)
}


function writeUpdateLog() {
	if (process.env.NODE_ENV === 'development') return
	const fs = require('fs')
	const txt = String(+new Date())
	fs.writeFile("public/checkUpdate.json", txt, (err) => {
		if (err) throw err;
	});
}

writeUpdateLog() // 检查代码是否更新

const isPROD = process.env.NODE_ENV === 'production'

let configureWebpackPlugins = [
	new CompressionWebpackPlugin({
		filename: '[path][base].gz',
		algorithm: 'gzip',
		test: new RegExp(
			'\\.(' + ['html', 'js', 'css'].join('|') + ')$'
		),
		threshold: 10240, // 只有大小大于该值的资源会被处理 10240
		minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
		deleteOriginalAssets: false // 删除原文件
	})
]

// if (isPROD) {
// 	configureWebpackPlugins.splice(0, 0, (new WebpackObfuscator({
// 		compact: true,
// 		controlFlowFlattening: false,
// 		deadCodeInjection: false,
// 		debugProtection: false,
// 		debugProtectionInterval: 0,
// 		disableConsoleOutput: true,
// 		identifierNamesGenerator: 'mangled',
// 		log: false,
// 		numbersToExpressions: false,
// 		renameGlobals: false,
// 		selfDefending: true,
// 		simplify: true,
// 		splitStrings: false,
// 		stringArray: true,
// 		stringArrayCallsTransform: false,
// 		stringArrayEncoding: [],
// 		stringArrayIndexShift: true,
// 		stringArrayRotate: true,
// 		stringArrayShuffle: true,
// 		stringArrayWrappersCount: 1,
// 		stringArrayWrappersChainedCalls: true,
// 		stringArrayWrappersParametersMaxCount: 2,
// 		stringArrayWrappersType: 'variable',
// 		stringArrayThreshold: 0.75,
// 		unicodeEscapeSequence: false
// 	})))
// }
// process.env.VUE_APP_APITYPE 全局变量在 .env 文件中定义,修改在打包
module.exports = {
	publicPath: isPROD ? '/app/webdicom' : '', // 实际路径

	productionSourceMap: false, // 生产禁止显示源代码 带 .js.map 的文件
	// chainWebpack: (config) => {
	//   config.module.rule('js').test(/\.js$/).use('babel-loader').loader('babel-loader')
	// },
	parallel: false,
	devServer: {
		/* 自动打开浏览器 */
		open: true,
		/* 设置为0.0.0.0则所有的地址均能访问 */
		host: '0.0.0.0',
		port: 8802,
		https: false,
		hotOnly: false,
		/* 使用代理 */
		proxy: {
			'/api': {
				/* 目标代理服务器地址 */
				target: 'http://***********/',
				/* 允许跨域 */
				changeOrigin: true,
			},
		},
	},
	configureWebpack: {
		plugins: configureWebpackPlugins,
		module: {
			rules: [{
				test: /\.glsl$/i,
				loader: 'shader-loader'
			},
			{
				test: /\.worker\.js$/,
				use: [
					{ loader: 'worker-loader' },
				],
			},]
		},
		resolve: {
			/*别名设置*/
			alias: {

				/*common------*/
				// $public: resolve('public/js'),  //公共插件(组件)
				$src: resolve('src'),
				$api: resolve('src/api'),
				$components: resolve('src/components'),
				$library: resolve('src/library'), //资源
				$assets: resolve('src/assets') //公共资源
			}
		},
	},
	transpileDependencies: [
		// can be string or regex
		'vtk.js'
	]
}