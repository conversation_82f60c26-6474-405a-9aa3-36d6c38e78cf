import createMprSlice from './mpr/createMprSlice.js';
import mprMetaDataStore from './mpr/mprMetadata/mprMetaDataStore.js';
import tryGetVtkVolumeForSeriesNumber from './mpr/tryGetVtkVolumeForSeriesNumber.js';
import mapVtkSliceToCornerstoneImage from './mpr/mapVtkSliceToCornerstoneImage.js';

export default function(imageId){

    const imageLoadObject = {
        promise: undefined,
        cancelFn: undefined,
        decacheData: undefined
    }

    imageLoadObject.promise = createImage(imageId);

    return imageLoadObject;
}
/**
 * 该方法是一个自定义MPR加载器，只要用于加载mpr图像
 * @param {*} imageId 图像相应的标识id
 */
export async function createImage(imageId){
    // console.log('MPR IMAGE ID: ', imageId);
    const [scheme, seriesNumber, imageOrientationPatient, imagePositionPatient, imageClip, clipKey, offset] = imageId.split(':'); // ["mpr", "0", "1,0,0,0,1,0", "center"]
        
    // 获取vtk容积，该容积给vtk用的
    const { vtkVolume, imageIds } = await tryGetVtkVolumeForSeriesNumber(seriesNumber);
    // Math.floor(vtkVolume.imageIds.length / 2)

    // 是不是可以不用这个 tag 值了？
    const cache = cornerstone.imageCache.imageCache[imageIds[0]]

    let oneImg = cache ? cache.image : null
    if (!oneImg) {
        oneImg = await cornerstone.loadAndCacheImage(imageIds[0]) 
    }
    // 创建mpr切片
    const createSliceResult = createMprSlice(vtkVolume, 
        { imageOrientationPatient, imagePositionPatient, imageClip, seriesNumber, clipKey, offset }, oneImg);

    // 切片数据转换成 Cornerstone 的图像结果
    const mappedSlice = mapVtkSliceToCornerstoneImage(createSliceResult.slice, imageIds[0], createSliceResult.metaData.imagePlaneModule);

    // js map数据结构存储 metaData 值，已传入 id 为键
    _createMprMetaData(imageId, createSliceResult.metaData); 
    // cornerstone 图像数据结构（该数据结构 cornerstone 能直接解析成图像）
    const image = Object.assign({}, mappedSlice, {
        imageId,
        floatPixelData: undefined,
        isMpr: true,
        data: oneImg.data
    })
    // 没窗宽窗位的，设置初始值
    if (image.windowCenter === undefined || image.windowWidth === undefined) {
        const maxVoi = image.maxPixelValue * image.slope + image.intercept
        const minVoi = image.minPixelValue * image.slope + image.intercept

        image.windowWidth = maxVoi - minVoi
        image.windowCenter = (maxVoi + minVoi) / 2
    }
    // 清除切片 vtk 切片数据 -- 不知道有没有用
    createSliceResult.slice = null;
    return image;
}

function _createMprMetaData(imageId, metaData){
    mprMetaDataStore.set(imageId, metaData);
}