// const csTools = cornerstoneTools;
import csTools from '$library/cornerstone/cornerstoneTools'

const { addToolState, clearToolState, getToolState, toolStyle, toolColors, getModule, store } = csTools;
const external = csTools.external;

const BaseTool = csTools.importInternal('base/BaseTool');
// State
// const getHandleNearImagePoint = csTools.importInternal('manipulators/getHandleNearImagePoint');

// Drawing
const getNewContext = csTools.importInternal('drawing/getNewContext');
const draw = csTools.importInternal('drawing/draw');
const setShadow = csTools.importInternal('drawing/setShadow');
const drawEllipse = csTools.importInternal('drawing/drawEllipse');
// const drawJoinedLines = csTools.importInternal('drawing/drawJoinedLines');
// const drawLine = csTools.importInternal('drawing/drawLine');
// const drawLinkedTextBox = csTools.importInternal('drawing/drawLinkedTextBox');

// Util
const calculateSUV = csTools.importInternal('util/calculateSUV');
const { calculateEllipseStatistics, pointInEllipse } = csTools.importInternal('util/ellipseUtils');

// const getROITextBoxCoords = csTools.importInternal('util/getROITextBoxCoords');
// const numbersWithCommas = csTools.importInternal('util/numbersWithCommas');
// const throttle = csTools.importInternal('util/throttle');
// const getPixelSpacing = csTools.importInternal('util/getPixelSpacing');
const triggerEvent = csTools.importInternal('util/triggerEvent');
const { state } = store;
const { circleRoiCursor } = csTools.importInternal('tools/cursors');

const {
	freehandArea,
	calculateFreehandStatistics,
	pointInFreehand,
} = csTools.importInternal('util/freehandUtils');

import MagicWand from './math/magicWand.js'

import getConfigByStorageKey from '$library/utils/configStorage.js'

/**
 * @public
 * @class CircleMeasureTool
 * @extends Tools.Base.BaseTool
 */
export default class EllipticalMeasureTool extends BaseTool {
	constructor(props = {}) {
		const defaultProps = {
			name: 'EllipticalMeasure',
			supportedInteractionTypes: ['Mouse', 'Touch'],
			svgCursor: circleRoiCursor,
			configuration: {
				color: toolColors.getToolColor(),
				circleRoiDiameter: true
			},
			currentData: null
		};

		super(props, defaultProps);

		this.postMouseDownCallback = this.mouseDownCallback.bind(this);
		this.postTouchStartCallback = this.mouseDownCallback.bind(this);

		this.mouseDragCallback = this.mouseDragCallback.bind(this);
		this.touchDragCallback = this.mouseDragCallback.bind(this);

		this.touchEndCallback = this.measure.bind(this);
		this.mouseUpCallback = this.measure.bind(this);
		this.mouseClickCallback = this.measure.bind(this);

		this.outerMeasure = this.measure.bind(this);


		this.circleRoiDiameter =
			state.circleRoiDiameter || this.configuration.circleRoiDiameter;
		// this.throttledUpdateCachedStats = throttle(this.updateCachedStats, 110);
	}

	addNewMeasurement(evt) {
		// console.log('addNewMeasurement');

		evt.stopImmediatePropagation();
		evt.stopPropagation();
		evt.preventDefault();

		const eventData = evt.detail;
		const measurementData = this.createNewMeasurement(eventData);
		const element = evt.detail.element;

		const enabled = external.cornerstone.getEnabledElement(element)
		measurementData.modality = enabled.modality

		addToolState(element, this.name, measurementData);
		this.drawing = true;
	}

	createNewMeasurement(eventData) {
		const goodEventData =
			eventData && eventData.currentPoints && eventData.currentPoints.image;

		if (!goodEventData) {
			return;
		}
		// console.log('createnew');
		// measurementData
		return {
			element: eventData.element,
			startPoints: eventData.currentPoints,
			currentPoints: eventData.currentPoints
		};
	}

	mouseDownCallback(evt) {
		const eventData = evt.detail;
		const { element, currentPoints } = eventData;
		const newData = {
			element,
			startPoints: currentPoints,
			currentPoints: currentPoints
		};

		this.currentData = newData
	}

	mouseDragCallback(evt) {
		const eventData = evt.detail;
		const { element } = eventData;
		// const toolData = getToolState(element, this.name);

		// if (!toolData || !toolData.data || !toolData.data.length) {
		// 	return;
		// }

		// const oldData = toolData.data[0];

		// const newData = {
		// 	element,
		// 	startPoints: oldData.startPoints,
		// 	currentPoints: eventData.currentPoints
		// };

		// csTools.clearToolState(element, this.name);
		// addToolState(element, this.name, newData);
		// external.cornerstone.updateImage(element);

		const newData = {
			element,
			startPoints: this.currentData.startPoints,
			currentPoints: eventData.currentPoints
		};

		this.currentData = newData
		external.cornerstone.updateImage(element);
	}

	renderToolData(evt) {
		// const eventData = evt.detail;
		// const { element } = eventData;
		// const toolData = getToolState(element, this.name);

		// if (!toolData || !toolData.data || !toolData.data.length) {
		// 	return;
		// }
		// this._drawRect(toolData.data[0]);

		this._drawRect(this.currentData);
	}

	_drawRect(data) {
		if (!this.drawing) {
			return;
		}
		const getDistance = external.cornerstoneMath.point.distance;
		const { element, currentPoints, startPoints } = data;
		const canvasEle = element.querySelector('canvas');
		const newContext = getNewContext(canvasEle);

		draw(newContext, (context) => {
			// setShadow(context, this.configuration);

			// let startCanvas = external.cornerstone.pixelToCanvas(
			//   element,
			//   startPoints
			// );
			// const endCanvas = external.cornerstone.pixelToCanvas(
			//   element,
			//   currentPoints
			// );
			let startCanvas = startPoints.canvas;
			const endCanvas = currentPoints.canvas;

			// if (this.circleRoiDiameter) {
			// 	startCanvas = {
			// 		x: (startCanvas.x + endCanvas.x) / 2,
			// 		y: (startCanvas.y + endCanvas.y) / 2
			// 	};
			// }
			const circleOptions = { color: toolColors.getToolColor(), lineWidth: 2.5, };

			drawEllipse(
				context,
          		element,
				startCanvas,
				endCanvas,
				circleOptions,
				'canvas',
				0.0
			);
		});
	}

	measure(evt, outerToolData, outerSuvThresValue) {
		this.drawing = false;
		const getDistance = external.cornerstoneMath.point.distance;
		const eventData = evt.detail;
		const startImgIndex = eventData.startImgIndex
		const { element, currentPoints, startPoints } = eventData;

		// const toolData = getToolState(element, this.name);

		let toolData = this.currentData; 
		if (outerToolData) { 
			toolData = outerToolData
		} else {
			if (!toolData) {
				return
			}

			const data = toolData; 

		}



		const configData = getConfigByStorageKey('configs-circleMeasure')

		// const { rowPixelSpacing, columnPixelSpacing, 
		// 	sliceThickness, sliceLocation,
		// 	imagePositionPatient } = cornerstone.metaData.get('imagePlaneModule', imageId);

		// 共享：    (读写) configData

		const modifiedEventData = {
			toolName: "LesionArea",
			element,
			startImgIndex,

			getMeasureData: (imageId, singleSliceMaxValue = 0) => {
				return external.cornerstone.loadImage(imageId).then(image => {
					const { sliceThickness, sliceLocation, rowPixelSpacing, columnPixelSpacing,
						imagePositionPatient } = cornerstone.metaData.get('imagePlaneModule', imageId);
					const seriesModule = external.cornerstone.metaData.get(
						'generalSeriesModule',
						imageId
					);
					const modality = seriesModule ? seriesModule.modality : null;
					if (modality === 'CT') return false
					let lesionPoints = []
					const origPixelData = image.origPixelData || image.getPixelData();

					let maxPixelValue = 0
					let samplePoint = { ...startPoints.image }
					let samplePoint2 = { ...currentPoints.image }
					let maxPoint = {  }
					let minX = samplePoint.x;
					let minY = samplePoint.y;
					let maxX = samplePoint2.x;
					let maxY = samplePoint2.y;
					minX = Math.floor(Math.min(minX, samplePoint2.x));
					minY = Math.floor(Math.min(minY, samplePoint2.y));
					maxX = Math.ceil(Math.max(maxX, samplePoint.x));
					maxY = Math.ceil(Math.max(maxY, samplePoint.y));
					// let step = roundRadius / 3
					const minorEllipse = {
						left: minX,
						top: minY ,
						width: Math.abs(maxX - minX) ,
						height: Math.abs(minY - maxY)  ,
					  };
					let step = 1
					for (let y = minY; y <= maxY; y += step) {
						for (let x = minX; x <= maxX; x += step) {
							// x = Math.round(x)
							// y = Math.round(y)


							const val = origPixelData[y * image.width + x]
							// if (pointInEllipse(minorEllipse,{ x, y })) lesionPoints.push({ x, y, val })
							if (val > maxPixelValue) {
								maxPixelValue = val
								maxPoint = { x, y }
							}
						}
					}

					const imageSuvInfo = getPointSUVinfo(image, maxPoint.x, maxPoint.y) // 取样点的suv值 即圈选区域最大suv值
					const maxSUV = imageSuvInfo.suv || 1e8
					const maxValue = singleSliceMaxValue ? singleSliceMaxValue : maxPixelValue

					let activeThresholdVal = maxValue * 1 / 4
					let showedSUVthresType = 0
					let showedSUVthresVal = 0 // 用于外部显示的suv阈值

					if (outerSuvThresValue) {  // 外部调节的阈值 优先于configData
						if (String(outerSuvThresValue).indexOf('%') > -1) {
							const pc = String(outerSuvThresValue).replace('%', '')
							activeThresholdVal = maxValue * (pc / 100)
							showedSUVthresType = 1
							showedSUVthresVal = pc
						} else {
							activeThresholdVal = maxPixelValue / maxSUV * outerSuvThresValue
							showedSUVthresType = 2
							showedSUVthresVal = outerSuvThresValue
						}
					} else {
						if (!configData) {
							//  不使用configData，默认33%
							activeThresholdVal = maxValue * 0.25
							showedSUVthresType = 1
							showedSUVthresVal = 25
						} else if (configData.thresholdType === 1) { // 阈值类型为百分比阈值
							activeThresholdVal = maxValue * (configData.percentThreshold / 100)
							showedSUVthresType = configData.thresholdType
							showedSUVthresVal = configData.percentThreshold
						} else { // 阈值类型为固定阈值
							// 当使用固定阈值时，给定一个SUV值，要算出其对应的像素值 = suv * _像素值 / _suv
							activeThresholdVal = maxPixelValue / maxSUV * configData.staticThreshold
							showedSUVthresType = configData.thresholdType
							showedSUVthresVal = configData.staticThreshold
						}
					}

					// 防止测量到别的部位的病灶， 超过单层2.1倍的不计算在内
					if (showedSUVthresType < 2) {
						if (singleSliceMaxValue > 0 && maxPixelValue > singleSliceMaxValue * 2.09) return false //
						if (singleSliceMaxValue > 0 && maxPixelValue < singleSliceMaxValue * 0.383) return false //
					}


					let canvasImageDataIndex = 0;
					let storedPixelDataIndex = 0;
					let sp;
					let compareVal = 0;
					let mapped = [0, 0, 0, 0];
					const numPixels = image.width * image.height
					const storedColorPixelData = new Uint8Array(numPixels * 4);

					while (storedPixelDataIndex < numPixels) {
						sp = origPixelData[storedPixelDataIndex++];
						compareVal = Math.floor(sp - image.minPixelValue)
						mapped[0] = Math.floor(compareVal / 256 / 256)
						mapped[1] = Math.floor(compareVal % 65536 / 256)
						mapped[2] = Math.floor(compareVal % 65536 % 256)
						// mapped = lookupTable.mapValue(sp);
						// 将原始像素值转化为Uint8Array
						storedColorPixelData[canvasImageDataIndex++] = mapped[0];
						storedColorPixelData[canvasImageDataIndex++] = mapped[1];
						storedColorPixelData[canvasImageDataIndex++] = mapped[2];
						storedColorPixelData[canvasImageDataIndex++] = 0;
					}

					// 计算出测量点位置的近值区域
					const lesionAreaImageData = MagicWand.getElliThresArea({
						data: storedColorPixelData,
						width: image.width,
						height: image.height,
						bytes: 4
					},
						minorEllipse,
						activeThresholdVal, //  阈值  
						pointInEllipse
					)

					const lesionAreaBounds = lesionAreaImageData.bounds = {
						minX,
						minY,
						maxX,
						maxY,
					}
					const lesionAreaCenter = {
						x: (lesionAreaBounds.minX + lesionAreaBounds.maxX) / 2,
						y: (lesionAreaBounds.minY + lesionAreaBounds.maxY) / 2,
					}


					// 计算区域的边缘点
					let contoursList = MagicWand.traceContours(lesionAreaImageData)

					contoursList = contoursList.filter(item => item.inner === false
						&& item.points.length > 2)

					contoursList.sort((a, b) => freehandArea(b.points, rowPixelSpacing * columnPixelSpacing) - freehandArea(a.points, rowPixelSpacing * columnPixelSpacing))

					contoursList.splice(1) // 暂时只考虑1个区域
					let allTracePoints = contoursList.reduce((prev, curr) => {
						return [...prev, ...curr.points]
					}, []) // 边缘点数组

					if (allTracePoints.find(item => item.x === 0)) {
						allTracePoints = []
					}

					// 输出边缘点用于勾画出病灶区域
					// allTracePoints = allTracePoints
					// 	.map(item => {
					// 		let { x, y } = item
					// 		return {
					// 			x,
					// 			y,
					// 			highlight: false,
					// 			active: false,
					// 			lines: []
					// 		}
					// 	})
					let peak = csTools.getPeakValue(element, minorEllipse, {rowPixelSpacing});


					lesionPoints = lesionAreaImageData.lesionPoints
					// console.log(lesionPoints)
					// console.log(allTracePoints)
					let { x: maxLengthX, y: maxLengthY } = calculatePolygonDimensions(allTracePoints)
					// console.log(maxLengthX * rowPixelSpacing, maxLengthY * columnPixelSpacing)
					const measurementData = {
						toolName: 'LesionArea',
						showedSUVthresVal,
						showedSUVthresType,
						maxPixelValue,
						maxPoint,
						peak,
						imageId,
						imageWidth: image.width,
						visible: true,
						active: false,
						select: false,
						invalidated: false,
						color: toolColors.getToolColor(),
						canComplete: false,
						highlight: false,
						polyBoundingBox: {
						},
						meanStdDev: {
						},
						area: 0.0,
						sliceThickness,
						unit: "",
						maxLengthX: maxLengthX > 1e9 ? 0 : maxLengthX * rowPixelSpacing / 10,  // cm
						maxLengthY: maxLengthY > 1e9 ? 0 : maxLengthY * columnPixelSpacing / 10, // cm
						lesionPoints: lesionPoints,
						currentPoints, startPoints,
						allTracePoints,
						handles: {
							start: {
								x: eventData.startPoints.image.x,
								y: eventData.startPoints.image.y,
								highlight: true,
								active: false,
							},
							end: {
								x: eventData.currentPoints.image.x,
								y: eventData.currentPoints.image.y,
								highlight: true,
								active: true,
							},
							initialRotation: 0,
							textBox: {
								active: false,
								hasMoved: false,
								moving: false,
								movesIndependently: true,
								drawnIndependently: true,
								allowedOutsideImage: false,
								hasBoundingBox: true,
								x: 0,
								y: 0,
								boundingBox: {
									width: 0,
									height: 0,
									left: 0,
									top: 0
								},
							},
						},
					};
					calcAreaSUV(image, measurementData)

					return measurementData
				}, err => { })

			}
		};

		triggerEvent(
			element,
			csTools.EVENTS.MEASUREMENT_COMPLETED,
			modifiedEventData
		);

		csTools.clearToolState(element, this.name);
		external.cornerstone.updateImage(element);
	}

	pointNearTool(element, data, coords, interactionType) {
		return false;
	}

	updateCachedStats(image, element, data) {
		data.invalidated = false;
	}
}

function calcAreaSUV(image, data) {
	let meanStdDev, meanStdDevSUV, text;

	const seriesModule = external.cornerstone.metaData.get(
		'generalSeriesModule',
		image.imageId
	);
	const columnPixelSpacing = image.columnPixelSpacing || 1;
	const rowPixelSpacing = image.rowPixelSpacing || 1;
	const scaling = columnPixelSpacing * rowPixelSpacing;
	const contoursList = data.allTracePoints
	// console.log(contoursList)
	if (contoursList.length < 1) return
	

	const modality = seriesModule ? seriesModule.modality : null;
	data._modality = modality

	const bounds = {
		left: contoursList[0].x,
		right: contoursList[0].x,
		bottom: contoursList[0].y,
		top: contoursList[0].y,
	};

	for (let i = 0; i < contoursList.length; i++) {
		bounds.left = Math.min(bounds.left, contoursList[i].x);
		bounds.right = Math.max(bounds.right, contoursList[i].x);
		bounds.bottom = Math.max(bounds.bottom, contoursList[i].y);
		bounds.top = Math.min(bounds.top, contoursList[i].y);
	}

	const polyBoundingBox = {
		left: bounds.left,
		top: bounds.top,
		width: Math.abs(bounds.right - bounds.left),
		height: Math.abs(bounds.top - bounds.bottom),
	};

	data.polyBoundingBox = polyBoundingBox;

	const points = data.lesionPoints = data.lesionPoints
	.filter(item => {
		return item.x > bounds.left && item.y > bounds.top && item.x < bounds.right && item.y < bounds.bottom
	}) // 病灶区域 点 
	// console.log(bounds)
	// console.log(points)

	if (points.length < 1) return

	const area =  points.length * scaling;

	data.area = !!area ? area / 100 : 0; // mm2 -> cm2


	if (!image.color) {

	 
 
		meanStdDev = _calculateSUVStats(
			points
		);

		text = `Area:${data.area.toFixed(2)}cm${String.fromCharCode(178)}
			Max:${meanStdDev.max.toFixed(2)}${data.unit}
			Avg:${meanStdDev.mean.toFixed(2)}${data.unit}`;
		if (modality === 'PT') {
			const suvmax = calculateSUV(
				image,
				(meanStdDev.max - image.intercept) / image.slope)
			meanStdDevSUV = {
				mean: calculateSUV(
					image,
					(meanStdDev.mean - image.intercept) / image.slope
				),
				stdDev: calculateSUV(
					image,
					(meanStdDev.stdDev - image.intercept) / image.slope
				),
				max: suvmax
				,
				min: calculateSUV(image, meanStdDev.min - image.intercept) / image.slope,
			};
			text = `Area:${data.area.toFixed(2)}cm${String.fromCharCode(178)}
				Max:${meanStdDevSUV.max.toFixed(2)}${data.unit}
				Avg:${meanStdDevSUV.mean.toFixed(2)}${data.unit}`;
			if (data.peak) {
				meanStdDevSUV.peak = calculateSUV(
					image,
					(data.peak - image.intercept) / image.slope
				)
				text += `
				Peak:${meanStdDevSUV.peak.toFixed(2)}${data.unit}`
			}
		}

		// If the mean and standard deviation values are sane, store them for later retrieval
		if (meanStdDev && !isNaN(meanStdDev.mean)) {
			data.meanStdDev = meanStdDev;
			data.meanStdDevSUV = meanStdDevSUV;
		}
	}

	data.text = text
}

function generateLinearModalityLUT(slope, intercept) {
	return function (storedPixelValue) {
		return storedPixelValue * slope + intercept;
	};
}

function generateNonLinearModalityLUT(modalityLUT) {
	var minValue = modalityLUT.lut[0];
	var maxValue = modalityLUT.lut[modalityLUT.lut.length - 1];
	var maxValueMapped = modalityLUT.firstValueMapped + modalityLUT.lut.length;
	return function (storedPixelValue) {
		if (storedPixelValue < modalityLUT.firstValueMapped) {
			return minValue;
		} else if (storedPixelValue >= maxValueMapped) {
			return maxValue;
		}

		return modalityLUT.lut[storedPixelValue];
	};
}

function getModalityLUT(slope, intercept, modalityLUT) {
	if (modalityLUT) {
		return generateNonLinearModalityLUT(modalityLUT);
	}

	return generateLinearModalityLUT(slope, intercept);
}

function getStoredPixels(image, x, y, width, height) {
	x = Math.round(x);
	y = Math.round(y);
	var storedPixels = [];
	var index = 0;
	var pixelData = image.getPixelData();

	for (var row = 0; row < height; row++) {
		for (var column = 0; column < width; column++) {
			var spIndex = (row + y) * image.columns + (column + x);
			storedPixels[index++] = pixelData[spIndex];
		}
	}

	return storedPixels;
}

function getPointSUVinfo(image, x, y) {
	const pixelValue = getStoredPixels(
		image,
		x,
		y,
		1,
		1
	)[0];
	const stats = {}
	stats.sp = pixelValue;
	stats.mo = stats.sp * image.slope + image.intercept;
	stats.suv = calculateSUV(image, stats.sp);
	return stats
}

function calculatePolygonDimensions(vertices) {
	let minX = Number.MAX_VALUE;
	let minY = Number.MAX_VALUE;
	let maxX = Number.MIN_VALUE;
	let maxY = Number.MIN_VALUE;

	for (let i = 0; i < vertices.length; i++) {
		const vertex = vertices[i];

		minX = Math.min(minX, vertex.x);
		minY = Math.min(minY, vertex.y);
		maxX = Math.max(maxX, vertex.x);
		maxY = Math.max(maxY, vertex.y);
	}

	const x = Math.abs(maxX - minX);
	const y = Math.abs(maxY - minY);

	return { y, x };

}


/**
 *
 *
 * @param {*} sp
 * @param {*} rectangle
 * @returns {{ count, number, mean: number,  variance: number,  stdDev: number,  min: number,  max: number }}
 */
function _calculateSUVStats(sp) {
	let sum = 0;
	let sumSquared = 0;
	let count = 0; 
	let min =   sp[0].val  ;
	let max =   sp[0].val ;
  
	for (let index = 0; index < sp.length; ) { 
		sum += sp[index].val;
		sumSquared += sp[index].val * sp[index].val;
		min = Math.min(min, sp[index].val);
		max = Math.max(max, sp[index].val);
		count++; // TODO: Wouldn't this just be sp.length?
		index++; 
	}
  
	if (count === 0) {
	  return {
		count,
		mean: 0.0,
		variance: 0.0,
		stdDev: 0.0,
		min: 0.0,
		max: 0.0,
	  };
	}
  
	const mean = sum / count;
	const variance = sumSquared / count - mean * mean;
  
	return {
	  count,
	  mean,
	  variance,
	  stdDev: Math.sqrt(variance),
	  min,
	  max,
	};
  }