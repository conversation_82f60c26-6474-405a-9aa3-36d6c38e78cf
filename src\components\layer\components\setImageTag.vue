<template>
    <div style="height: 100%">
        <el-dialog
            append-to-body
            title="患者信息配置"
            :visible.sync="visible"
            :close-on-click-modal="false"
            width="750px"
            custom-class="my-dialog">
            <div class="c-main">
                <div class="header">
                    <h5>图像信息</h5>
                    <div>
                        <span>显示方向信息 </span>
                        <el-switch
                            v-model="showDirection"
                            active-color="#13ce66"
                            inactive-color="#eeeeee"
                            @change="onChangeShow">
                        </el-switch>
                    </div>
                    <div>
                        <span class="i-padding-right-10">设备名称</span>
                        <el-select v-model="modality" size="mini" style="width: 140px" placeholder="">
                            <el-option
                            v-for="item in options.modality"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="content">
                    <div class="menu">
                        <el-scrollbar style="height: 100%" class="overflow-x-hide">
                            <el-checkbox-group v-model="checkedTag" @change="handleCheckedTagChange">
                                <el-checkbox v-for="tag in imageInfoList" :label="tag" :key="tag.id">{{ tag.tagName }}</el-checkbox>
                            </el-checkbox-group>
                        </el-scrollbar>
                    </div>
                    <div class="menu-area">
                        <div class="menu-box" :class="{active: selectItem == 'leftTop'}" @click="onClickArea('leftTop')">
                            <h6>左上角</h6>
                            <ul class="menu-content">
                                <li v-for="item in showList.leftTop">{{ item.tagName }}</li>
                            </ul>
                        </div>
                        <div class="menu-box" :class="{active: selectItem == 'rightTop'}" @click="onClickArea('rightTop')">
                            <h6>右上角</h6>
                            <ul class="menu-content">
                                <li v-for="item in showList.rightTop">{{ item.tagName }}</li>
                            </ul>
                        </div>
                        <div class="menu-box i-amend-margin" :class="{active: selectItem == 'leftBottom'}" @click="onClickArea('leftBottom')">
                            <h6>左下角</h6>
                            <ul class="menu-content">
                                <li v-for="item in showList.leftBottom">{{ item.tagName }}</li>
                            </ul>
                        </div>
                        <div class="menu-box i-amend-margin" :class="{active: selectItem == 'rightBottom'}" @click="onClickArea('rightBottom')">
                            <h6>右下角</h6>
                            <ul class="menu-content">
                                <li v-for="item in showList.rightBottom">{{ item.tagName }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="footer">
                        <el-button type="small" @click="onClickSave" icon="el-icon-document-checked" >保 存</el-button>
                        <el-button type="small" @click="visible = false" icon="el-icon-close" >关 闭</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import getConfigByStorageKey, { getConfigByKey } from '$library/utils/configStorage.js'

export default {
    name: 'setImageTag',
    data() {
        return {
            visible: false,
            modality: 'CT',
            checkedTag: [],
            selectItem: '', // 选中四个角中一个
            storageKey: 'configs-modalityTagItems',
            options: {
                modality: [
                    { label: 'CT', value: 'CT' },
                    { label: 'PT', value: 'PT' },
                    { label: 'MR', value: 'MR' },
                    { label: 'NM', value: 'NM' },
                    { label: '其它', value: 'default' }
                ]
            },
            imageInfoList: [
                // value 首字母大写都是 tag 获取，小写是其它方式获取（cornerstone 元数据获取， vue 计算属性变化获取）
                // tag 获取
                { id: 1, value: 'ManufacturersModelName', tagName: "Manufacturer's Model Name" },
                { id: 2, value: 'StudyID', tagName: "Study ID" },
                { id: 3, value: 'SeriesNumber', tagName: "Series Number" },
                // 元数据
                { id: 4, value: 'sliceLocation', tagName: "Slice Location" },  // 切片位置  TODO 相同 7
                // 计算属性
                { id: 5, value: 'instanceNumber', tagName: "Instance Number" },// 1/10 当前图像所在序列数
                // 计算属性
                { id: 6, value: 'wwwc', tagName: "WL" },                       // 窗宽窗位
                // 元数据
                { id: 7, value: 'ImagePositionPatient', tagName: "Image Position Patient" }, // TODO 相同 4
                { id: 8, value: 'XRayTubeCurrent', tagName: "X-Ray Tube Current" },
                { id: 9, value: 'Exposure', tagName: "Exposure", name: 'mA:' },
                { id: 10, value: 'KVP', tagName: "KVP" },
                { id: 11, value: 'InstitutionName', tagName: "Institution Name" },
                { id: 12, value: 'AccessionNumber', tagName: "Accession Number" },
                { id: 13, value: 'PatientsName', tagName: "Patient's Name" },
                { id: 14, value: 'PatientsSexAgeID', tagName: "Patient's Sex Age ID" },
                { id: 15, value: 'PatientsSex', tagName: "Patient's Sex" },
                { id: 16, value: 'PatientsAge', tagName: "Patient's Age" },
                { id: 17, value: 'PatientID', tagName: "Patient ID" },
                { id: 18, value: 'PatientsBirthDate', tagName: "Patient's Birth Date" },
                { id: 19, value: 'StudyDate', tagName: "Study Date" },
                { id: 20, value: 'StudyTime', tagName: "Study Time" },
                { id: 21, value: 'AcquisitionTime', tagName: "Acquisition Time" },
                { id: 22, value: 'PatientPosition', tagName: "Patient Position" },
                { id: 23, value: 'SeriesDescription', tagName: "Series Description" },
                { id: 25, value: 'SeriesDate', tagName: "Series Date" },
                { id: 26, value: 'SeriesTime', tagName: "Series Time" },
                // 设备类型
                { id: 24, value: 'Modality', tagName: "Modality" },
                // 元数据获取
                { id: 27, value: 'sliceThickness', tagName: "Slice Thickness" },
                // 计算属性
                { id: 28, value: 'zoom', tagName: "Zoom Factor" },   
                { id: 29, value: 'PercentPhaseFieldOfView', tagName: "Percent Phase Field Of View" },
                { id: 30, value: 'SequenceName', tagName: "Sequence Name" },
                { id: 31, value: 'RepetitionTime', tagName: "Repetition Time" }, 
                { id: 32, value: 'EchoTime', tagName: "Echo Time" },   

                { id: 33, value: 'Radiopharmaceutical', tagName: "Radiopharmaceutical" },   
                { id: 34, value: 'RadionuclideName', tagName: " radionuclide name" },
                { id: 35, value: 'TracerName', tagName: "tracer name" },
                { id: 36, value: 'BatchDescription', tagName: "batch description" },
            ],
            showList: {
                leftTop: [
                    { id: 1, type: 1, value: 'x00081110', tagName: "Manufacturer's Model Name"},
                    { id: 2, type: 1, value: 'x00200010', tagName: "Study ID", name: 'Ex:'},
                    { id: 3, type: 1, value: 'x00200011', tagName: "Series Number", name: 'Se:'},
                ],
                rightTop: [],
                leftBottom: [],
                rightBottom: []
            },
            modalityTagItems: {
                CT: { leftTop: [], rightTop: [], leftBottom: [], rightBottom: [] },
                PT: { leftTop: [], rightTop: [], leftBottom: [], rightBottom: [] },
                MR: { leftTop: [], rightTop: [], leftBottom: [], rightBottom: [] },
                NM: { leftTop: [], rightTop: [], leftBottom: [], rightBottom: [] },
                default: { leftTop: [], rightTop: [], leftBottom: [], rightBottom: [] },
            },


            // 显示方向信息
            showDirection: true,
            storageDirection: 'configs-show-direction',
        }
    },
    activated() {
        this.$nextTick(() => {
            this.visible = true;
        })
    },
    mounted() {
        this.initData();
    },
    watch: {
        // 监听设备改变
        modality(newVal, oldVal) {
            // 存储当前配置,
            this.modalityTagItems[oldVal] = this.showList;

            // 替换新的显示
            this.showList = this.modalityTagItems[newVal];

            this.checkedTag = [];
            this.selectItem = '';
        },
        // 监听改变区域
        selectItem(val) {
            if (!val) {
                return;
            }
            // 重新覆盖 list，不覆盖设置选中不生效
            this.showList[val].map((item) => {
                const idx = this.imageInfoList.findIndex(sla => sla.id === item.id)
                this.imageInfoList[idx] = item
            })
            this.checkedTag = this.showList[val];
        }
    },
    methods: {
        initData() {
            this.modalityTagItems = getConfigByStorageKey(this.storageKey);
            if (this.modality) {
                this.showList = this.modalityTagItems[this.modality];
            }
            this.showDirection = getConfigByStorageKey(this.storageDirection);
        },
        // 左侧列表选中触发事件
        handleCheckedTagChange(val) {
            // 没选中
            if (!this.selectItem) {
                return;
            }
            this.showList[this.selectItem] = this.$fun.deepClone(val);
        },
        // 点击上右下左...区域
        onClickArea(val){
            this.selectItem = val;
        },
        onChangeShow(val) {
            localStorage.setItem(this.storageDirection, val)
            this.$store.state.showDirection = this.showDirection;
            this.$message({
                message: '修改成功！',
                type: 'success'
            });
        },
        // 点击保存
        onClickSave() {
            const modalityTagItems = this.$fun.deepClone(this.modalityTagItems);
            localStorage.setItem(this.storageKey, JSON.stringify(modalityTagItems))

            this.$store.commit('setTagItems', modalityTagItems);

            // 保存方向信息
            localStorage.setItem(this.storageDirection, this.showDirection);
            this.$store.state.showDirection = this.showDirection;

            this.$message({
                message: '保存成功！',
                type: 'success'
            });
        }
    }
}
</script>
<style lang="scss" scoped>
.header{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 14px;
}
.i-padding-right-10{
    padding-right: 10px;
}
.content{
    height: 500px;
    display: flex;
    .menu{
        width: 240px;
        padding: 5px 0px 5px 10px;
        margin-right: 30px;
        border: 1px solid #d2d2d2;
        border-radius: 2px;
        .el-checkbox{
            display: block;
            ::v-deep .el-checkbox__label{
                line-height: 24px;
            }
        }
    }
    .menu-area{
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .menu-box{
            width: 220px;
            height: 240px;
            margin-bottom: 10px;
            display: flex;
            flex-direction: column;
            cursor: pointer;
            &.active{
                h6{
                    background: #3a8ee6;
                    color: white;
                }
            }
            &:hover{
                h6{
                    font-weight: bold;
                }       
            }
            &.i-amend-margin{
                margin-top: 10px;
                margin-bottom: 0px;
            }
            h6{
                height: 24px;
                line-height: 24px;
                padding-left: 6px;
            }
            .menu-content{
                flex: 1;
                padding: 6px 0px 6px 6px;
                border: 1px solid #d2d2d2;
                border-radius: 2px;
                li {
                    display: block;
                    height: 22px;
                    line-height: 22px;
                }
            }
        }
    }
}
.footer{
    margin-top: 10px;
    text-align: right;  
}
</style>